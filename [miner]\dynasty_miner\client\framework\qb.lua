if Config.Framework ~= "QBCORE" then
    return
end

Citizen.CreateThreadNow(function()
    if Config.Framework == "QBCORE" then
        QBCore = exports['qb-core']:GetCoreObject()
        while QBCore.Functions.GetPlayerData().job == nil do
            Wait(10)
        end
        PlayerData = QBCore.Functions.GetPlayerData()
        Wait(200)
        if Config.RequireJob then
            if Config.MinerJob[PlayerData.job.name] then
                CanInteract = true
            else
                CanInteract = false
            end
        else
            CanInteract = true
        end
    end
end)

---@OnPlayerLoaded event for QBCore
AddEventHandler('QBCore:Client:OnPlayerLoaded', function(Player)
    Player = QBCore.Functions.GetPlayerData()
    if not Player then return Wait(1250) end
    PlayerData = Player
    PlayerJob = PlayerData.job
    PlayerLoaded = true
    for _, object in pairs(AllObjects) do
        if object then
            DeleteEntity(object)
        end
    end
    if Peds.minerPed and Peds.sellerPed and Peds.questPed and Peds.leaderboardPed then
        DeleteEntity(Peds.minerPed)
        DeleteEntity(Peds.sellerPed)
        DeleteEntity(Peds.questPed)
        DeleteEntity(Peds.leaderboardPed)
    end
    Wait(2000)
    CreateObjects()
    LoadBlips()
end)

---@OnJobUpdate event for QBCore
RegisterNetEvent('QBCore:Client:OnJobUpdate')
AddEventHandler('QBCore:Client:OnJobUpdate', function(job)
    PlayerData.job = job
    PlayerJob = job
    for _, blip in pairs(CreatedBlips) do
        RemoveBlip(blip)
    end
    if Config.RequireJob then
        if Config.MinerJob[PlayerData.job.name] then
            CanInteract = true
        else
            CanInteract = false
        end
    else
        CanInteract = true
    end
    UpdateTargetOptions()
    LoadedBlips = nil
    LoadBlips()
end)

---@TriggerServerCallback event for QBCore.
function TriggerServerCallback(name, cb, ...)
    QBCore.Functions.TriggerCallback(name, cb, ...)
end
