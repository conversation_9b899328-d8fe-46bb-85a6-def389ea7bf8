fx_version 'cerulean'
game 'gta5'
name 'PLT Lumberjack Jobs Streams'
contact '<EMAIL>'
discord 'https://discord.gg/3h8tebmBeD'
website 'https://polat.tebex.io/'
tutorial'https://polat.gitbook.io/'
author 'BzZz&Lund&Rotto&PLT streams necessary for plt_lumberjack'
lua54 'yes'
description '<PERSON>(JCB owner):https://discord.gg/DNVR5eK5XQ, BzZz(tree props):https://bzzz.tebex.io, Rotto(platform)https://discord.gg/cna7mTXK8g, PLT: https://polat.tebex.io/'
data_file 'DLC_ITYP_REQUEST' 'polat_lumberjack_tree.ytyp'
data_file 'DLC_ITYP_REQUEST' 'polat_lumberjack_ramp001.ytyp'
data_file 'DLC_ITYP_REQUEST' 'polat_lumberjack_chainsaw003.ytyp'   
data_file 'HANDLING_FILE' 'handling.meta'
data_file 'VEHICLE_METADATA_FILE' 'vehicles.meta'
--data_file 'CARCOLS_FILE' 'carcols.meta'                 --If you activate these two lines, the sirens of the telehandler will be active. But this may works like a police siren because of the scripts used on most servers.
--data_file 'VEHICLE_VARIATION_FILE' 'carvariations.meta' --If you activate these two lines, the sirens of the telehandler will be active. But this may works like a police siren because of the scripts used on most servers.
files {'*.meta'}
dependency '/assetpacks'