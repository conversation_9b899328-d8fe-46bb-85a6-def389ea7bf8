-- DON'T TOUCH THESE --
Config = {}
Config.Shops = {}
Config.Locales = {}
-----------------------

Config.Locale = 'en' --Language option. Edit the locale.lua file to add more, or translate the texts


Config.Shops = {
    ['onyxtheatres'] = {
        paymentType = 1,
        shopName = "Onyx Theatres", 
        sellOnly = false, 
        sellJob = {'onyx','rea'}, 
        infinite = false, 
        coords = vector3(350.9744, 176.0776, 102.9960),
        logoPath = "img/onyxtheatres.png",
        items = {
            ['ticket'] = {label = "Cinema Ticket", buyPrice = 7500, maxCount = 60, sellPrice = 7500,},
        }
    },

    ['grindustries'] = {
        paymentType = 1,
        shopName = "Import Export", 
        sellOnly = false, 
        infinite = false, 
        coords = vector3(-1082.5814, -247.0314, 37.5985),
        logoPath = "img/grindustries.png",
        blip = {
            color = 1,
            sprite = 271,
            scale = 0.1
        },
        cashierPed = {
            ped = 'a_f_y_business_01',
            coords = vector3(-1083.1864, -245.7836, 36.763),
            heading = 213.0570
        },
        items = {
            ['boarmeat'] = {label = "Boar Meat", buyPrice = 2375, maxCount = 50000, sellPrice = 2375,},
            ['boartusk'] = {label = "Boar Tusk", buyPrice = 2375, maxCount = 50000, sellPrice = 2375,},
            ['deermeat'] = {label = "Deer Meat", buyPrice = 2060, maxCount = 50000, sellPrice = 2060,},
            ['deerskin'] = {label = "Deer Skin", buyPrice = 1375, maxCount = 50000, sellPrice = 1375,},
            ['coyotemeat'] = {label = "Coyote Meat", buyPrice = 2375, maxCount = 50000, sellPrice = 2375,},
            ['coyotefur'] = {label = "Coyote Fur", buyPrice = 3375, maxCount = 50000, sellPrice = 3375,},

            ['raw_meat'] = {label = "Raw Meat", buyPrice = 400, maxCount = 50000, sellPrice = 400,},

            ['skin_deer_ruined'] = {label = "Tattered Deer Pelt", buyPrice = 625, maxCount = 50000, sellPrice = 625,},
            ['skin_deer_low'] = {label = "Worn Deer Pelt", buyPrice = 700, maxCount = 50000, sellPrice = 700,},
            ['skin_deer_medium'] = {label = "Supple Deer Pelt", buyPrice = 850, maxCount = 50000, sellPrice = 850,},
            ['skin_deer_good'] = {label = "Prime Deer Pelt", buyPrice = 950, maxCount = 50000, sellPrice = 950,},
            ['skin_deer_perfect'] = {label = "Flawless Deer Pelt", buyPrice = 1125, maxCount = 50000, sellPrice = 1125,},

            ['skin_boar_ruined'] = {label = "Tattered Boar Pelt", buyPrice = 625, maxCount = 50000, sellPrice = 625,},
            ['skin_boar_low'] = {label = "Worn Boar Pelt", buyPrice = 700, maxCount = 50000, sellPrice = 700,},
            ['skin_boar_medium'] = {label = "Supple Boar Pelt", buyPrice = 850, maxCount = 50000, sellPrice = 850,},
            ['skin_boar_good'] = {label = "Prime Boar Pelt", buyPrice = 950, maxCount = 50000, sellPrice = 950,},
            ['skin_boar_perfect'] = {label = "Flawless Boar Pelt", buyPrice = 1125, maxCount = 50000, sellPrice = 1125,},

            ['skin_coyote_ruined'] = {label = "Tattered Coyote Pelt", buyPrice = 600, maxCount = 50000, sellPrice = 600,},
            ['skin_coyote_low'] = {label = "Worn Coyote Pelt", buyPrice = 625, maxCount = 50000, sellPrice = 625,},
            ['skin_coyote_medium'] = {label = "Supple Coyote Pelt", buyPrice = 665, maxCount = 50000, sellPrice = 665,},
            ['skin_coyote_good'] = {label = "Prime Coyote Pelt", buyPrice = 725, maxCount = 50000, sellPrice = 725,},
            ['skin_coyote_perfect'] = {label = "Flawless Coyote Pelt", buyPrice = 1000, maxCount = 50000, sellPrice = 1000,},

            -- ['sardine'] = {label = "Sardine Fish", buyPrice = 370, maxCount = 50000, sellPrice = 370,},
            -- ['mackerel'] = {label = "Mackerel Fish", buyPrice = 370, maxCount = 50000, sellPrice = 370,},
            -- ['prawn'] = {label = "Prawns", buyPrice = 370, maxCount = 50000, sellPrice = 370,},
            -- ['tunafish'] = {label = "Tuna Fish", buyPrice = 400, maxCount = 50000, sellPrice = 400,},
            -- ['anchovy'] = {label = "Anchovy", buyPrice = 390, maxCount = 50000, sellPrice = 390,},
            -- ['squid'] = {label = "Squid", buyPrice = 420, maxCount = 50000, sellPrice = 420,},
            -- ['crab'] = {label = "Crab", buyPrice = 435, maxCount = 50000, sellPrice = 435,},
            -- ['seer'] = {label = "Seer Fish", buyPrice = 475, maxCount = 50000, sellPrice = 475,},
            -- ['shark'] = {label = "Shark", buyPrice = 900, maxCount = 50000, sellPrice = 900,},

            ['potato'] = {label = "Potato", buyPrice = 325, maxCount = 50000, sellPrice = 300,},
            ['tomato'] = {label = "Tomato", buyPrice = 350, maxCount = 50000, sellPrice = 325,},
            ['orange'] = {label = "Orange", buyPrice = 375, maxCount = 50000, sellPrice = 350,},
            ['cabbage'] = {label = "Cabbage", buyPrice = 350, maxCount = 50000, sellPrice = 300,},
            ['coffee_beans'] = {label = "Coffee Beans", buyPrice = 200, maxCount = 50000, sellPrice = 175,},

            ['coal_ore'] = {label = "Coal Ore", buyPrice = 590, maxCount = 50000, sellPrice = 590,},
            ['flint'] = {label = "Flint", buyPrice = 590, maxCount = 50000, sellPrice = 590,},
            ['sulfur_chunk'] = {label = "Sulfur Chunk", buyPrice = 625, maxCount = 50000, sellPrice = 625,},
            ['gold_nugget'] = {label = "Gold Nugget", buyPrice = 665, maxCount = 50000, sellPrice = 665,},
            ['gold_dust'] = {label = "Gold Dust", buyPrice = 675, maxCount = 50000, sellPrice = 675,},
            ['quartz_crystal'] = {label = "Quartz Crystal", buyPrice = 725, maxCount = 50000, sellPrice = 725,},
            ['emerald_crystal'] = {label = "Emerald Crystal", buyPrice = 790, maxCount = 50000, sellPrice = 790,},
            ['beryl_chunk'] = {label = "Beryl Chunk", buyPrice = 800, maxCount = 50000, sellPrice = 800,},
            ['green_garnet'] = {label = "Green Garnet", buyPrice = 825, maxCount = 50000, sellPrice = 825,},
            ['ruby_crystal'] = {label = "Ruby Crystal", buyPrice = 900, maxCount = 50000, sellPrice = 900,},
            ['corundum_chunk'] = {label = "Corundum Chunk", buyPrice = 900, maxCount = 50000, sellPrice = 900,},
            ['pink_sapphire'] = {label = "Pink Sapphire", buyPrice = 925, maxCount = 50000, sellPrice = 925,},
            ['amethyst_geode'] = {label = "Amethyst Geode", buyPrice = 1025, maxCount = 50000, sellPrice = 1025,},
            ['purple_quartz'] = {label = "Purple Quartz", buyPrice = 1140, maxCount = 50000, sellPrice = 1140,},
            ['clear_crystal'] = {label = "Clear Crystal", buyPrice = 1200, maxCount = 50000, sellPrice = 1200,},
            ['diamond_crystal'] = {label = "Diamond Crystal", buyPrice = 1325, maxCount = 50000, sellPrice = 1325,},
            ['graphite_chunk'] = {label = "Graphite Chunk", buyPrice = 1475, maxCount = 50000, sellPrice = 1475,},
            ['blue_diamond'] = {label = "Blue Diamond", buyPrice = 1825, maxCount = 50000, sellPrice = 1825,},
            ['copper'] = {label = "Copper", buyPrice = 1000, maxCount = 50000, sellPrice = 1000,},
            ['iron'] = {label = "Iron", buyPrice = 1075, maxCount = 50000, sellPrice = 1075,},

            ['dendrogyra_coral'] = {label = "Dendrogyra", buyPrice = 1875, maxCount = 50000, sellPrice = 1875,},
            ['antipatharia_coral'] = {label = "Antipatharia", buyPrice = 2000, maxCount = 50000, sellPrice = 2000,},
            ['montastraea_coral'] = {label = "Montastraea", buyPrice = 2125, maxCount = 50000, sellPrice = 2125,},
            ['keshi_pearl'] = {label = "Keshi Pearls", buyPrice = 2250, maxCount = 50000, sellPrice = 2250,},
            ['cortez_pearl'] = {label = "Cortez Pearls", buyPrice = 2375, maxCount = 50000, sellPrice = 2375,},
            ['marine_fossil'] = {label = "Marine Fossil", buyPrice = 2625, maxCount = 50000, sellPrice = 2625,},

            -- peaks fishing - REBALANCED (reduced by ~35% from original)

             -- Common Fish (7,000 - 13,000)
            ['bitterling'] = {label = "Bitterling", buyPrice = 8300, maxCount = 1000000, sellPrice = 8300},
            ['pale_chub'] = {label = "Pale Chub", buyPrice = 9400, maxCount = 1000000, sellPrice = 9400},
            ['dace'] = {label = "Dace", buyPrice = 7300, maxCount = 1000000, sellPrice = 7300},
            ['carp'] = {label = "Carp", buyPrice = 10900, maxCount = 1000000, sellPrice = 10900},
            ['goldfish'] = {label = "Goldfish", buyPrice = 5700, maxCount = 1000000, sellPrice = 5700},
            ['killifish'] = {label = "Killifish", buyPrice = 6200, maxCount = 1000000, sellPrice = 6200},
            ['crawfish'] = {label = "Crawfish", buyPrice = 6800, maxCount = 1000000, sellPrice = 6800},
            ['tadpole'] = {label = "Tadpole", buyPrice = 5700, maxCount = 1000000, sellPrice = 5700},
            ['frog'] = {label = "Frog", buyPrice = 7300, maxCount = 1000000, sellPrice = 7300},
            ['freshwater_goby'] = {label = "Freshwater Goby", buyPrice = 7800, maxCount = 1000000, sellPrice = 7800},
            ['loach'] = {label = "Loach", buyPrice = 8800, maxCount = 1000000, sellPrice = 8800},
            ['bluegill'] = {label = "Bluegill", buyPrice = 9900, maxCount = 1000000, sellPrice = 9900},
            ['yellow_perch'] = {label = "Yellow Perch", buyPrice = 10400, maxCount = 1000000, sellPrice = 10400},
            ['black_bass'] = {label = "Black Bass", buyPrice = 10900, maxCount = 1000000, sellPrice = 10900},
            ['tilapia'] = {label = "Tilapia", buyPrice = 9400, maxCount = 1000000, sellPrice = 9400},
            ['pond_smelt'] = {label = "Pond Smelt", buyPrice = 6200, maxCount = 1000000, sellPrice = 6200},
            ['sweetfish'] = {label = "Sweetfish", buyPrice = 6800, maxCount = 1000000, sellPrice = 6800},
            ['horse_mackerel'] = {label = "Horse Mackerel", buyPrice = 6200, maxCount = 1000000, sellPrice = 6200},
            ['sea_bass'] = {label = "Sea Bass", buyPrice = 10900, maxCount = 1000000, sellPrice = 10900},
            ['dab'] = {label = "Dab", buyPrice = 7300, maxCount = 1000000, sellPrice = 7300},
            ['olive_flounder'] = {label = "Olive Flounder", buyPrice = 10400, maxCount = 1000000, sellPrice = 10400},
            
            -- Uncommon Fish (14,000 - 25,000)
            ['koi'] = {label = "Koi", buyPrice = 14300, maxCount = 1000000, sellPrice = 14300},
            ['pop_eyed_goldfish'] = {label = "Pop-eyed Goldfish", buyPrice = 11700, maxCount = 1000000, sellPrice = 11700},
            ['ranchu_goldfish'] = {label = "Ranchu Goldfish", buyPrice = 12700, maxCount = 1000000, sellPrice = 12700},
            ['angelfish'] = {label = "Angel Fish", buyPrice = 13500, maxCount = 1000000, sellPrice = 13500},
            ['betta'] = {label = "Betta", buyPrice = 12000, maxCount = 1000000, sellPrice = 12000},
            ['neon_tetra'] = {label = "Neon Tetra", buyPrice = 15600, maxCount = 1000000, sellPrice = 15600},
            ['rainbowfish'] = {label = "Rainbow Fish", buyPrice = 14600, maxCount = 1000000, sellPrice = 14600},
            ['sea_butterfly'] = {label = "Sea Butterfly", buyPrice = 15600, maxCount = 1000000, sellPrice = 15600},
            ['seahorse'] = {label = "Seahorse", buyPrice = 15100, maxCount = 1000000, sellPrice = 15100},
            ['clownfish'] = {label = "Clown Fish", buyPrice = 13000, maxCount = 1000000, sellPrice = 13000},
            ['surgeonfish'] = {label = "Surgeon Fish", buyPrice = 11400, maxCount = 1000000, sellPrice = 11400},
            ['butterfly_fish'] = {label = "Butterfly Fish", buyPrice = 16100, maxCount = 1000000, sellPrice = 16100},
            ['zebra_turkeyfish'] = {label = "Zebra Turkeyfish", buyPrice = 12500, maxCount = 1000000, sellPrice = 12500},
            ['barred_knifejaw'] = {label = "Barred Knifejaw", buyPrice = 17200, maxCount = 1000000, sellPrice = 17200},
            ['red_snapper'] = {label = "Red Snapper", buyPrice = 20800, maxCount = 1000000, sellPrice = 20800},
            ['moray_eel'] = {label = "Moray Eel", buyPrice = 13800, maxCount = 1000000, sellPrice = 13800},
            ['ribbon_eel'] = {label = "Ribbon Eel", buyPrice = 14000, maxCount = 1000000, sellPrice = 14000},

            -- Rare Fish (26,000 - 42,000)
            ['sturgeon'] = {label = "Sturgeon", buyPrice = 28600, maxCount = 1000000, sellPrice = 28600},
            ['giant_snakehead'] = {label = "Giant Snakehead", buyPrice = 31200, maxCount = 1000000, sellPrice = 31200},
            ['golden_trout'] = {label = "Golden Trout", buyPrice = 23400, maxCount = 1000000, sellPrice = 23400},
            ['stringfish'] = {label = "String Fish", buyPrice = 23400, maxCount = 1000000, sellPrice = 23400},
            ['king_salmon'] = {label = "King Salmon", buyPrice = 26000, maxCount = 1000000, sellPrice = 26000},
            ['napoleonfish'] = {label = "Napoleon Fish", buyPrice = 29600, maxCount = 1000000, sellPrice = 29600},
            ['dorado'] = {label = "Dorado", buyPrice = 25500, maxCount = 1000000, sellPrice = 25500},
            ['gar'] = {label = "Gar", buyPrice = 26000, maxCount = 1000000, sellPrice = 26000},
            ['arapaima'] = {label = "Arapaima", buyPrice = 29100, maxCount = 1000000, sellPrice = 29100},
            ['tuna_fish'] = {label = "Tuna", buyPrice = 25000, maxCount = 1000000, sellPrice = 25000},
            ['blue_marlin'] = {label = "Blue Marlin", buyPrice = 33800, maxCount = 1000000, sellPrice = 33800},
            ['giant_trevally'] = {label = "Giant Trevally", buyPrice = 28600, maxCount = 1000000, sellPrice = 28600},
            ['mahi_mahi'] = {label = "Mahi Mahi", buyPrice = 28600, maxCount = 1000000, sellPrice = 28600},
            ['ray'] = {label = "Ray", buyPrice = 20800, maxCount = 1000000, sellPrice = 20800},

            -- Epic Fish (52,000 - 85,000)

            ['ocean_sunfish'] = {label = "Ocean Sunfish", buyPrice = 41600, maxCount = 1000000, sellPrice = 41600},
            ['oarfish'] = {label = "Oar Fish", buyPrice = 57200, maxCount = 1000000, sellPrice = 57200},

            -- Legendary Fish (104,000 - 169,000)

            ['coelacanth'] = {label = "Coelacanth", buyPrice = 91000, maxCount = 1000000, sellPrice = 91000},
            ['barreleye'] = {label = "Barrel Eye", buyPrice = 104000, maxCount = 1000000, sellPrice = 104000},

            -- Treasure Items (19,500 - 52,000)
            ['old_boot'] = {label = "Old Boot", buyPrice = 15600, maxCount = 1000000, sellPrice = 15600},
            ['rusty_anchor'] = {label = "Rusty Anchor", buyPrice = 20800, maxCount = 1000000, sellPrice = 20800},
            ['broken_bottle'] = {label = "Antique Bottle", buyPrice = 28600, maxCount = 1000000, sellPrice = 28600},
            ['diving_watch'] = {label = "Vintage Diving Watch", buyPrice = 20800, maxCount = 1000000, sellPrice = 20800},
            ['shipwreck_plank'] = {label = "Shipwreck Fragment", buyPrice = 18200, maxCount = 1000000, sellPrice = 18200},
        }   
    },

    ['illegalstore'] = {
        paymentType = 3,        
        shopName = "DC Medicalstore", 
        sellOnly = false, 
        infinite = false, 
        coords = vector3(1653.4376, 4856.6528, 42.0144),
        logoPath = "img/illegalstore.png",
        blip = {
            color = 25,
            sprite = 140,
            scale = 0.8
        },
        cashierPed = {
            ped = 'g_m_y_ballasout_01',
            coords = vector3(1653.4376, 4856.6528, 41.0144),
            heading = 194.7885
        },
        items = {
            ['meth'] = {label = "Meth", buyPrice = 320, maxCount = 50000, sellPrice = 320,},
            ['tweak'] = {label = "Tweak", buyPrice = 2000, maxCount = 50000, sellPrice = 2000,},
            -- ['idukki_gold'] = {label = "Idukki Gold", buyPrice = 1900, maxCount = 50000, sellPrice = 1800,},
            -- ['weed_lemonhaze'] = {label = "Lemonhaze Weed", buyPrice = 1850, maxCount = 50000, sellPrice = 1750,},
            -- ['cocaine'] = {label = "Cocaine", buyPrice = 2200, maxCount = 50000, sellPrice = 2100,},

            -- ['coca'] = {label = "Cocaine Leaves", buyPrice = 250, maxCount = 50000, sellPrice = 200,},
            ['coke_pooch'] = {label = "Cocaine Pouch", buyPrice = 250, maxCount = 50000, sellPrice = 250,},
            ['weed_packaged'] = {label = "Packaged Weed", buyPrice = 3220, maxCount = 50000, sellPrice = 3220,},
            ['meth_packaged'] = {label = "Packaged Meth", buyPrice = 3800, maxCount = 50000, sellPrice = 3800,},
            ['cocaine_packaged'] = {label = "Packaged Cocaine", buyPrice = 4500, maxCount = 50000, sellPrice = 4500,},
            ['inside-weed'] = {label = "Special Weed", buyPrice = 1150, maxCount = 50000, sellPrice = 1150,},
            -- ['inside-meth'] = {label = "Packaged Weed", buyPrice = 1325, maxCount = 50000, sellPrice = 1275,},
            ['inside-coke'] = {label = "Special Coke", buyPrice = 1375, maxCount = 50000, sellPrice = 1375,},
            ['ls_cocaine_bag'] = {label = "Cocaine Bag", buyPrice =10500, maxCount = 50000, sellPrice = 10500,},
            ['ls_crack_bag'] = {label = "Crack Bag", buyPrice = 17500, maxCount = 50000, sellPrice = 17500,},

            ['ls_plain_jane_joint'] = {label = "Plain Jane Joint", buyPrice = 4800, maxCount = 50000, sellPrice = 4800,},
            ['ls_banana_kush_joint'] = {label = "Bananakush Joint", buyPrice = 4900, maxCount = 50000, sellPrice = 4900,},
            ['ls_blue_dream_joint'] = {label = "Blue Dream Joint", buyPrice = 4950, maxCount = 50000, sellPrice = 4950,},
            ['ls_purple_haze_joint'] = {label = "Purple Haze Joint", buyPrice = 5000, maxCount = 50000, sellPrice = 5000,},
            ['ls_orange_crush_joint'] = {label = "Orange Crush Joint", buyPrice = 5100, maxCount = 50000, sellPrice = 5100,},
            ['ls_cosmic_kush_joint'] = {label = "Cosmic Kush Joint", buyPrice = 5200, maxCount = 50000, sellPrice = 5200,},
            ['ls_plain_jane_bag'] = {label = "Plain Jane Bag", buyPrice = 39500, maxCount = 50000, sellPrice = 39500,},
            ['ls_banana_kush_bag'] = {label = "Bananakush Bag", buyPrice = 41500, maxCount = 50000, sellPrice = 41500,},
            ['ls_blue_dream_bag'] = {label = "Blue Dream Bag", buyPrice = 42500, maxCount = 50000, sellPrice = 42500,},
            ['ls_purple_haze_bag'] = {label = "Purple Haze Bag", buyPrice = 43500, maxCount = 50000, sellPrice = 43500,},
            ['ls_orange_crush_bag'] = {label = "Orange Crush Bag", buyPrice = 44500, maxCount = 50000, sellPrice = 42500,},
            ['ls_cosmic_kush_bag'] = {label = "Cosmic Kush Bag", buyPrice = 45670, maxCount = 50000, sellPrice = 43670,},
            -- ['weaponcrate'] = {label = "Crate of Weapons", buyPrice = 50000, maxCount = 50000, sellPrice = 50000,},
            -- ['blueprintbox'] = {label = "Blue Print", buyPrice = 25000, maxCount = 50000, sellPrice = 25000,},
            -- ['gunpowder'] = {label = "Gun Powder", buyPrice = 7000, maxCount = 50000, sellPrice = 7000,},

        }
    },

    ['robbery'] = {
        paymentType = 3,
        sellOnly = false, -- If true, the shop doesn't have a buy tab.
        infinite = false, -- If true, the stop doesn't keep track of the storage, players can sell and buy as many items as they want.
        coords = vector3(502.8959, -2739.3567, 3.0686),
        logoPath = "img/robbery.png",
        shopName = "Zavala Traders",
        blip = {
            color = 5, -- Blip color
            sprite = 272, -- Blip sprite
            scale = 1.0 -- Blip sclae
        },
        cashierPed = {
            ped = 'u_m_m_streetart_01',
            coords = vector3(502.8959, -2739.3567, 2.0686),
            heading = 149.6718
        },
        items = {
            ['jewels'] = {label = "Jewels", buyPrice = 180, maxCount = 1000, sellPrice = 180,},
            ['coke_pooch'] = {label = "Cocaine Pouch", buyPrice = 100, maxCount = 1000, sellPrice = 100,},
            ['weed_pooch'] = {label = "Weed Pouch", buyPrice = 100, maxCount = 1000, sellPrice = 100,},
            ['statue'] = {label = "Statue", buyPrice = 15000, maxCount = 1000, sellPrice = 15000,},
            ['gold'] = {label = "Gold Bar", buyPrice = 610, maxCount = 1000, sellPrice = 610,},
            ['rolex'] = {label = "Rolex", buyPrice = 2100, maxCount = 1000, sellPrice = 2100,},
            ['ring'] = {label = "Ring", buyPrice = 600, maxCount = 1000, sellPrice = 600,},
            ['diamond_mb'] = {label = "Diamond Box", buyPrice = 2600, maxCount = 1000, sellPrice = 2600,},
            ['diamond'] = {label = "Diamond", buyPrice = 1200, maxCount = 1000, sellPrice = 1200,},
            ['necklace'] = {label = "Necklace", buyPrice = 1600, maxCount = 1000, sellPrice = 1600,},
            ['mia_painting'] = {label = "Mia Painting", buyPrice = 8700, maxCount = 1000, sellPrice = 8700,},
            ['sun_painting'] = {label = "Sun Painting", buyPrice = 8600, maxCount = 1000, sellPrice = 8600,},
            ['itachi_painting'] = {label = "Itachi Painting", buyPrice = 6350, maxCount = 1000, sellPrice = 6350,},
            ['joker_painting'] = {label = "Joker Painting", buyPrice = 6900, maxCount = 1000, sellPrice = 6900,},
            ['xx_painting'] = {label = "XXXten Painting", buyPrice = 11200, maxCount = 1000, sellPrice = 11200,},
            ['shammy_painting'] = {label = "Shammy Painting", buyPrice = 8700, maxCount = 1000, sellPrice = 8700,},
            ['quinn_painting'] = {label = "Quinn Painting", buyPrice = 7700, maxCount = 1000, sellPrice = 7700,},
            ['dead_painting'] = {label = "Dead Painting", buyPrice = 8200, maxCount = 1000, sellPrice = 8200,},
            ['pogo'] = {label = "Pogo Sculpture", buyPrice = 2700, maxCount = 1000, sellPrice = 2700,},
            ['bottle'] = {label = "Bottle", buyPrice = 1100, maxCount = 1000, sellPrice = 1100,},
            ['panther'] = {label = "Panther", buyPrice = 2700, maxCount = 1000, sellPrice = 2700,},
            ['artgun'] = {label = "Art Gun", buyPrice = 2800, maxCount = 1000, sellPrice = 2800,},
            ['artskull'] = {label = "Art Skull", buyPrice = 2750, maxCount = 1000, sellPrice = 2750,},
            ['artegg'] = {label = "Art Egg", buyPrice = 2100, maxCount = 1000, sellPrice = 2100,},
            ['artlamp'] = {label = "Art Lamp", buyPrice = 1500, maxCount = 1000, sellPrice = 1500,},
            ['arthorse'] = {label = "Art Horse", buyPrice = 1800, maxCount = 1000, sellPrice = 1800,},

            --Fish related treasure items - REBALANCED (reduced by ~35% from original)
            ['great_white_shark'] = {label = "Great White Shark", buyPrice = 56550, maxCount = 1000000, sellPrice = 56550},
            ['gold_coin'] = {label = "Gold Doubloon", buyPrice = 14300, maxCount = 1000000, sellPrice = 14300},
            ['silver_necklace'] = {label = "Silver Necklace", buyPrice = 15600, maxCount = 1000000, sellPrice = 15600},
            ['treasure_chest'] = {label = "Small Treasure Chest", buyPrice = 17550, maxCount = 1000000, sellPrice = 17550},
            ['ancient_statue'] = {label = "Ancient Statue", buyPrice = 16250, maxCount = 1000000, sellPrice = 16250},
            ['pearl'] = {label = "Giant Pearl", buyPrice = 17550, maxCount = 1000000, sellPrice = 17550},
            ['saw_shark'] = {label = "Saw Shark", buyPrice = 22100, maxCount = 1000000, sellPrice = 22100},
            ['hammerhead_shark'] = {label = "Hammerhead Shark", buyPrice = 19500, maxCount = 1000000, sellPrice = 19500},
            ['whale_shark'] = {label = "Whale Shark", buyPrice = 28600, maxCount = 1000000, sellPrice = 28600},


            ['deer_horn'] = {label = "Deer Horn", buyPrice = 3190, maxCount = 30000, sellPrice = 3190,},
            ['boar_tusk'] = {label = "Boar Tusk", buyPrice = 2875, maxCount = 2300, sellPrice = 2875,},
            ['coyote_fur'] = {label = "Coyote Fur", buyPrice = 3625, maxCount = 30000, sellPrice = 3625,},

            ['uchip'] = {label = "Universal Chip", buyPrice = 1750, maxCount = 30000, sellPrice = 1750,},
            ['wire'] = {label = "Wires", buyPrice = 1750, maxCount = 30000, sellPrice = 1750,},
            ['zmetal'] = {label = "Z Metal", buyPrice = 6750, maxCount = 30000, sellPrice = 6750,},
            ['gpowder'] = {label = "G Powder", buyPrice = 6675, maxCount = 30000, sellPrice = 6675,},
            ['adtape'] = {label = "Advanced C3 Tape", buyPrice = 6250, maxCount = 30000, sellPrice = 6250,},
            ['scrap_metal'] = {label = "Metal Scrap", buyPrice = 1000, maxCount = 30000, sellPrice = 1000,},
            ['plastic'] = {label = "Plastic", buyPrice = 1000, maxCount = 30000, sellPrice = 1000,},

        }
    },

    ['lumber'] = {
        paymentType = 1,
        sellOnly = true, -- If true, the shop doesn't have a buy tab.
        infinite = true, -- If true, the stop doesn't keep track of the storage, players can sell and buy as many items as they want.
        coords = vector3(-567.4637, 5240.5576, 70.4703),
        logoPath = "img/lumber.png",
        shopName = "Lumber Traders",
        blip = {
            color = 5, -- Blip color
            sprite = 272, -- Blip sprite
            scale = 1.0 -- Blip sclae
        },
        cashierPed = {
            ped = 'a_m_y_hiker_01',
            coords = vector3(-567.4637, 5240.5576, 69.4703),
            heading = 69.2798
        },
        items = {
            ['agave'] = {label = "Agave Fruit", buyPrice = 1750, maxCount = 30000, sellPrice = 175,},
            ['coca'] = {label = "Coca", buyPrice = 1500, maxCount = 30000, sellPrice = 150,},
            ['lumber'] = {label = "Lumber", buyPrice = 1750, maxCount = 30000, sellPrice = 1750,},
            ['plywood'] = {label = "Plywood", buyPrice = 1775, maxCount = 30000, sellPrice = 1775,},
            ['wood_chips'] = {label = "Wood Chips", buyPrice = 1825, maxCount = 30000, sellPrice = 1825,},
            ['sawdust'] = {label = "Sawdust", buyPrice = 1775, maxCount = 30000, sellPrice = 1775,},
            ['timber'] = {label = "Timber", buyPrice = 1800, maxCount = 30000, sellPrice = 1800,},
        }
    },

    -- ['baitsell'] = {
    --     paymentType = 1,
    --     sellOnly = true, -- If true, the shop doesn't have a buy tab.
    --     infinite = true, -- If true, the stop doesn't keep track of the storage, players can sell and buy as many items as they want.
    --     coords = vector3(2740.8877, 3473.8467, 55.6964),
    --     logoPath = "img/baitsell.png",
    --     shopName = "Bait Selling",
    --     blip = {
    --         color = 5, -- Blip color
    --         sprite = 272, -- Blip sprite
    --         scale = 1.0 -- Blip sclae
    --     },
    --     cashierPed = {
    --         ped = 'a_m_y_hiker_01',
    --         coords = vector3(2740.8877, 3473.8467, 54.6964),
    --         heading = 241.9718
    --     },
    --     items = {
    --         ['fishing_rod'] = {label = "Fishing Rod", buyPrice = 700, maxCount = 30000, sellPrice = 8500,},
    --         ['turtlebait'] = {label = "Turtle Bait", buyPrice = 700, maxCount = 30000, sellPrice = 1000,},
    --         ['fishbait'] = {label = "Fish Bait", buyPrice = 600, maxCount = 30000, sellPrice = 100,},
    --         ['sharkbait'] = {label = "Shark Bait", buyPrice = 700, maxCount = 30000, sellPrice = 250,},
    --     }
    -- },

}