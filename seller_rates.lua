-- DON'T TOUCH THESE --
Config = {}
Config.Shops = {}
Config.Locales = {}
-----------------------

Config.Locale = 'en' --Language option. Edit the locale.lua file to add more, or translate the texts


Config.Shops = {
    ['onyxtheatres'] = {
        paymentType = 1,
        shopName = "Onyx Theatres", 
        sellOnly = false, 
        sellJob = {'onyx','rea'}, 
        infinite = false, 
        coords = vector3(350.9744, 176.0776, 102.9960),
        logoPath = "img/onyxtheatres.png",
        items = {
            ['ticket'] = {label = "Cinema Ticket", buyPrice = 7500, maxCount = 60, sellPrice = 7500,},
        }
    },

    ['grindustries'] = {
        paymentType = 1,
        shopName = "Import Export", 
        sellOnly = false, 
        infinite = false, 
        coords = vector3(-1082.5814, -247.0314, 37.5985),
        logoPath = "img/grindustries.png",
        blip = {
            color = 1,
            sprite = 271,
            scale = 0.1
        },
        cashierPed = {
            ped = 'a_f_y_business_01',
            coords = vector3(-1083.1864, -245.7836, 36.763),
            heading = 213.0570
        },
        items = {
            ['boarmeat'] = {label = "Boar Meat", buyPrice = 950, maxCount = 50000, sellPrice = 950,},
            ['boartusk'] = {label = "Boar Tusk", buyPrice = 950, maxCount = 50000, sellPrice = 950,},
            ['deermeat'] = {label = "Deer Meat", buyPrice = 825, maxCount = 50000, sellPrice = 825,},
            ['deerskin'] = {label = "Deer Skin", buyPrice = 550, maxCount = 50000, sellPrice = 550,},
            ['coyotemeat'] = {label = "Coyote Meat", buyPrice = 950, maxCount = 50000, sellPrice = 950,},
            ['coyotefur'] = {label = "Coyote Fur", buyPrice = 1350, maxCount = 50000, sellPrice = 1350,},

            ['raw_meat'] = {label = "Raw Meat", buyPrice = 160, maxCount = 50000, sellPrice = 160,},

            ['skin_deer_ruined'] = {label = "Tattered Deer Pelt", buyPrice = 250, maxCount = 50000, sellPrice = 250,},
            ['skin_deer_low'] = {label = "Worn Deer Pelt", buyPrice = 280, maxCount = 50000, sellPrice = 280,},
            ['skin_deer_medium'] = {label = "Supple Deer Pelt", buyPrice = 340, maxCount = 50000, sellPrice = 340,},
            ['skin_deer_good'] = {label = "Prime Deer Pelt", buyPrice = 380, maxCount = 50000, sellPrice = 380,},
            ['skin_deer_perfect'] = {label = "Flawless Boar Meat", buyPrice = 450, maxCount = 50000, sellPrice = 450,},

            ['skin_boar_ruined'] = {label = "Tattered Boar Pelt", buyPrice = 250, maxCount = 50000, sellPrice = 250,},
            ['skin_boar_low'] = {label = "Worn Boar Pelt", buyPrice = 280, maxCount = 50000, sellPrice = 280,},
            ['skin_boar_medium'] = {label = "Supple Boar Pelt", buyPrice = 340, maxCount = 50000, sellPrice = 340,},
            ['skin_boar_good'] = {label = "Prime Boar Pelt", buyPrice = 380, maxCount = 50000, sellPrice = 380,},
            ['skin_boar_perfect'] = {label = "Flawless Boar Meat", buyPrice = 450, maxCount = 50000, sellPrice = 450,},

            ['skin_coyote_ruined'] = {label = "Tattered Coyote Pelt", buyPrice = 240, maxCount = 50000, sellPrice = 240,},
            ['skin_coyote_low'] = {label = "Worn Coyote Pelt", buyPrice = 250, maxCount = 50000, sellPrice = 250,},
            ['skin_coyote_medium'] = {label = "Supple Coyote Pelt", buyPrice = 265, maxCount = 50000, sellPrice = 265,},
            ['skin_coyote_good'] = {label = "Prime Coyote Pelt", buyPrice = 290, maxCount = 50000, sellPrice = 290,},
            ['skin_coyote_perfect'] = {label = "Flawless Coyote Meat", buyPrice = 400, maxCount = 50000, sellPrice = 400,},

            -- ['sardine'] = {label = "Sardine Fish", buyPrice = 370, maxCount = 50000, sellPrice = 370,},
            -- ['mackerel'] = {label = "Mackerel Fish", buyPrice = 370, maxCount = 50000, sellPrice = 370,},
            -- ['prawn'] = {label = "Prawns", buyPrice = 370, maxCount = 50000, sellPrice = 370,},
            -- ['tunafish'] = {label = "Tuna Fish", buyPrice = 400, maxCount = 50000, sellPrice = 400,},
            -- ['anchovy'] = {label = "Anchovy", buyPrice = 390, maxCount = 50000, sellPrice = 390,},
            -- ['squid'] = {label = "Squid", buyPrice = 420, maxCount = 50000, sellPrice = 420,},
            -- ['crab'] = {label = "Crab", buyPrice = 435, maxCount = 50000, sellPrice = 435,},
            -- ['seer'] = {label = "Seer Fish", buyPrice = 475, maxCount = 50000, sellPrice = 475,},
            -- ['shark'] = {label = "Shark", buyPrice = 900, maxCount = 50000, sellPrice = 900,},

            ['potato'] = {label = "Potato", buyPrice = 130, maxCount = 50000, sellPrice = 120,},
            ['tomato'] = {label = "Tomato", buyPrice = 140, maxCount = 50000, sellPrice = 130,},
            ['orange'] = {label = "Orange", buyPrice = 150, maxCount = 50000, sellPrice = 140,},
            ['cabbage'] = {label = "Cabbage", buyPrice = 140, maxCount = 50000, sellPrice = 120,},
            ['coffee_beans'] = {label = "Coffee Beans", buyPrice = 80, maxCount = 50000, sellPrice = 70,},

            ['coal_ore'] = {label = "Coal Ore", buyPrice = 235, maxCount = 50000, sellPrice = 235,},
            ['flint'] = {label = "Flint", buyPrice = 235, maxCount = 50000, sellPrice = 235,},
            ['sulfur_chunk'] = {label = "Sulfur Chunk", buyPrice = 250, maxCount = 50000, sellPrice = 250,},
            ['gold_nugget'] = {label = "Gold Nugget", buyPrice = 265, maxCount = 50000, sellPrice = 265,},
            ['gold_dust'] = {label = "Gold Dust", buyPrice = 270, maxCount = 50000, sellPrice = 270,},
            ['quartz_crystal'] = {label = "Quartz Crystal", buyPrice = 290, maxCount = 50000, sellPrice = 290,},
            ['emerald_crystal'] = {label = "Emerald Crystal", buyPrice = 315, maxCount = 50000, sellPrice = 315,},
            ['beryl_chunk'] = {label = "Beryl Chunk", buyPrice = 320, maxCount = 50000, sellPrice = 320,},
            ['green_garnet'] = {label = "Green Garnet", buyPrice = 330, maxCount = 50000, sellPrice = 330,},
            ['ruby_crystal'] = {label = "Ruby Crystal", buyPrice = 360, maxCount = 50000, sellPrice = 360,},
            ['corundum_chunk'] = {label = "Corundum Chunk", buyPrice = 360, maxCount = 50000, sellPrice = 360,},
            ['pink_sapphire'] = {label = "Pink Sapphire", buyPrice = 370, maxCount = 50000, sellPrice = 370,},
            ['amethyst_geode'] = {label = "Amethyst Geode", buyPrice = 410, maxCount = 50000, sellPrice = 410,},
            ['purple_quartz'] = {label = "Purple Quartz", buyPrice = 455, maxCount = 50000, sellPrice = 455,},
            ['clear_crystal'] = {label = "Clear Crystal", buyPrice = 480, maxCount = 50000, sellPrice = 480,},
            ['diamond_crystal'] = {label = "Diamond Crystal", buyPrice = 530, maxCount = 50000, sellPrice = 530,},
            ['graphite_chunk'] = {label = "Graphite Chunk", buyPrice = 590, maxCount = 50000, sellPrice = 590,},
            ['blue_diamond'] = {label = "Blue Diamond", buyPrice = 730, maxCount = 50000, sellPrice = 730,},
            ['copper'] = {label = "Copper", buyPrice = 400, maxCount = 50000, sellPrice = 400,},
            ['iron'] = {label = "Iron", buyPrice = 430, maxCount = 50000, sellPrice = 430,},

            ['dendrogyra_coral'] = {label = "Dendrogyra", buyPrice = 750, maxCount = 50000, sellPrice = 750,},
            ['antipatharia_coral'] = {label = "Antipatharia", buyPrice = 800, maxCount = 50000, sellPrice = 800,},
            ['montastraea_coral'] = {label = "Montastraea", buyPrice = 850, maxCount = 50000, sellPrice = 850,},
            ['keshi_pearl'] = {label = "Keshi Pearls", buyPrice = 900, maxCount = 50000, sellPrice = 900,},
            ['cortez_pearl'] = {label = "Cortez Pearls", buyPrice = 950, maxCount = 50000, sellPrice = 950,},
            ['marine_fossil'] = {label = "Marine Fossil", buyPrice = 1050, maxCount = 50000, sellPrice = 1050,},

            -- peaks fishing

             -- Common Fish (11,000 - 21,000)
            ['bitterling'] = {label = "Bitterling", buyPrice = 12800, maxCount = 1000000, sellPrice = 12800},
            ['pale_chub'] = {label = "Pale Chub", buyPrice = 14400, maxCount = 1000000, sellPrice = 14400},
            ['dace'] = {label = "Dace", buyPrice = 11200, maxCount = 1000000, sellPrice = 11200},
            ['carp'] = {label = "Carp", buyPrice = 16800, maxCount = 1000000, sellPrice = 16800},
            ['goldfish'] = {label = "Goldfish", buyPrice = 8800, maxCount = 1000000, sellPrice = 8800},
            ['killifish'] = {label = "Killifish", buyPrice = 9600, maxCount = 1000000, sellPrice = 9600},
            ['crawfish'] = {label = "Crawfish", buyPrice = 10400, maxCount = 1000000, sellPrice = 10400},
            ['tadpole'] = {label = "Tadpole", buyPrice = 8800, maxCount = 1000000, sellPrice = 8800},
            ['frog'] = {label = "Frog", buyPrice = 11200, maxCount = 1000000, sellPrice = 11200},
            ['freshwater_goby'] = {label = "Freshwater Goby", buyPrice = 12000, maxCount = 1000000, sellPrice = 12000},
            ['loach'] = {label = "Loach", buyPrice = 13600, maxCount = 1000000, sellPrice = 13600},
            ['bluegill'] = {label = "Bluegill", buyPrice = 15200, maxCount = 1000000, sellPrice = 15200},
            ['yellow_perch'] = {label = "Yellow Perch", buyPrice = 16000, maxCount = 1000000, sellPrice = 16000},
            ['black_bass'] = {label = "Black Bass", buyPrice = 16800, maxCount = 1000000, sellPrice = 16800},
            ['tilapia'] = {label = "Tilapia", buyPrice = 14400, maxCount = 1000000, sellPrice = 14400},
            ['pond_smelt'] = {label = "Pond Smelt", buyPrice = 9600, maxCount = 1000000, sellPrice = 9600},
            ['sweetfish'] = {label = "Sweetfish", buyPrice = 10400, maxCount = 1000000, sellPrice = 10400},
            ['horse_mackerel'] = {label = "Horse Mackerel", buyPrice = 9600, maxCount = 1000000, sellPrice = 9600},
            ['sea_bass'] = {label = "Sea Bass", buyPrice = 16800, maxCount = 1000000, sellPrice = 16800},
            ['dab'] = {label = "Dab", buyPrice = 11200, maxCount = 1000000, sellPrice = 11200},
            ['olive_flounder'] = {label = "Olive Flounder", buyPrice = 16000, maxCount = 1000000, sellPrice = 16000},
            
            -- Uncommon Fish (22,000 - 39,000)
            ['koi'] = {label = "Koi", buyPrice = 22000, maxCount = 1000000, sellPrice = 22000},
            ['pop_eyed_goldfish'] = {label = "Pop-eyed Goldfish", buyPrice = 18000, maxCount = 1000000, sellPrice = 18000},
            ['ranchu_goldfish'] = {label = "Ranchu Goldfish", buyPrice = 19600, maxCount = 1000000, sellPrice = 19600},
            ['angelfish'] = {label = "Angel Fish", buyPrice = 20800, maxCount = 1000000, sellPrice = 20800},
            ['betta'] = {label = "Betta", buyPrice = 18400, maxCount = 1000000, sellPrice = 18400},
            ['neon_tetra'] = {label = "Neon Tetra", buyPrice = 24000, maxCount = 1000000, sellPrice = 24000},
            ['rainbowfish'] = {label = "Rainbow Fish", buyPrice = 22400, maxCount = 1000000, sellPrice = 22400},
            ['sea_butterfly'] = {label = "Sea Butterfly", buyPrice = 24000, maxCount = 1000000, sellPrice = 24000},
            ['seahorse'] = {label = "Seahorse", buyPrice = 23200, maxCount = 1000000, sellPrice = 23200},
            ['clownfish'] = {label = "Clown Fish", buyPrice = 20000, maxCount = 1000000, sellPrice = 20000},
            ['surgeonfish'] = {label = "Surgeon Fish", buyPrice = 17600, maxCount = 1000000, sellPrice = 17600},
            ['butterfly_fish'] = {label = "Butterfly Fish", buyPrice = 24800, maxCount = 1000000, sellPrice = 24800},
            ['zebra_turkeyfish'] = {label = "Zebra Turkeyfish", buyPrice = 19200, maxCount = 1000000, sellPrice = 19200},
            ['barred_knifejaw'] = {label = "Barred Knifejaw", buyPrice = 26400, maxCount = 1000000, sellPrice = 26400},
            ['red_snapper'] = {label = "Red Snapper", buyPrice = 32000, maxCount = 1000000, sellPrice = 32000},
            ['moray_eel'] = {label = "Moray Eel", buyPrice = 21200, maxCount = 1000000, sellPrice = 21200},
            ['ribbon_eel'] = {label = "Ribbon Eel", buyPrice = 21600, maxCount = 1000000, sellPrice = 21600},

            -- Rare Fish (40,000 - 65,000)
            ['sturgeon'] = {label = "Sturgeon", buyPrice = 44000, maxCount = 1000000, sellPrice = 44000},
            ['giant_snakehead'] = {label = "Giant Snakehead", buyPrice = 48000, maxCount = 1000000, sellPrice = 48000},
            ['golden_trout'] = {label = "Golden Trout", buyPrice = 36000, maxCount = 1000000, sellPrice = 36000},
            ['stringfish'] = {label = "String Fish", buyPrice = 36000, maxCount = 1000000, sellPrice = 36000},
            ['king_salmon'] = {label = "King Salmon", buyPrice = 40000, maxCount = 1000000, sellPrice = 40000},
            ['napoleonfish'] = {label = "Napoleon Fish", buyPrice = 45600, maxCount = 1000000, sellPrice = 45600},
            ['dorado'] = {label = "Dorado", buyPrice = 39200, maxCount = 1000000, sellPrice = 39200},
            ['gar'] = {label = "Gar", buyPrice = 40000, maxCount = 1000000, sellPrice = 40000},
            ['arapaima'] = {label = "Arapaima", buyPrice = 44800, maxCount = 1000000, sellPrice = 44800},
            ['tuna_fish'] = {label = "Tuna", buyPrice = 38400, maxCount = 1000000, sellPrice = 38400},
            ['blue_marlin'] = {label = "Blue Marlin", buyPrice = 52000, maxCount = 1000000, sellPrice = 52000},
            ['giant_trevally'] = {label = "Giant Trevally", buyPrice = 44000, maxCount = 1000000, sellPrice = 44000},
            ['mahi_mahi'] = {label = "Mahi Mahi", buyPrice = 44000, maxCount = 1000000, sellPrice = 44000},
            ['ray'] = {label = "Ray", buyPrice = 32000, maxCount = 1000000, sellPrice = 32000},

            -- Epic Fish (80,000 - 130,000)

            ['ocean_sunfish'] = {label = "Ocean Sunfish", buyPrice = 64000, maxCount = 1000000, sellPrice = 64000},
            ['oarfish'] = {label = "Oar Fish", buyPrice = 88000, maxCount = 1000000, sellPrice = 88000},

            -- Legendary Fish (160,000 - 260,000)

            ['coelacanth'] = {label = "Coelacanth", buyPrice = 140000, maxCount = 1000000, sellPrice = 140000},
            ['barreleye'] = {label = "Barrel Eye", buyPrice = 160000, maxCount = 1000000, sellPrice = 160000},

            -- Treasure Items (30,000 - 80,000)
            ['old_boot'] = {label = "Old Boot", buyPrice = 24000, maxCount = 1000000, sellPrice = 24000},
            ['rusty_anchor'] = {label = "Rusty Anchor", buyPrice = 32000, maxCount = 1000000, sellPrice = 32000},
            ['broken_bottle'] = {label = "Antique Bottle", buyPrice = 44000, maxCount = 1000000, sellPrice = 44000},
            ['diving_watch'] = {label = "Vintage Diving Watch", buyPrice = 32000, maxCount = 1000000, sellPrice = 32000},
            ['shipwreck_plank'] = {label = "Shipwreck Fragment", buyPrice = 28000, maxCount = 1000000, sellPrice = 28000},
        }   
    },

    ['illegalstore'] = {
        paymentType = 3,        
        shopName = "DC Medicalstore", 
        sellOnly = false, 
        infinite = false, 
        coords = vector3(1653.4376, 4856.6528, 42.0144),
        logoPath = "img/illegalstore.png",
        blip = {
            color = 25,
            sprite = 140,
            scale = 0.8
        },
        cashierPed = {
            ped = 'g_m_y_ballasout_01',
            coords = vector3(1653.4376, 4856.6528, 41.0144),
            heading = 194.7885
        },
        items = {
            ['meth'] = {label = "Meth", buyPrice = 320, maxCount = 50000, sellPrice = 320,},
            ['tweak'] = {label = "Tweak", buyPrice = 2000, maxCount = 50000, sellPrice = 2000,},
            -- ['idukki_gold'] = {label = "Idukki Gold", buyPrice = 1900, maxCount = 50000, sellPrice = 1800,},
            -- ['weed_lemonhaze'] = {label = "Lemonhaze Weed", buyPrice = 1850, maxCount = 50000, sellPrice = 1750,},
            -- ['cocaine'] = {label = "Cocaine", buyPrice = 2200, maxCount = 50000, sellPrice = 2100,},

            -- ['coca'] = {label = "Cocaine Leaves", buyPrice = 250, maxCount = 50000, sellPrice = 200,},
            ['coke_pooch'] = {label = "Cocaine Pouch", buyPrice = 250, maxCount = 50000, sellPrice = 250,},
            ['weed_packaged'] = {label = "Packaged Weed", buyPrice = 3220, maxCount = 50000, sellPrice = 3220,},
            ['meth_packaged'] = {label = "Packaged Meth", buyPrice = 3800, maxCount = 50000, sellPrice = 3800,},
            ['cocaine_packaged'] = {label = "Packaged Cocaine", buyPrice = 4500, maxCount = 50000, sellPrice = 4500,},
            ['inside-weed'] = {label = "Special Weed", buyPrice = 1150, maxCount = 50000, sellPrice = 1150,},
            -- ['inside-meth'] = {label = "Packaged Weed", buyPrice = 1325, maxCount = 50000, sellPrice = 1275,},
            ['inside-coke'] = {label = "Special Coke", buyPrice = 1375, maxCount = 50000, sellPrice = 1375,},
            ['ls_cocaine_bag'] = {label = "Cocaine Bag", buyPrice =10500, maxCount = 50000, sellPrice = 10500,},
            ['ls_crack_bag'] = {label = "Crack Bag", buyPrice = 17500, maxCount = 50000, sellPrice = 17500,},

            ['ls_plain_jane_joint'] = {label = "Plain Jane Joint", buyPrice = 4800, maxCount = 50000, sellPrice = 4800,},
            ['ls_banana_kush_joint'] = {label = "Bananakush Joint", buyPrice = 4900, maxCount = 50000, sellPrice = 4900,},
            ['ls_blue_dream_joint'] = {label = "Blue Dream Joint", buyPrice = 4950, maxCount = 50000, sellPrice = 4950,},
            ['ls_purple_haze_joint'] = {label = "Purple Haze Joint", buyPrice = 5000, maxCount = 50000, sellPrice = 5000,},
            ['ls_orange_crush_joint'] = {label = "Orange Crush Joint", buyPrice = 5100, maxCount = 50000, sellPrice = 5100,},
            ['ls_cosmic_kush_joint'] = {label = "Cosmic Kush Joint", buyPrice = 5200, maxCount = 50000, sellPrice = 5200,},
            ['ls_plain_jane_bag'] = {label = "Plain Jane Bag", buyPrice = 39500, maxCount = 50000, sellPrice = 39500,},
            ['ls_banana_kush_bag'] = {label = "Bananakush Bag", buyPrice = 41500, maxCount = 50000, sellPrice = 41500,},
            ['ls_blue_dream_bag'] = {label = "Blue Dream Bag", buyPrice = 42500, maxCount = 50000, sellPrice = 42500,},
            ['ls_purple_haze_bag'] = {label = "Purple Haze Bag", buyPrice = 43500, maxCount = 50000, sellPrice = 43500,},
            ['ls_orange_crush_bag'] = {label = "Orange Crush Bag", buyPrice = 44500, maxCount = 50000, sellPrice = 42500,},
            ['ls_cosmic_kush_bag'] = {label = "Cosmic Kush Bag", buyPrice = 45670, maxCount = 50000, sellPrice = 43670,},
            -- ['weaponcrate'] = {label = "Crate of Weapons", buyPrice = 50000, maxCount = 50000, sellPrice = 50000,},
            -- ['blueprintbox'] = {label = "Blue Print", buyPrice = 25000, maxCount = 50000, sellPrice = 25000,},
            -- ['gunpowder'] = {label = "Gun Powder", buyPrice = 7000, maxCount = 50000, sellPrice = 7000,},

        }
    },

    ['robbery'] = {
        paymentType = 3,
        sellOnly = false, -- If true, the shop doesn't have a buy tab.
        infinite = false, -- If true, the stop doesn't keep track of the storage, players can sell and buy as many items as they want.
        coords = vector3(502.8959, -2739.3567, 3.0686),
        logoPath = "img/robbery.png",
        shopName = "Zavala Traders",
        blip = {
            color = 5, -- Blip color
            sprite = 272, -- Blip sprite
            scale = 1.0 -- Blip sclae
        },
        cashierPed = {
            ped = 'u_m_m_streetart_01',
            coords = vector3(502.8959, -2739.3567, 2.0686),
            heading = 149.6718
        },
        items = {
            ['jewels'] = {label = "Jewels", buyPrice = 180, maxCount = 1000, sellPrice = 180,},
            ['coke_pooch'] = {label = "Cocaine Pouch", buyPrice = 100, maxCount = 1000, sellPrice = 100,},
            ['weed_pooch'] = {label = "Weed Pouch", buyPrice = 100, maxCount = 1000, sellPrice = 100,},
            ['statue'] = {label = "Statue", buyPrice = 15000, maxCount = 1000, sellPrice = 15000,},
            ['gold'] = {label = "Gold Bar", buyPrice = 610, maxCount = 1000, sellPrice = 610,},
            ['rolex'] = {label = "Rolex", buyPrice = 2100, maxCount = 1000, sellPrice = 2100,},
            ['ring'] = {label = "Ring", buyPrice = 600, maxCount = 1000, sellPrice = 600,},
            ['diamond_mb'] = {label = "Diamond Box", buyPrice = 2600, maxCount = 1000, sellPrice = 2600,},
            ['diamond'] = {label = "Diamond", buyPrice = 1200, maxCount = 1000, sellPrice = 1200,},
            ['necklace'] = {label = "Necklace", buyPrice = 1600, maxCount = 1000, sellPrice = 1600,},
            ['mia_painting'] = {label = "Mia Painting", buyPrice = 8700, maxCount = 1000, sellPrice = 8700,},
            ['sun_painting'] = {label = "Sun Painting", buyPrice = 8600, maxCount = 1000, sellPrice = 8600,},
            ['itachi_painting'] = {label = "Itachi Painting", buyPrice = 6350, maxCount = 1000, sellPrice = 6350,},
            ['joker_painting'] = {label = "Joker Painting", buyPrice = 6900, maxCount = 1000, sellPrice = 6900,},
            ['xx_painting'] = {label = "XXXten Painting", buyPrice = 11200, maxCount = 1000, sellPrice = 11200,},
            ['shammy_painting'] = {label = "Shammy Painting", buyPrice = 8700, maxCount = 1000, sellPrice = 8700,},
            ['quinn_painting'] = {label = "Quinn Painting", buyPrice = 7700, maxCount = 1000, sellPrice = 7700,},
            ['dead_painting'] = {label = "Dead Painting", buyPrice = 8200, maxCount = 1000, sellPrice = 8200,},
            ['pogo'] = {label = "Pogo Sculpture", buyPrice = 2700, maxCount = 1000, sellPrice = 2700,},
            ['bottle'] = {label = "Bottle", buyPrice = 1100, maxCount = 1000, sellPrice = 1100,},
            ['panther'] = {label = "Panther", buyPrice = 2700, maxCount = 1000, sellPrice = 2700,},
            ['artgun'] = {label = "Art Gun", buyPrice = 2800, maxCount = 1000, sellPrice = 2800,},
            ['artskull'] = {label = "Art Skull", buyPrice = 2750, maxCount = 1000, sellPrice = 2750,},
            ['artegg'] = {label = "Art Egg", buyPrice = 2100, maxCount = 1000, sellPrice = 2100,},
            ['artlamp'] = {label = "Art Lamp", buyPrice = 1500, maxCount = 1000, sellPrice = 1500,},
            ['arthorse'] = {label = "Art Horse", buyPrice = 1800, maxCount = 1000, sellPrice = 1800,},

            --Fish related kunthams
            ['great_white_shark'] = {label = "Great White Shark", buyPrice = 87000, maxCount = 1000000, sellPrice = 87000},
            ['gold_coin'] = {label = "Gold Doubloon", buyPrice = 22000, maxCount = 1000000, sellPrice = 22000},
            ['silver_necklace'] = {label = "Silver Necklace", buyPrice = 24000, maxCount = 1000000, sellPrice = 24000},
            ['treasure_chest'] = {label = "Small Treasure Chest", buyPrice = 27000, maxCount = 1000000, sellPrice = 27000},
            ['ancient_statue'] = {label = "Ancient Statue", buyPrice = 25000, maxCount = 1000000, sellPrice = 25000},
            ['pearl'] = {label = "Giant Pearl", buyPrice = 27000, maxCount = 1000000, sellPrice = 27000},
            ['saw_shark'] = {label = "Saw Shark", buyPrice = 34000, maxCount = 1000000, sellPrice = 34000},
            ['hammerhead_shark'] = {label = "Hammerhead Shark", buyPrice = 30000, maxCount = 1000000, sellPrice = 30000},
            ['whale_shark'] = {label = "Whale Shark", buyPrice = 44000, maxCount = 1000000, sellPrice = 44000},


            ['deer_horn'] = {label = "Deer Horn", buyPrice = 1275, maxCount = 30000, sellPrice = 1275,},
            ['boar_tusk'] = {label = "Boar Tusk", buyPrice = 1150, maxCount = 2300, sellPrice = 1150,},
            ['coyote_fur'] = {label = "Coyote Fur", buyPrice = 1450, maxCount = 30000, sellPrice = 1450,},

            ['uchip'] = {label = "Universal Chip", buyPrice = 700, maxCount = 30000, sellPrice = 700,},
            ['wire'] = {label = "Wires", buyPrice = 700, maxCount = 30000, sellPrice = 700,},
            ['zmetal'] = {label = "Z Metal", buyPrice = 2700, maxCount = 30000, sellPrice = 2700,},
            ['gpowder'] = {label = "G Powder", buyPrice = 2670, maxCount = 30000, sellPrice = 2670,},
            ['adtape'] = {label = "Advanced C3 Tape", buyPrice = 2500, maxCount = 30000, sellPrice = 2500,},
            ['scrap_metal'] = {label = "Metal Scrap", buyPrice = 400, maxCount = 30000, sellPrice = 400,},
            ['plastic'] = {label = "Plastic", buyPrice = 400, maxCount = 30000, sellPrice = 400,},

        }
    },

    ['lumber'] = {
        paymentType = 1,
        sellOnly = true, -- If true, the shop doesn't have a buy tab.
        infinite = true, -- If true, the stop doesn't keep track of the storage, players can sell and buy as many items as they want.
        coords = vector3(-567.4637, 5240.5576, 70.4703),
        logoPath = "img/lumber.png",
        shopName = "Lumber Traders",
        blip = {
            color = 5, -- Blip color
            sprite = 272, -- Blip sprite
            scale = 1.0 -- Blip sclae
        },
        cashierPed = {
            ped = 'a_m_y_hiker_01',
            coords = vector3(-567.4637, 5240.5576, 69.4703),
            heading = 69.2798
        },
        items = {
            ['agave'] = {label = "Agave Fruit", buyPrice = 700, maxCount = 30000, sellPrice = 70,},
            ['coca'] = {label = "Coca", buyPrice = 600, maxCount = 30000, sellPrice = 60,},
            ['lumber'] = {label = "Lumber", buyPrice = 700, maxCount = 30000, sellPrice = 700,},
            ['plywood'] = {label = "Plywood", buyPrice = 710, maxCount = 30000, sellPrice = 710,},
            ['wood_chips'] = {label = "Wood Chips", buyPrice = 730, maxCount = 30000, sellPrice = 730,},
            ['sawdust'] = {label = "Sawdust", buyPrice = 710, maxCount = 30000, sellPrice = 710,},
            ['timber'] = {label = "Timber", buyPrice = 720, maxCount = 30000, sellPrice = 720,},
        }
    },

    -- ['baitsell'] = {
    --     paymentType = 1,
    --     sellOnly = true, -- If true, the shop doesn't have a buy tab.
    --     infinite = true, -- If true, the stop doesn't keep track of the storage, players can sell and buy as many items as they want.
    --     coords = vector3(2740.8877, 3473.8467, 55.6964),
    --     logoPath = "img/baitsell.png",
    --     shopName = "Bait Selling",
    --     blip = {
    --         color = 5, -- Blip color
    --         sprite = 272, -- Blip sprite
    --         scale = 1.0 -- Blip sclae
    --     },
    --     cashierPed = {
    --         ped = 'a_m_y_hiker_01',
    --         coords = vector3(2740.8877, 3473.8467, 54.6964),
    --         heading = 241.9718
    --     },
    --     items = {
    --         ['fishing_rod'] = {label = "Fishing Rod", buyPrice = 700, maxCount = 30000, sellPrice = 8500,},
    --         ['turtlebait'] = {label = "Turtle Bait", buyPrice = 700, maxCount = 30000, sellPrice = 1000,},
    --         ['fishbait'] = {label = "Fish Bait", buyPrice = 600, maxCount = 30000, sellPrice = 100,},
    --         ['sharkbait'] = {label = "Shark Bait", buyPrice = 700, maxCount = 30000, sellPrice = 250,},
    --     }
    -- },

}