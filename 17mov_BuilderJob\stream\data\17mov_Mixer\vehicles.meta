<?xml version="1.0" encoding="UTF-8"?> 
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />

  <InitDatas>
    <Item>
      <modelName>17mov_Mixer</modelName>
      <txdName>17mov_Mixer</txdName>
      <handlingId>boxville</handlingId>
      <gameName>17mov_Mixer</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>phantom</audioNameHash>
      <layout>LAYOUT_TRUCK_MIXER</layout>
      <coverBoundOffsets>MIXER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE</povCameraName>
      <FirstPersonDriveByIKOffset x="0.078000" y="-0.105000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.018000" z="-0.018000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.183000" y="-0.015000" z="-0.066000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.158000" y="-0.083000" z="0.033000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.031000" y="-0.013000" z="-0.060000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.138000" z="-0.018000" />
	  <FirstPersonMobilePhoneOffset x="0.148000" y="0.243000" z="0.538000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.156000" y="0.223000" z="0.445000" />
      <PovCameraOffset x="0.000000" y="-0.090000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="-0.060000" y="0.000000" z="0.018000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.312600" />
      <wheelScaleRear value="0.312600" />
      <dirtLevelMin value="0.300000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.400000" />
      <envEffScaleMax value="0.600000" />
      <envEffScaleMin2 value="0.400000" />
      <envEffScaleMax2 value="0.600000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000	
        90.000000	
        130.000000	
        260.000000	
        750.000000	
        750.000000
      </lodDistances>
      <minSeatHeight value="0.872" />
      <identicalModelSpawnDistance value="80" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="2" />
      <flags>FLAG_BIG FLAG_NO_BOOT FLAG_AVOID_TURNS FLAG_EXTRAS_REQUIRE FLAG_EXTRAS_STRONG FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_INDUSTRIAL</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
		<Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName />
        </Item>
		<Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName />
        </Item>
		<Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="true" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_FRONT_LEFT</Item>
        <Item>TRUCK_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
 </InitDatas>
 <txdRelationships>
     <Item>
      <parent>vehicles_sultan_interior</parent>
      <child>police3</child>
    </Item>
  </txdRelationships>
  </CVehicleModelInfo__InitDataList>