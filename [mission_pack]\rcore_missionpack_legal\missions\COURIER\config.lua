--[ VERSION 1.0.0 ]

Config.MISSION_COURIER = {}

-- The map blip for the job (removeable)
Config.MISSION_COURIER.Blip = {
    Position = vector3(78.86, 111.92, 80.16),
    Scale = 0.8,
    Sprite = 501,
    Colour = 5
}

-- The table of allowed jobs for this mission (nil indicates everyone is allowed)
--[[
    EXAMPLE:
        Config.MISSION_COURIER.AllowedJobs = {
            unemployed = true
        }
]]
Config.MISSION_COURIER.AllowedJobs = nil

-- The position for the starting checkpoint
Config.MISSION_COURIER.StartPosition = vector3(78.86, 111.92, 80.16)

-- The position for the end of the mission
Config.MISSION_COURIER.EndPosition = vector3(67.58, 123.34, 78.14)

-- The mission reward for each delivery
Config.MISSION_COURIER.Reward = 4000

-- The reward for recipient
Config.MISSION_COURIER.PlayerReward = 1500

-- The mission cooldown for each player (in seconds)
Config.MISSION_COURIER.Cooldown = 900

-- The number of times the mission will repeat
Config.MISSION_COURIER.Repetitions = 5

-- The cooldown for player when he accepts the delivery (in seconds)
Config.MISSION_COURIER.PlayerCooldown = 3600

-- The cooldown for each delivery (in seconds)
Config.MISSION_COURIER.DeliveryCooldown = 60

-- The command used to accept the delivery
Config.MISSION_COURIER.AcceptCommand = 'acceptpackage'

-- The model hash of the truck used in the mission
--[[
    PLEASE NOTE:
    This should only be rarely changed as it can break the mission if changed to a wrong vehicle model
    Change only if you use custom liveries
]]
Config.MISSION_COURIER.TruckModel = `boxville2`

-- The table of available positions for the truck to spawn on (including heading)
Config.MISSION_COURIER.TruckPositions = {
    vector4(74.23, 119.11, 79.08, 160.0),
    vector4(70.12, 120.61, 79.06, 160.0),
    vector4(63.52, 123.06, 79.06, 160.0),
    vector4(59.25, 124.65, 79.14, 160.0)
}

Config.MISSION_COURIER.Labels = {
    ['MISSION_NAME'] = 'Go Postal Courier',
    ['COOLDOWN_MESSAGE'] = 'There are no deliveries available right now.',
    ['ENTER_TRUCK'] = 'Get into the ~y~delivery truck~s~.',
    ['ENTER_TRUCK_DELIVERY'] = 'Get into the ~y~delivery truck~s~ and wait for a delivery.',
    ['DELIVERY_PHONE'] = 'Recipient\'s phone number: %s',
    ['DELIVERY_MESSAGE'] = 'A delivery driver wants to deliver you a package, type /acceptpackage to accept.',
    ['DELIVERY_ACCEPTED'] = 'The package has been accepted, driver is on his way.',
    ['GARAGE_BLIP'] = 'Garage',
}

Config.MISSION_COURIER.Subtitles = {
    ['MISSION_START'] = { 'Drive to the ~y~recipient~s~.', 'Deliver the ~y~package~s~.', 'The package is in your ~y~trunk~s~.' },
    ['MISSION_SUCCESS'] = { 'Successfully ~y~delivered~s~ the package.' },
    ['MISSION_REPEAT'] = { 'Wait for another ~y~delivery~s~.' },
    ['MISSION_END'] = { 'Return the ~y~delivery truck~s~.' },
}