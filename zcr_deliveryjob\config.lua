zcr_deliveryjob = {} local PLT = zcr_deliveryjob
---------------------------------------------------------------------------------------------------------------------------------------------------------
PLT.U = Lang["EN"]                                              -- SET = ("EN" or "TR" or "Custom"), Edit locale.lua to add more languages.
PLT.MaxOrder = 50                                               -- SET = (Number), Maximum number of pending orders.
PLT.NewOrderTime = 60                                           -- SET = (Second), Time to wait for a new order to be added to the list.
PLT.DeliveryItems = {
    ["lighter"]     = {label = "Lighter",     price = 2650, minMaxAmount ={1,9}, orderDensity = 20, timeToCancel=60*30},
    ["marlborocig"]     = {label = "Marlboro Cig",     price = 2350, minMaxAmount ={1,5}, orderDensity = 15, timeToCancel=60*60*3},
    ["redwcig"] = {label = "Redwood Cig", price = 2150, minMaxAmount ={1,3}, orderDensity = 20, timeToCancel=60*50},
    ["marlboro"] = {label = "Marlboro Pack", price = 4155, minMaxAmount ={5,15},orderDensity = 20, timeToCancel=60*40},
    ["redw"]      = {label = "Redwood Pack",      price = 4600, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["vape"]      = {label = "Vape",      price = 3800, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["liquid"]      = {label = "Liquid",      price = 3300, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["cubancigar"]      = {label = "Cuban Cigar",      price = 2150, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["davidoffcigar"]      = {label = "Davidoff Cigar",      price = 4250, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["og_kush_joint"]      = {label = "OG Kush Joint",      price = 3250, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["blue_dream_joint"]      = {label = "Blue Dream Joint",      price = 3400, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["purple_haze_joint"]      = {label = "Purple Haze Joint",      price = 3550, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["banana_kush_joint"]      = {label = "Banana Kush Joint",      price = 3300, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["piswasser"]      = {label = "Beer",      price = 5900, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["mount_whisky"]      = {label = "Mount Whiskey",      price = 5650, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["tequila"]      = {label = "Tequila",      price = 8500, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["nogo_vodka"]      = {label = "Vodka",      price = 7700, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["costa_del_perro"]      = {label = "Costa Del Perro",      price = 7450, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["rockford_hill"]      = {label = "Rockford Hills",      price = 11600, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["vinewood_red"]      = {label = "Vinewood Red",      price = 9500, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["vinewood_blanc"]      = {label = "Vinewood Blanc",      price = 9600, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["raine"]      = {label = "Jinja",      price = 17200, minMaxAmount ={1,9}, orderDensity = 30, timeToCancel=60*70},
    ["molnupiravir"]      = {label = "Molnupiravir",      price = 2600, minMaxAmount ={3,9}, orderDensity = 60, timeToCancel=60*70},
    ["gingermedicine"]      = {label = "Ginger Medicine",      price = 2650, minMaxAmount ={3,9}, orderDensity = 50, timeToCancel=60*70},
    ["nasuamedicine"]      = {label = "Nausea Medicine",      price = 2670, minMaxAmount ={3,9}, orderDensity = 40, timeToCancel=60*70},
    ["coughmedicine"]      = {label = "Cough Medicine",      price = 2580, minMaxAmount ={3,9}, orderDensity = 60, timeToCancel=60*70},
    ["multivitamins"]      = {label = "Eternal Multi Vitamins",      price = 2770, minMaxAmount ={3,9}, orderDensity = 40, timeToCancel=60*70},
    ["diatabs"]      = {label = "Diatabs",      price = 2630, minMaxAmount ={3,9}, orderDensity = 60, timeToCancel=60*70},
    ["acetaminophen"]      = {label = "Acetaminophen",      price = 2560, minMaxAmount ={3,9}, orderDensity = 50, timeToCancel=60*70},
    ["offlotion"]      = {label = "Off Lotion",      price = 2820, minMaxAmount ={3,9}, orderDensity = 50, timeToCancel=60*70},
    ["antihistamine"]      = {label = "Anti Histamine",      price = 2780, minMaxAmount ={3,9}, orderDensity = 45, timeToCancel=60*70},
    ["ambroxol"]      = {label = "Ambroxol",      price = 2640, minMaxAmount ={3,9}, orderDensity = 70, timeToCancel=60*70},
    ["baraclude"]      = {label = "Baraclude",      price = 2590, minMaxAmount ={3,9}, orderDensity = 55, timeToCancel=60*70},
    ["metronidazole"]      = {label = "Metronidazole",      price = 2680, minMaxAmount ={3,9}, orderDensity = 45, timeToCancel=60*70},
}

PLT.TotalPercent = 0
for k,v in pairs(PLT.DeliveryItems) do
    v.minPercent = PLT.TotalPercent
    v.maxPercent = v.minPercent+ v.orderDensity
    PLT.TotalPercent  = v.maxPercent
end
