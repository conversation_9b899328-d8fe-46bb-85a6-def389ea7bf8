-- Heist Management System
-- Standalone resource for GTA Online style heist notifications and status tracking

local showHeistBanner = false
local hmsOpen = false
local heistStatusData = {}

-- Event handler for showing heist notifications
RegisterNetEvent('heist-notify:show')
AddEventHandler('heist-notify:show', function(initialText, rewardTable, waitTime, playSound)
    HeistNotify(initialText, rewardTable, waitTime, playSound)
end)

-- Main function to display heist notification
function HeistNotify(_initialText, _table, _waitTime, _playSound)
    showHeistBanner = true
    if _playSound ~= nil and _playSound == true then
        PlaySoundFrontend(-1, "CHECKPOINT_PERFECT", "HUD_MINI_GAME_SOUNDSET", 1)
    end
    showHeist(_initialText, _table)
    Citizen.CreateThread(function()
        Citizen.Wait(tonumber(_waitTime) * 1000)
        showHeistBanner = false
    end)
end

-- Function to create and display the heist scaleform
function showHeist(ZinitialText, Ztable)
    Citizen.CreateThread(function()
        function drawHeist(_initialText, _table)
            local scaleform = Request('HEIST_CELEBRATION')
            local scaleform_bg = Request('HEIST_CELEBRATION_BG')
            local scaleform_fg = Request('HEIST_CELEBRATION_FG')

            local scaleform_list = {
                scaleform,
                scaleform_bg,
                scaleform_fg
            }

            for key, scaleform_handle in pairs(scaleform_list) do
                CallFunction(scaleform_handle, false, "CREATE_STAT_WALL", 1, "HUD_COLOUR_FREEMODE_DARK", 1)
                CallFunction(scaleform_handle, false, "ADD_BACKGROUND_TO_WALL", 1, 80, 1)
    
                CallFunction(scaleform_handle, false, "ADD_MISSION_RESULT_TO_WALL", 1, _initialText.missionTextLabel, _initialText.passFailTextLabel, _initialText.messageLabel, true, true, true)
    
                if _table ~= nil then
                    if _table[1] ~= nil then
                        CallFunction(scaleform_handle, false, "CREATE_STAT_TABLE", 1, 10)
        
                        for i, k in pairs(_table) do
                            CallFunction(scaleform_handle, false, "ADD_STAT_TO_TABLE", 1, 10, _table[i].stat, _table[i].value, true, true, false, false, 0)
                        end
        
                        CallFunction(scaleform_handle, false, "ADD_STAT_TABLE_TO_WALL", 1, 10)
                    end
                end
    
                CallFunction(scaleform_handle, false, "SHOW_STAT_WALL", 1)
                CallFunction(scaleform_handle, false, "createSequence", 1, 1, 1)
            end

            return scaleform, scaleform_bg, scaleform_fg
        end
        local scale, scale_bg, scale_fg = drawHeist(ZinitialText, Ztable)
        while showHeistBanner do
            Citizen.Wait(1)
            DrawScaleformMovieFullscreenMasked(scale_bg, scale_fg, 255, 255, 255, 50)
            DrawScaleformMovieFullscreen(scale, 255, 255, 255, 255)
        end
        StartScreenEffect("HeistCelebToast")
    end)
end

-- Function to request and load scaleform movies
function Request(scaleform)
    local scaleform_handle = RequestScaleformMovie(scaleform)
    while not HasScaleformMovieLoaded(scaleform_handle) do
        Citizen.Wait(0)
    end
    return scaleform_handle
end

-- Function to call scaleform methods with parameters
function CallFunction(scaleform, returndata, the_function, ...)
    BeginScaleformMovieMethod(scaleform, the_function)
    local args = {...}

    if args ~= nil then
        for i = 1,#args do
            local arg_type = type(args[i])

            if arg_type == "boolean" then
                ScaleformMovieMethodAddParamBool(args[i])
            elseif arg_type == "number" then
                if not string.find(args[i], '%.') then
                    ScaleformMovieMethodAddParamInt(args[i])
                else
                    ScaleformMovieMethodAddParamFloat(args[i])
                end
            elseif arg_type == "string" then
                ScaleformMovieMethodAddParamTextureNameString(args[i])
            end
        end

        if not returndata then
            EndScaleformMovieMethod()
        else
            return EndScaleformMovieMethodReturnValue()
        end
    end
end

-- ===== HEIST MANAGEMENT SYSTEM (HMS) =====

-- Command to open HMS
RegisterCommand('hms', function()
    OpenHMS()
end, false)

-- Open HMS interface
function OpenHMS()
    if not hmsOpen then
        hmsOpen = true
        SetNuiFocus(true, true)
        TriggerServerEvent('hms:requestStatus')
        SendNUIMessage({
            type = "openHMS"
        })
    end
end

-- Close HMS interface
function CloseHMS()
    if hmsOpen then
        hmsOpen = false
        SetNuiFocus(false, false)
        SendNUIMessage({
            type = "closeHMS"
        })
    end
end

-- NUI Callbacks
RegisterNUICallback('closeHMS', function(data, cb)
    CloseHMS()
    cb('ok')
end)

RegisterNUICallback('refreshStatus', function(data, cb)
    TriggerServerEvent('hms:requestStatus')
    cb('ok')
end)

RegisterNUICallback('requestStatus', function(data, cb)
    TriggerServerEvent('hms:requestStatus')
    cb('ok')
end)

RegisterNUICallback('markLocation', function(data, cb)
    if data.coords then
        SetNewWaypoint(data.coords.x, data.coords.y)
        ESX.ShowNotification('Waypoint set for ' .. data.heistId:upper(), 'info', 3000)
    end
    cb('ok')
end)

-- Receive heist status from server
RegisterNetEvent('hms:receiveStatus')
AddEventHandler('hms:receiveStatus', function(statusData)
    heistStatusData = statusData
    SendNUIMessage({
        type = "updateStatus",
        data = statusData
    })
end)

-- Receive individual heist status updates
RegisterNetEvent('hms:updateStatus')
AddEventHandler('hms:updateStatus', function(heistType, heistData)
    heistStatusData[heistType] = heistData
    if hmsOpen then
        SendNUIMessage({
            type = "updateSingleStatus",
            heistType = heistType,
            data = heistData
        })
    end
end)

-- Close HMS on ESC
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if hmsOpen then
            if IsControlJustPressed(0, 322) then -- ESC key
                CloseHMS()
            end
        end
    end
end)
