@import url("https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Oswald:wght@200..700&display=swap');


@font-face {
  font-family: "Nature Beauty Personal Use";
  src: url(./font/Nature\ Beauty\ Personal\ Use.ttf);
  font-style: normal;
}

* {
  margin: 0;
  box-sizing: border-box;
  user-select: none;
}

body {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-size: cover;
  overflow: hidden;
}

#app {
}

.noDetailText {
  color: rgba(255, 255, 255, 0.42);
  font-family: Barlow;
  font-size: 1.1077vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%;
}


.General {

  gap: 1.3542vw;
  position: relative;
  height: 45.9896vw;
  padding: 0vw 2.3958vw;
  padding-bottom: 4.5833vw;
  display: flex;
  flex-direction: column;
  width: 58.6458vw;
  border-radius: 1.1458vw;
  background: radial-gradient(
      11.83% 12.14% at 100% 0%,
      rgba(255, 56, 56, 0.2) 0%,
      rgba(255, 56, 56, 0) 100%
    ),
    radial-gradient(
      49.91% 39.95% at 2.58% -2.85%,
      rgba(132, 181, 255, 0.19) 0%,
      rgba(132, 181, 255, 0) 100%
    ),
    rgba(22, 28, 32, 0.96);
  outline: 0.6771vw solid rgba(0, 0, 0, 0.23);
}

.topSide {
  z-index: 4;
  padding-right: 1.1979vw;
  width: 100%;
  gap: 1.5104vw;
  display: flex;
  flex-direction: row-reverse;
}

.profileBox {
  justify-content: center;
  align-items: center;
  gap: 1.25vw;
  display: flex;
  width: 13.6458vw;
  height: 4.8438vw;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(153, 153, 153, 0) 100%
  );
}

.profileImg {
  position: relative;
  width: 2.0313vw;
  height: 2.0313vw;
  border-radius: 50%;
  background-size: cover;
}

.profileImg::before {
  content: "";
  position: absolute;
  display: flex;
  top: -0.2604vw;
  left: -0.2604vw;
  right: -0.2604vw;
  bottom: -0.2604vw;
  border-radius: 50%;
  border: 0.0521vw solid #7ba9df;
}

.profileTextBox {
  display: flex;
  gap: 0.2828vw;
  flex-direction: column;
}

.profileText {
  color: #fff;
  font-family: Barlow;
  font-size: 0.94vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 16.243px */
}

.profileSubText {
  color: #4fff95;
  font-family: Barlow;
  font-size: 0.8225vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 14.213px */
}

.levelBox {
  gap: 0.8vw;
  padding-left: 0.6vw;
  align-items: center;
  position: relative;
  display: flex;
  width: 22.2917vw;
  height: 4.8438vw;
  background: linear-gradient(
    180deg,
    rgba(255, 240, 103, 0.12) 0%,
    rgba(255, 240, 103, 0) 100%
  );
}

.single-chart {
  width: 3.5vw;
  justify-content: space-around;
}

.circular-chart {
  display: block;
  margin: 0.5208vw auto;
  max-width: 80%;
  max-height: 13.0208vw;
}

.circle-bg {
  fill: none;
  stroke: rgba(255, 255, 255, 0.09);
  stroke-width: 2;
}

.circle {
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  animation: progress 1s ease-out forwards;
}

@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

.circular-chart.orange .circle {
  stroke: #ffd569;
}

.percentage {
  fill: #ffd569;
  font-family: "Barlow";
  font-style: normal;
  font-weight: 600;
  font-size: 0.9em;
  text-anchor: middle;
}

.levelLine {
  position: relative;
  width: 0.2864vw;
  height: 0.2864vw;
  border-radius: 1.3746vw;
  background: radial-gradient(50% 50% at 50% 50%, #ffe353 0%, #b29500 100%);
  box-shadow: 0px 0px 0.2978vw 0.2864vw rgba(255, 227, 83, 0.35);
}

.levelLine::before {
  content: "";
  position: absolute;
  display: flex;
  top: -0.15vw;
  left: -0.15vw;
  right: -0.15vw;
  bottom: -0.15vw;
  border-radius: 50%;
  border: 0.0573vw solid #ffe353;
}

.levelTextBox {
  display: flex;
  flex-direction: column;
  gap: 0.3646vw;
}

.levelTexts {
  display: flex;
  align-items: center;
  gap: 0.4688vw;
}

.levelText {
  color: #ffe353;
  font-family: Barlow;
  font-size: 1.0417vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 18px */
}

.levelSubText {
  color: rgba(255, 255, 255, 0.66);
  text-align: right;
  font-family: Barlow;
  font-size: 0.8333vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 18.72px */
}

.levelSubText span {
  color: #fff;
  font-family: Barlow;
  font-size: 0.8333vw;
  font-style: normal;
  font-weight: 600;
  line-height: 117%;
}

.expBarBox {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 1.3021vw;
  border-radius: 0.2604vw;
  width: 2.2917vw;
  height: 2.2917vw;
  border: 0.0521vw solid rgba(255, 227, 83, 0.13);
  background: rgba(255, 227, 83, 0.12);
  color: #ffe353;
  text-align: center;
  font-family: Barlow;
  font-size: 0.8333vw;
  font-style: normal;
  font-weight: 600;
  line-height: 117%; /* 18.72px */
}

.expBar {
  display: flex;
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 40%;
  border-radius: 0px 0px 0.2604vw 0.2604vw;
  background: rgba(255, 227, 83, 0.12);
}

.titleBox {
  padding-left: 1vw;
  display: flex;
  align-items: center;
  left: 0.5208vw;
  top: -2.0313vw;
  position: absolute;
  width: 16.1979vw;
  height: 6.25vw;
  background-size: cover;
  background-image: url(./img/titlebg.png);
}

.logo {
  width: 5.6771vw;
  height: 4.8958vw;
}

.titleTextBox {
  padding-bottom: 0.9vw;
  display: flex;
  flex-direction: column;
}

.titleText {
  font-family: Barlow;
  font-size: 1.9243vw;
  font-style: normal;
  font-weight: 800;
  line-height: 90%; /* 33.252px */
  background: linear-gradient(
    93deg,
    rgba(56, 87, 169, 0.9) -3.7%,
    rgba(135, 190, 240, 0.9) 98.85%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.titleSubText {
  color: #fff;
  font-family: "Nature Beauty Personal Use";
  font-size: 1.2064vw;
  font-style: normal;
  font-weight: 400;
  line-height: 60%; /* 20.846px */
}

.bottomSide {
  overflow: hidden;
  flex: 1;
  z-index: 4;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.8854vw;
}

.categoryList {
  width: 100%;
  display: flex;
  gap: 0.7813vw;
}

.categoryBox {
  position: relative;
  display: flex;
  align-items: center;
  height: 2.6042vw;
  padding-right: 0.7813vw;
  padding-left: 2.7604vw;
  border-radius: 0.2083vw;
  border: 0.0521vw solid rgba(255, 255, 255, 0.06);
  background: rgba(255, 255, 255, 0.08);
  transition: 200ms ease-in-out;
}

.categoryBox:hover {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.15);
}

.categoryBox:hover .categoryImg {
  opacity: 1;
}

.categoryBox:hover .categoryTextBox .categoryText {
  color: white;
}

.categoryImg {
  transition: 200ms ease-in-out;
  z-index: 1;
  object-fit: cover;
  position: absolute;
  left: 0;
  bottom: 0;
  opacity: 0.7;
}

.category1 {
  height: 100%;
  width: 3.8021vw;
}

.categoryTextBox {
  display: flex;
  flex-direction: column;
  gap: 0.2083vw;
}

.categoryText {
  color: rgb(255, 255, 255, 0.7);
  font-family: Barlow;
  font-size: 0.8093vw;
  font-style: normal;
  font-weight: 700;
  line-height: 90%; /* 13.985px */
  transition: 200ms ease-in-out;
}

.categorySubText {
  color: rgba(255, 255, 255, 0.63);
  font-family: Barlow;
  font-size: 0.5729vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 9.9px */
}

.category2 {
  height: 100%;
  width: 3.125vw;
}

.selectCategory {
  border-radius: 0.2083vw;
  background: linear-gradient(
      270deg,
      rgba(72, 189, 255, 0) 0%,
      rgba(72, 189, 255, 0.34) 100%
    ),
    rgba(255, 255, 255, 0.08) !important;
}

.selectCategory img {
  opacity: 1 !important;
}

.selectCategory .categoryTextBox .categoryText {
  color: #fff !important;
}

.jobSide {
  gap: 0.7813vw;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.regionBoxs {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.625vw;
}

.regionTopSide {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.regionTitleBox {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.regionTitle {
  display: flex;
  align-items: center;
  gap: 0.4167vw;
}

.regionTitleText {
  display: flex;
  flex-direction: column;
  gap: 0.2083vw;
  color: #fff;
  font-family: Barlow;
  font-size: 0.8464vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 14.625px */
}

.regionTitleText p {
  color: rgba(255, 255, 255, 0.67);
  font-family: Barlow;
  font-size: 0.7292vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 12.6px */
}

.regionList {
  display: flex;
  width: 100%;
  gap: 1.0938vw !important;
}

.swiper-container {
  width: 100%;
}

.swiper-slide {
  width: auto; /* veya sabit bir genişlik verin */
}

.regionBox {
  justify-content: space-between;
  overflow: hidden;
  padding: 0.7292vw;
  padding-top: 2.8125vw;
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 0.3125vw;
  border: 0.0521vw solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.04);
  min-width: 12.81vw;
  max-width: 12.81vw;
  height: 14.0625vw;
  transition: 200ms ease-in-out;
  cursor: pointer;
}

.regionNameBox {
  transition: 200ms ease-in-out;
  z-index: 2;
  padding: 0vw 0.4167vw;
  align-items: center;
  gap: 0.4167vw;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2.0833vw;
  border-radius: 0.3125vw 0.3125vw 0px 0px;
  background: rgba(0, 0, 0, 0.33);
  backdrop-filter: blur(8.899999618530273px);
}

.regionNameIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.1042vw;
  background: rgba(255, 255, 255, 0.13);
  width: 1.25vw;
  height: 1.25vw;
}

.regionName {
  color: #fff;
  font-family: Barlow;
  font-size: 0.7292vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 12.6px */
}

.regionBG {
  z-index: 1;
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  left: 0;
  top: 0;
}

.regionInfo {
  z-index: 2;
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 0.4688vw;
}

.regionInfoTextBox {
  display: flex;
  flex-direction: column;
  gap: 0.1042vw;
}

.regionInfoText {
  color: #fff;
  font-family: Barlow;
  font-size: 0.7764vw;
  font-style: normal;
  font-weight: 600;
  line-height: 117%; /* 17.442px */
}

.regionInfoSubText {
  color: rgba(255, 255, 255, 0.66);
  font-family: Barlow;
  font-size: 0.6039vw;
  font-style: normal;
  font-weight: 400;
  line-height: 117%; /* 13.566px */
}

.regionInfos {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.regionInfosBox {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.1563vw;
  background: rgba(255, 213, 105, 0.28);
  width: 5.5vw;
  height: 1.3542vw;
  color: rgba(255, 213, 105, 0.92);
  text-align: center;
  font-family: Barlow;
  font-size: 0.625vw;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 14.04px */
}

.regionInfosPlayer {
  width: 5.3vw;
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  text-align: center;
  font-family: Barlow;
  font-size: 0.6771vw;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 15.21px */
}

.regionAwardBox {
  z-index: 3;
  width: 100%;
  display: flex;
  flex-direction: column;
  color: rgba(255, 255, 255, 0.58);
  font-family: Barlow;
  font-size: 0.6771vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 11.7px */
  gap: 0.3125vw;
}

.regionAwardList {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.regionAward {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 6.9792vw;
  height: 1.3542vw;
  border-radius: 0.1563vw;
  background: rgba(67, 255, 97, 0.08);
  backdrop-filter: blur(0.1979vw);
  color: #43ff61;
  text-align: center;
  text-shadow: 0px 0px 0.688vw rgba(67, 255, 97, 0.3);
  font-family: Barlow;
  font-size: 0.6771vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 11.7px */
}

.expAward {
  width: 3.9583vw;
  color: #ffe353;

  text-align: center;
  text-shadow: 0px 0px 0.688vw rgba(255, 227, 83, 0.3);
  font-family: Barlow;
  font-size: 0.6771vw;
  font-style: normal;
  font-weight: 600;
  line-height: 117%; /* 15.21px */
  border-radius: 0.1563vw;
  background: rgba(255, 227, 83, 0.08);
  backdrop-filter: blur(3.799999952316284px);
}

.playerListBox {
  display: flex;
  flex-direction: column;
  gap: 0.4688vw;
}

.playerListTitle {
  display: flex;
  align-items: center;
  gap: 0.4948vw;
  color: #fff;
  text-align: center;
  font-family: Barlow;
  font-size: 1.0302vw;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 23.142px */
}

.playerList {
  display: flex;
  gap: 0.5729vw;
}

.playerBox {
  padding-left: 0.7813vw;
  width: 13.0208vw;
  height: 3.2813vw;
  display: flex;
  align-items: center;
  gap: 0.8854vw;
  border-radius: 0.2083vw;
  border: 0.0521vw solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.04);
}

.ownerPlayer {
  border: 0.0521vw solid rgba(182, 255, 181, 0.08);
  background: rgb(181, 210, 255, 0.1);
}

.playerImg {
  position: relative;
  width: 1.7411vw;
  height: 1.7411vw;
  border-radius: 50%;
  background-size: cover;
}

.playerImg::before {
  content: "";
  position: absolute;
  display: flex;
  top: -0.2188vw;
  left: -0.2188vw;
  right: -0.2188vw;
  bottom: -0.2188vw;
  border-radius: 50%;
  border: 0.0446vw solid #fff;
}

.ownerPlayer .playerImg::before {
  border: 0.0446vw solid #7ba9df !important;
}

.playerTextBox {
  display: flex;
  flex-direction: column;
  gap: 0.1042vw;
}

.playerName {
  color: #fff;
  font-family: Barlow;
  font-size: 0.8333vw;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 18.72px */
}

.playerName span {
  color: rgba(255, 255, 255, 0.6);
  font-family: Barlow;
  font-size: 0.8333vw;
  font-style: normal;
  font-weight: 400;
  line-height: 117%;
}

.playerLevel {
  padding-left: 0.1vw;
  display: flex;
  align-items: center;
  gap: 0.3646vw;
}

.playerLevelLine {
  position: relative;
  width: 0.1894vw;
  height: 0.1894vw;
  border-radius: 1.3746vw;
  background: radial-gradient(50% 50% at 50% 50%, #ffe353 0%, #b29500 100%);
  box-shadow: 0px 0px 0.197vw 0.1894vw rgba(255, 227, 83, 0.35);
}

.playerLevelLine::before {
  content: "";
  position: absolute;
  display: flex;
  top: -0.1135vw;
  left: -0.1135vw;
  right: -0.1135vw;
  bottom: -0.1135vw;
  border-radius: 50%;
  border: 0.0573vw solid #ffe353;
}

.playerLevelText {
  color: #ffe353;
  font-family: Barlow;
  font-size: 0.7292vw;
  font-style: normal;
  font-weight: 400;
  line-height: 117%; /* 16.38px */
}

.playerInvite {
  display: flex;
  align-items: center !important;
  gap: 0.3125vw;
  justify-content: center !important;
  border-radius: 0.2083vw;
  border: 1px solid rgba(255, 255, 255, 0.08) !important;
  background: rgba(255, 255, 255, 0.01) !important;
  color: rgba(255, 255, 255, 0.51);
  font-family: Barlow;
  font-size: 0.8967vw;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 20.143px */
  transition: 250ms ease-in-out;
}

.playerInvite:hover {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.05) !important;
}

.invitePlayer {
  display: flex;
  align-items: center;
  justify-content: space-between !important;
  padding: 0.5208vw !important;
  border: 0.0521vw solid rgba(255, 255, 255, 0.08) !important;
  background: rgba(255, 255, 255, 0.06) !important;
}

.input-wrapper {
  position: relative;
  height: 100%;
  width: 9vw;
}

.modern-input {
  padding: 0vw 0.5208vw;
  width: 100%;
  height: 100%;
  border: none;
  transition: border-bottom 0.3s;
  border-radius: 0.2083vw;
  background: rgba(255, 255, 255, 0.06);
  color: rgba(255, 255, 255, 0.863);
  font-family: Barlow;
  font-size: 0.8967vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 20.143px */
  outline: none;
}

.modern-input::placeholder {
  color: rgba(255, 255, 255, 0.49);
}

.inviteButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.2396vw;
  height: 100%;
  border-radius: 0.2083vw;
  border: 0.0521vw solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.12);
  transition: 250ms ease-in-out;
}

.inviteButton svg {
  fill: white;
  fill-opacity: 0.53;
  width: 1.3021vw;
  height: 1.3021vw;
  transition: 250ms ease-in-out;
}

.inviteButton:hover svg {
  fill: black;
  fill-opacity: 0.8;
}

.inviteButton:hover {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.8);
}

.detailBox {
  position: relative;
  gap: 0.625vw;
  padding: 1.0417vw;
  margin-top: 0.625vw;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 7.9167vw;
  border-radius: 9px;
  border: 1px solid rgba(255, 255, 255, 0.21);
}

.noDetail {
  align-items: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  position: absolute;
  gap: 0.8333vw;
  padding: 0vw 0.7813vw;
  border-radius: 0.2083vw;
  height: 2.6563vw;
  color: #fff;
  background: rgba(255, 255, 255, 0.04);
}

.detailTitle {
  display: flex;
  align-items: center;
  gap: 0.4167vw;
  color: #fff;
  text-align: center;
  font-family: Barlow;
  font-size: 0.9192vw;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 20.65px */
}

.detailTextBox {
  display: flex;
  gap: 1.1458vw;
}

.detailTexts {
  display: flex;
  flex-direction: column;
  gap: 0.2083vw;
}

.detailText {
  color: rgba(255, 255, 255, 0.69);
  font-family: Barlow;
  font-size: 0.813vw;
  font-style: normal;
  font-weight: 600;
  line-height: 117%; /* 18.263px */
}

.detailSubText {
  width: 20.5208vw;
  color: rgba(255, 255, 255, 0.46);
  font-family: Barlow;
  font-size: 0.813vw;
  font-style: normal;
  font-weight: 400;
  line-height: 117%; /* 18.263px */
}

.rewardBox {
  display: flex;
  flex-direction: column;
  gap: 0.2083vw;
  padding: 0.8333vw;
  padding-top: 1.3542vw;
  position: absolute;
  right: 0.8333vw;
  justify-content: space-between;
  align-items: center;
  top: 0.8333vw;
  width: 11.3021vw;
  height: 6.25vw;
  border-radius: 0.3646vw;
  background: rgba(255, 255, 255, 0.02);
}

.rewardTopBox {
  position: absolute;
  left: 0;
  top: 0;
  height: 0.9375vw;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.3646vw 0.3646vw 0px 0px;
  background: rgba(255, 255, 255, 0.08);
  color: #ffffffb9;
  text-align: center;
  font-family: Barlow;
  font-size: 0.4167vw;

  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 9.36px */
}

.reward {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.2083vw;
  width: 100%;
  border-radius: 0.1627vw;
  background: rgba(67, 255, 97, 0.12);
  backdrop-filter: blur(0.2061vw);
  width: 9.6354vw;
  height: 1.9271vw;
  color: rgba(67, 255, 97, 0.62);
  text-align: center;
  text-shadow: 0px 0px 0.7164vw rgba(67, 255, 97, 0.6);
  font-family: Barlow;
  font-size: 0.4167vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 7.2px */
}

.rewardText {
  color: #43ff61;
  text-align: center;
  text-shadow: 0px 0px 0.7164vw rgba(67, 255, 97, 0.6);
  font-family: Barlow;
  font-size: 0.7049vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 12.181px */
}

.expReward {
  height: 1.8vw;
  background: rgba(255, 227, 83, 0.12);
  color: rgba(255, 227, 83, 0.53);
  text-align: center;
  text-shadow: 0px 0px 0.7164vw rgba(255, 227, 83, 0.6);
  font-family: Barlow;
  font-size: 0.4167vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 7.2px */
}

.expReward .rewardText {
  color: #ffe353;
  text-align: center;
  text-shadow: 0px 0px 0.7164vw rgba(255, 227, 83, 0.6);
  font-family: Barlow;
  font-size: 0.7049vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 12.181px */
}

.startButton {
  align-items: center;
  justify-content: center;
  overflow: hidden;
  display: flex;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3.2292vw;
  z-index: 5;
  border-radius: 0px 0px 1.1458vw 1.1458vw;
  background: linear-gradient(
    90deg,
    rgba(125, 193, 255, 0.4) 0%,
    rgba(75, 92, 153, 0.4) 100%
  );
  /* backdrop-filter: blur(0.8542vw); */

  transition: 250ms ease-in-out;
}

.buttonImg {
  position: absolute;
  left: 23.5vw;
  height: 100%;
  width: 4.8958vw;
  object-fit: cover;
  transition: 200ms ease-in-out;
}

.startButton:hover {
  cursor: pointer;
}

.startButton:hover .startText {
  color: #05192c;
}
.startButton:hover .buttonImg {
  transform: scale(150);
}

.startText {
  z-index: 1;
  color: #7dc1ff;
  font-family: Barlow;
  font-size: 1.1458vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 19.8px */
  transition: 250ms ease-in-out;
}

.exitBox {
  z-index: 3;
  position: absolute;
  top: 1.0938vw;
  right: 1.0938vw;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.1979vw;
  height: 1.1979vw;
  border-radius: 0.1917vw;
  background: linear-gradient(171deg, #ff5454 6.9%, #d12929 95.31%);
  transition: 250ms ease-in-out;
}

.exitBox:hover {
  cursor: pointer;
  box-shadow: 0px 0px 1.2292vw 0px #f54b4b8e;
}

.bgEffect {
  position: absolute;
  z-index: 1;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 45.8333vw;
  object-fit: cover;
  border-radius: 1.1458vw;
}

.leftEffect {
  z-index: 1;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 44.4271vw;
  object-fit: cover;
  border-radius: 1.1458vw;
}

.marketSide {
  z-index: 5;
  padding-top: 0.7813vw;
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 1.0938vw;
  flex: 1;
  overflow: hidden;
}

.marketTopSide {
  justify-content: space-between;
  display: flex;
  width: 100%;
}

.searchInputBox {
  width: 19.2188vw;
  height: 1.4063vw;
  display: flex;
  align-items: center;
  position: relative;
}

.searchInput {
  padding: 0vw 0.4vw;
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  border-radius: 0.1563vw;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.726);
  font-family: Barlow;
  font-size: 0.625vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 10.8px */
  transition: 200ms ease-in-out;
}

.searchInput:focus {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.08);
}
.searchInputBox svg {
  position: absolute;
  right: 0.4vw;
}

.marketList {
  display: flex;
  align-content: flex-start;
  width: 100%;
  flex-wrap: wrap;
  gap: 0.7292vw;
  overflow-y: scroll;
  overflow-x: hidden;
  flex: 1;
  scroll-behavior: smooth;
}

.sellMenu .marketList {
  gap: 0.6771vw;
}
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

.itemBox,
.itemSellBox {
  padding: 0.8333vw;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 12.9167vw;
  height: 12.7604vw;
  border-radius: 0.3125vw;
  border: 0.0521vw solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.06) 0%,
    rgba(255, 255, 255, 0) 124.47%
  );
  transition: 200ms ease-in-out;
}

.itemBox:hover,
.itemSellBox:hover {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0) 124.47%
  );
}

.itemTopSide {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.itemNameBox {
  display: flex;
  flex-direction: column;
  gap: 0.3125vw;
}

.itemName {
  color: #fff;
  font-family: Barlow;
  font-size: 0.9375vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 16.2px */
}

.itemSubName {
  color: rgba(255, 255, 255, 0.56);
  font-family: Barlow;
  font-size: 0.7292vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 12.6px */
}

.itemKg {
  color: rgba(255, 255, 255, 0.52);
  font-family: Barlow;
  font-size: 0.625vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 10.8px */
}

.itemImg {
  position: absolute;
  left: 50%;
  top: 55%;
  object-fit: contain;
  transform: translate(-50%, -50%);
  width: 9.669vw;
  height: 7.3351vw;
}

.itemBuyButtonBox {
  padding-left: 0.4167vw;
  align-items: center;
  justify-content: space-between;
  z-index: 1;
  display: flex;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1.875vw;
  border-radius: 0px 0px 0.3125vw 0.3125vw;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(0.1797vw);
}

.itemAmountBox {
  display: flex;
  align-items: center;
  gap: 0.4167vw;
}

.itemAmountButton {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.1157vw;
  background: rgba(255, 255, 255, 0.12);
  width: 1.0417vw;
  height: 1.0417vw;
  transition: 200ms ease-in-out;
}

.itemAmountButton:hover {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.3);
}

.itemAmountButton:hover svg {
  fill-opacity: 1;
}

.itemAmountButton svg {
  fill: white;
  width: 0.8333vw;
  height: 0.8333vw;
  fill-opacity: 0.6;
  transition: 200ms ease-in-out;
}

.itemAmountText {
  color: #fff;
  width: 2vw;
  text-align: center;
  font-family: Barlow;
  font-size: 0.7292vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 12.6px */
}

.itemBuyButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 6.9271vw;
  height: 100%;
  border-radius: 0px 0px 0.3125vw 0px;
  background: rgba(67, 255, 97, 0.16);
  transition: 200ms ease-in-out;
  color: #43ff61;
  font-family: Barlow;
  font-size: 0.625vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 10.8px */
}

.itemBuyButton:hover {
  cursor: pointer;
  background: rgba(67, 255, 97, 1);
  color: black;
}

.sellMenuActive {
  padding-bottom: 2.3958vw;
}
.itemSellBox {
  width: 17.5vw;
}

.itemSellButtonList {
  display: flex;
  height: 100%;
}

.itemSellButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 6.9271vw;
  height: 100%;
  color: #ff5050;
  font-family: Barlow;
  font-size: 0.625vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 10.8px */
  background: rgba(255, 80, 80, 0.16);
  transition: 200ms ease-in-out;
}

.itemSellButton:hover {
  cursor: pointer;
  background: rgba(255, 80, 80, 0.5);
  color: #ffd5d5;
}
.itemSellAllButton:hover {
  cursor: pointer;
  background: #db3434;
  color: white;
}
.itemSellAllButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4.5833vw;
  height: 100%;
  border-radius: 0px 0px 0.3125vw 0px;
  background: #ff5050;
  color: #000;
  font-family: Barlow;
  font-size: 0.625vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 10.8px */
  transition: 200ms ease-in-out;
}

/*  */

.requestMenu {
  padding: 1.25vw;
  z-index: 5;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 27.5vw;
  height: 10.9896vw;
  border-radius: 0.2604vw;
  outline: 0.4688vw solid rgba(0, 0, 0, 0.23);
  background: radial-gradient(49.91% 39.95% at 2.58% -2.85%, rgba(132, 181, 255, 0.19) 0%, rgba(132, 181, 255, 0.00) 100%), rgba(22, 28, 32, 0.96);
}

.requestTitle {
  font-family: Barlow;
  font-size: 1.5734vw;
  font-style: normal;
  font-weight: 800;
  line-height: 117%; /* 35.344px */
  background: linear-gradient(93deg, rgba(56, 87, 169, 0.90) -3.7%, rgba(135, 190, 240, 0.90) 98.85%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.requestText {
  color: #fff;
  text-align: center;
  font-family: Barlow;
  font-size: 0.7519vw;
  font-style: normal;
  font-weight: 400;
  line-height: 153%; /* 22.089px */
}

.requestButtonList {
  display: flex;
  gap: 1.0938vw;
}

.denyButton {
  background: linear-gradient(
    98deg,
    rgba(169, 56, 56, 0.9) -1.83%,
    rgba(240, 135, 135, 0.9) 149.2%
  );
  color: #ffb0b0;
}

.acceptButton {
  background: linear-gradient(
    98deg,
    rgba(58, 169, 56, 0.9) -1.83%,
    rgba(137, 240, 135, 0.9) 149.2%
  );

  box-shadow: 0px 0px 2.474vw 0px rgba(78, 176, 76, 0.35);
  color: #b2ffb0;
}

.denyButton:hover {
  cursor: pointer;
  color: #ffffff;
  box-shadow: 0px 0px 2.474vw 0px rgba(176, 76, 76, 0.6);
}

.acceptButton:hover {
  cursor: pointer;
  color: #ffffff;
  box-shadow: 0px 0px 2.474vw 0px rgba(78, 176, 76, 0.6);
}

.requestButton {
  width: 9.8958vw;
  height: 2.0313vw;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Barlow;
  font-size: 0.8333vw;
  text-align: center;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 18.72px */
  border-radius: 0.2604vw;
  transition: 200ms ease-in-out;
}

.finishBox {
  animation: finishAnimation 1s ease-in-out;
  justify-content: space-between;
  padding: 0vw 4.5833vw;
  align-items: center;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  width: 100%;
  height: 15vw;
  background: radial-gradient(
      49.91% 39.95% at 2.58% -2.85%,
      rgba(132, 181, 255, 0.19) 0%,
      rgba(132, 181, 255, 0) 100%
    ),
    rgba(22, 28, 32, 0.96);
}

@keyframes finishAnimation {
  0% {
    transform: translateY(200%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
.earnBox {
  z-index: 3;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  padding: 0.8333vw 0.7813vw;
  width: 25.1042vw;
  height: 8.6458vw;
  border-radius: 4px;
  background: linear-gradient(
    270deg,
    rgba(255, 255, 255, 0.06) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

.earnTitle {
  color: #fff;
  font-family: Barlow;
  font-size: 1.0417vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 18px */
}

.earnList {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.earn {
  gap: 1vw;
  align-items: center;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 11.3542vw;
  height: 5.2vw;
  border-radius: 0.2083vw;
}

.expEarn {
  background: linear-gradient(
    180deg,
    rgba(255, 240, 103, 0.07) 0%,
    rgba(153, 144, 62, 0) 100%
  );
}

.moneyEarn {
  background: linear-gradient(
    180deg,
    rgba(103, 255, 136, 0.07) 0%,
    rgba(103, 255, 136, 0) 100%
  );
}

.earnTopSide {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 0.9896vw;
  border-radius: 0.2083vw 0.2083vw 0px 0px;
  text-align: center;
  font-family: Barlow;
  font-size: 0.5208vw;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 11.7px */
}

.expEarn .earnTopSide {
  color: #fff6a6;
  background: rgba(255, 241, 128, 0.08);
}

.moneyEarn .earnTopSide {
  color: #a6ffc9;
  background: rgba(103, 255, 136, 0.08);
}

.scoreEarn .earnTopSide {
  color: #a6bfff;
  background: rgba(103, 144, 255, 0.08) !important;
}

.earnTextBox {
  display: flex;
  flex-direction: column;
  gap: 0.2604vw;
  align-items: center;
}

.earnText {
  font-family: Barlow;
  font-size: 1.25vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 21.6px */
}

.expEarn .earnTextBox .earnText {
  color: #fff180;
}

.moneyEarn .earnTextBox .earnText {
  color: #64ff8f;
}

.scoreEarn .earnTextBox .earnText {
  color: #6495ff;
}

.earnSubText {
  font-family: Barlow;
  font-size: 0.625vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 10.8px */
}

.expEarn .earnTextBox .earnSubText {
  color: rgba(255, 241, 128, 0.53);
}

.moneyEarn .earnTextBox .earnSubText {
  color: rgba(100, 255, 143, 0.53);
}

.scoreEarn .earnTextBox .earnSubText {
  color: rgba(100, 134, 255, 0.53);
}

.scoreEarn {
  width: 100%;
  background: rgba(103, 144, 255, 0.08) !important;
}

.finishTitleBox {
  z-index: 3;
  display: flex;
  flex-direction: column;
  gap: 0.5208vw;
  align-items: center;
}

.finishSubTitle {
  color: #fff;
  font-family: Oswald;
  font-size: 2.0549vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%; /* 35.509px */
  text-transform: uppercase;
}

.finishTitle {
  text-shadow: 0px 0px 137.6px #6a85cc;
  -webkit-text-stroke-width: 2;
  -webkit-text-stroke-color: rgba(255, 255, 255, 0.13);
  font-family: Oswald;
  font-size: 6.4725vw;
  font-style: normal;
  font-weight: 700;
  line-height: 90%; /* 111.845px */
  text-transform: uppercase;
  background: linear-gradient(
    93deg,
    rgba(50, 100, 157, 0.9) -3.7%,
    rgba(135, 190, 240, 0.9) 98.85%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.finishName {
  color: rgba(255, 255, 255, 0.63);
  font-family: Barlow;
  font-size: 1.5vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 27px */
}

.finishEffect {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 62.6563vw;
  height: 100%;
  object-fit: cover;
  pointer-events: none;
}

.scoreList {
  right: 3.2917vw;
  bottom: 3.7917vw;
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 0.2083vw;
}

.scoreBox {
  padding: 0vw 0.6771vw;
  justify-content: space-between;
  display: flex;
  align-items: center;
  width: 12.3438vw;
  height: 2.7vw;
  border-radius: 0.4167vw;
  background: rgba(31, 41, 50, 0.96);
}

.scorePlayerBox {
  display: flex;
  align-items: center;
  gap: 0.625vw;
}
.scorePlayerImg {
  position: relative;
  width: 1.368vw;
  height: 1.368vw;
  background-size: cover;
  border-radius: 50%;
}

.scorePlayerImg::before {
  content: "";
  position: absolute;
  display: flex;
  top: -0.1755vw;
  left: -0.1755vw;
  right: -0.1755vw;
  bottom: -0.1755vw;
  border-radius: 50%;
  border: 0.0351vw solid #fff;
}

.scorePlayerTextBox {
  display: flex;
  flex-direction: column;
  gap: 0.1042vw;
}

.scorePlayerName {
  color: #fff;
  font-family: Barlow;
  font-size: 0.7159vw;
  font-style: normal;
  font-weight: 500;
  line-height: 117%; /* 16.083px */
}

.scorePlayerLevel {
  padding-left: 0.15vw;
  display: flex;
  align-items: center;
  gap: 0.4167vw;
}

.scorePlayerLevelLine {
  position: relative;
  width: 0.1627vw;
  height: 0.1627vw;
  border-radius: 0.781vw;
  background: radial-gradient(50% 50% at 50% 50%, #ffe353 0%, #b29500 100%);
  box-shadow: 0px 0px 0.1692vw 0.1627vw rgba(255, 227, 83, 0.35);
}

.scorePlayerLevelLine::before {
  content: "";
  position: absolute;
  display: flex;
  top: -0.15vw;
  left: -0.15vw;
  right: -0.15vw;
  bottom: -0.15vw;
  border-radius: 50%;
  border: 0.0326vw solid #ffe353;
}

.scorePlayerLevelText {
  color: #ffe353;
  font-family: Barlow;
  font-size: 0.6265vw;
  font-style: normal;
  font-weight: 400;
  line-height: 117%; /* 14.073px */
}

.scoreSay {
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  padding: 0vw 0.3646vw;
  height: 1.4063vw;
  gap: 0.1563vw;
  border-radius: 0.1042vw;
  background: rgba(255, 255, 255, 0.09);
}

.kn {
  width: 0.9375vw;
  height: 0.9375vw;
  object-fit: cover;
}

.scoreSayText {
  color: #8dc0ff;
  font-family: Barlow;
  font-size: 0.9276vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 16.028px */
}

.allScoreBox {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 0.5208vw;
  left: 50%;
  transform: translateX(-50%);
  top: 2.2917vw;
}

.allScore {
  gap: 0.4688vw;
  display: flex;
  align-items: center;
  padding: 0vw 0.4167vw;
  width: fit-content;
  border-radius: 0.2604vw;
  background: rgba(31, 41, 50, 0.96);
  height: 1.7188vw;
}

.allScoreImg {
  width: 0.9896vw;
  height: 0.9896vw;
  object-fit: cover;
}

.allScoreText {
  color: #fff;
  font-family: Barlow;
  font-size: 0.9566vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 16.531px */
}

.allScoreText span {
  color: rgba(255, 255, 255, 0.51);
  font-family: Barlow;
  font-size: 0.9566vw;
  font-style: normal;
  font-weight: 400;
  line-height: 90%;
}

.loadingBox {
  position: absolute;
  z-index: 99;
  bottom: 5%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  position: absolute;
  flex-direction: column;
  align-items: center;
  gap: 0.3125vw;
}

.loadingBar {
  padding: 0vw 0.4688vw;
  display: flex;
  align-items: center;
  width: 17.6563vw;
  height: 0.9896vw;
  border-radius: 1.875vw 1.875vw 0px 0px;
  background: rgba(31, 41, 50, 0.96);
}

.bars {
  display: flex;
  align-items: center;
  width: 100%;
  height: 0.4167vw;
  border-radius: 1.1458vw 1.1458vw 0px 0px;
  background: rgba(255, 255, 255, 0.09);
}

.bar {
  height: 100%;
  border-radius: 1.1458vw 1.1458vw 0px 0px;
  background: #fff;
  width: 50%;
}

.loadingText {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0vw 0.3646vw;
  position: relative;
  height: 1.0417vw;
  border-radius: 0.1042vw;
  width: fit-content;
  background: rgba(31, 41, 50, 0.96);
  color: #fff;
  font-family: Barlow;
  font-size: 0.5208vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%; /* 9px */
}

.loadingLine {
  position: absolute;
  top: 0;
  background: #72b8ff;
  height: 0.0521vw;
  width: 50%;
}

.tubeBox {
  display: flex;
  flex-direction: column;
  bottom: 24.5vw;
  right: 1.5vw;
  position: absolute;
  z-index: 99;
  height: 25vw; 
  width: 0.9375vw;
  align-items: center;
  border-radius: 0.2604vw;
  padding-bottom: 1.5104vw;
  background: rgba(41, 55, 65, 0.9);
  padding-top: 0.1563vw;
}

.tubeIcon {
  position: absolute;
  bottom: 0;
  width: 1.3542vw;
  height: 1.3542vw;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.2604vw;
  background: rgba(32, 42, 49, 0.9);
  /* backdrop-filter: blur(0.1042vw); */
}

.tubeImg {
  object-fit: cover;
  width: 1.7vw;
  height: 1.7vw;
}

.tubeBar {
  position: relative;
  width: 0.625vw;
  display: flex;
  align-items: center;
  flex-direction: column-reverse;
  height: 100%;
  border-radius: 0.1042vw;
  background: rgba(255, 255, 255, 0.04);
}

.bar {
  width: 0.4167vw;
  border-radius: 0.1042vw;
  background: linear-gradient(180deg, #54ccff 0%, #327a99 100%);
  transition: 200ms ease-in-out;
}

.tubeSay {
  display: flex;
  align-items: center;
  position: absolute;
  border-radius: 0.3646vw;
  background: #fff;
  width: 1.5625vw;
  height: 0.1563vw;
}

.say1 {
  top: 0.3125vw;
}

.say1::before {
  content: "F";
}


.say2 {
  top: 50%;
  transform: translateY(-50%);
}

.say2::before {
  content: "H";
}

.say3 {
  bottom: 0.3125vw;
}

.say3::before {
  content: "M";
}

.tubeSay::before {
  position: absolute;
  left: -0.7vw;
  color: rgba(255, 255, 255, 0.51);
  font-family: Barlow;
  font-size: 0.742vw;
  font-style: normal;
  font-weight: 500;
  line-height: 90%;
}

.notifyList {
  z-index: 99;
  position: absolute;
  left: 2.0833vw;
  top: 2.0833vw;
  display: flex;
  flex-direction: column;
  gap: 0.6771vw;
}

.notifyBox {
  position: relative;
  padding: 0.625vw 0.7813vw;
  display: flex;
  align-items: center;
  gap: 0.7292vw;
  width: 17vw;
  height: fit-content;
  border-radius: 0.3646vw;
  animation: notify 1s ease-in-out;
}

.notifyBox:nth-child(2) {
  animation: notify 1.1s ease-in-out !important;
}

.notifyBox:nth-child(3) {
  animation: notify 1.2s ease-in-out !important;
}

.notifyBox:nth-child(4) {
  animation: notify 1.3s ease-in-out !important;
}

.notifyBox:nth-child(5) {
  animation: notify 1.4s ease-in-out !important;
}

.notifyBox:nth-child(6) {
  animation: notify 1.5s ease-in-out !important;
}

.notifyBox:nth-child(7) {
  animation: notify 1.6s ease-in-out !important;
}

.notifyBox:nth-child(8) {
  animation: notify 1.7s ease-in-out !important;
}

.infoNotify {
  background: rgba(31, 41, 50, 0.96);
}

.succesNotify {
  background: rgba(32, 50, 31, 0.96);
}

.errorNotify {
  background: rgba(50, 31, 31, 0.96);
}

.notifyBox svg {
  width: 1.7708vw;
  height: 1.7708vw;
}

.infoNotify svg {
  fill: #48bdff !important;
}

.errorNotify svg {
  fill: #ff4848 !important;
}

.succesNotify svg {
  fill: #62ff48 !important;
}

.notifyTextBox {
  z-index: 5;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.1042vw;
}

.notifyText {
  font-family: Barlow;
  font-size: 0.8vw;
  font-style: normal;
  font-weight: 600;
  line-height: 90%; /* 10.8px */
}

.infoNotify .notifyTextBox .notifyText {
  color: #48bdff;
}

.errorNotify .notifyTextBox .notifyText {
  color: #ff4848;
}

.succesNotify .notifyTextBox .notifyText {
  color: #62ff48;
}

.notifySubText {
  color: #fff;
  font-family: Barlow;
  font-size: 0.6vw;
  font-style: normal;
  font-weight: 400;
  line-height: 127%; /* 12.7px */
}

.notifyLine {
  position: absolute;
  left: 0;
  width: 0.1vw;
  height: 45%;
}

.infoNotify .notifyLine {
  background: #48bdff;
  box-shadow: 0px 0px 0.7031vw 0.1563vw rgba(72, 189, 255, 0.74);
}

.errorNotify .notifyLine {
  background: #ff4848;
  box-shadow: 0px 0px 0.7031vw 3px rgba(255, 72, 72, 0.74);
}

.succesNotify .notifyLine {
  background: #62ff48;

  box-shadow: 0px 0px 0.7031vw 0.1563vw rgba(98, 255, 72, 0.74);
}

@keyframes notify {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}