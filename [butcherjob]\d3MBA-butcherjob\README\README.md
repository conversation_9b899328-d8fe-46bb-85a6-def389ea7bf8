# Thank You for Purchasing <PERSON> Scrip<PERSON>!

We appreciate your support and hope you enjoy using the script.

## Butcher job Script

**Discord:** d3MBA#0001

### Preview

[![Butcherjob Preview](https://youtu.be/eF_SU1ur_P8)]

### Support

Join our Discord server for support: [https://discord.gg/d3MBA]

### Installation

1. Download the latest version of d3MBA-lib from [https://github.com/d3MBA/d3MBA-lib]
2. Customize the config file of d3MBA-lib
3. Make sure d3MBA-lib, is started before all other d3MBA scripts - 
(you don't need to to start it yourself, it will be done automatically when you start the d3MBA-lib)

4. Drag and drop `d3MBA-butcherjob` into your server resources

5. If you using `ESX framework` or `Framework.SpecificItemLabels = true` then you should
copy item labels from `README/item-labels.lua` and paste it into d3MBA-lib/item-labels/labels.lua

6. Upload `d3_butcherjob.sql` into your server SQL
  
7. Add information items provided ("items" folder) into your -
[QB-Core] - `qb-core/shared/items.lua`, [ox_inventory] - `ox_inventory/data/items.lua`, [ESX] - `upload sql file from items folder to database`

8. Add images provided into your `inventory/html/images`, [ox_inventory] - `ox_inventory/web/build/images` 
9. Customize the script to your liking, support can be provided if needed
10. Restart your server

