return {
    enabled = true,

    organizer = {
        coords = vec4(1654.0995, 3811.9346, 33.8558, 214.4073),

        model = 'a_m_y_beach_01',
        scenario = 'WORLD_HUMAN_CLIPBOARD',

        blip = {
            enabled = true,
            sprite = 267,
            display = 4,
            scale = 0.47,
            colour = 46,
            label = 'Tournament Organizer'
        }
    },

    tournamentBlip = {
        sprite = 68,
        display = 4,
        scale = 0.7,
        color = 5,
        label = 'Fishing Tournament'
    },

    entryFee = 1000,

    timing = {
        duration = 60,
        announce = 30,
    },
    
    reconnectionTime = 10,

    scheduling = {
        enabled = false,
        
        timezoneOffset = 0,
        
        schedule = {
            -- [0] = { { 12, 00 }, { 18, 00 } },
            -- [1] = { { 12, 00 }, { 18, 00 } },
            -- [2] = { { 12, 00 }, { 18, 00 } },
            -- [3] = { { 12, 00 }, { 18, 00 } },
            -- [4] = { { 12, 00 }, { 18, 00 } },
            -- [5] = { { 12, 00 }, { 18, 00 } },
            -- [6] = { { 12, 00 }, { 18, 00 } },
        }
    },
    
    rarityMultipliers = {
        common = 1.0,
        uncommon = 1.25,
        rare = 1.5,
        epic = 1.75,
        legendary = 2.0
    },
    
    pointCalculation = {
        baseMultiplier = 15,

        logBase = 100,
        
        minimumPoints = 5
    },
    
    rewards = {
        prize = {
            enabled = true,

            type = 'money',

            base = 50000,

            addEntryFees = true,
            playerMultiplier = 2.0,

            amount = {
                min = 50000,
                max = 100000
            }
        },
        
        bonus = {
            enabled = true,

            maxItems = 3,
            
            items = {
                { name = 'giant_trevally', amount = { min = 4, max = 8 }, chance = 50 },
                { name = 'arapaima', amount = { min = 4, max = 8 }, chance = 50 },
                { name = 'oarfish', amount = { min = 4, max = 8 }, chance = 50 },
                { name = 'barreleye', amount = { min = 4, max = 8 }, chance = 50 },
                { name = 'sturgeon', amount = { min = 4, max = 8 }, chance = 50 },
                { name = 'red_snapper', amount = { min = 4, max = 8 }, chance = 50 },
            }
        }
    },
    
    ---@param playerId number
    ---@param message string
    notification = function(playerId, message)
        local utils = require 'modules.utils.server'

        utils.notify(playerId, message, 'inform')
    end,

    commands = {
        starttournament = {
            enabled = true,
            name = 'starttournament', 
            help = 'Start a fishing tournament',
            restricted = 'group.admin'
        },
        
        jointournament = {
            enabled = true,
            name = 'jointournament',
            help = 'Join a fishing tournament',
            restricted = 'group.user'
        },
    }
}