{"stats": {"menu": {"title": "Fishing Statistics", "personal": {"title": "Personal Stats", "description": "Track your fishing achievements and progress", "header": "Personal Fishing Stats", "header_description": "Your fishing journey statistics"}, "leaderboard": {"title": "Global Leaderboard", "description": "Compete with top fishers worldwide", "header": "Top fishers by total catch", "no_records": {"title": "No Records Yet", "description": "Be the first to make a splash! Cast your line and make history!"}}}, "labels": {"total_fish": "Total Fish Caught", "biggest_fish": "Biggest Fish", "total_weight": "Total Weight", "time_spent": "Time Spent Fishing", "most_caught": "Most Caught Fish", "none": "None", "unknown": "Unknown", "hour": "hour", "hours": "hours", "minute": "minute", "minutes": "minutes"}, "format": {"fish_count": "%s (x%d)", "fish_weight": "%s (%s)", "leaderboard_position": "#%d %s", "total_fish": "Total Fish: %d", "best_catch": "Best Catch: %s (%s)"}}, "tasks": {"notify": {"completed_reward": "Task completed! You've earned $%s", "no_active_tasks": "No active fishing tasks available at the moment"}, "time_remaining_format": "%d hours %d minutes", "menu": {"title": "Fishing Tasks", "daily_title": "Daily Fishing Tasks", "no_tasks": "No active tasks found", "time_remaining": "Time remaining: %s", "reward": "<PERSON><PERSON>", "money_format": "$%d", "progress": "Progress", "weight_progress": "%.2f/%.1f kg", "count_progress": "%d/%d"}}, "skills": {"notify": {"gained_xp": "You've gained %d fishing XP! Keep casting!", "level_up": "Congratulations! You're now Fishing Level %d!", "level_reward": "You received rewards for reaching Fishing Level %d!", "admin_added_xp": "Added %d XP to %s", "admin_removed_xp": "Removed %d XP from %s", "admin_set_level": "Set %s's level to %d", "received_xp": "You received %d fishing XP from an admin", "lost_xp": "You lost %d fishing XP", "level_set": "An admin set your fishing level to %d"}, "menu": {"title": "Fishing Skills", "description": "View your fishing skill progress", "context_title": "Fishing Skill Progression", "current_level": "Current Level: %d", "xp_progress": "XP: %d / %d", "next_level": "Next Level: %d", "xp_needed": "XP Needed: %d"}}, "tournament": {"notify": {"tournament_starting_soon": "Tournament at %s starting soon! Registration is open. The competition begins in %d minutes!", "tournament_begun": "The fishing tournament has officially begun! Good luck!", "tournament_not_waiting": "Tournament is not currently waiting for players", "tournament_already_joined": "You are already in the tournament", "tournament_insufficient_funds": "Not enough money to join the tournament", "tournament_joined": "You have joined the tournament!", "tournament_rejoined": "You've been added back to the tournament with your previous score!", "tournament_earned_reward": "You earned %s %s in the tournament!", "tournament_participation_reward": "You received %s %s for participating in the tournament!", "tournament_received_item": "You received %dx %s!", "tournament_scheduling_disabled": "Tournament scheduling is disabled.", "tournament_no_today": "No tournaments scheduled for today.", "player_joined_tournament": "%s has joined the tournament!", "player_left_tournament": "%s has left the tournament", "earned_tournament_points": "You earned %s tournament points!", "tournament_in_zone": "A fishing tournament is currently starting in this zone. Go to the tournament organizer to join!", "tournaments_for_day": "Tournaments for %s:", "tournament_at_time": "- Tournament at %s", "tournament_ended": "The fishing tournament at %s has ended! Winner: %s", "no_zones_available": "No fishing zones available for tournaments", "tournament_created": "Tournament created successfully!", "tournament_creation_failed": "Failed to create tournament", "wrong_tournament_zone": "You must fish in the tournament zone to earn points!"}, "menu": {"title": "Fishing Tournament", "organizer_title": "Tournament Organizer", "active_tournament_description": "A fishing tournament is currently taking place at %s", "no_tournament": "No tournament is currently active", "tournament_info": "Tournament Information", "tournament_time_left": "Time until start: %d min %d sec", "tournament_time_remaining": "Time remaining: %d min %d sec", "tournament_in_progress": "Tournament In Progress", "tournament_already_started": "A tournament is already underway at %s", "missed_tournament": "Too Late to Join", "next_tournament_message": "Check the schedule for the next tournament", "status": "Status", "registering": "Registering", "in_progress": "In Progress", "location": "Location", "entry_fee": "Entry Fee", "participants": "Participants", "join_tournament": "Join Tournament", "join_description": "Entry fee: $%d", "already_joined": "Already Registered", "already_joined_description": "You are already registered for this tournament", "todays_schedule": "Today's Schedule", "tournament_time": "Tournament %d"}, "dialog": {"join_confirmation_header": "Join Tournament", "join_confirmation_content": "Join this fishing tournament for $%d?", "join_now": "Join Now", "cancel": "Cancel", "create_tournament": "Create Fishing Tournament", "select_zone": "Select Fishing Zone", "announce_time": "Announcement Period", "tournament_duration": "Tournament Duration", "entry_fee": "Entry Fee", "minutes": "Minutes"}, "ui": {"tournament_title": "Fishing Tournament", "position": "Position", "points": "Points", "no_one": "No One", "starting_in": "STARTING IN", "time_left": "TIME LEFT", "tournament_ended": "Tournament Ended"}, "interaction": {"talk_to_organizer": "Talk to Tournament Organizer"}}, "market": {"notify": {"not_enough_fish": "You don't have enough fish to sell", "sold_fish_singular": "Sold %dx %s for $%d", "sold_fish_plural": "Sold %d fish for a total of $%d", "no_fish_sold": "No fish were sold", "no_fish_to_sell": "You don't have any fish to sell right now"}, "menu": {"title": "Fish Market", "exchange_title": "Fish Market Exchange", "exchange_description": "Trade your catches for cold, hard cash", "sell_all": "<PERSON><PERSON>", "quick_sale": "Quick sale: %d fish for $%d total", "items": "Items", "total_value": "Total Value", "action": "Action", "instant_transaction": "Instant transaction", "rarity": "<PERSON><PERSON>", "demand": "Demand", "base_price": "Base Price", "current_price": "Current Price", "fish_description": "Qty: %d | Price: $%d %s | Value: $%d"}, "dialog": {"sale_confirmation_header": "Market Sale Confirmation", "sale_confirmation_content": "Confirm sale of %d %s for $%d?\n\nCurrent market price: $%d per unit", "bulk_sale_confirmation_content": "Confirm sale of %d %s for $%d?", "sell_now": "Sell Now", "cancel": "Cancel", "sell_item_title": "Sell %s", "quantity_to_sell": "Quantity to Sell", "market_price": "Market Price: $%d per fish | Max: %d", "sale_amount": "Sale Amount", "total_value_format": "Total Value: $%d"}, "interaction": {"talk_to_merchant": "Talk to Fish Merchant"}}, "store": {"notify": {"purchased_rod": "You've purchased a new fishing rod!", "cannot_afford_rod": "You can't afford this fishing rod", "cannot_afford": "You can't afford this purchase", "inventory_full": "Your inventory is full", "purchased_items": "You've purchased %dx %s", "cannot_carry": "You cannot carry this item"}, "menu": {"title": "Fishing Supply Store", "category_description": "Browse %s for your fishing adventures", "item_price": "%s | $%d", "price": "Price"}, "dialog": {"purchase_title": "Purchase %s", "quantity": "Quantity", "price_per_item": "Price: $%d per item"}, "interaction": {"talk_to_merchant": "Talk to Fishing Supplier"}}, "upgrade": {"notify": {"rod_max_upgrade": "This rod is already fully upgraded", "cant_afford": "Cannot afford this upgrade", "rod_upgraded": "Rod upgraded to %s", "no_rod_found": "No fishing rod found in inventory", "insufficient_level": "You need to be level %d to upgrade this rod", "cannot_upgrade": "Cannot upgrade this fishing rod", "cancelled": "Rod upgrade cancelled"}, "menu": {"title": "Upgrade Fishing Rod", "description": "Improve your fishing rod to catch better fish", "menu_title": "Rod Upgrade Shop", "select_rod": "Select Rod from Inventory", "select_description": "Choose a specific fishing rod to upgrade", "select_rod_title": "Select Fishing Rod to Upgrade", "select_rod_button": "Select Fishing Rod", "select_rod_description": "Choose which rod to upgrade from your collection", "rod_selection_desc": "%s Rod (Tier %d) - Durability: %d%%", "rod_durability_desc": "Durability: %d%%", "rod_tier": "Tier", "rod_durability": "Durability", "confirm_header": "Rod Upgrade Confirmation", "confirm_content": "Upgrade your %s to a %s for $%d?", "confirm_content_with_level": "Upgrade your %s to a %s for $%d?\nRequired fishing level: %d", "upgrade_button": "Upgrade Rod", "cancel_button": "Cancel"}, "interaction": {"target_label": "Upgrade Fishing Rod"}}, "minigame": {"ui": {"use": "Use", "catch_fish": "to catch the fish", "left_arrow": "←", "right_arrow": "→", "time_remaining": "%ds", "caught_it": "Caught it!", "time_expired": "Time expired!", "it_got_away": "It got away!", "or": "or"}}, "fishing": {"notify": {"need_zone_water": "You need to be in a fishing zone and near water", "need_water": "You need to be near water", "failed_bait": "Failed to use bait", "cancelled": "Fishing cancelled", "fish_got_away": "The fish got away!", "no_catch": "You didn't catch anything", "found_treasure": "You found %s!", "caught_fish": "You caught a %s weighing %skg!", "found_treasure_dropped": "You found %s but your inventory is full! It was dropped nearby.", "caught_fish_dropped": "You caught a %s weighing %skg but your inventory is full! It was dropped nearby.", "rod_no_bait": "This fishing rod does not support bait", "rod_no_tackle": "This fishing rod does not support tackle", "gave_rod": "Gave %s to player %s", "invalid_fish": "Invalid fish name", "gave_fish": "Gave %dx %s to player %d", "received_fish": "Received %dx %s from admin", "fishing_with_bait": "You started fishing with %s", "fishing_without_bait": "You started fishing without bait", "fish_biting": "A fish is biting your line! Get ready", "rod_broken": "Your fishing rod has broken!", "rod_already_broken": "This fishing rod is broken and cannot be used", "continue_fishing": "Continuing to fish...", "no_bait_left": "You've run out of bait!", "invalid_bait": "This item cannot be used as bait for fishing rods", "invalid_tackle": "This item cannot be used as tackle for fishing rods", "auto_fish_started": "Auto-fishing enabled", "auto_fish_stopped": "Auto-fishing disabled", "already_auto_fishing": "You are already auto-fishing", "insufficient_rod_level": "You need to be level %d to use this fishing rod"}, "input": {"auto_fish_header": "Auto Fishing", "auto_fish_question": "Would you like to enable auto-fishing?"}, "metadata": {"description_treasure": "%s  \n  \n**Location:** %s  \n**Type:** %s", "description_fish": "%s  \n  \n**Location:** %s  \n**Rarity:** %s", "treasure_type": "Treasure", "admin_location": "<PERSON><PERSON> Spawned", "rod_stats": "  \n  \n**Statistics:**  \nTime Spent Fishing: %d minutes  \nFish Caught: %d  \nHeaviest Catch: %s"}, "storage": {"bait": "Bait Storage", "tackle": "Tackle Storage"}, "drop_prefix": {"treasure": "Treasure", "fish": "Fish"}, "global_waters": "Global Waters"}, "net": {"notify": {"invalid_placement": "Invalid placement location", "need_deeper_water": "Nets must be placed in deeper water", "too_far": "You're too far away to place a net here", "failed_placement": "Failed to place the net", "net_placed": "You've placed a fishing net", "not_owner": "You cannot remove a net you don't own", "net_retrieved": "You have retrieved your fishing net", "max_nets": "You can only have %d active fishing nets at a time", "no_bait": "The net has run out of bait and stopped operating", "no_bait_to_start": "You need to add bait before starting the net operation", "fish_full": "The net has stopped operating because the fish storage is full", "net_broken": "The net has broken and stopped operating", "caught_fish": "Your fishing net caught: %s", "operation_toggled": "Fishing operation %s", "invalid_bait": "This item cannot be used as bait for fishing nets", "invalid_fish": "This item cannot be placed in the fish storage", "invalid_bait_in_stash": "Net cannot operate with invalid bait items in the storage", "placement_cancelled": "Net placement cancelled"}, "menu": {"title": "Fishing Net Management", "net_status": "Net Status", "condition_description": "Current net condition: %d/100", "bait_storage": "Open Bait Storage", "bait_description": "Add bait to keep your net fishing", "fish_storage": "Open Fish Storage", "fish_description": "Collect your caught fish", "start_operation": "Start Fishing Operation", "stop_operation": "Stop Fishing Operation", "start_description": "Start automatic fishing (requires bait)", "stop_description": "Stop the net from fishing automatically", "status_format": "%s (Currently: %s)", "status_active": "active", "status_inactive": "inactive", "remove_net": "Remove Net", "remove_description": "Take down your fishing net and add it to your inventory"}, "textui": {"place_net": "[E] - Place Net | [X] - Cancel"}, "interaction": {"check_net": "Check Fishing Net"}}, "start": {"menu": {"title": "Fishing Instructor", "welcome_message": "Welcome to the fishing area! Check your stats or take on daily tasks.", "main_title": "Fishing Guide", "level_display": "Fishing Level: %d", "xp_display": "%d / %d XP", "no_skill_data": "No skill data available", "stats_option": "Fishing Statistics", "stats_description": "View your fishing achievements and records", "daily_tasks": "Daily Fishing Tasks", "daily_tasks_description": "Complete daily tasks to earn rewards"}, "interaction": {"talk_to_instructor": "Talk to Fishing Instructor"}}, "rarity": {"common": "Common", "uncommon": "Uncommon", "rare": "Rare", "epic": "Epic", "legendary": "Legendary", "treasure": "Treasure"}}