local playerClothing = { -- From here you configure your outfit
    male = {
        ["arms"] = 65,                                  ---@Arms
        ["tshirt_1"] = 59,      ["tshirt_2"] = 1,       ---@Tshirt
        ["torso_1"] = 5,        ["torso_2"] = 2,        ---@Torso
     -- ["decals_1"] = 0,       ["decals_2"] = 0,       ---@Decals
        ["pants_1"] = 9,        ["pants_2"] = 4,        ---@<PERSON><PERSON>
        ["shoes_1"] = 0,        ["shoes_2"] = 0,        ---@Shoes
        ["helmet_1"] = 145,     ["helmet_2"] = 1,       ---@Helmet
     -- ["chain_1"] = 0,        ["chain_2"] = 0,        ---@Chain
     -- ["ears_1"] = 0,         ["ears_2"] = 0,         ---@Ears
     -- ["mask_1"] = 0,         ["mask_2"] = 0,         ---@Mask
     -- ["bags_1"] = 0,         ["bags_2"] = 0,         ---@Bags
     -- ["glasses_1"] = 0,      ["glasses_2"] = 0,      ---@Glass<PERSON>
     -- ["vest_1"] = 0,         ["vest_2"] = 0,         ---@Vest
     -- ["watches_1"] = 0,      ["watches"] = 0,        ---@Watches
     -- ["bracelets_1"] = 0,    ["bracelets_2"] = 0,    ---@Bracelets
    },
    female = {
        ["arms"] = 84,                                  ---@Arms
        ["tshirt_1"] = 529,     ["tshirt_2"] = 0,       ---@Tshirt
        ["torso_1"] = 255,      ["torso_2"] = 15,       ---@Torso
     -- ["decals_1"] = 0,       ["decals_2"] = 0,       ---@Decals
        ["pants_1"] = 101,      ["pants_2"] = 6,        ---@Pants
        ["shoes_1"] = 107,      ["shoes_2"] = 9,        ---@Shoes
        ["helmet_1"] = 144,     ["helmet_2"] = 1,       ---@Helmet
     -- ["chain_1"] = 0,        ["chain_2"] = 0,        ---@Chain
     -- ["ears_1"] = 0,         ["ears_2"] = 0,         ---@Ears
     -- ["mask_1"] = 0,         ["mask_2"] = 0,         ---@Mask
     -- ["bags_1"] = 0,         ["bags_2"] = 0,         ---@Bags
        ["glasses_1"] = 5,      ["glasses_2"] = 0,      ---@Glasses
     -- ["vest_1"] = 0,         ["vest_2"] = 0,         ---@Vest
     -- ["watches_1"] = 0,      ["watches"] = 0,        ---@Watches
     -- ["bracelets_1"] = 0,    ["bracelets_2"] = 0,    ---@Bracelets
    }
}

function ToggleJobClothes()
    if Config.Framework == "ESX" then
        if Player.onDuty then
            -- ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
            --     TriggerEvent('skinchanger:loadSkin', skin)
            -- end)
            Player.onDuty = false
        else
            -- ESX.TriggerServerCallback('esx_skin:getPlayerSkin', function(skin)
            --     local outfit = {}
            --     if skin.sex == 0 then
            --         outfit = playerClothing.male
            --     else
            --         outfit = playerClothing.female
            --     end
            --     TriggerEvent('skinchanger:loadClothes', skin, {
            --         -- Don't modify this! configure your outfit in Config.Clothing at line 1
            --         ['arms']        = outfit["arms"],		
			-- 		['tshirt_1']    = outfit["tshirt_1"], 	    ['tshirt_2']    = outfit["tshirt_2"],
			-- 		['torso_1']     = outfit["torso_1"], 	    ['torso_2']     = outfit["torso_2"],
			-- 		['decals_1']    = outfit["decals_1"], 	    ['decals_2']    = outfit["decals_2"],
			-- 		['pants_1']     = outfit["pants_1"], 	    ['pants_2']     = outfit["pants_2"],
			-- 		['shoes_1']     = outfit["shoes_1"], 	    ['shoes_2']     = outfit["shoes_2"],
			-- 		['helmet_1']    = outfit["helmet_1"], 	    ['helmet_2']    = outfit["helmet_2"],
			-- 		['chain_1']     = outfit["chain_1"], 	    ['chain_2']     = outfit["chain_2"],
			-- 		['ears_1']      = outfit["ears_1"], 	    ['ears_2']      = outfit["ears_2"],
			-- 		['mask_1']      = outfit["mask_1"], 	    ['mask_2']      = outfit["mask_2"],
			-- 		['bags_1']      = outfit["bags_1"], 	    ['bags_2']      = outfit["bags_2"],
			-- 		['glasses_1']   = outfit["glasses_1"], 	    ['glasses_2']   = outfit["glasses_2"],
			-- 		['bproof_1']    = outfit["vest_1"], 	    ['bproof_2']    = outfit["vest_2"],
			-- 		['watches_1']   = outfit["watches_1"], 	    ['watches_2']   = outfit["watches_2"],
			-- 		['bracelets_1'] = outfit["bracelets_1"],    ['bracelets_2'] = outfit["bracelets_2"],
            --     })
                Player.onDuty = true
            -- end)
        end
    elseif Config.Framework == "QBCORE" then
        if Player.onDuty then
            TriggerServerEvent("qb-clothes:loadPlayerSkin")
            --TriggerEvent("illenium-appearance:client:reloadSkin")
            Player.onDuty = false
        else
            local outfit = {}
            if PlayerData.charinfo.gender == 0 then -- male model
                outfit = playerClothing.male
            else -- female model
                outfit = playerClothing.female
            end
            local jobClothes = {
                    ["outfitData"] = {
                        -- Don't modify this! configure your outfit in Config.Clothing at line 1
                        ["arms"]      = { item = outfit["arms"], texture = 0 },
                        ["t-shirt"]   = { item = outfit["tshirt_1"], texture = outfit["tshirt_2"] },
                        ["torso2"]    = { item = outfit["torso_1"], texture = outfit["torso_2"] },
                        ["decals"]    = { item = outfit["decals_1"], texture = outfit["decals_2"] },
                        ["pants"]     = { item = outfit["pants_1"], texture = outfit["pants_2"] },
                        ["shoes"]     = { item = outfit["shoes_1"], texture = outfit["shoes_2"] },
                        ["hat"]       = { item = outfit["helmet_1"], texture = outfit["helmet_2"] },
                        ["accessory"] = { item = outfit["chain_1"], texture = outfit["chain_2"] },
                        ["ear"]       = { item = outfit["ears_1"], texture = outfit["ears_2"] },
                        ["mask"]      = { item = outfit["mask_1"], texture = outfit["mask_2"] },
                        ["bag"]       = { item = outfit["bags_1"], texture = outfit["bags_2"] },
                        ["glass"]     = { item = outfit["glasses_1"], texture = outfit["glasses_2"] },
                        ["vest"]      = { item = outfit["vest_1"], texture = outfit["vest_2"] },
                        ["watch"]     = { item = outfit["watches_1"], texture = outfit["watches_2"] },
                        ["bracelet"]  = { item = outfit["bracelets_1"], texture = outfit["bracelets_2"] },
                    }
                }
            TriggerEvent("qb-clothing:client:loadOutfit", jobClothes)
            Player.onDuty = true
        end
    end
end