## SB SUBMARINE HEIST - HEIST SCRIPT ##

JUST DRAG AND DROP THIS RESOURCE IN YOUR RESOURCE FOLDER AND ADJUST WITH YOUR CONFIGS


## Note : This script uses Asset Escrow System

## Editable  : Config ,  Partly Editable Client and sever side files are given

## SU<PERSON><PERSON><PERSON>E CONTROL ##

## 1 . USE YOUR ARROW KEYS TO CONTOL SUBMARINE FOR FORWARD/BACKWARD AND LEFT AND RIGHT
## 2 . Q - GO UP 
## 3 . LCTRL - GO DOWN

## DEPENDENCIES ##

1.Interact sound - https://github.com/plunkettscott/interact-sound
2.Howdy-hackminigame - https://github.com/HiHowdy/howdy-hackminigame
3.Mx_fixwiring - https://github.com/mxlolshop/minigameFixWiring
4.subheistmap - included in this heist package itself - Check your cfx panel and download this
5.controlroom - included in this heist package itself - Check your cfx panel and download this
6.Es_extended(or)qb-core(or)custom
7.Onesync and Onesync Infinity


## INSTALLING THE DEPENDENCIES ##

## 1.INTERACT SOUND ##
1.Download the resource from - https://github.com/plunkettscott/interact-sound
2.Go to sb_sub_heist>sounds - You will see a set of sounds file copy that 
3.Go to interact-sound>client>html>sounds - paste the copied files here
4.Make sure you add all the sounds in interact-sound>fxmanifest also

## 2.HOWDY-HACKMINIGAME ## 
1.Download the resource from -  https://github.com/HiHowdy/howdy-hackminigame
2.Go to sb_sub_heist>minigame - Copy the index file
3.Go to howdy-hackminigame>build - paste the copied files here

## 3.SUBMARINE HEIST MAP ##
1.You will receive the file in this package itself
2.Download the subheistmap and controlroom from mail or cfx
3.Start the maps in your cfg

## NOW START THE RESOURCES ##
1.ensure interact_sound
2.ensure howdy-hackminigame
3.ensure mx_fixwiring
4.ensure subheistmap
5.ensure controlroom
6.ensure sb_sub_heist

## INSTALLATION ##
1. ensure interact_sound
2. ensure howdy-hackminigame
3. ensure mx_fixwiring
4. ensure subheistmap
5. ensure controlroom
6. ensure sb_sub_heist
7. Add the Heist items in your inventory

## IMPORTANT INFORMATION ##
1. If you want to reset or clear the heist, you can use /smcancel - only for admins.
   
###COMMON ISSUES###

If you get an error - Problem in starting resources

Make sure the folder name is "sb_sub_heist" 
   
##FEATURES##

1. ESX/QB compatible: If you use any custom framework, you can integrate it with the script.
2. OneSync and OneSync Infinity support (tested on OneSync Infinity).
3. Fully optimized.
4. You can do the heist with your friends.
5. Notifications can be customized.
6. Reward money can be adjusted in the config.
7. This Script has a character system, there are four characters: the Droner, the Controller, the Bomber, and the Diver. Each character has its own unique set of tasks to complete.
8. Remote Control Submarine
9. Built-in cinematics.
10. Prevented exploits and glitches.
11. Reset command available for admins.
12. Discord logs can be configured in the settings.
13. Police alert and police count can be adjusted in the config.

## HEIST ITEMS ##

subcard
detonator
drone_sh
c4
oxy_mask_sh

Add the above items in your server

