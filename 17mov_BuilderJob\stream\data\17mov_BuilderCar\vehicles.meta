<?xml version="1.0" encoding="UTF-8"?> 
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />

  <InitDatas>
    <Item>
      <modelName>17mov_BuilderCar</modelName>
      <txdName>17mov_BuilderCar</txdName>
      <handlingId>sandking</handlingId>
      <gameName>17mov_BuilderCar</gameName>
      <vehicleMakeName>17mov</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>bison</audioNameHash>
      <layout>LAYOUT_VAN_ARMORED</layout>
      <coverBoundOffsets>INSURGENT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.060000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.155000" y="0.295000" z="0.533000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.175000" z="0.465000" />
	  <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.136000" y="0.126000" z="0.475000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.136000" y="0.126000" z="0.475000" />
			<SeatIndex value="3" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="0.000000" y="-0.135000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.045000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.236300" />
      <wheelScaleRear value="0.236300" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        150.000000
        500.000000
        500.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.832" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="50" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_HAS_INTERIOR_EXTRAS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_UTILITY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
	 <Item>
          <driverName>S_M_Y_Sheriff_01</driverName>
          <npcName />
        </Item>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_ORACLE2_FRONT_LEFT</Item>
        <Item>STD_ORACLE2_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
 </InitDatas>
 <txdRelationships>
     <Item>
      <parent>vehicles_sultan_interior</parent>
      <child>police3</child>
    </Item>
  </txdRelationships>
  </CVehicleModelInfo__InitDataList>