<?xml version="1.0" encoding="UTF-8"?> 
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />

  <InitDatas>
    <Item>
      <modelName>17mov_ElectricianCar</modelName>
      <txdName>17mov_ElectricianCar</txdName>
      <handlingId>speedo</handlingId>
      <gameName>17mov_ElectricianCar</gameName>
      <vehicleMakeName>17mov</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>bison</audioNameHash>
      <layout>LAYOUT_VAN_SPEEDO4</layout>
      <coverBoundOffsets>SPEEDO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_SPEEDO4_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_SPEEDO4</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.120000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="-0.020000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.080000" y="-0.110000" z="-0.085000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.080000" y="-0.110000" z="-0.085000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.040000" y="0.000000" z="-0.040000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.040000" y="0.000000" z="-0.040000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.070000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.030000" y="-0.120000" z="-0.060000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="-0.070000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.110000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.125000" y="0.230000" z="0.445000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.225000" y="0.268000" z="0.438000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.281000" y="0.491000" z="0.586000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.156000" y="0.533000" z="0.586000" />
			<SeatIndex value="3" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.115000" z="0.575000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.050000" z="0.080000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.245000" />
      <wheelScaleRear value="0.245000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.100000" />
      <damageOffsetScale value="0.100000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        40.000000
        500.000000
        500.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.881" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="999" />
      <flags>FLAG_SMALL_WORKER FLAG_IS_VAN FLAG_DELIVERY FLAG_EXTRAS_ALL FLAG_IS_BULKY FLAG_TURRET_MODS_ON_ROOF FLAG_CARGOBOB_HOOK_UP_CHASSIS FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING FLAG_KEEP_ALL_TURRETS_SYNCHRONISED FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_INCREASED_RAMMING_FORCE_WITH_CHASSIS_MOD</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_SPEEDO</dashboardType>
      <vehicleClass>VC_VAN</vehicleClass>
      <wheelType>VWT_MUSCLE</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
		<Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName />
        </Item>
		<Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName />
        </Item>
		<Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_SPEEDO4_FRONT_LEFT</Item>
        <Item>VAN_SPEEDO4_FRONT_RIGHT</Item>
        <Item>VAN_PONY_REAR_LEFT</Item>
        <Item>VAN_PONY_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
 </InitDatas>
 <txdRelationships>
    <Item>
      <parent>vehicles_speedo_interior</parent>
      <child>speedo</child>
    </Item>
  </txdRelationships>
  </CVehicleModelInfo__InitDataList>