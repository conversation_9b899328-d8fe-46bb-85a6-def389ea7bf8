<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_575_1554)">
<path d="M5 6.83192L9.51539 4.57422V9.08961L5 6.83192Z" fill="url(#paint0_linear_575_1554)"/>
</g>
<defs>
<filter id="filter0_d_575_1554" x="0.986316" y="0.560535" width="12.543" height="12.543" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.00684"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_575_1554"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_575_1554" result="shape"/>
</filter>
<linearGradient id="paint0_linear_575_1554" x1="9.51539" y1="9.08961" x2="4.49829" y2="9.08961" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#9B9B9B"/>
</linearGradient>
</defs>
</svg>
