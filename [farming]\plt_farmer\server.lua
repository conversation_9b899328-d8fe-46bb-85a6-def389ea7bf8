PLT = plt_farmer
Info = {}
Objeler = {}

ESX = exports["es_extended"]:getSharedObject()

RegisterNetEvent("plt_farmer:MissionComplate")
AddEventHandler("plt_farmer:MissionComplate", function(money)
  local src = source
  pltEntityDelete(src)
  local xPlayer = ESX.GetPlayerFromId(src)

  if PLT.PaymentMethodWithBank  then 
    xPlayer.addAccountMoney('bank', money)
  else
    xPlayer.addMoney(money)
  end

  local data = {
    ['Player'] = src,
    ['Log'] = 'Farming Job', 
    ['Title'] = 'Farming Job Payments',
    ['Message'] = GetPlayerName(src)..' Earned '..money, 
    ['Color'] = 'green', 
}
TriggerEvent('Melx_Core:ZLog', data,"https://discord.com/api/webhooks/1236977346054062123/ji0KEbps12EhiWC1GIm3wmovY88hxDBju9VUGU1ReWOjtM0B_cnVRTamDgnuZWCkS-hM")

  TriggerClientEvent('plt_farmer:SendNotify', src, "error",money..PLT.U["won"],9000)
end) 

