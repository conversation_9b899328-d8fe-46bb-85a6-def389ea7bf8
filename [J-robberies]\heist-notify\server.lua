-- Heist Management System - Server Side
-- Integrates with existing heist cooldown systems

local ESX = exports['es_extended']:getSharedObject()

-- Initialize heist status from config
local HeistStatus = {}

-- Load heist configurations dynamically
Citizen.CreateThread(function()
    for heistType, heistConfig in pairs(Config.HMS.Heists) do
        if heistConfig.enabled then
            HeistStatus[heistType] = {
                name = heistConfig.name,
                status = "ready",
                location = heistConfig.location,
                ongoing = false,
                requiredCops = heistConfig.requiredCops,
                cooldownMinutes = heistConfig.cooldownMinutes,
                rewardMin = heistConfig.rewardMin,
                rewardMax = heistConfig.rewardMax,
                riskLevel = heistConfig.riskLevel,
                experience = heistConfig.experience,
                coords = heistConfig.coords,
                description = heistConfig.description,
                requiredItems = heistConfig.requiredItems,
                instructions = heistConfig.instructions,
                rewardType = heistConfig.rewardType or 'cash',
                rewardItems = heistConfig.rewardItems
            }
        end
    end
    print("^2[HMS]^7 Loaded " .. GetTableLength(HeistStatus) .. " heist configurations")
end)

-- Helper function to get table length
function GetTableLength(t)
    local count = 0
    for _ in pairs(t) do count = count + 1 end
    return count
end

-- Function to get item label from ox_inventory
function GetItemLabel(itemName)
    if not itemName then return 'Unknown Item' end

    -- Try to get from ox_inventory exports
    local success, itemData = pcall(function()
        return exports.ox_inventory:Items(itemName)
    end)

    if success and itemData and itemData.label then
        return itemData.label
    end

    -- Fallback to known item mappings if ox_inventory is not available
    local itemMappings = {
        ['hack_usb'] = 'Trojan USB',
        ['drill'] = 'Drill',
        ['hacker_device'] = 'Hacker Device',
        ['gold'] = 'Gold Bar',
        ['diamond'] = 'Diamond',
        ['inside-coke'] = 'Cocaine Package',
        ['inside-weed'] = 'Weed Package',
        ['inside-meth'] = 'Meth Package',
        ['bag'] = 'Duffel Bag',
        ['coke_pooch'] = 'Cocaine Pouch',
        ['weed_pooch'] = 'Weed Pouch',
        ['pogo'] = 'Golden Pogo Stick',
        ['bottle'] = 'Vintage Bottle',
        ['necklace'] = 'Diamond Necklace'
    }

    return itemMappings[itemName] or itemName
end

-- Function to format required items with proper labels
function FormatRequiredItems(itemList)
    if not itemList or #itemList == 0 then
        return 'None'
    end

    local formattedItems = {}
    for _, itemName in pairs(itemList) do
        table.insert(formattedItems, GetItemLabel(itemName))
    end

    return table.concat(formattedItems, ', ')
end

-- Store cooldown data received from heist resources
local HeistCooldowns = {}

-- Initialize cooldowns from config
Citizen.CreateThread(function()
    Citizen.Wait(100) -- Wait for HeistStatus to be loaded
    for heistType, heistInfo in pairs(HeistStatus) do
        HeistCooldowns[heistType] = {
            lastRobbed = 0,
            cooldownTime = math.floor(heistInfo.cooldownMinutes * 60) -- Convert minutes to seconds
        }
    end
    print("^2[HMS]^7 Initialized cooldowns for " .. GetTableLength(HeistCooldowns) .. " heists")
end)

-- Calculate cooldown status
function GetHeistCooldownStatus(heistType)
    local data = HeistCooldowns[heistType]
    if not data then return "ready", 0 end

    if data.lastRobbed > 0 then
        local timeLeft = data.cooldownTime - (os.time() - data.lastRobbed)
        if timeLeft > 0 then
            return "cooldown", timeLeft
        end
    end
    return "ready", 0
end

-- Update heist status
function UpdateHeistStatus(heistType, status)
    if HeistStatus[heistType] then
        HeistStatus[heistType].status = status
        HeistStatus[heistType].ongoing = (status == "ongoing")

        -- Broadcast update to all clients
        TriggerClientEvent('hms:updateStatus', -1, heistType, HeistStatus[heistType])
    end
end

-- Get current heist statuses
RegisterServerEvent('hms:requestStatus')
AddEventHandler('hms:requestStatus', function()
    local source = source

    -- Get police count
    local policeCount = #ESX.GetExtendedPlayers('job', 'police')

    -- Get real-time status from each heist resource
    local statusData = {}

    for heistType, heistInfo in pairs(HeistStatus) do
        local status, cooldownRemaining = GetHeistCooldownStatus(heistType)

        -- Override with ongoing status if heist is active
        if heistInfo.ongoing then
            status = "ongoing"
            cooldownRemaining = 0
        -- Check if not enough police
        elseif policeCount < heistInfo.requiredCops and status == "ready" then
            status = "no_police"
            cooldownRemaining = 0
        end

        statusData[heistType] = {
            name = heistInfo.name,
            status = status,
            location = heistInfo.location,
            cooldownRemaining = cooldownRemaining,
            requiredCops = heistInfo.requiredCops,
            rewardMin = heistInfo.rewardMin,
            rewardMax = heistInfo.rewardMax,
            riskLevel = heistInfo.riskLevel,
            experience = heistInfo.experience
        }
    end

    -- Prepare config data for client
    local configData = {}
    for heistType, heistInfo in pairs(HeistStatus) do
        local heistConfig = Config.HMS.Heists[heistType]
        local rewardDisplay = ''

        if heistInfo.rewardType == 'items' and heistInfo.rewardItems then
            -- Format item rewards
            local itemStrings = {}
            for _, item in pairs(heistInfo.rewardItems) do
                table.insert(itemStrings, item.count .. ' ' .. item.name)
            end
            rewardDisplay = table.concat(itemStrings, ', ')
        else
            -- Format cash rewards
            rewardDisplay = '$' .. heistInfo.rewardMin .. ' - $' .. heistInfo.rewardMax
        end

        configData[heistType] = {
            name = heistInfo.name,
            icon = heistConfig and heistConfig.icon or '🎯',
            location = heistInfo.location,
            reward = rewardDisplay,
            requiredCops = heistInfo.requiredCops,
            cooldown = heistInfo.cooldownMinutes .. ' minutes',
            items = FormatRequiredItems(heistInfo.requiredItems),
            riskLevel = heistInfo.riskLevel,
            experience = heistInfo.experience,
            instructions = heistInfo.instructions,
            coords = heistInfo.coords,
            rewardType = heistInfo.rewardType or 'cash',
            rewardItems = heistInfo.rewardItems
        }
    end

    -- Send both formats for compatibility
    TriggerClientEvent('hms:receiveStatus', source, {
        heists = statusData,
        policeCount = policeCount,
        configs = configData
    })
end)

-- Events from heist resources to update status
RegisterServerEvent('hms:heistStarted')
AddEventHandler('hms:heistStarted', function(heistType)
    UpdateHeistStatus(heistType, "ongoing")
end)

RegisterServerEvent('hms:heistEnded')
AddEventHandler('hms:heistEnded', function(heistType)
    UpdateHeistStatus(heistType, "ready")
    -- Update cooldown data
    if HeistCooldowns[heistType] then
        HeistCooldowns[heistType].lastRobbed = os.time()
    end
end)

RegisterServerEvent('hms:heistFailed')
AddEventHandler('hms:heistFailed', function(heistType)
    UpdateHeistStatus(heistType, "ready")
    -- Update cooldown data for failed heists too
    if HeistCooldowns[heistType] then
        HeistCooldowns[heistType].lastRobbed = os.time()
    end
end)

-- Event for heist resources to update their cooldown data
RegisterServerEvent('hms:updateCooldown')
AddEventHandler('hms:updateCooldown', function(heistType, lastRobbed, cooldownTime)
    if HeistCooldowns[heistType] then
        HeistCooldowns[heistType].lastRobbed = lastRobbed or os.time()
        if cooldownTime then
            HeistCooldowns[heistType].cooldownTime = cooldownTime
        end
    end
end)

-- Event to show heist success notification with actual rewards
RegisterServerEvent('hms:showHeistSuccess')
AddEventHandler('hms:showHeistSuccess', function(heistType, actualRewards, playerSource)
    local targetPlayer = playerSource or source
    local heistConfig = Config.HMS.Heists[heistType]

    if heistConfig then
        local successNotification = {
            missionTextLabel = heistConfig.name .. " Complete",
            passFailTextLabel = "~g~SUCCESS",
            messageLabel = "Heist completed successfully!"
        }

        -- Format actual rewards for display
        local rewardTable = {}
        if actualRewards then
            for _, reward in pairs(actualRewards) do
                table.insert(rewardTable, {
                    stat = reward.label or reward.stat,
                    value = reward.value
                })
            end
        end

        TriggerClientEvent('heist-notify:show', targetPlayer, successNotification, rewardTable, 7, true)
    end
end)

-- Event to show heist failure notification
RegisterServerEvent('hms:showHeistFailed')
AddEventHandler('hms:showHeistFailed', function(heistType, reason, playerSource)
    local targetPlayer = playerSource or source
    local heistConfig = Config.HMS.Heists[heistType]

    if heistConfig then
        local failureNotification = {
            missionTextLabel = heistConfig.name .. " Failed",
            passFailTextLabel = "~r~FAILED",
            messageLabel = reason or "Heist failed!"
        }

        TriggerClientEvent('heist-notify:show', targetPlayer, failureNotification, nil, 5, true)
    end
end)

-- Export functions for other resources
exports('updateHeistStatus', UpdateHeistStatus)
exports('getHeistStatus', function(heistType)
    return HeistStatus[heistType]
end)
exports('getAllHeistStatus', function()
    return HeistStatus
end)

-- Export function to show success notification with actual rewards
exports('showHeistSuccess', function(heistType, actualRewards, playerSource)
    TriggerEvent('hms:showHeistSuccess', heistType, actualRewards, playerSource)
end)

-- Export function to show failure notification
exports('showHeistFailed', function(heistType, reason, playerSource)
    TriggerEvent('hms:showHeistFailed', heistType, reason, playerSource)
end)
