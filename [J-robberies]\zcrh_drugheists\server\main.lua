local ESX = exports['es_extended']:getSharedObject()
local rob = false
local robbers = {}
local lastRobbed = 0

RegisterServerEvent('zcrh_drugheists:tooFar')
AddEventHandler('zcrh_drugheists:tooFar', function(currentStore)
	local source = source
	rob = false
	for _, xPlayer in pairs(ESX.GetExtendedPlayers('job', 'police')) do
		TriggerClientEvent('zcrh_drugheists:killBlip', xPlayer.source)
	end
	if robbers[source] then
		TriggerClientEvent('zcrh_drugheists:tooFar', source)
		ESX.ClearTimeout(robbers[source])
        robbers[source] = nil
		TriggerClientEvent('esx:showNotification', source, _U('robbery_cancelled_at', Stores[currentStore].nameOfStore))
		-- Notify HMS that heist failed
		TriggerEvent('hms:heistFailed', 'drugheist')
	end
end)

RegisterServerEvent('zcrh_drugheists:robberyStarted')
AddEventHandler('zcrh_drugheists:robberyStarted', function(currentStore)
	local source  = source
	local xPlayer  = ESX.GetPlayerFromId(source)
	if Stores[currentStore] then
		local store = Stores[currentStore]
		if (os.time() - lastRobbed) < Config.TimerBeforeNewRob and lastRobbed ~= 0 then
			TriggerClientEvent('esx:showNotification', source, _U('recently_robbed', Config.TimerBeforeNewRob - (os.time() - lastRobbed)))
			return
		end
		if not rob then
			local xPlayers = ESX.GetExtendedPlayers('job', 'police')
			if #xPlayers >= Config.PoliceNumberRequired then
				rob = true
				for _, xPlayer in pairs(xPlayers) do
					TriggerClientEvent('zcrh_drugheists:setBlip', xPlayer.source, Stores[currentStore].position)
				end
				TriggerClientEvent('esx:showNotification', source, _U('started_to_rob', store.nameOfStore))
				-- Notify HMS that heist started
				TriggerEvent('hms:heistStarted', 'drugheist')
				TriggerClientEvent('zcrh_drugheists:currentlyRobbing', source, currentStore)
				TriggerClientEvent('zcrh_drugheists:startTimer', source)
				lastRobbed = os.time()
				robbers[source] = ESX.SetTimeout(store.secondsRemaining * 1000, function()
					rob = false
					-- Clear robber entry
					robbers[source] = nil
                    if xPlayer then
                        local rewardAmount = math.random(160,200)
                        xPlayer.addInventoryItem(store.reward, rewardAmount)

                        -- Show heist success with actual rewards
                        if Config.HeistNotifications.Success.Use then
                            local rewards = {
                                {
                                    stat = "Cocaine Collected",
                                    value = "~g~" .. rewardAmount .. "x"
                                }
                            }
                            TriggerClientEvent('heist-notify:show', source, Config.HeistNotifications.Success, rewards, 7, true)
                        end

                        -- Reset client state to stop DrawTxt and prevent false failure
                        TriggerClientEvent('zcrh_drugheists:robberyComplete', source, store.reward, rewardAmount)

                        local xPlayers = ESX.GetExtendedPlayers('job', 'police')
                        for _, xPlayer in pairs(xPlayers) do
                            TriggerClientEvent('zcrh_drugheists:killBlip', xPlayer.source)
                        end
                        -- Notify HMS that heist ended
                        TriggerEvent('hms:heistEnded', 'drugheist')
                        -- Update HMS with actual cooldown data
                        TriggerEvent('hms:updateCooldown', 'drugheist', os.time(), Config.TimerBeforeNewRob)
                    end
				end)
			else
				TriggerClientEvent('esx:showNotification', source, _U('min_police', Config.PoliceNumberRequired))
			end
		else
			TriggerClientEvent('esx:showNotification', source, _U('robbery_already'))
		end
	end
end)

-- Handle player disconnection during robbery
AddEventHandler('playerDropped', function()
	local source = source
	if robbers[source] then
		rob = false
		ESX.ClearTimeout(robbers[source])
		robbers[source] = nil

		-- Clean up police blips when player leaves
		for _, xPlayer in pairs(ESX.GetExtendedPlayers('job', 'police')) do
			TriggerClientEvent('zcrh_drugheists:killBlip', xPlayer.source)
		end

		-- Notify HMS that heist failed due to player disconnect
		TriggerEvent('hms:heistFailed', 'drugheist')
	end
end)
