local ESX = exports['es_extended']:getSharedObject()
local rob = false
local robbers = {}
local playerRewards = {} -- Track actual rewards per player

RegisterServerEvent('zcrh_vangelico:toofar')
AddEventHandler('zcrh_vangelico:toofar', function(robb)
	local source = source
	local xPlayers = ESX.GetExtendedPlayers('job', 'police')
	rob = false
	for i=1, #xPlayers, 1 do
		TriggerClientEvent('esx:showNotification', xPlayers[i].source, _U('robbery_cancelled_at') .. Stores[robb].nameofstore)
		TriggerClientEvent('zcrh_vangelico:killblip', xPlayers[i].source)
	end
	if(robbers[source])then
		TriggerClientEvent('zcrh_vangelico:toofarlocal', source)
		robbers[source] = nil
		playerRewards[source] = nil -- Clear rewards data
		TriggerClientEvent('esx:showNotification', source, _U('robbery_has_cancelled') .. Stores[robb].nameofstore)
	end
end)

RegisterServerEvent('zcrh_vangelico:endrob')
AddEventHandler('zcrh_vangelico:endrob', function(robb)
	local source = source
	local xPlayers = ESX.GetExtendedPlayers('job', 'police')
	rob = false
	for i=1, #xPlayers, 1 do
		TriggerClientEvent('zcrh_vangelico:killblip', xPlayers[i].source)
	end
	if(robbers[source])then
		TriggerClientEvent('zcrh_vangelico:robberycomplete', source)

		-- Show heist completion with actual rewards
		if playerRewards[source] and Config.HeistNotifications.Success.Use then
			local rewards = {
				{
					stat = "Jewels Collected",
					value = "~g~" .. playerRewards[source].totalJewels .. "x"
				},
				{
					stat = "Windows Broken",
					value = "~g~" .. playerRewards[source].windowsBroken .. "x"
				}
			}
			TriggerClientEvent('heist-notify:show', source, Config.HeistNotifications.Success, rewards, 7, true)
			playerRewards[source] = nil -- Clear rewards data
		end

		robbers[source] = nil
		TriggerClientEvent('esx:showNotification', source, _U('robbery_has_ended') .. ' : ' .. Stores[robb].nameofstore)
		-- Notify HMS that heist ended
		TriggerEvent('hms:heistEnded', 'vangelico')
		-- Update HMS with actual cooldown data
		TriggerEvent('hms:updateCooldown', 'vangelico', Stores[robb].lastrobbed, 2100)
	end
end)

RegisterServerEvent('zcrh_vangelico:rob')
AddEventHandler('zcrh_vangelico:rob', function(robb)
	local source = source

	if Stores[robb] then

		local store = Stores[robb]

		if (os.time() - store.lastrobbed) < Config.SecBetwNextRob and store.lastrobbed ~= 0 then

            TriggerClientEvent('zcrh_vangelico:togliblip', source)
			TriggerClientEvent('esx:showNotification', source, _U('already_robbed') .. (Config.SecBetwNextRob - (os.time() - store.lastrobbed)) .. _U('seconds'))
			return
		end

		if rob == false then

			rob = true
			local policeList = ESX.GetExtendedPlayers('job', 'police')
			for i=1, #policeList, 1 do
				TriggerClientEvent('zcrh_vangelico:setblip', policeList[i].source, Stores[robb].position)
			end

			-- cd_dispatch notification
			TriggerEvent('cd_dispatch:AddNotification', {
				job_table = {'police'},
				coords = vector3(-631.7767, -230.1540, 38.0570),
				title = '10-99 | JEWELRY HEIST',
				message = 'Suspects are robbing the Vangelico jewelry store',
				flash = 1,
				unique_id = tostring(math.random(0000000,9999999)),
				blip = {
					sprite = 617,
					scale = 1.2,
					colour = 1,
					flashes = true,
					text = '911 - Jewelry Heist',
					time = (8*60*1000),
					sound = 1,
				}
			})

			TriggerClientEvent('esx:showNotification', source, _U('started_to_rob') .. store.nameofstore .. _U('do_not_move'))
			TriggerClientEvent('zcrh_vangelico:currentlyrobbing', source, robb)
			robbers[source] = robb
			-- Notify HMS that heist started
			TriggerEvent('hms:heistStarted', 'vangelico')
            CancelEvent()
			Stores[robb].lastrobbed = os.time()
		else
			TriggerClientEvent('esx:showNotification', source, _U('robbery_already'))
		end
	end
end)

RegisterServerEvent('zcrh_vangelico:collectJewels')
AddEventHandler('zcrh_vangelico:collectJewels', function()
	local source = source
	local xPlayer = ESX.GetPlayerFromId(source)
	local jewelAmount = math.random(Config.MinJewels, Config.MaxJewels)

	xPlayer.addInventoryItem('jewels', jewelAmount)

	-- Track actual rewards
	if not playerRewards[source] then
		playerRewards[source] = {
			totalJewels = 0,
			windowsBroken = 0
		}
	end

	playerRewards[source].totalJewels = playerRewards[source].totalJewels + jewelAmount
	playerRewards[source].windowsBroken = playerRewards[source].windowsBroken + 1
end)

ESX.RegisterServerCallback('zcrh_vangelico:getPoliceCount', function(_, cb)
	local copsOnline = #ESX.GetExtendedPlayers('job', 'police')
	cb(copsOnline)
end)

