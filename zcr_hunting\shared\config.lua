lib.locale()

Config = {}
Config.Debug = false
Config.Target = "ox_target"               -- only supporting ox_target and qb-target | nil to disable targeting
Config.SpawnDelay = 30             -- seconds [how much time it should take between spawning animals]
Config.DeleteEntityRadius = 300.0 -- will delete animal if your 400 meters away from them

Config.TrackerItem = "animal_tracker"
Config.TrackingDuration = 60      -- seconds
Config.DelayBetweenTracks = 120   -- seconds
Config.TrackingFailureChance = 20 -- [1 - 100]

Config.BaitItem = "huntingbait"
Config.BaitAttractionDistance = 100.0 -- in 200 radius it will atract an animal
Config.BaitTimeLimit = 2              -- minutes

Config.ImagesPath = "nui://zcr_hunting/_icons/"


-- _____                           __  _
-- / ____|                         / _| (_)
-- | |      __ _  _ __ ___   _ __  | |_  _  _ __  ___
-- | |     / _` || '_ ` _ \ | '_ \ |  _|| || '__|/ _ \
-- | |____| (_| || | | | | || |_) || |  | || |  |  __/
-- \_____|\__,_||_| |_| |_|| .__/ |_|  |_||_|   \___|
--                         | |
--                         |_|

Config.Campfire = {
    enable = true,
    campfireItem = "campfire",
    items = {
        {
            label = "Cooked meat",
            give = "cooked_meat",
            cookTime = 5, -- seconds
            require = {
                {
                    label = "Raw Meat",
                    quantity = 1,
                    item = "raw_meat",
                },
            }
        },
    }
}

-- _    _                _    _                  ______
-- | |  | |              | |  (_)                |___  /
-- | |__| | _   _  _ __  | |_  _  _ __    __ _      / /  ___   _ __    ___  ___
-- |  __  || | | || '_ \ | __|| || '_ \  / _` |    / /  / _ \ | '_ \  / _ \/ __|
-- | |  | || |_| || | | || |_ | || | | || (_| |   / /__| (_) || | | ||  __/\__ \
-- |_|  |_| \__,_||_| |_| \__||_||_| |_| \__, |  /_____|\___/ |_| |_| \___||___/
--                                        __/ |
--                                       |___/

Config.HuntingZones = {
    ["CHILIAD_MOUNTAINS"] = {
        coords = vec3(1125.88, 4622.2, 80.08),
        radius = 200.0,
        maxSpawns = 5,                                                  -- max animals spawned at one time
        allowedWeapons = { "WEAPON_M700", "WEAPON_KNIFE" }, -- nil if you want to allow every weapon
        blip = {
            enable = true,
            color = 44,
            opacity = 128,
        },
        animals = {
            {
                model = "a_c_deer",
                chance = 80, -- chance of spawning
                harvestTime = 5,
                harvestWeapons = { "WEAPON_KNIFE" },
                blip = {
                    enable = true,
                    name = 'Deer',
                    type = 141,
                    scale = 0.6,
                    color = 47,
                },
                marker = {
                    enable = true,
                    color = { r = 250, g = 164, b = 134, a = 32 }
                },
                items = {
                    skins = {
                        {
                            item = "skin_deer_ruined",
                            chance = 70,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_deer_low",
                            chance = 50,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_deer_medium",
                            chance = 40,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_deer_good",
                            chance = 30,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_deer_perfect",
                            chance = 10,
                            maxQuantity = math.random(1,3),
                        },
                    },
                    meat = {
                        {
                            item = "raw_meat",
                            chance = 0,
                            maxQuantity = 10,
                        },
                        -- {
                        --     item = "raw_meat",
                        --     chance = 0,
                        --     maxQuantity = 10,
                        -- },
                    },
                    extra = { -- rare items
                        {
                            item = "deer_horn",
                            chance = 5,
                            maxQuantity = math.random(1,3),
                        },
                        -- {
                        --     item = "deer_horn",
                        --     chance = 40,
                        --     maxQuantity = math.random(1,3),
                        -- },
                    }

                }
            },
            {
                model = "a_c_coyote",
                chance = 80, -- chance of spawning
                harvestTime = 5,
                harvestWeapons = { "WEAPON_KNIFE" },
                blip = {
                    enable = true,
                    name = 'Coyote',
                    type = 141,
                    scale = 0.6,
                    color = 36,
                },
                marker = {
                    enable = true,
                    color = { r = 255, g = 246, b = 77, a = 199 }
                },
                items = {
                    skins = {
                        {
                            item = "skin_coyote_ruined",
                            chance = 70,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_coyote_low",
                            chance = 50,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_coyote_medium",
                            chance = 40,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_coyote_good",
                            chance = 30,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_coyote_perfect",
                            chance = 10,
                            maxQuantity = math.random(1,3),
                        },
                    },
                    meat = {
                        {
                            item = "raw_meat",
                            chance = 0,
                            maxQuantity = 10,
                        },
                    },
                    extra = { -- rare items
                        {
                            item = "coyote_fur",
                            chance = 5,
                            maxQuantity = math.random(1,3),
                        },
                    }

                }
            },
            {
                model = "a_c_boar",
                chance = 80, -- chance of spawning
                harvestTime = 5,
                harvestWeapons = { "WEAPON_KNIFE" },
                blip = {
                    enable = true,
                    name = 'Boar',
                    type = 141,
                    scale = 0.6,
                    color = 21,
                },
                marker = {
                    enable = true,
                    color = { r = 51, g = 37, b = 31, a = 150 }
                },
                items = {
                    skins = {
                        {
                            item = "skin_boar_ruined",
                            chance = 70,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_boar_low",
                            chance = 50,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_boar_medium",
                            chance = 40,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_boar_good",
                            chance = 30,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_boar_perfect",
                            chance = 10,
                            maxQuantity = math.random(1,3),
                        },
                    },
                    meat = {
                        {
                            item = "raw_meat",
                            chance = 0,
                            maxQuantity = 10,
                        },
                    },
                    extra = { -- rare items
                        {
                            item = "boar_tusk",
                            chance = 5,
                            maxQuantity = math.random(1,3),
                        },
                    }
            
                }
            },
        }
    },
    ["CHILIAD_MOUNTAINS2"] = {
        coords = vec3(-727.2025, 4917.1626, 208.3831),
        radius = 160.0,
        maxSpawns = 5,                                                  -- max animals spawned at one time
        allowedWeapons = { "WEAPON_M700", "WEAPON_KNIFE" }, -- nil if you want to allow every weapon
        blip = {
            enable = true,
            color = 44,
            opacity = 128,
        },
        animals = {
            {
                model = "a_c_deer",
                chance = 80, -- chance of spawning
                harvestTime = 5,
                harvestWeapons = { "WEAPON_KNIFE" },
                blip = {
                    enable = true,
                    name = 'Deer',
                    type = 141,
                    scale = 0.6,
                    color = 47,
                },
                marker = {
                    enable = true,
                    color = { r = 250, g = 164, b = 134, a = 32 }
                },
                items = {
                    skins = {
                        {
                            item = "skin_deer_ruined",
                            chance = 70,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_deer_low",
                            chance = 50,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_deer_medium",
                            chance = 40,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_deer_good",
                            chance = 30,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_deer_perfect",
                            chance = 10,
                            maxQuantity = math.random(1,3),
                        },
                    },
                    meat = {
                        {
                            item = "raw_meat",
                            chance = 0,
                            maxQuantity = 10,
                        },
                        -- {
                        --     item = "raw_meat",
                        --     chance = 0,
                        --     maxQuantity = 10,
                        -- },
                    },
                    extra = { -- rare items
                        {
                            item = "deer_horn",
                            chance = 5,
                            maxQuantity = math.random(1,3),
                        },
                        -- {
                        --     item = "deer_horn",
                        --     chance = 40,
                        --     maxQuantity = math.random(1,3),
                        -- },
                    }

                }
            },
            {
                model = "a_c_coyote",
                chance = 80, -- chance of spawning
                harvestTime = 5,
                harvestWeapons = { "WEAPON_KNIFE" },
                blip = {
                    enable = true,
                    name = 'Coyote',
                    type = 141,
                    scale = 0.6,
                    color = 36,
                },
                marker = {
                    enable = true,
                    color = { r = 255, g = 246, b = 77, a = 199 }
                },
                items = {
                    skins = {
                        {
                            item = "skin_coyote_ruined",
                            chance = 70,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_coyote_low",
                            chance = 50,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_coyote_medium",
                            chance = 40,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_coyote_good",
                            chance = 30,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_coyote_perfect",
                            chance = 10,
                            maxQuantity = math.random(1,3),
                        },
                    },
                    meat = {
                        {
                            item = "raw_meat",
                            chance = 0,
                            maxQuantity = 10,
                        },
                    },
                    extra = { -- rare items
                        {
                            item = "coyote_fur",
                            chance = 5,
                            maxQuantity = math.random(1,3),
                        },
                    }

                }
            },
            {
                model = "a_c_boar",
                chance = 80, -- chance of spawning
                harvestTime = 5,
                harvestWeapons = { "WEAPON_KNIFE" },
                blip = {
                    enable = true,
                    name = 'Boar',
                    type = 141,
                    scale = 0.6,
                    color = 21,
                },
                marker = {
                    enable = true,
                    color = { r = 51, g = 37, b = 31, a = 150 }
                },
                items = {
                    skins = {
                        {
                            item = "skin_boar_ruined",
                            chance = 70,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_boar_low",
                            chance = 50,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_boar_medium",
                            chance = 40,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_boar_good",
                            chance = 30,
                            maxQuantity = math.random(1,3),
                        },
                        {
                            item = "skin_boar_perfect",
                            chance = 10,
                            maxQuantity = math.random(1,3),
                        },
                    },
                    meat = {
                        {
                            item = "raw_meat",
                            chance = 0,
                            maxQuantity = 10,
                        },
                    },
                    extra = { -- rare items
                        {
                            item = "boar_tusk",
                            chance = 5,
                            maxQuantity = math.random(1,3),
                        },
                    }
            
                }
            },
        }
    },
}
