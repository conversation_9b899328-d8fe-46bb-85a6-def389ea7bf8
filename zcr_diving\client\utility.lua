jobData = {
    jobname = nil,
    job_grade_name = nil,
    job_grade = nil,
    job_label = nil
}

RegisterNetEvent("esx:playerLoaded")
AddEventHandler("esx:playerLoaded", function()
    Wait(1000)
    TriggerServerEvent('zcr_diving:loadData')
end)

RegisterNetEvent("QBCore:Client:OnPlayerLoaded")
AddEventHandler("QBCore:Client:OnPlayerLoaded", function()
    Wait(1000)
    TriggerServerEvent('zcr_diving:loadData')
end)

CreateThread(function()
    Core, Config.Framework = GetCore()
    spawnPed()
    createBlips()
    SetPlayerJob()
end)
AddEventHandler('onResourceStop', function(resource)
    if (GetCurrentServerEndpoint() == nil) then
        return
    end
    if (resource == GetCurrentResourceName()) then
        TriggerServerEvent('zcr_diving:loadData')
        ClearPedTasks(PlayerPedId())
    end
end)

function SetPlayerJob()
    while Core == nil do
        Wait(0)
    end
    Wait(500)
    while not nuiLoaded do
        Wait(50)
    end
    WaitPlayer()

    if Config.Framework == 'esx' or Config.Framework == 'oldesx' then
        local PlayerData = Core.GetPlayerData()
        jobData.jobname = PlayerData.job.name
        jobData.job_grade_name = PlayerData.job.label
        jobData.job_grade = tonumber(PlayerData.job.grade)
    else
        local PlayerData = Core.Functions.GetPlayerData()
        jobData.jobname = PlayerData["job"].name
        jobData.job_grade_name = PlayerData["job"].label
        jobData.job_grade = PlayerData["job"].grade.level
    end
end

function WaitPlayer()
    if Config.Framework == "esx" or Config.Framework == 'oldesx' then
        while Core == nil do
            Wait(0)
        end
        while Core.GetPlayerData() == nil do
            Wait(0)
        end
        while Core.GetPlayerData().job == nil do
            Wait(0)
        end
    else
        while Core == nil do
            Wait(0)
        end
        while Core.Functions.GetPlayerData() == nil do
            Wait(0)
        end
        while Core.Functions.GetPlayerData().metadata == nil do
            Wait(0)
        end
    end
end

RegisterNetEvent("esx:setJob")
AddEventHandler("esx:setJob", function(job)
    Wait(1000)
    SetPlayerJob()
end)

RegisterNetEvent("QBCore:Client:OnJobUpdate")
AddEventHandler("QBCore:Client:OnJobUpdate", function(data)
    Wait(1000)
    SetPlayerJob()
end)


local blips = {}

function createBlips()
    if Config.Diving['blip']['show'] then
        blips = AddBlipForCoord(tonumber(Config.Diving['coords'].intreactionCoords.x),
        tonumber(Config.Diving['coords'].intreactionCoords.y),
        tonumber(Config.Diving['coords'].intreactionCoords.z))
        SetBlipSprite(blips, Config.Diving['blip'].blipType)
        SetBlipDisplay(blips, 4)
        SetBlipScale(blips, Config.Diving['blip'].blipScale)
        SetBlipColour(blips, Config.Diving['blip'].blipColor)
        SetBlipAsShortRange(blips, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(tostring(Config.Diving['blip'].blipName))
        EndTextCommandSetBlipName(blips)
    end
end

function canOpen()
    local ped = PlayerPedId()
    if IsPedInAnyVehicle(ped, false) then
        return false
    end
    if Config.Diving['job'] then
        if Config.Diving['job'] ~= 'all' and Config.Diving['job'] ~= jobData.jobname then
                sendNotification(Config.NotificationText['wrongjob'].text, Config.NotificationText['wrongjob'].type)
            return false
        end
    end
    return true
end

function spawnPed()
    if Config.Diving.coords.ped then
        WaitForModel(Config.Diving.coords.pedHash)
        local createNpc = CreatePed("PED_TYPE_PROSTITUTE",
            Config.Diving.coords.pedHash,
            Config.Diving.coords.pedCoords.x,
            Config.Diving.coords.pedCoords.y,
            Config.Diving.coords.pedCoords.z - 0.98,
            Config.Diving.coords.pedHeading, false,
            false)
        FreezeEntityPosition(createNpc, true)
        SetEntityInvincible(createNpc, true)
        SetBlockingOfNonTemporaryEvents(createNpc, true)
    end
end



function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    SetTextScale(0.3, 0.3)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 90)
end

function SetBlipAttributes(blip, id)
    SetBlipSprite(blip, 1)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 0.8)
    SetBlipAsShortRange(blip, true)
    SetBlipColour(blip, 26)
    ShowNumberOnBlip(blip, id)
    SetBlipShowCone(blip, false)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentSubstringPlayerName("Diving : " .. id)
    EndTextCommandSetBlipName(blip)
end

RegisterNetEvent('zcr_diving:openMenu', function()
    if canOpen() then
        openDivingMenu()
    end
end)

RegisterNetEvent('zcr_diving:openSellMenu', function()
    openSellMenu()
end)

function WaitForModel(model)
    if not IsModelValid(model) then
        return
    end

    if not HasModelLoaded(model) then
        RequestModel(model)
    end

    while not HasModelLoaded(model) do
        Citizen.Wait(0)
    end
end

function GetVehicles()
    return GetGamePool('CVehicle')
end

function GetVehiclesInArea(coords, maxDistance)
    return EnumerateEntitiesWithinDistance(GetVehicles(), false, coords, maxDistance)
end

function EnumerateEntitiesWithinDistance(entities, isPlayerEntities, coords, maxDistance)
    local nearbyEntities = {}

    if coords then
        coords = vector3(coords.x, coords.y, coords.z)
    else
        local playerPed = PlayerPedId()
        coords = GetEntityCoords(playerPed)
    end
    for k, entity in pairs(entities) do
        local distance = #(coords - GetEntityCoords(entity))

        if distance <= maxDistance then
            nearbyEntities[#nearbyEntities + 1] = isPlayerEntities and k or entity
        end
    end
    return nearbyEntities
end

Citizen.CreateThread(function()
    Config.OpenTrigger = function()
        if Config.InteractionHandler == "qb-target" then
            exports['qb-target']:RemoveZone('twdiving' .. 1)
            exports['qb-target']:RemoveZone('twdiving' .. 2)
            exports['qb-target']:AddBoxZone("twdiving" .. 1,
                vector3(Config.Diving.coords.intreactionCoords.x,
                    Config.Diving.coords.intreactionCoords.y,
                    Config.Diving.coords.intreactionCoords.z), 1.5,
                1.5,
                {
                    name = "twdiving" .. 1,
                    debugPoly = false,
                    heading = -20,
                    minZ = Config.Diving.coords.intreactionCoords.z - 2,
                    maxZ = Config.Diving.coords.intreactionCoords.z + 2,
                }, {
                    options = {
                        {
                            type = "client",
                            event = "zcr_diving:openMenu",
                            icon = 'fas fa-credit-card',
                            label = "Open Diving Menu",

                        },
                    },
                    distance = 2
                })
                exports['qb-target']:AddBoxZone("twdiving" .. 2,
                vector3(Config.Diving.seelCoords.intreactionCoords.x,
                    Config.Diving.seelCoords.intreactionCoords.y,
                    Config.Diving.seelCoords.intreactionCoords.z), 1.5,
                1.5,
                {
                    name = "twdiving" .. 2,
                    debugPoly = false,
                    heading = -20,
                    minZ = Config.Diving.seelCoords.intreactionCoords.z - 2,
                    maxZ = Config.Diving.seelCoords.intreactionCoords.z + 2,
                }, {
                    options = {
                        {
                            type = "client",
                            event = "zcr_diving:openSellMenu",
                            icon = 'fas fa-credit-card',
                            label = "Open Sell Menu",

                        },
                    },
                    distance = 2
                })
        elseif Config.InteractionHandler == "ox-target" then
            exports.ox_target:removeZone('twdiving' .. 1)
            exports.ox_target:removeZone('twdiving' .. 2)

            exports['ox_target']:addBoxZone({
                coords = vector3(Config.Diving.coords.intreactionCoords.x,
                    Config.Diving.coords.intreactionCoords.y,
                    Config.Diving.coords.intreactionCoords.z),
                minZ = Config.Diving.coords.intreactionCoords.z - 2,
                maxZ = Config.Diving.coords.intreactionCoords.z + 2,
                heading = -20,
                name = "twdiving" .. 1,
                id = "twdiving" .. 1,
                options = {
                    {
                        type = "client",
                        event = "zcr_diving:openMenu",
                        icon = 'fas fa-credit-card',
                        label = "Open Diving Menu",
                    },
                },
                distance = 2
            })

            -- exports['ox_target']:addBoxZone({
            --     coords = vector3(Config.Diving.seelCoords.intreactionCoords.x,
            --         Config.Diving.seelCoords.intreactionCoords.y,
            --         Config.Diving.seelCoords.intreactionCoords.z),
            --     minZ = Config.Diving.seelCoords.intreactionCoords.z - 2,
            --     maxZ = Config.Diving.seelCoords.intreactionCoords.z + 2,
            --     heading = -20,
            --     name = "twdiving" .. 2,
            --     id = "twdiving" .. 2,
            --     options = {
            --         {
            --             type = "client",
            --             event = "zcr_diving:openSellMenu",
            --             icon = 'fas fa-credit-card',
            --             label = "Open Sell Menu",
            --         },
            --     },
            --     distance = 2
            -- })
        elseif Config.InteractionHandler == "drawtext" then
            Citizen.CreateThread(function()
                while true do
                    local wait = 1500
                    local playerPed = PlayerPedId()
                    local coords = GetEntityCoords(playerPed)
                    local distance = #(coords - Config.Diving.coords.intreactionCoords)
                    if distance < 1.5 then
                        wait = 0
                        DrawText3D(Config.Diving.coords.intreactionCoords.x,
                        Config.Diving.coords.intreactionCoords.y,
                        Config.Diving.coords.intreactionCoords.z + 1.0,
                        "Press [~g~E~s~] to Diving Menu")
                        if IsControlJustReleased(0, 38) then
                            if canOpen() then
                                openDivingMenu()
                            end
                        end
                    end

                    local distance2 = #(coords - Config.Diving.seelCoords.intreactionCoords)
                    if distance2 < 1.5 then
                        wait = 0
                        DrawText3D(Config.Diving.seelCoords.intreactionCoords.x,
                        Config.Diving.seelCoords.intreactionCoords.y,
                        Config.Diving.seelCoords.intreactionCoords.z + 1.0,
                        "Press [~g~E~s~] to Sell Coral")
                        if IsControlJustReleased(0, 38) then
                            if canOpen() then
                                openSellMenu()
                            end
                        end
                    end

                    Citizen.Wait(wait)
                end
            end)
        end
    end
end)

function TriggerCallback(name, data)
    local incomingData = false
    local status = 'UNKOWN'
    local counter = 0
    while Core == nil do
        Wait(0)
    end
    if Config.Framework == 'esx' then
        Core.TriggerServerCallback(name, function(payload)
            status = 'SUCCESS'
            incomingData = payload
        end, data)
    else
        Core.Functions.TriggerCallback(name, function(payload)
            status = 'SUCCESS'
            incomingData = payload
        end, data)
    end
    CreateThread(function()
        while incomingData == 'UNKOWN' do
            Wait(1000)
            if counter == 4 then
                status = 'FAILED'
                incomingData = false
                break
            end
            counter = counter + 1
        end
    end)

    while status == 'UNKOWN' do
        Wait(0)
    end
    return incomingData
end

local lastCoords = {}

GetRandomCoordInCircle = function(coord, radiusNumber, count)
    local coordstable = {}
    local minDistance = 2.0 -- En az 2.0 mesafe
    for i = 1, tonumber(count) do
        local radius = tonumber(radiusNumber) or 75.0
        local coords = vector3(coord.x, coord.y, coord.z + 0.8) 
        local x, y, z = GenerateRandomCoords(coords, radius, minDistance, i, coordstable)
        if x ~= nil and y ~= nil and z ~= nil then
            table.insert(coordstable, vector3(x, y, z))
        end
    end
    lastCoords = coordstable
    return coordstable
end

function GenerateRandomCoords(coords, radius, minDistance, attempt, coordstable)
    local x, y, z
    repeat
        x = coords.x + math.random(-radius, radius)
        y = coords.y + math.random(-radius, radius)
        z = FindZForCoords(x, y)
    until not IsTooCloseToLastCoords(x, y, minDistance)
    return x, y, z
end

function IsTooCloseToLastCoords(x, y, minDistance)
    for _, existingCoord in ipairs(lastCoords) do
        local distance = GetDistanceBetweenCoords(existingCoord.x, existingCoord.y, x, y)
        if distance < minDistance then
            return true
        end
    end
    return false
end

function FindZForCoords(x, y)
    local found = true
    local START_Z = 1500.0
    local z = START_Z
    while found and z > 0 do
        local _found, _z = GetGroundZAndNormalFor_3dCoord(x + 0.0, y + 0.0, z - 1.0)
        if _found then
            z = _z + 0.0
        end
        found = _found
        Wait(0)
    end
    return z + 0.0
end

function GetDistanceBetweenCoords(x1, y1, x2, y2)
    return math.sqrt((x2 - x1)^2 + (y2 - y1)^2)
end


-- GetRandomCoordInCircle = function(coord, radiusNumber, count)
--     local coordstable = {}
--     for i = 1, tonumber(count) do
--         local radius = tonumber(radiusNumber) or 75.0
--         local coords = vector3(coord.x, coord.y, coord.z + 0.8) 
--         local x = coords.x + math.random(-radius, radius)
--         local y = coords.y + math.random(-radius, radius)
--         local z = FindZForCoords(x, y)
--         table.insert(coordstable, vector3(x, y, z))
--     end
--     return coordstable
-- end

-- function FindZForCoords(x, y)
--     local found = true
--     local START_Z = 1500.0
--     local z = START_Z
--     while found and z > 0 do
--         local _found, _z = GetGroundZAndNormalFor_3dCoord(x + 0.0, y + 0.0, z - 1.0)
--         if _found then
--             z = _z + 0.0
--         end
--         found = _found
--         Wait(0)
--     end
--     return z + 0.0
-- end


function ChangeObjectAnim(dataName)
    local playerPed = PlayerPedId()
    if dataName == 'coral' then
        LoadAnimation('weapons@first_person@aim_rng@generic@projectile@thermal_charge@')
        TaskPlayAnim(playerPed, 'weapons@first_person@aim_rng@generic@projectile@thermal_charge@', 'plant_floor', 8.0, -8.0, -1, 2, 0, false, false, false)
        local coords = GetEntityCoords(playerPed)
        FreezeEntityPosition(playerPed, true)
        SetTimeout(2800, function()
            ClearPedTasksImmediately(playerPed)
            FreezeEntityPosition(playerPed, false)
        end)
    elseif dataName == 'suitCase' then
        LoadAnimation('amb@world_human_gardener_plant@female@base')
        TaskPlayAnim(playerPed, 'amb@world_human_gardener_plant@female@base', 'base_female', 8.0, -8.0, -1, 2, 0, false, false, false)
        local coords = GetEntityCoords(playerPed)
        FreezeEntityPosition(playerPed, true)
        SetTimeout(2800, function()
            ClearPedTasksImmediately(playerPed)
            FreezeEntityPosition(playerPed, false)
        end)
    elseif dataName == 'trash' then
        LoadAnimation('amb@world_human_gardener_plant@female@base')
        TaskPlayAnim(playerPed, 'amb@world_human_gardener_plant@female@base', 'base_female', 8.0, -8.0, -1, 2, 0, false, false, false)
        local coords = GetEntityCoords(playerPed)
        FreezeEntityPosition(playerPed, true)
        SetTimeout(2800, function()
            ClearPedTasksImmediately(playerPed)
            FreezeEntityPosition(playerPed, false)
        end)
    elseif dataName == 'box' then
        LoadAnimation('timetable@maid@ig_2@')
        TaskPlayAnim(playerPed, 'timetable@maid@ig_2@', 'ig_2_base', 8.0, -8.0, -1, 2, 0, false, false, false)
        local coords = GetEntityCoords(playerPed)
        FreezeEntityPosition(playerPed, true)
        SetTimeout(2800, function()
            ClearPedTasksImmediately(playerPed)
            FreezeEntityPosition(playerPed, false)
        end)
    else
        LoadAnimation('anim@mp_radio@garage@medium')
        TaskPlayAnim(playerPed, 'anim@mp_radio@garage@medium', 'idle_a', 8.0, -8.0, -1, 2, 0, false, false, false)
        local coords = GetEntityCoords(playerPed)
        trimmer = CreateObject(GetHashKey('prop_hedge_trimmer_01'), coords.x, coords.y, coords.z,  true,  true, true)
        AttachEntityToEntity(trimmer, playerPed, GetPedBoneIndex(playerPed, 57005), 0.09, 0.02, 0.01, -121.0, 181.0, 187.0, true, true, false, true, 1, true)
        SetEntityHeading(trimmer, GetEntityHeading(playerPed))
        FreezeEntityPosition(playerPed, true)
        SetTimeout(2800, function()
            DetachEntity(trimmer, 1, false)
            DeleteObject(trimmer)
            ClearPedTasksImmediately(playerPed)
            
            FreezeEntityPosition(playerPed, false)
        end)
    end
end

function LoadAnimation(dict)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do Wait(10) end
end

function GetVehicles()
    return GetGamePool('CVehicle')
end

function GetVehiclesInArea(coords, maxDistance)
    return EnumerateEntitiesWithinDistance(GetVehicles(), false, coords, maxDistance)
end

function EnumerateEntitiesWithinDistance(entities, isPlayerEntities, coords, maxDistance)
    local nearbyEntities = {}

    if coords then
        coords = vector3(coords.x, coords.y, coords.z)
    else
        local playerPed = PlayerPedId()
        coords = GetEntityCoords(playerPed)
    end
    for k, entity in pairs(entities) do
        local distance = #(coords - GetEntityCoords(entity))

        if distance <= maxDistance then
            nearbyEntities[#nearbyEntities + 1] = isPlayerEntities and k or entity
        end
    end
    return nearbyEntities
end