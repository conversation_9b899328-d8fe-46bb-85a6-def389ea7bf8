-- Initialize ESX
local ESX = exports['es_extended']:getSharedObject()

-- Variables
CanRobbery = true
CurrentRobber = nil
local playerRewards = {} -- Track actual rewards per player

function SendNotify(Number, source)
    if source ~= nil and source ~= 0 then
        TriggerClientEvent('client-truck:notification', source, Number)
    end
end

-- Function to get police count properly
function GetPoliceCount()
    local policeCount = 0
    for _, jobName in pairs(Config.CopsJobs) do
        local players = ESX.GetExtendedPlayers('job', jobName)
        policeCount = policeCount + #players
        print('^3[TRUCK HEIST]^7 Found ' .. #players .. ' players with job: ' .. jobName)
    end
    print('^3[TRUCK HEIST]^7 Total police count: ' .. policeCount .. ' | Required: ' .. Config.RequiredCopsCount)
    return policeCount
end

-- Register server callback
ESX.RegisterServerCallback("zcrh_truck:server:PossibleRobbery", function(source, cb)
    Citizen.Wait(1)

    local policeOnline = GetPoliceCount()

    if policeOnline >= Config.RequiredCopsCount then
        if CanRobbery then
            cb(true)
        else
            if CurrentRobber ~= nil then
                SendNotify(4, source)
            elseif CurrentRobber == nil then
                SendNotify(5, source)
            end
            cb(false)
        end
    else
        SendNotify(6, source)
        cb(false)
    end
end)

RegisterServerEvent('zcrh_truck:server:sync')
AddEventHandler('zcrh_truck:server:sync', function(type, playerleft)
    if type == 'START ROBBERY' then
        CanRobbery = false
        CurrentRobber = source
        -- Notify HMS that truck robbery started
        TriggerEvent('hms:heistStarted', 'truckrobbery')

        if source ~= nil and source ~= 0 then
            DiscordWebhook('robberyProcess', '**'.. PlayerNameFunction(source)..' ['.. source ..'] **'.. Config.Webhooks.Locale['HasStarted'] ..'\n**'.. Config.Webhooks.Locale['Identifier'] ..':** '.. GetIdentifierFunction(source) ..'')
        end

    elseif type == 'RESET ROBBERY' then
        -- Show heist completion with actual rewards if player completed it
        if CurrentRobber and playerRewards[CurrentRobber] and playerleft == nil and Config.SuccessRobbery.Use then
            local rewards = {}

            -- Format collected items
            for itemLabel, count in pairs(playerRewards[CurrentRobber].totalItems) do
                table.insert(rewards, {
                    stat = itemLabel,
                    value = "~g~" .. count .. "x"
                })
            end

            if #rewards > 0 then
                TriggerClientEvent('heist-notify:show', CurrentRobber, Config.SuccessRobbery, rewards, 7, true)
            end

            playerRewards[CurrentRobber] = nil -- Clear rewards data
        end

        CurrentRobber = nil
        -- Notify HMS that truck robbery ended
        TriggerEvent('hms:heistEnded', 'truckrobbery')
        TriggerEvent('hms:updateCooldown', 'truckrobbery', os.time(), Config.Cooldown * 60) -- Convert minutes to seconds

        if source ~= nil and source ~= 0 then
            if playerleft == nil then
                DiscordWebhook('robberyFinished', '**'.. PlayerNameFunction(source)..' ['.. source ..'] **'.. Config.Webhooks.Locale['HasFinished'] ..'\n**'.. Config.Webhooks.Locale['Identifier'] ..':** '.. GetIdentifierFunction(source) ..'')
            else
                DiscordWebhook('robberyFinished', Config.Webhooks.Locale['RobberHasQuit'])
            end
        end

        TriggerClientEvent('zcrh_truck:client:sync', -1, type)

        Citizen.CreateThread(function()
            Citizen.Wait(1000*60*Config.Cooldown)
            CanRobbery = true
        end)
    end
end)

RegisterServerEvent('zcrh_truck:server:AddItem')
AddEventHandler('zcrh_truck:server:AddItem', function()
    local random = math.random(1, #Config.TruckRewardItems)
    local randomCount = math.random(Config.TruckRewardItems[random].count[1], Config.TruckRewardItems[random].count[2])

    AddItem(source, Config.TruckRewardItems[random].item, randomCount)

    -- Track actual rewards
    if not playerRewards[source] then
        playerRewards[source] = {
            totalItems = {},
            itemDetails = ""
        }
    end

    local itemLabel = Config.TruckRewardItems[random].label
    if not playerRewards[source].totalItems[itemLabel] then
        playerRewards[source].totalItems[itemLabel] = 0
    end
    playerRewards[source].totalItems[itemLabel] = playerRewards[source].totalItems[itemLabel] + randomCount

    TriggerClientEvent('zcrh_truck:client:sync', CurrentRobber, 'RECEIVED ITEMS', {label = Config.TruckRewardItems[random].label, count = randomCount})
end)

RegisterServerEvent('zcrh_truck:server:SellItems')
AddEventHandler('zcrh_truck:server:SellItems', function()
    local Text = ''
    local AllGotMoney = 0
    for k, v in pairs(Config.TruckRewardItems) do
        local itemCount = GetItemCount(source, v.item)
        if itemCount > 0 then
            if Text == '' then
                Text = itemCount..'x '..v.label
            else
                Text = Text..', '..itemCount..'x '..v.label
            end

            RemoveItem(source, v.item, itemCount)
            AllGotMoney += itemCount*v.sellPrice
        end
    end
    if AllGotMoney > 0 then
        AddMoneyFunction(source, AllGotMoney)
        notification(source, Config.Notify[12][1], Config.Notify[12][2]..' '..Text..', '.. Config.Notify[13][2] ..' '.. AllGotMoney ..' '..Config.MoneyForm, Config.Notify[12][3], Config.Notify[12][4])
    else
        SendNotify(11, source)
    end
end)

-----------------------------------------------------------
----------------------| player drop |----------------------
-----------------------------------------------------------

AddEventHandler('playerDropped', function()
	if CurrentRobber == source then
        TriggerEvent('zcrh_truck:server:sync', 'RESET ROBBERY', true)
    end
end)