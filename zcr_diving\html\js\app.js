let audioPlayer = null;
const store = Vuex.createStore({
  components: {},

  state: {
  },
  getters: {},
  mutations: {},
  actions: {},
  searchInput: "",
});

const app = Vue.createApp({
  components: {},
  data: () => ({
    mainShow: false,
    tubeShow: false,
    tubeModel : 1,
    tubeLevel: false,
    notifyShow : false,
    tubeOxygen: 0,
    currentScreen: "jobScreen", // jobScreen, marketScreen, sellScreen
    marketSearch: "",
    sellMarketSearch: "",
    progressShow: false,
    progressbar: 0,
    progressbarLabel: "",
    interval: null,
    playerShow: false,
    finishShow: false,
    scoreBoxShow: false,
    requestMenuShow: false,
    serverName: "TWORST",
    serverMoneyType: "$",
    reqestHostName: "",
    hostIdentifier: "",
    progressData: false,
    Locales: {},
    invitePlayer: {
      1: false,
      2: false,
      3: false,
      4: false,
    },
    finishjobData: false,
    inviteModal: "",
    playerData: {
      name: "<PERSON><PERSON>",
      bank: 56000,
      identifier: "KUR10469",
      level: 1,
      owner: true,
      image:
        "https://cdn.discordapp.com/attachments/1096751382435987477/1216107608088383558/Rectangle_8.png?ex=65ff2f77&is=65ecba77&hm=2ee0757dc12ec3bec16ecfa4c6ccd92a0da34530409d0964bbb6b863d5c1ccc3&",
    },
    notifications: [
 

    ],
    playerList: [
      {
        level: 1,
        owner: true,
        name: "Tworst Shop",
        identifier: "KUR10469",
        src: 1,
        image:
          "https://cdn.discordapp.com/attachments/1096751382435987477/1216815442664886394/Rectangle_8.png?ex=6601c2b0&is=65ef4db0&hm=63be976059d626cd196eb859f1e41b4c8b08cff8d00873e4e2d0a91a27e0c7aa&",
      },
      {
        level: 2,
        owner: false,
        name: "Tworst Shop",
        identifier: "KUR104699",
        src: 2,
        image:
          "https://cdn.discordapp.com/attachments/1096751382435987477/1216815442664886394/Rectangle_8.png?ex=6601c2b0&is=65ef4db0&hm=63be976059d626cd196eb859f1e41b4c8b08cff8d00873e4e2d0a91a27e0c7aa&",
      },
    ],
    xpData: {
      requiredXp: 500,
      nextLevelXp: 1000,
      currentLevelXp: 500,
      level: 4,
    },
    marketData: [
      {
        itemName: "liftbag",
        itemLabel: "Diving Liftbag",
        itemKG: 0.1,
        itemCount: 1,
        itemPrice: 500,
        itemImage:
          "https://cdn.discordapp.com/attachments/1096751382435987477/1241160721149202482/pngegg_5_1.png?ex=664a81ff&is=6649307f&hm=a6cd3760db3c7da2fa82e8a70ebb1afcdae72f38aeeaac948f698c185db2fd16&",
      },
      {
        itemName: "liftbag",
        itemLabel: "Diving Liftbag2",
        itemKG: 0.1,
        itemCount: 1,
        itemPrice: 500,
        itemImage:
          "https://cdn.discordapp.com/attachments/1096751382435987477/1241160721149202482/pngegg_5_1.png?ex=664a81ff&is=6649307f&hm=a6cd3760db3c7da2fa82e8a70ebb1afcdae72f38aeeaac948f698c185db2fd16&",
      },
    ],
    sellMarketData: [
      {
        itemName: "liftbag",
        itemLabel: "Diving Liftbag",
        itemMaxValue: 5,
        itemPrice: 500,
        itemCount: 2,
        itemImage:
          "https://cdn.discordapp.com/attachments/1096751382435987477/1241160721149202482/pngegg_5_1.png?ex=664a81ff&is=6649307f&hm=a6cd3760db3c7da2fa82e8a70ebb1afcdae72f38aeeaac948f698c185db2fd16&",
      },
    ],
    missionData: [
      {
        regionName: "mission1",
        regionLabel: "Mission 1",
        regionInfo:
          "Lorem ipsum dolor sit amet consectetur. Quis mi nec nibh leo at scelerisque. Sagittis bibendum nunc volutpa.",
        location: "Los Santos",
        regionPNG: "region1.png",
        minLevel: 0,
        jobTask: [{ name: "Clear" }, { name: "Water" }],
        Awards: {
          money: 56000,
          xp: 100,
        },
      },
    ],

    selectJobData: false,
    jobStart : false,
  }),

  watch: {},

  beforeDestroy() {
    clearInterval(this.checkInterval); // Bileşen yok edilmeden önce intervali temizle
  },
  mounted() {
    window.addEventListener("keyup", this.keyHandler);
    window.addEventListener("message", this.eventHandler);
    
    this.checkInterval = setInterval(() => {
      if (this.notifications.length > 0) {
        this.notifyShow = true;

        let delays = []; // Bekleme sürelerini depolamak için bir dizi oluştur
        this.notifications.forEach((notification, index) => {
          let delay = setTimeout(() => {
            const indexToRemove = this.notifications.indexOf(notification);
            if (indexToRemove !== -1) {
              this.notifications.splice(indexToRemove, 1);
              if (this.notifications.length === 0) {
                this.notifyShow = false;
              }
            }
            clearTimeout(delays[indexToRemove]); // Belirli bir bildirimi kaldırınca ilgili bekleme süresini temizle
          }, 1500 * (index + 1));
          delays.push(delay); // Her bekleme süresini diziye ekle
        });
      }
    }, 2500); // Her 1 saniyede bir kontrol et
    
    
  },

  methods: {
    async ChangeScreen(screen) {
      //   if (this.currentScreen === screen) return;

      clicksound("click.wav");

      if (screen === "marketScreen") {
        this.marketSearch = "";
        try {
          let data = await postNUI("getMarketData");
          this.marketData = data;
        } catch (error) {
          console.error("Failed to fetch market data:", error);
        }
      } else if (screen === "sellScreen") {
        this.sellMarketSearch = "";
        try {
          let data = await postNUI("getSellMarketData");
          this.sellMarketData = data;
        } catch (error) {
          console.error("Failed to fetch sell market data:", error);
        }
      }

      this.currentScreen = screen;
    },
    BuyItems(value, index) {
      if (
        this.playerData.bank <
        this.marketData[index].itemPrice * this.marketData[index].itemCount
      ) {
        clicksound("errorclick.mp3");
        return;
      }
      this.playerData.bank -=
        this.marketData[index].itemPrice * this.marketData[index].itemCount;
      postNUI("buyItem", this.marketData[index]);
      this.marketData[index].itemCount = 1;
      clicksound("click.wav");
    },
    changeSellCount(type, value, index) {
      if (this.sellMarketData[index].itemCount == 0) return;

      if (type == "add") {
        if (
          this.sellMarketData[index].itemCount >=
          this.sellMarketData[index].itemMaxValue
        )
          return;
        this.sellMarketData[index].itemCount += 1;
      } else {
        if (this.sellMarketData[index].itemCount <= 1) return;
        this.sellMarketData[index].itemCount -= 1;
      }
    },
    changeMarketCount(type, value, index) {
      if (type == "add") {
        this.marketData[index].itemCount += 1;
      } else {
        if (this.marketData[index].itemCount <= 1) return;
        this.marketData[index].itemCount -= 1;
      }
    },
    sellItem(value, index) {
      if (this.sellMarketData[index].itemCount == 0) return;
      postNUI("sellItem", this.sellMarketData[index]);
      this.playerData.bank +=
        this.sellMarketData[index].itemPrice *
        this.sellMarketData[index].itemCount;
      let NewCount =
        this.sellMarketData[index].itemMaxValue -
        this.sellMarketData[index].itemCount;
      this.sellMarketData[index].itemCount = NewCount;
      this.sellMarketData[index].itemMaxValue = NewCount;
    },
    sellAllItem(value, index) {
      if (this.sellMarketData[index].itemCount == 0) return;
      postNUI("sellItem", this.sellMarketData[index]);
      this.playerData.bank +=
        this.sellMarketData[index].itemPrice *
        this.sellMarketData[index].itemCount;
      this.sellMarketData[index].itemCount = 0;
      this.sellMarketData[index].itemMaxValue = 0;
    },
    startProgress(label, time) {
      this.progressbarLabel = label;
      this.progressbar = 0;
      clearInterval(this.interval);

      const updateInterval = 100;
      const iterations = (time * 1000) / updateInterval;

      let iterationCount = 0;
      this.interval = setInterval(() => {
        iterationCount++;
        this.progressbar += 100 / iterations;

        if (iterationCount >= iterations) {
          clearInterval(this.interval);
          setTimeout(() => {
            stopsound();
            this.progressbar = 0;
            this.progressbarLabel = "";
          }, 1000);
        }
      }, updateInterval);
    },
    firePlayer(identifier, key) {
      if (identifier == null) {
        this.invitePlayer[key] = true;
        return;
      }
      if (identifier == this.playerData.identifier) {
        return;
      }

      const index = this.playerList.findIndex(
        (player) => player.identifier === identifier
      );
      const owner = this.playerList.findIndex(
        (player) => player.owner === true
      );
      if (
        this.playerList[index].identifier === this.playerList[owner].identifier
      ) {
        return;
      }
      if (index !== -1) {
        if (this.playerList[owner].owner) {
          clicksound("click.wav");
          postNUI("firePlayer", {
            lobbyID: this.playerList[owner].identifier,
            identifier: identifier,
            targetID: this.playerList[index].src,
          });
        }
      }
    },
    InvitePlayer(key) {
      if (this.inviteModal.length > 0) {
        postNUI("invitePlayer", { targetID: this.inviteModal, key: key });
        this.invitePlayer[key] = !this.invitePlayer[key];
        this.inviteModal = "";
        clicksound("click.wav");
        return;
      } else {
        this.invitePlayer[key] = !this.invitePlayer[key];
        clicksound("click.wav");
        return;
      }
    },
    declineInvite() {
      clicksound("errorclick.mp3");
      this.requestMenuShow = false;
      // postNUI("declineInvite", {identifier: this.playerData.identifier});
    },
    acceptInvite() {
      clicksound("click.wav");
      this.requestMenuShow = false;
      postNUI("acceptInvite", { hostIdentifier: this.hostIdentifier });
    },
    openInveteModel(identifier, key) {
      clicksound("click.wav");
      const index = this.playerList.findIndex(
        (player) => player.identifier === identifier
      );

      if (this.playerList[index].owner) {
        this.invitePlayer[key] = !this.invitePlayer[key];
      }
    },
    selectMission(data, keyy) {
      let identifier = this.playerData.identifier;
      if (identifier == null) {
        return;
      }

      const identifierData = this.playerList.findIndex(
        (player) => player.identifier === identifier
      );
      const owner = this.playerList.findIndex(
        (player) => player.owner === true
      );
      if (
        this.playerList[identifierData].identifier ===
        this.playerList[owner].identifier
      ) {
        if (data == null) return;
        if (
          this.selectJobData == "NOSELECT" ||
          this.selectJobData == false || this.selectJobData
        ) {
          if (data.regionName == this.selectJobData.regionName) {
            this.selectJobData = false;
            postNUI("selectMission", this.selectJobData);
            clicksound("click.wav");
            return;
          }
        }

        const index = this.missionData.findIndex(
          (mission) => mission.regionName === data.regionName
        );
        if (this.missionData[index].minLevel > this.playerData.level) {
          clicksound("errorclick.mp3");
          return;
        }

        this.selectJobData = this.missionData[index];
        
        postNUI("selectMission", this.selectJobData);
        clicksound("click.wav");
      }
    },
    formatJobRequeire(value) {
      if (value == null) return 0;
      if (value == 2 || value == 4) {
        return value - 1;
      } else {
        return value;
      }
    },
    formatNumber(number) {
      if (number == null) return 0;
      return number.toLocaleString("tr-TR");
    },
    startJob() {
      if (this.jobStart) {
        clicksound("click.wav");
        postNUI("resetJob");

    } else {
        if (this.selectJobData) {
            clicksound("click.wav");
            postNUI("startJob", this.selectJobData);
        }
    }

    },
    keyHandler(event) {
      if (event.keyCode == 27) {
        this.closeNUI();
      }
    },
    closeNUI() {
      this.mainShow = false;
      postNUI("closeNUI");
    },
    eventHandler(event) {
      switch (event.data.action) {
        case "CHECK_NUI":
          postNUI("checkNUI");
          break;
        case "OPEN_DIVING":
          this.playerData = event.data.payload;
          this.mainShow = true;
          this.ChangeScreen("jobScreen");
          break;
        case "OPEN_SELL_MENU":
          this.playerData = event.data.payload;
          this.ChangeScreen("sellScreen");
          this.mainShow = true;

        break;
        case "CLOSENUI":
          this.mainShow = false;
          break;
        case "START_JOB":
          this.mainShow = false;
          this.progressData = event.data.payload;
          this.scoreBoxShow = true;
          this.playerShow = true;
          this.jobStart = true;
          break;
        case "FINISH_JOB":
          this.progressData = false;
          this.scoreBoxShow = false;
          this.playerShow = false;
          this.selectJobData = false;
          this.finishShow = true;
          this.finishjobData = event.data.payload;
          this.tubeShow = false;
          this.tubeOxygen = 0;
          // 5 saniyte sonra bu ekranı kapat
          setTimeout(() => {
            this.finishShow = false;
            this.finishjobData = false;
          }, 5000);
          this.jobStart = false
          break;
        case "RESET_JOB":
          this.progressData = false;
          this.scoreBoxShow = false;
          this.playerShow = false;
          this.finishShow = false;
          this.finishjobData = false;
          this.jobStart = false;
          this.tubeShow = false;
          this.tubeOxygen = 0;
          if (this.mainShow) {
            this.mainShow = false;
          }
          break;
        case "REFRESH_JOBTASK":
          this.progressData = event.data.payload;
          break;
        case "showProgressBar":
          this.startProgress(event.data.payload.label, event.data.payload.time);
          break;
        case "SERVER_NAME":
          this.serverName = event.data.payload;
          break;
        case "SERVER_MONEY_TYPE":
          this.serverMoneyType = event.data.payload;
          break;
        case "SERVER_MISSIONS":
          this.missionData = event.data.payload;
          if (!this.missionData || !Array.isArray(this.missionData)) return;

          for (let i = 0; i < this.missionData.length; i++) {
            const entry = this.missionData[i];
            if (!entry.jobTask || !Array.isArray(entry.jobTask)) continue;

            for (let j = 0; j < entry.jobTask.length; j++) {
              const task = entry.jobTask[j];
              if (task.name === "seaClean") {
                task.singleAmount = task.amount
                  ? Math.floor(task.amount / 2)
                  : 0;
                task.amount = task.amount;
              } else if (task.name === "sealooting") {
                task.singleAmount = task.amount
                  ? Math.floor(task.amount)
                  : 0;
                task.amount = task.amount;
              } else if (task.name === "seaChanged") {
                task.singleAmount = task.amount
                  ? Math.floor(task.amount / 2)
                  : 0;
                task.amount = task.amount;
              } else if (task.name === "seaSuitCase") {
                task.singleAmount = task.amount
                  ? Math.floor(task.amount)
                  : 0;
                task.amount = task.amount;
              }
            }
          }
          break;
        case "LOAD_LOBBY":
          this.playerList = event.data.payload;
          break;
        case "INVITE_MENU":
          if (this.mainShow) {
            this.mainShow = false;
          }
          this.requestMenuShow = true;
          this.reqestHostName = event.data.payload.lobbyOwner;
          this.hostIdentifier = event.data.payload.identifier;
          this.playerData = event.data.payload.playerData;
          break;
        case "REFRESH_LOBBY":
          if (event.data.payload == "NOSELECT") {
            this.selectJobData = false;
          } else {
            this.selectJobData = event.data.payload;
          }
          break;
        case "LOCALES":
          this.Locales = event.data.payload;
          break;
        case "TUBE_UPDATE":
          this.tubeShow = true;
          // this.tubeModel = event.data.payload;
          this.tubeOxygen = event.data.payload;
          break;
        case "OXYGEN_LEVEL":
          this.tubeShow = true;
          // this.tubeModel = event.data.payload;
          if (event.data.payload == 0) {
            this.tubeOxygen = 0;
          }
          if (this.tubeShow) {
            this.tubeOxygen = event.data.payload.oxygen;
            this.tubeLevel = event.data.payload.level;
          }
          break;
        case "NOTIFICATION":
          this.notifications.push(event.data.payload);
          this.notifyShow = true;
          break;
        default:
          break;
      }
    },
  },
  computed: {
    tubeStyle() {
      switch (this.tubeModel) {
        case 1:
          return { height: '15vw' };
        case 2:
          return { height: '20vw' };
        case 3:
          return { height: '25vw' };
        default:
          return {}; // Varsayılan olarak boş bir nesne döndürür
    }
  },
  tubeHeight() {
    if (!this.tubeOxygen || this.tubeOxygen <= 0) {
      return '0%'; 
    }
    return `${this.tubeOxygen}%`;
  },
  filteredTasks() {
    if (!this.progressData || !this.progressData.JobTask) return [];
    if (this.progressData.JobTask.length === 0) return [];
    return this.progressData.JobTask.filter(task => task.name === 'seaClean' || task.name === 'seaChanged');
    },
    progressPercentage() {
      if (this.playerData.xp == null || this.playerData.nextxp == null)
        return 0;
      let percentage = (this.playerData.xp / this.playerData.nextxp) * 100;
      return percentage > 100 ? 100 : percentage;
    },
    computedStrokeDasharray() {
      const totalLength = 100; // stroke-dasharray toplam uzunluğu
      const dashLength = (this.progressPercentage / 100) * totalLength; // İlerlemenin uzunluğu
      const gapLength = totalLength - dashLength; // Boşluğun uzunluğu
      return `${dashLength}, ${gapLength}`;
    },
  },
});

app.use(store).mount("#app");
var resourceName = "zcr_diving";

if (window.GetParentResourceName) {
  resourceName = window.GetParentResourceName();
}

window.postNUI = async (name, data) => {
  try {
    const response = await fetch(`https://${resourceName}/${name}`, {
      method: "POST",
      mode: "cors",
      cache: "no-cache",
      credentials: "same-origin",
      headers: {
        "Content-Type": "application/json",
      },
      redirect: "follow",
      referrerPolicy: "no-referrer",
      body: JSON.stringify(data),
    });
    return !response.ok ? null : response.json();
  } catch (error) {
    // console.log(error)
  }
};

function clicksound(val) {
  let audioPath = `./sound/${val}`;
  audioPlayer = new Howl({
    src: [audioPath],
  });
  audioPlayer.volume(0.4);
  audioPlayer.play();
}

function stopsound() {
  if (audioPlayer) {
    audioPlayer.stop();
  }
}
