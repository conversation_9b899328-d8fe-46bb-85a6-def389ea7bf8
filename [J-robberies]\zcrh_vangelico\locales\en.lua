Locales['en'] = {

	['robbery_cancelled'] = 'the robbery has been cancelled!',
	['shop_robbery'] = 'Vangelico Jewellery',
	['press_to_rob'] = 'Shoot to start the robbery',
	['seconds_remaining'] = '~w~ seconds remaining',
	['robbery_cancelled_at'] = ' robbery has been cancelled at: ',
	['robbery_has_cancelled'] = ' the robbery has been cancelled: ',
	['already_robbed'] = 'the jewelry has already been robbed. Wait: ',
	['seconds'] = 'seconds.',
	['rob_in_prog'] = ' robbery in progress at: ',
	['started_to_rob'] = 'You started the robbery ',
	['do_not_move'] = ', take the jewels from the windows!',
	['alarm_triggered'] = 'the alarm has been triggered',
	['hold_pos'] = 'when you\'ve collected all the jewels, deal with the cops and escape!',
	['robbery_complete'] = ' The robbery has been successful! Handle the cops and escape! ',
	['robbery_complete_at'] = ' The robbery has been successful at: ',
	['min_two_police'] = 'there must be at least ',
	['min_two_police2'] = ' ~w~cops in town to rob.',
	['robbery_already'] = 'A robbery is already in progress.',
	['robbery_has_ended'] = 'Robbery finished',
	['end'] = 'The jewelry has been robbed!',

	['field'] = 'Press ~y~E~s~ to ~o~collect~s~ the jewels',
	['collectinprogress'] = '~r~Jewellery~s~ collection ~y~in progress~s~...',

	['press_to_collect'] = 'to collect ~y~jewels~s~',
	['smash_case'] = 'broken windows',
	['press_to_sell'] = 'Press ~INPUT_PICKUP~ to sell jewels',
	['need_bag'] = 'You need the bag! Go to the nearest clotheshop.'

}
