local Framework = exports['d3MBA-lib']:GetFrameworkObject()

RegisterNetEvent("d3MBA-butcher:server:CollectButcherKnife", function(ShelfCoords)
    if ShelfCoords == nil then BanPlayer(source, Config.KickReason) return end 

    local PlayerPed = GetPlayerPed(source) 
    local Distance = #(GetEntityCoords(PlayerPed) - vector3(ShelfCoords)) 

    if Distance <= 5.0 then 
        Framework.AddItem(source, Config.Items.Knife, 1)
    else
        BanPlayer(source, Config.KickReason)
    end
end)

