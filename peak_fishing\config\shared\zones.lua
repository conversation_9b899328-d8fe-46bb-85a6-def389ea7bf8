return {
    useZones = true,
    
    list = {
        zancudo_river = {
            label = 'Zancudo River',

            coords = vec3(-465.0, 2920.0, 12.0),
            radius = 80.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },

            fishTypes = {
                common = { 'bitterling', 'pale_chub', 'dace', 'carp', 'goldfish', 'killifish', 'crawfish', 'tadpole', 'frog', 'freshwater_goby', 'loach', 'bluegill', 'yellow_perch', 'black_bass', 'tilapia', 'pond_smelt', 'sweetfish' },
                uncommon = { 'koi', 'pop_eyed_goldfish', 'ranchu_goldfish', 'angelfish', 'betta', 'neon_tetra', 'rainbowfish' },
                rare = { 'giant_snakehead', 'golden_trout', 'stringfish', 'dorado', 'gar', 'arapaima', 'sturgeon', 'king_salmon' },
                epic = { },
                legendary = { },
                treasure = { 'old_boot', 'broken_bottle', 'silver_necklace', 'ancient_statue' }
            },
        },

        chilliad = {
            label = 'Chilliad Sea',

            coords = vec3(-1612.0079, 5262.9692, 3.9741),
            radius = 80.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },

            fishTypes = {
                common = { 'bitterling', 'pale_chub', 'dace', 'carp', 'goldfish', 'killifish', 'crawfish', 'tadpole', 'frog', 'freshwater_goby', 'loach', 'bluegill', 'yellow_perch', 'black_bass', 'tilapia', 'pond_smelt', 'sweetfish' },
                uncommon = { 'koi', 'pop_eyed_goldfish', 'ranchu_goldfish', 'angelfish', 'betta', 'neon_tetra', 'rainbowfish' },
                rare = { 'giant_snakehead', 'golden_trout', 'stringfish', 'dorado', 'gar', 'arapaima', 'sturgeon', 'king_salmon' },
                epic = { },
                legendary = { },
                treasure = { 'old_boot', 'broken_bottle', 'silver_necklace', 'ancient_statue' }
            },
        },

        tongvavalley = {
            label = 'Tongva Valley',

            coords = vec3(3867.1526, 4463.7666, 2.7249),
            radius = 90.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },

            fishTypes = {
                common = { 'bitterling', 'pale_chub', 'dace', 'carp', 'goldfish', 'killifish', 'crawfish', 'tadpole', 'frog', 'freshwater_goby', 'loach', 'bluegill', 'yellow_perch', 'black_bass', 'tilapia', 'pond_smelt', 'sweetfish' },
                uncommon = { 'koi', 'pop_eyed_goldfish', 'ranchu_goldfish', 'angelfish', 'betta', 'neon_tetra', 'rainbowfish' },
                rare = { 'giant_snakehead', 'golden_trout', 'stringfish', 'dorado', 'gar', 'arapaima', 'sturgeon', 'king_salmon' },
                epic = { },
                legendary = { },
                treasure = { 'old_boot', 'broken_bottle', 'silver_necklace', 'ancient_statue' }
            },
        },

        vinewood_hills_lake = {
            label = 'Vinewood Hills Lake',

            coords = vec3(21.0, 875.0, 195.0),
            radius = 90.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },

            fishTypes = {
                common = { 'bitterling', 'pale_chub', 'dace', 'carp', 'goldfish', 'killifish', 'crawfish', 'tadpole', 'frog', 'freshwater_goby', 'loach', 'bluegill', 'yellow_perch', 'black_bass', 'tilapia', 'pond_smelt', 'sweetfish' },
                uncommon = { 'koi', 'pop_eyed_goldfish', 'ranchu_goldfish', 'angelfish', 'betta', 'neon_tetra', 'rainbowfish' },
                rare = { 'giant_snakehead', 'golden_trout', 'stringfish', 'dorado', 'gar', 'arapaima', 'sturgeon', 'king_salmon' },
                epic = { },
                legendary = { },
                treasure = { 'old_boot', 'broken_bottle', 'silver_necklace', 'ancient_statue' }
            },
        },

        lighthouse_lake = {
            label = 'Light House',

            coords = vec3(3373.0571, 5183.5874, 1.4602),
            radius = 90.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },

            fishTypes = {
                common = { 'bitterling', 'pale_chub', 'dace', 'carp', 'goldfish', 'killifish', 'crawfish', 'tadpole', 'frog', 'freshwater_goby', 'loach', 'bluegill', 'yellow_perch', 'black_bass', 'tilapia', 'pond_smelt', 'sweetfish' },
                uncommon = { 'koi', 'pop_eyed_goldfish', 'ranchu_goldfish', 'angelfish', 'betta', 'neon_tetra', 'rainbowfish' },
                rare = { 'giant_snakehead', 'golden_trout', 'stringfish', 'dorado', 'gar', 'arapaima', 'sturgeon', 'king_salmon' },
                epic = { },
                legendary = { },
                treasure = { 'old_boot', 'broken_bottle', 'silver_necklace', 'ancient_statue' }
            },
        },
        
        alamo_sea = {
            label = 'Alamo Sea',

            coords = vec3(1310.0, 4250.0, 33.0),
            radius = 50.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },

            fishTypes = {
                common = { 'bitterling', 'pale_chub', 'dace', 'carp', 'goldfish', 'killifish', 'crawfish', 'tadpole', 'frog', 'freshwater_goby', 'loach', 'bluegill', 'yellow_perch', 'black_bass', 'tilapia', 'pond_smelt', 'sweetfish' },
                uncommon = { 'koi', 'pop_eyed_goldfish', 'ranchu_goldfish', 'angelfish', 'betta', 'neon_tetra', 'rainbowfish' },
                rare = { 'giant_snakehead', 'golden_trout', 'stringfish', 'dorado', 'gar', 'arapaima', 'sturgeon', 'king_salmon' },
                epic = { },
                legendary = { },
                treasure = { 'old_boot', 'broken_bottle', 'silver_necklace', 'ancient_statue' }
            },
        },

        del_perro_pier = {
            label = 'Del Perro Pier',

            coords = vec3(-1800.0, -1180.0, 12.0),
            radius = 100.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },

            fishTypes = {
                common = { 'horse_mackerel', 'sea_bass', 'dab', 'olive_flounder' },
                uncommon = { 'sea_butterfly', 'seahorse', 'clownfish', 'surgeonfish', 'butterfly_fish', 'zebra_turkeyfish', 'barred_knifejaw', 'red_snapper', 'moray_eel', 'ribbon_eel' },
                rare = { 'napoleonfish', 'tuna_fish', 'blue_marlin', 'giant_trevally', 'mahi_mahi', 'ray' },
                epic = { 'saw_shark', 'hammerhead_shark', 'whale_shark', 'ocean_sunfish', 'oarfish' },
                legendary = { 'great_white_shark', 'coelacanth', 'barreleye' },
                treasure = { 'old_boot', 'broken_bottle', 'rusty_anchor', 'gold_coin', 'pearl', 'diving_watch', 'treasure_chest', 'silver_necklace', 'ancient_statue', 'shipwreck_plank' }
            },
        },

        paleto = {
            label = 'Paleto',

            coords = vec3(-278.6934, 6637.4268, 7.5462),
            radius = 100.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },

            fishTypes = {
                common = { 'horse_mackerel', 'sea_bass', 'dab', 'olive_flounder' },
                uncommon = { 'sea_butterfly', 'seahorse', 'clownfish', 'surgeonfish', 'butterfly_fish', 'zebra_turkeyfish', 'barred_knifejaw', 'red_snapper', 'moray_eel', 'ribbon_eel' },
                rare = { 'napoleonfish', 'tuna_fish', 'blue_marlin', 'giant_trevally', 'mahi_mahi', 'ray' },
                epic = { 'saw_shark', 'hammerhead_shark', 'whale_shark', 'ocean_sunfish', 'oarfish' },
                legendary = { 'great_white_shark', 'coelacanth', 'barreleye' },
                treasure = { 'old_boot', 'broken_bottle', 'rusty_anchor', 'gold_coin', 'pearl', 'diving_watch', 'treasure_chest', 'silver_necklace', 'ancient_statue', 'shipwreck_plank' }
            },
        },

        chumash_pier = {
            label = 'Chumash Pier',

            coords = vec3(-3350.0, 967.0, 9.0),
            radius = 80.0,

            blip = {
                enabled = true,

                radius = {
                    color = 38,
                    alpha = 150
                },

                center = {
                    sprite = 68,
                    color = 3,
                    scale = 0.7,
                    display = 4,
                    shortRange = true
                }
            },
            
            fishTypes = {
                common = { 'horse_mackerel', 'sea_bass', 'dab', 'olive_flounder' },
                uncommon = { 'sea_butterfly', 'seahorse', 'clownfish', 'surgeonfish', 'butterfly_fish', 'zebra_turkeyfish', 'barred_knifejaw', 'red_snapper', 'moray_eel', 'ribbon_eel' },
                rare = { 'napoleonfish', 'tuna_fish', 'blue_marlin', 'giant_trevally', 'mahi_mahi', 'ray' },
                epic = { 'saw_shark', 'hammerhead_shark', 'whale_shark', 'ocean_sunfish', 'oarfish' },
                legendary = { 'great_white_shark', 'coelacanth', 'barreleye' },
                treasure = { 'old_boot', 'broken_bottle', 'rusty_anchor', 'gold_coin', 'pearl', 'diving_watch', 'treasure_chest', 'silver_necklace', 'ancient_statue', 'shipwreck_plank' }
            },
        },
    }
}