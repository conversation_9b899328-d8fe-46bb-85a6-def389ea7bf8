AllObjects = {}

ObjectList = {
    Conveyor_1 = {
        coords = vec3(2955.797119, 2781.570068, 38.65),
        model = Get<PERSON><PERSON><PERSON><PERSON>('cs5_4_q_conv4'),
        rotation = vec3(-5.999979, 3.999954, -151.998672)
    },
    Conveyor_2 = {
        coords = vec3(2957.624023, 2768.287598, 40.5),
        model = GetHash<PERSON><PERSON>('cs5_4_q_conv4'),
        rotation = vec3(-5.999979, 3.999954, -151.998672)
    },
    Conveyor_3 = {
        coords = vec3(2954.345947, 2791.996094, 38.96),
        model = GetHash<PERSON>ey('prop_ind_conveyor_04'),
        rotation = vec3(23.999992, -0.000004, 7.999949)
    },
    Conveyor_4 = {
        coords = vec3(2958.549561, 2761.358887, 44.613716),
        model = GetHash<PERSON>ey('prop_ind_conveyor_01'),
        rotation = vec3(-7.999940, -0.000000, 7.999985)
    },
    Conveyor_5 = {
        coords = vec3(2955.701416, 2782.223877, 41.72),
        model = GetHashKey('prop_ind_conveyor_01'),
        rotation = vec3(-7.999940, -0.000000, 7.999985)
    },
    Conveyor_6 = {
        coords = vec3(2957.021240, 2772.763184, 43.006470),
        model = GetHashKey('prop_ind_conveyor_01'),
        rotation = vec3(-7.999940, -0.000000, 7.999985)
    },
    BoxFight = {
        coords = vec3(2959.081787, 2753.872559, 42.593098),
        model = GetHashKey('prop_skip_08b'),
        rotation = vec3(0.000000, -0.000000, 106.998299)
    },
    Table = {
        coords = vec3(2945.131104, 2747.680908, 42.30),
        model = GetHashKey('prop_table_03'),
        rotation = vec3(0, 0, -80.999771)
    },
    Notebook = {
        coords = vec3(2944.83, 2747.62, 43.12),
        model = GetHashKey('vw_prop_notebook_01a'),
        rotation = vec3(0, 0, 190)
    },
    Papers = {
        coords = vec3(2944.96, 2746.97, 43.10),
        model = GetHashKey('v_ind_cs_paper'),
        rotation = vec3(0, 0, 100)
    },
    Ashtray = {
        coords = vec3(2944.66, 2748.11, 43.10),
        model = GetHashKey('v_res_mp_ashtrayb'),
        rotation = vec3(0, 0, 0)
    },
    Box = {
        coords = vec3(2945.432861, 2745.69, 42.32),
        model = GetHashKey('prop_dynasty_box'),
        rotation = vec3(0.000000, -0.000000, 10.997986),
    },
    ---@Use this box if you have Config.K4MB1 enabled
    -- Box = {
    --     coords = vec3(-591.69, 2132.88, 128.081299),
    --     model = GetHashKey('prop_dynasty_box'),
    --     rotation = vec3(0.000000, -0.000000, 90.997986),
    -- },
    Light_1 = {
        coords = vec3(2944.38, 2743.12, 42.36),
        model = GetHashKey('prop_ind_light_03c'),
        rotation = vec3(0.00, -0.00, 151.99),
    },
    Light_2 = {
        coords = vec3(2975.26, 2795.75, 39.00),
        model = GetHashKey('prop_ind_light_03c'),
        rotation = vec3(0.00, 0.00, -89.99),
    },
    Light_3 = {
        coords = vec3(2936.29, 2776.51, 38.00),
        model = GetHashKey('prop_ind_light_03c'),
        rotation = vec3(0.00, -0.00, 146.99),
    },
    Light_4 = {
        coords = vec3(2952.86, 2818.39, 41.30),
        model = GetHashKey('prop_ind_light_03c'),
        rotation = vec3(0.00, 0.00, 8.99),
    },
    Light_5 = {
        coords = vec3(2926.40, 2794.46, 39.62),
        model = GetHashKey('prop_ind_light_03c'),
        rotation = vec3(0.00, 0.00, 86.99),
    },
    Light_6 = {
        coords = vec3(2966.65, 2775.81, 37.66),
        model = GetHashKey('prop_ind_light_03c'),
        rotation = vec3(0.00, 0.00, 146.99),
    },
    Light_7 = {
        coords = vec3(2931.692383, 2819.551758, 44.98137),
        model = GetHashKey('prop_ind_light_03c'),
        rotation = vec3(0.00, 0.00, 35.99),
    },
    Support_1 = {
        coords = vec3(2959.633057, 2757.382813, 40.380482),
        model = GetHashKey('prop_dynasty_support'),
        rotation = vec3(0.999980, -0.000003, -81.999596),
    },
    Support_2 = {
        coords = vec3(2957.139648, 2774.378174, 37.766121),
        model = GetHashKey('prop_dynasty_support'),
        rotation = vec3(0.000000, 0.000000, -85.999641),
    },
}