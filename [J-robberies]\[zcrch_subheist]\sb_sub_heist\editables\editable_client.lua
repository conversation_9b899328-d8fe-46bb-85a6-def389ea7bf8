--####################
--####################
--#######CORE#########
--####################
--####################

ESX = nil
QBCore = nil
PlayerData = {}
PlayerJob = nil
PlayerGrade = nil

Citizen.CreateThread(function()
if Config.submarineheist.Framework == 'ESX' then
    ESX = Config.Framework_core
    
    while not ESX.IsPlayerLoaded() do
        Wait(100)
    end
    
    PlayerData = ESX.GetPlayerData()
    PlayerJob = PlayerData.job.name
    PlayerGrade = PlayerData.job.grade

    RegisterNetEvent("esx:setJob", function(job)
        PlayerJob = job.name
        PlayerGrade = job.grade
    end)
elseif Config.submarineheist.Framework == 'QB' then
    QBCore = Config.Framework_core
    
    CreateThread(function ()
        while true do
            PlayerData = QBCore.Functions.GetPlayerData()
            if PlayerData.citizenid ~= nil then
                PlayerJob = PlayerData.job.name
                PlayerGrade = PlayerData.job.grade.level
                break
            end
            Wait(100)
        end
    end)

    RegisterNetEvent("QBCore:Client:OnJobUpdate", function(job)
        PlayerJob = job.name
        PlayerGrade = job.grade.level
    end)
elseif Config.submarineheist.Framework == 'CUSTOM' then
    Custom = Config.Framework_core
else
    print("Error: Invalid framework specified in Config.submarineheist.Framework.")
    return
end
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	PlayerData = xPlayer
end)

AddEventHandler('QBCore:Client:OnPlayerLoaded', function()
	PlayerData = QBCore.Functions.GetPlayerData()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	PlayerData.job = job
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate')
AddEventHandler('QBCore:Client:OnJobUpdate', function(job)
	PlayerData.job = job
end)

--############################
--############################
--#######NOTIFICATIONS#########
--############################
--###########################

RegisterNetEvent('sb_subheist:Notify')
AddEventHandler('sb_subheist:Notify', function(msg, type, length)
    SubmarineheistNotify(msg, type, length)
end)

function SubmarineheistNotify(msg, type, length)
    SetNotificationTextEntry('STRING')
    AddTextComponentString(msg)
    DrawNotification(0, 1)
end

function ShowHelpNotification(text)
    SetTextComponentFormat('STRING')
    AddTextComponentString(text)
    DisplayHelpTextFromStringLabel(0, 0, 1, 50)
end

function Heisthelps(msg, length)
BeginTextCommandPrint('STRING')
AddTextComponentString(msg)
EndTextCommandPrint(length,true)
end

RegisterNetEvent('sb_subheist:policeAlert')
AddEventHandler('sb_subheist:policeAlert', function()
	SubmarineheistNotify(Languages['police_alert'], 'error', 10000)	
    subheistblips = AddBlipForCoord(Config.police.Blipcoords)
	SetBlipSprite(subheistblips, 456)
	SetBlipScale(subheistblips, 1.0)
	SetBlipColour(subheistblips, 1)
	BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Languages['blip_name'])
    EndTextCommandSetBlipName(subheistblips)
	Citizen.Wait(Config.police.Bliptime*60000)
	RemoveBlip(subheistblips)
end)

--############################
--############################
--#######NEARBY PLAYER#########
--############################
--###########################

function nearyby()
    if Config.submarineheist.Framework == 'ESX' then
        local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
        if closestPlayer ~= -1 and closestDistance <= Config.nearbydsistance then
            SubmarineheistNotify(Languages['person_nearby'], 'error', 10000)
            return true 
        else
            return false
        end
    elseif Config.submarineheist.Framework == 'QB' then
        local closestPlayer, closestDistance = QBCore.Functions.GetClosestPlayer()
        if closestPlayer ~= -1 and closestDistance <= Config.nearbydsistance then
            SubmarineheistNotify(Languages['person_nearby'], 'error', 10000)
            return true 
        else
            return false
        end
	elseif Config.submarineheist.Framework == 'CUSTOM' then
    -- Handle your custom framework here	
    end
end

--##################################
--##################################
--#######MINIGAMES-EDITABLE#########
--#################################
--################################


function hackerminigame(sobj)
    local success = exports['howdy-hackminigame']:Begin(3, 5000)
    if success then
        successfulgamehacker(sobj)
    else
        failuregamehacker(sobj)
    end
end

function fuseminigame(sobj,k, v)
    TriggerEvent("Mx::StartMinigameElectricCircuit", '50%', '50%', '1.0', '30vmin', '1.ogg', function()
       successfulgamefuse(sobj,k, v)
    end)
end

--failuregamefuse(sobj,k, v)

--############################
--############################
--#######HMS INTEGRATION#########
--############################
--###########################

-- Function to trigger heist failure (can be called when heist fails)
function TriggerSubmarineHeistFailure(reason)
    TriggerServerEvent('sb_subheist:hms:heistFailed', reason or Languages['failed'])
end

-- Export the failure function for use by main script
exports('TriggerSubmarineHeistFailure', TriggerSubmarineHeistFailure)
