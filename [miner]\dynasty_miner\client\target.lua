function InteractWithPedStartMission(Ped)
    if Config.TargetScript == "ox" then
        local options = {
            {
                name = 'ds:startworking',
                label = Lang("TARGET_START_WORKING"),
                icon = 'fa-solid fa-person-digging',
                distance = 4,
                onSelect = function()
                    ToggleMission()
                end
            },
        }
        exports.ox_target:addLocalEntity(Ped, options)
    elseif Config.TargetScript == "qb" then
        exports['qb-target']:AddTargetEntity(Ped, {
            options = {
                {
                    num = 1,
                    label = Lang("TARGET_START_WORKING"),
                    icon = 'fa-solid fa-person-digging',
                    action = function()
                        ToggleMission()
                    end,
                },
            },
            distance = 4,
        })
    end
end

function InteractWithPedStopMission(Ped)
    if Config.TargetScript == "ox" then
        local options = {
            {
                name = 'ds:stopworking',
                label = Lang("TARGET_STOP_WORKING"),
                icon = 'fa-solid fa-vest',
                distance = 4,
                onSelect = function()
                    ToggleMission()
                end
            },
        }
        exports.ox_target:addLocalEntity(Ped, options)
    elseif Config.TargetScript == "qb" then
        exports['qb-target']:AddTargetEntity(Ped, {
            options = {
                {
                    num = 1,
                    label = Lang("TARGET_STOP_WORKING"),
                    icon = 'fa-solid fa-vest',
                    action = function()
                        ToggleMission()
                    end,
                },
            },
            distance = 4,
        })
    end
end

function InteractiveMineRock(level, baseStoneID, oreID)
    if Config.TargetScript == "ox" then
        local options = {
            {
                name = 'ds:minerock',
                label = Lang("TARGET_MINE_ORE"),
                icon = 'fa-solid fa-gem',
                distance = 4,
                onSelect = function()
                    ExtractMineralChecks(level, baseStoneID, oreID)
                end
            }
        }
        exports.ox_target:addLocalEntity(Stones[level][baseStoneID][oreID].prop, options)
    elseif Config.TargetScript == "qb" then
        exports['qb-target']:AddTargetEntity(Stones[level][baseStoneID][oreID].prop, {
            options = {
                {
                    num = 1,
                    label = Lang("TARGET_MINE_ORE"),
                    icon = 'fa-solid fa-gem',
                    action = function()
                        ExtractMineralChecks(level, baseStoneID, oreID)
                    end,
                },
            },
            distance = 4,
        })
    end
end

function InteractiveBox(object)
    if Config.TargetScript == "ox" then
        local options = {
            {
                name = 'ds:newbox',
                label = Lang("TARGET_BUY_PICKAXE"),
                icon = 'fa-solid fa-hand-holding-hand',
                distance = 3,
                event = 'dynasty_miner:pickaxeMenu'
                -- onSelect = function()
                --     GivePickaxeToPlayer()
                -- end
            }
        }
        exports.ox_target:addLocalEntity(object, options)
    elseif Config.TargetScript == "qb" then
        exports['qb-target']:AddTargetEntity(object, {
            options = {
                {
                    num = 1,
                    label = Lang("TARGET_BUY_PICKAXE"),
                    icon = 'fa-solid fa-hand-holding-hand',
                    event = 'dynasty_miner:pickaxeMenu'
                    -- action = function()
                    --     GivePickaxeToPlayer()
                    -- end,
                },
            },
            distance = 3,
        })
    end
end

function InteractWithConveyor(level, baseStoneID, oreID)
    if Config.TargetScript == "ox" then
        InteractZone = exports.ox_target:addSphereZone({
            coords = vec3(2954.22, 2794.16, 40.84),
            radius = 1,
            debug = drawZones,
            options = {
                {
                    name = 'ds:minerock',
                    label = Lang("TARGET_DEPOSIT_ORE"),
                    icon = 'fa-solid fa-gem',
                    distance = 3,
                    onSelect = function()
                        PlaceStoneInConveyor(Player.stone)
                    end
                }
            }
        })
    elseif Config.TargetScript == "qb" then
        exports['qb-target']:AddBoxZone("InteractZone", vector3(2954.22, 2794.16, 40.84), 3.0, 3.0, {
            name = "InteractZone",
            heading = 12.0,
            debugPoly = false,
            minZ = 38.84,
            maxZ = 40.84 + 2,
        }, {
            options = {
                {
                    num = 1,
                    icon = 'fa-solid fa-gem',
                    label = Lang("TARGET_DEPOSIT_ORE"),
                    action = function()
                        PlaceStoneInConveyor(Player.stone)
                    end,
                }
            },
            distance = 3,
        })
    end
end

function InteractWithPedSeller(Ped)
    if Config.TargetScript == "ox" then
        local options = {
            {
                name = 'ds:startworking',
                label = Lang("TARGET_SELL_ITEMS"),
                icon = 'fa-solid fa-gem',
                distance = 4,
                event = 'dynasty_miner:sellMenu',
            },
        }
        exports.ox_target:addLocalEntity(Ped, options)
    elseif Config.TargetScript == "qb" then
        exports['qb-target']:AddTargetEntity(Ped, {
            options = {
                {
                    num = 1,
                    label = Lang("TARGET_SELL_ITEMS"),
                    icon = 'fa-solid fa-gem',
                    event = 'dynasty_miner:sellMenu',
                },
            },
            distance = 4,
        })
    end
end

function InteractWithPedQuest(Ped)
    if Config.TargetScript == "ox" then
        local options = {
            {
                name = 'ds:startworking',
                label = Lang("TARGET_QUEST"),
                icon = 'fa-solid fa-circle-exclamation',
                distance = 4,
                event = 'dynasty_miner:questMenu',
            },
        }
        exports.ox_target:addLocalEntity(Ped, options)
    elseif Config.TargetScript == "qb" then
        exports['qb-target']:AddTargetEntity(Ped, {
            options = {
                {
                    num = 1,
                    label = Lang("TARGET_QUEST"),
                    icon = 'fa-solid fa-circle-exclamation',
                    event = 'dynasty_miner:questMenu',
                },
            },
            distance = 4,
        })
    end
end

function InteractWithPedLeaderboard(Ped)
    if Config.TargetScript == "ox" then
        local options = {
            {
                name = 'ds:startworking',
                label = Lang("TARGET_OPEN_LEADERBOARD"),
                icon = "fa-solid fa-clipboard",
                distance = 4,
                onSelect = function()
                    ToggleLeaderBoard()
                end
            },
        }
        exports.ox_target:addLocalEntity(Ped, options)
    elseif Config.TargetScript == "qb" then
        exports['qb-target']:AddTargetEntity(Ped, {
            options = {
                {
                    num = 1,
                    label = Lang("TARGET_OPEN_LEADERBOARD"),
                    icon = "fa-solid fa-clipboard",
                    action = function()
                        ToggleLeaderBoard()
                    end,
                },
            },
            distance = 4,
        })
    end
end