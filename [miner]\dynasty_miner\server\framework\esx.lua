if Config.Framework ~= "ESX" then
    return
end

Citizen.CreateThreadNow(function()
    ESX = exports["es_extended"]:getSharedObject()
end)

function GetPlayeridentifier(source)
    local Player = GetPlayerId(source)
    if Player then
        return Player.identifier
    end
end

function GetPlayerId(source)
    local Player = ESX.GetPlayerFromId(source)
    return Player
end

function GetItemCountFromInventory(source, item)
    local xPlayer = ESX.GetPlayerFromId(source)
    local itemData = xPlayer.getInventoryItem(item)
    if itemData then
        return itemData.count
    end
end

function AddInventoryItem(src, item, quantity)
    local xPlayer = ESX.GetPlayerFromId(src)
    xPlayer.addInventoryItem(item, quantity)
end

function RemoveInventoryItem(item, quantity)
    local xPlayer = ESX.GetPlayerFromId(source)
    xPlayer.removeInventoryItem(item, quantity)
end

function AddMoney(source, price) --MODIFICACION
    local xPlayer = GetPlayerId(source)
    xPlayer.addAccountMoney('money', price)
end

function RemoveMoney(price, src)
    if src then
        local xPlayer = GetPlayerId(src)
        xPlayer.removeAccountMoney('money', price)
    else
        local xPlayer = GetPlayerId(source)
        xPlayer.removeAccountMoney('money', price)
    end
end

function GetMoney(source)
    local xPlayer = GetPlayerId(source)
    local money = xPlayer.getMoney()
    return money
end

function RegisterServerCallback(name, cb)
    ESX.RegisterServerCallback(name, cb)
end

function GetPlayers()
    local xPlayers = ESX.GetPlayers()
    return xPlayers
end

function GetPlayerFromIdentifier(identifier) --MODIFICACION
    local xPlayer = ESX.GetPlayerFromIdentifier(identifier)
    if xPlayer then
        return xPlayer.source
    else
        return nil
    end
end
