fx_version 'cerulean'

game 'gta5'

lua54 'yes'

this_is_a_map 'yes'

title 'Dynasty Miner'

author 'Dynasty Store'

version '1.9.4 Beta'

shared_scripts {
    '@es_extended/imports.lua',
    '@ox_lib/init.lua',
    'config/*.lua'
}

ui_page 'html/index.html'

server_script '@oxmysql/lib/MySQL.lua'

files {
    'html/index.html',
    'html/style.css',
    'html/script.js',
    'html/*.mp3',
    'html/*.otf'
}

client_scripts {
    'client/framework/*.lua',
    'client/*.lua',
}

server_scripts {
    'server/framework/*.lua',
    'server/*.lua',
}

escrow_ignore {
    'config/config.lua',
    'config/clothing.lua',
    'config/translations.lua',
    'client/framework/*.lua',
    'client/target.lua',
    'client/props.lua',
    'client/inventory.lua',
    'server/framework/*.lua',
    'server/inventory.lua',
}
dependency '/assetpacks'