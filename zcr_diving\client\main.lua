nuiLoaded = false
vehiclelowered = false
twovehiclelowered = false
Core = nil
spawnobje = false
CoopDataClient = {}
LowerTable = {}
TrashTable = {}
seaChangedTable = {}
seaCleanTable = {}
joobTaskClient = {}
objectCount = 0
blips = {}
ChangedBlip = {}
CleanBlip = {}
VehBlip = {}
FinishBlip = {}
--new
boxes = {}
BoxTable = {}
LiftTable = {}
attempts = 0
graplleTarget = false
currentAreaNameChanced = {}
currentAreaNameSuitCase = {}
currentAreaNameClean = {}
seaSuitCaseTable = {}
ShipBoxTable = {}
totalboxnumber = 1
currentGear = {
    mask = 0,
    tank = 0,
    oxygen = 0,
    level = 0,
    enabled = false
}
function checkNUI()
    while not nuiLoaded do
        Wait(0)
    end
end
AddEventHandler('onResourceStop', function(resource)
	if resource == GetCurrentResourceName() then
        for k,v in pairs(BoxTable) do 
            if v.objectName then
                DeleteEntity(v.objectName)
            end
            BoxTable = {}
        end
        deleteGear()
        for k,v in pairs(LiftTable) do 
            if v.objectName then
                DeleteEntity(v.objectName)
            end
            LiftTable = {}
        end
        for k,v in pairs(TrashTable) do 
            if v.objectName then
                DeleteEntity(v.objectName)
            end
            TrashTable = {}
        end
        for k,v in pairs(seaCleanTable) do 
            if v.objectName then
                DeleteEntity(v.objectName)
            end
            seaCleanTable = {}
        end
        for k,v in pairs(seaChangedTable) do 
            if v.objectName then
                DeleteEntity(v.objectName)
            end
            seaChangedTable = {}
        end
        
        for k,v in pairs(seaSuitCaseTable) do 
            if v.objectName then
                DeleteEntity(v.objectName)
            end
            seaSuitCaseTable = {}
        end
        for k,v in pairs(ShipBoxTable) do 
            if v.objectName then
                DeleteEntity(v.objectName)
            end
            ShipBoxTable = {}
        end

        for i = 1, #blips do
            RemoveBlip(blips[i])
        end
        blips = {}
        for i = 1, #ChangedBlip do
            RemoveBlip(ChangedBlip[i])
        end

        for i = 1, #CleanBlip do
            RemoveBlip(CleanBlip[i])
        end
        for i = 1, #VehBlip do
            RemoveBlip(VehBlip[i])
        end
        
        for i = 1, #FinishBlip do
            RemoveBlip(FinishBlip[i])
        end

        

        VehBlip = {}
        FinishBlip = {}

        ChangedBlip = {}
        CleanBlip = {}
        CoopDataClient = {}
        joobTaskClient = {}

        SetEnableScuba(PlayerPedId(), false)
        SetEnableScubaGearLight(PlayerPedId(), false)
        SetPedMaxTimeUnderwater(PlayerPedId(), 1.00)
	end
end)

function NuiMessage(action, payload)
    checkNUI()
    SendNUIMessage({
        action = action,
        payload = payload
    })
end

CreateThread(function()
    while not nuiLoaded do
        if NetworkIsSessionStarted() then
            SendNUIMessage({
                action = "CHECK_NUI",
            })
        end
        Wait(2000)
    end
end)

RegisterNUICallback("checkNUI", function(data, cb)
    nuiLoaded = true
    cb("ok")
end)

RegisterNetEvent('zcr_diving:client:useScuba', function(Slevel)
    if not Slevel then return end
    if not IsPedSwimming(PlayerPedId()) and not IsPedInAnyVehicle(PlayerPedId()) then
        gearAnim()
        Wait(2000)
        local tankModel
        if Slevel == 'level1' then
            tankModel = Config.Tube['level1'].model
        elseif Slevel == 'level2' then
            tankModel = Config.Tube['level2'].model
        elseif Slevel == 'level3' then
            tankModel = Config.Tube['level3'].model
        else
            tankModel = `p_s_scuba_tank_s`
        end
        RequestModel(tankModel)
        while not HasModelLoaded(tankModel) do
            Wait(0)
        end
        currentGear.tank = CreateObject(tankModel, 1.0, 1.0, 1.0, 1, 1, 0)
        currentGear.level = Slevel
        local bone1 = GetPedBoneIndex(PlayerPedId(), 24818)
        AttachEntityToEntity(currentGear.tank, PlayerPedId(), bone1, -0.25, -0.25, 0.0, 180.0, 90.0, 0.0, 1, 1, 0,0, 2, 1)
        ClearPedTasks(PlayerPedId())
        SetPedMaxTimeUnderwater(PlayerPedId(), 2000.00)
        if Slevel == 'level1' then
            sendNotification(Config.NotificationText['level1tube'].text, Config.NotificationText['level1tube'].type)
            --exports['codem-inventory']:RemoveItem(source, scuba_tube_l1, 1)
        elseif Slevel == 'level2' then
            sendNotification(Config.NotificationText['level2tube'].text, Config.NotificationText['level2tube'].type)
            --exports['codem-inventory']:RemoveItem(source, scuba_tube_l2, 1)
        elseif Slevel == 'level3' then
            sendNotification(Config.NotificationText['level3tube'].text, Config.NotificationText['level3tube'].type)
           --exports['codem-inventory']:RemoveItem(source, scuba_tube_l3, 1)
        end
    end
end)

RegisterNetEvent('zcr_diving:client:useScubaMask', function()
    if currentGear.tank == false then 
        sendNotification(Config.NotificationText['requiredTube'].text, Config.NotificationText['requiredTube'].type)
        return
    end
    gearAnim()
    Wait(2000)
    local maskModel = Config.Tube['mask'].model
    RequestModel(maskModel)
    while not HasModelLoaded(maskModel) do
        Wait(0)
    end
    currentGear.mask = CreateObject(maskModel, 1.0, 1.0, 1.0, 1, 1, 0)
    local bone2 = GetPedBoneIndex(PlayerPedId(), 12844)
    AttachEntityToEntity(currentGear.mask, PlayerPedId(), bone2, 0.0, 0.0, 0.0, 180.0, 90.0, 0.0, 1, 1, 0, 0, 2
        , 1)
    ClearPedTasks(PlayerPedId())
    currentGear.enabled = true
    currentGear.oxygen = 100
    SetPedMaxTimeUnderwater(PlayerPedId(), 500.00)
    SetEnableScuba(PlayerPedId(), true)
    SetEnableScubaGearLight(PlayerPedId(), true)
    sendNotification(Config.NotificationText['wearMask'].text, Config.NotificationText['wearMask'].type)
end)

Citizen.CreateThread(function()
    while true do
        local waitTime = 1000
        if CoopDataClient and CoopDataClient.roomSetting and currentGear then
            if IsPedSwimmingUnderWater(PlayerPedId()) and currentGear.oxygen > 0 and currentGear.enabled  then
    
                currentGear.oxygen = currentGear.oxygen - 1
                NuiMessage('OXYGEN_LEVEL', {oxygen = currentGear.oxygen, level = currentGear.level})
    
                if currentGear.oxygen <= 0 then
                    deleteGear()
                    sendNotification(Config.NotificationText['oxygen'].text, Config.NotificationText['oxygen'].type)
                    SetPedMaxTimeUnderwater(PlayerPedId(), 1.00)
                    currentGear.enabled = false
                end
            end

            if currentGear.level == 'level1' then
                waitTime = Config.Tube['level1'].waitTime * 1000
            elseif currentGear.level == 'level2' then
                waitTime = Config.Tube['level2'].waitTime * 1000
            elseif currentGear.level == 'level3' then
                waitTime = Config.Tube['level3'].waitTime * 1000
            else
                waitTime = 1000 
            end
        end
        Wait(waitTime)
    end
end)


function gearAnim()
    RequestAnimDict("clothingshirt")
    while not HasAnimDictLoaded("clothingshirt") do
        Wait(0)
    end
	TaskPlayAnim(PlayerPedId(), "clothingshirt", "try_shirt_positive_d", 8.0, 1.0, -1, 49, 0, 0, 0, 0)
end

function deleteGear()
	if currentGear.mask ~= 0 then
        DetachEntity(currentGear.mask, 0, 1)
        DeleteEntity(currentGear.mask)
		currentGear.mask = 0
    end
	if currentGear.tank ~= 0 then
        DetachEntity(currentGear.tank, 0, 1)
        DeleteEntity(currentGear.tank)
		currentGear.tank = 0
	end
end

function openDivingMenu()
    if nuiLoaded and Core then
        local playerData = TriggerCallback('zcr_diving:server:getPlayerData')
        local ServerLobby = TriggerCallback('zcr_diving:server:CreatePlayerLobby')
        if playerData then
            SetNuiFocus(true, true)
            NuiMessage('OPEN_DIVING', playerData)
            NuiMessage('LOAD_LOBBY', ServerLobby)
        end
    end
end

function openSellMenu()
    if nuiLoaded and Core then
        local playerData = TriggerCallback('zcr_diving:server:getPlayerData')
        if playerData then
            SetNuiFocus(true, true)
            NuiMessage('OPEN_SELL_MENU', playerData)
        end
    end
end

Citizen.CreateThread(function()
    while Core == nil and not nuiLoaded do
        Citizen.Wait(0)
    end
    Config.OpenTrigger()
    NuiMessage('SERVER_NAME', Config.ServeName)
    NuiMessage('LOCALES', Config.Locales)
    NuiMessage('SERVER_MONEY_TYPE', Config.MoneyType)
    NuiMessage('SERVER_MISSIONS', Config.Diving['Mission'])
end)

RegisterNetEvent('zcr_diving:client:invetePlayer', function(lobbyOwner, identifier)
    local playerData = TriggerCallback('zcr_diving:server:getPlayerData')
    if playerData then
        SetNuiFocus(true, true)
        NuiMessage('INVITE_MENU', {lobbyOwner = lobbyOwner, identifier = identifier, playerData = playerData})
    end
end)

RegisterNetEvent('zcr_diving:client:RefreshLobby', function(lobbyData)
    local playerData = TriggerCallback('zcr_diving:server:getPlayerData')
    CoopDataClient = lobbyData
    if playerData then
        SetNuiFocus(true, true)
        NuiMessage('OPEN_DIVING', playerData)
        NuiMessage('LOAD_LOBBY', CoopDataClient.players)
        NuiMessage('REFRESH_LOBBY', CoopDataClient.roomSetting.Mission)
    end
end)

RegisterNetEvent('zcr_diving:client:StartJob', function(jobData, jobTask)
    if jobData.roomSetting.startJob then
        joobTaskClient = jobTask
        CoopDataClient = jobData
        NuiMessage('CLOSENUI')
        NuiMessage('START_JOB', joobTaskClient)
        SetNuiFocus(false, false)
        TriggerEvent('zcr_diving:server:FinishJob', CoopDataClient.roomSetting.owneridentifier)

    end
end)

RegisterNetEvent('zcr_diving:client:RefreshJob', function(jobTask)
    joobTaskClient = jobTask
    NuiMessage('REFRESH_JOBTASK', joobTaskClient)
end)

RegisterNetEvent('zcr_diving:client:TakeLooby', function()
    SetEntityCoords(PlayerPedId(),  Config.Diving.coords.intreactionCoords.x,  Config.Diving.coords.intreactionCoords.y,  Config.Diving.coords.intreactionCoords.z)
    local playerData = TriggerCallback('zcr_diving:server:getPlayerData')
    local ServerLobby = TriggerCallback('zcr_diving:server:CreatePlayerLobby')
    if playerData then
        SetNuiFocus(true, true)
        NuiMessage('OPEN_DIVING', playerData)
        NuiMessage('LOAD_LOBBY', ServerLobby)
    end
end)

RegisterNUICallback('closeNUI', function()
    SetNuiFocus(false, false)
end)

RegisterNUICallback('invitePlayer', function(data)
    TriggerServerEvent('zcr_diving:server:invetePlayer',data)
end)

RegisterNUICallback('firePlayer', function(data)
    TriggerServerEvent('zcr_diving:server:firePlayer', data)
end)

RegisterNUICallback('acceptInvite', function(data)
    SetNuiFocus(false, false)
    TriggerServerEvent('zcr_diving:server:acceptInvite', data)
end)

RegisterNUICallback('selectMission', function(data)
    TriggerServerEvent('zcr_diving:server:selectMission', data)
end)

RegisterNUICallback('startJob', function(data)
    if data then
        local distance = #(GetEntityCoords(PlayerPedId()) - vector3(data.jobDeliverTheVehicleCoords.x, data.jobDeliverTheVehicleCoords.y, data.jobDeliverTheVehicleCoords.z))
        if distance < 100 then
            newCoords = data.jobDeliverTheVehicleCoords
            lastCoords = GetEntityCoords(PlayerPedId())
            Wait(500)
            local distanceCheck = GetVehiclesInArea(newCoords, 10.0)
            if next(distanceCheck) == nil then
                TriggerServerEvent('zcr_diving:server:startJob', data)
            else
                sendNotification(Config.NotificationText['vehicleexist'].text, Config.NotificationText['vehicleexist'].type)
            end
        else
            DoScreenFadeOut(1000)
            while not IsScreenFadedOut() do
                Citizen.Wait(1000)
            end
            newCoords = data.jobDeliverTheVehicleCoords
            lastCoords = GetEntityCoords(PlayerPedId())
            SetEntityCoords(PlayerPedId(), newCoords.x, newCoords.y, newCoords.z - 10)
            Wait(500)
            local distanceCheck = GetVehiclesInArea(newCoords, 10.0)
            if next(distanceCheck) == nil then
                SetEntityCoords(PlayerPedId(), lastCoords)
                Wait(500)
                DoScreenFadeIn(1000)
                TriggerServerEvent('zcr_diving:server:startJob', data)
            else
                SetEntityCoords(PlayerPedId(), lastCoords)
                Wait(500)
                DoScreenFadeIn(1000)
                sendNotification(Config.NotificationText['vehicleexist'].text, Config.NotificationText['vehicleexist'].type)
            end
        end

    end
end)

RegisterNUICallback('resetJob', function()
    -- SetNuiFocus(false, false)
    TriggerServerEvent('zcr_diving:server:resetJobButton', CoopDataClient.roomSetting.owneridentifier)
end)

RegisterNUICallback('getMarketData', function(data, cb)
    local marketData = Config.Diving['Market']["BuyItems"]
    cb(marketData)
end)

RegisterNUICallback('getSellMarketData', function(data, cb)
    local marketData = TriggerCallback('zcr_diving:server:getSellMarketData')
    cb(marketData)
end)

RegisterNUICallback("buyItem", function(data)
    TriggerServerEvent('zcr_diving:server:buyItem', data)
end)

RegisterNUICallback('sellItem', function(data)
    TriggerServerEvent('zcr_diving:server:sellItem', data)
end)

RegisterNetEvent('zcr_diving:client:teleportPlayer', function(name, coords)
    DoScreenFadeOut(1000)
    while not IsScreenFadedOut() do
        Citizen.Wait(1000)
    end

    if name == "vehicle" then
        SetEntityCoords(PlayerPedId(), coords.x + 5.0, coords.y - 7.0, coords.z)
    else
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z)
    end
    Wait(1000)
    DoScreenFadeIn(1000)
end)


RegisterNetEvent('zcr_diving:client:syncSettings', function(name, areaName, settingsData)
    if name == "waypoint" then
        SetNewWaypoint(areaName, settingsData)
    elseif name == 'blip' then
        for k, v in pairs(settingsData) do
            if areaName == "seaClean" then
                cleanBlip = AddBlipForCoord(settingsData.x, settingsData.y, settingsData.z)
                SetBlipSprite (cleanBlip, 1)
                SetBlipColour (cleanBlip, 29)
                SetBlipScale  (cleanBlip, 0.8)
                SetBlipAsShortRange(cleanBlip, true)
                BeginTextCommandSetBlipName("STRING")
                AddTextComponentString("TRASH")
                EndTextCommandSetBlipName(cleanBlip)
                table.insert(CleanBlip, cleanBlip)
            elseif areaName == "seaChanged" then
                changedblip = AddBlipForCoord(settingsData.x, settingsData.y, settingsData.z)
                SetBlipSprite (changedblip, 1)
                SetBlipColour (changedblip, 77)
                SetBlipScale  (changedblip, 0.8)
                SetBlipAsShortRange(changedblip, true)
                BeginTextCommandSetBlipName("STRING")
                AddTextComponentString("Coral")
                EndTextCommandSetBlipName(changedblip)
                table.insert(ChangedBlip, changedblip)
            elseif areaName == "vehBlip" then
                vehBlip = AddBlipForCoord(settingsData.x, settingsData.y, settingsData.z)
                SetBlipSprite(vehBlip, 529)
                SetBlipColour(vehBlip, 0)
                SetBlipScale(vehBlip, 0.8)
                SetBlipAsShortRange(vehBlip, true)
                BeginTextCommandSetBlipName("STRING")
                AddTextComponentString("Tug")
                EndTextCommandSetBlipName(vehBlip)
               -- table.insert(VehBlip, vehBlip)

                Citizen.CreateThread(function()
                    while true do
                        if DoesEntityExist(clientvehInfo) then
                            local updatedCoords = GetEntityCoords(clientvehInfo)
                            SetBlipCoords(vehBlip, updatedCoords.x, updatedCoords.y, updatedCoords.z)
                        else
                            if CoopDataClient and CoopDataClient.roomSetting and CoopDataClient.roomSetting.owneridentifier then
                                local vehicleData =  TriggerCallback('zcr_diving:server:getVehicleData', CoopDataClient.roomSetting.owneridentifier)
                                if vehicleData then
                                    clientvehInfo = NetToVeh(vehicleData)
                                    local vehiclecoords = GetEntityCoords(clientvehInfo)
                                    SetBlipCoords(vehBlip, vehiclecoords.x, vehiclecoords.y, vehiclecoords.z)
                                end
                            end
                        end
                        Citizen.Wait(5000) 
                    end
                end)
            end
        end
    elseif name == "anchor" then
        if areaName then
            sendNotification(Config.NotificationText['achordrop'].text, Config.NotificationText['achordrop'].type)
        else
            sendNotification(Config.NotificationText['achorpickeddrop'].text, Config.NotificationText['achorpickeddrop'].type)
        end
        if not DoesEntityExist(clientvehInfo) then
            local vehicleData =  TriggerCallback('zcr_diving:server:getVehicleData', CoopDataClient.roomSetting.owneridentifier)
            if vehicleData then
                clientvehInfo = NetToVeh(vehicleData)
            end
            FreezeEntityPosition(clientvehInfo, areaName)
        else
            FreezeEntityPosition(clientvehInfo, areaName)
        end
    end
end)



RegisterNetEvent('zcr_diving:client:sendNotification', function(message, type)
    sendNotification(message, type)
end)

function sendNotification(messages, type)
    -- if nuiLoaded then
    --     NuiMessage('NOTIFICATION', {message = messages, type = type})
    -- end
    exports['zcr_notify']:notification('fa-solid fa-anchor text-info','Diving Job',"", messages, 15000)
end

local liftKey = 38 -- E tuşu
RegisterNetEvent('zcr_diving:client:StartJobOwner', function(jobData, jobTask, ServervehInfo, owner, vehicleCoordsServer)
    if jobData.roomSetting.startJob then
        joobTaskClient = jobTask
        CoopDataClient = jobData
        NuiMessage('REFRESH_JOBTASK', joobTaskClient)
        NuiMessage('CLOSENUI')
        NuiMessage('START_JOB', joobTaskClient)
        SetNuiFocus(false, false)
        
        while true do
            local distanceVeh = #(GetEntityCoords(PlayerPedId()) - vector3(vehicleCoordsServer.x, vehicleCoordsServer.y, vehicleCoordsServer.z))
            if distanceVeh < 50 then
                clientvehInfo = NetToVeh(ServervehInfo)
                Config.SetVehicleFuel(clientvehInfo, 100.0)
                Config.GiveVehicleKey(GetVehicleNumberPlateText(clientvehInfo), GetHashKey(Config.Diving['jobVehicle']), clientvehInfo)
                --if owner then
                    local vehiclecoords = GetEntityCoords(clientvehInfo)
                    vehBlip = AddBlipForCoord(vehiclecoords.x, vehiclecoords.y, vehiclecoords.z)
                    SetBlipSprite(vehBlip, 529)
                    SetBlipColour(vehBlip, 0)
                    SetBlipScale(vehBlip, 0.8)
                    SetBlipAsShortRange(vehBlip, true)
                    BeginTextCommandSetBlipName("STRING")
                    AddTextComponentString("Tug")
                    EndTextCommandSetBlipName(vehBlip)
                    table.insert(VehBlip, vehBlip)
                    TriggerServerEvent('zcr_diving:server:syncSettings', "waypoint", vehiclecoords.x, vehiclecoords.y, CoopDataClient.roomSetting.owneridentifier)
                    -- TriggerServerEvent('zcr_diving:server:syncSettings', "blip", "vehBlip", vehiclecoords, CoopDataClient.roomSetting.owneridentifier)
                    --break
                --end
                break
            end
            Wait(1000)
        end
        sendNotification(Config.NotificationText['gotosite'].text, Config.NotificationText['gotosite'].type)
        Citizen.CreateThread(function()
            while true do
                if DoesEntityExist(clientvehInfo) then
                    local updatedCoords = GetEntityCoords(clientvehInfo)
                    SetBlipCoords(vehBlip, updatedCoords.x, updatedCoords.y, updatedCoords.z)
                end
                Citizen.Wait(5000)
            end
        end)
        if owner then
            if CoopDataClient and CoopDataClient.roomSetting then
                local jobTask = jobData.roomSetting.Mission.jobTask
                for k, v in pairs(jobTask) do
                    if v.name == "seaChanged" then
                        while true do
                            local distance = #(GetEntityCoords(PlayerPedId()) - vector3(v.coords.x, v.coords.y, v.coords.z))
                            if distance < 150 then
                                SpawnObjectGenerator(v.name, v.coords, v.areaDistance, v.amount)
                                break
                            else
                                if not next(ChangedBlip) then
                                    changedblip = AddBlipForCoord(v.coords.x, v.coords.y, v.coords.z)
                                    SetBlipSprite (changedblip, 1)
                                    SetBlipColour (changedblip, 77)
                                    SetBlipScale  (changedblip, 0.8)
                                    SetBlipAsShortRange(changedblip, true)
                                    BeginTextCommandSetBlipName("STRING")
                                    AddTextComponentString("Coral")
                                    EndTextCommandSetBlipName(changedblip)
                                    table.insert(ChangedBlip, changedblip)
                                    TriggerServerEvent('zcr_diving:server:syncSettings', 'blip', v.name, v.coords, CoopDataClient.roomSetting.owneridentifier)
                                end
                                SetNewWaypoint(v.coords.x, v.coords.y)
                                TriggerServerEvent('zcr_diving:server:syncSettings', 'waypoint', v.coords.x, v.coords.y, CoopDataClient.roomSetting.owneridentifier)
                            end
                            Wait(3000)
                        end
                    elseif v.name == "sealooting" then
                        while true do
                            local distance = #(GetEntityCoords(PlayerPedId()) - vector3(v.coords.x, v.coords.y, v.coords.z))
                            if distance < 150 then
                                jobTaskCoords = v.coords
                                local radius = GetRandomCoordInCircle(jobTaskCoords, v.areaDistance, v.amount)
                                CreateDivingObject(radius, jobData.roomSetting.Mission.boxModel)
                                break
                            end
                            Wait(3000)
                        end
                    elseif v.name == "seaClean" then
                        while true do
                            local distance = #(GetEntityCoords(PlayerPedId()) - vector3(v.coords.x, v.coords.y, v.coords.z))
                            if distance < 150 then
                                SpawnObjectGenerator(v.name, v.coords, v.areaDistance, v.amount)
                                break
                            else
                                if not next(CleanBlip) then
                                    cleanBlip = AddBlipForCoord(v.coords.x, v.coords.y, v.coords.z)
                                    SetBlipSprite (cleanBlip, 1)
                                    SetBlipColour (cleanBlip, 29)
                                    SetBlipScale  (cleanBlip, 0.8)
                                    SetBlipAsShortRange(cleanBlip, true)
                                    BeginTextCommandSetBlipName("STRING")
                                    AddTextComponentString("TRASH")
                                    EndTextCommandSetBlipName(cleanBlip)
                                    table.insert(CleanBlip, cleanBlip)
                                    TriggerServerEvent('zcr_diving:server:syncSettings', 'blip', v.name, v.coords, CoopDataClient.roomSetting.owneridentifier)
                                end
                            end
                            Wait(3000)
                        end
                    elseif v.name == "seaSuitCase" then
                        while true do
                            local distance = #(GetEntityCoords(PlayerPedId()) - vector3(v.coords.x, v.coords.y, v.coords.z))
                            if distance < 150 then
                                SpawnObjectGenerator(v.name, v.coords, v.areaDistance, v.amount)
                                break
                            end
                            Wait(3000)
                        end
                    end
                end
            end

        end
    end
end)

function CreateDivingObject(area, boxModel)
    if CoopDataClient and CoopDataClient.roomSetting and CoopDataClient.roomSetting.startJob then
        if not area or not boxModel then
            return
        end
        for k, v in pairs(area) do
            local selectedBoxModel, selectedBoxCategory = GetRandomBoxModel(boxModel)
            local success, groundZ = GetGroundZFor_3dCoord(v.x, v.y, v.z + 0.8)
            if success then
                local model = selectedBoxModel.model[math.random(#selectedBoxModel.model)]
                local box = CreateObject(GetHashKey(model), v.x, v.y, groundZ, false, true, true)

                local boxHeight = GetEntityHeightAboveGround(box)
                local netId = NetworkGetNetworkIdFromEntity(box)
                bigbox = false
                if selectedBoxCategory == "bigbox" then
                    bigbox = true
                else
                    bigbox = false
                end
                BoxTable[k] = {
                    netId = netId or 0,
                    objectName = box,
                    changed = false,
                    coords = v,
                    objectCount = k,
                    raised = false,
                    interacted = false,
                    liftbagBool = false,
                    boxHeight = boxHeight,
                    BigBox = bigbox,
                    selectedBoxModel = selectedBoxModel,
                    riseSpeed = selectedBoxModel.riseSpeed or 0.02, -- Default rise speed
                }    
                -- SetEntityCollision(box, false, false)
                FreezeEntityPosition(box, true)
                TriggerServerEvent('zcr_diving:server:syncAddBox', model, v, k, boxHeight, bigbox, selectedBoxModel, CoopDataClient.roomSetting.owneridentifier)
            end
        end
    end
end

RegisterNetEvent('zcr_diving:client:syncCoords', function(coordsTable, areaName)
    if areaName == "seaChanged" then
        currentAreaNameChanced = coordsTable
    elseif areaName == "seaSuitCase" then
        currentAreaNameSuitCase = coordsTable
    elseif areaName == "seaClean" then
        currentAreaNameClean = coordsTable
    end
end)

function SpawnObjectGenerator(areaName, DefaultareaCoords, areaDistance, areaCount)
    if CoopDataClient and CoopDataClient.roomSetting and CoopDataClient.roomSetting.startJob then
        local modelNames
        if areaName == 'seaChanged' then
            currentAreaNameChanced = GetRandomCoordInCircle(DefaultareaCoords, areaDistance, areaCount)
            SetNewWaypoint(currentAreaNameChanced[1].x,currentAreaNameChanced[1].y)
            TriggerServerEvent('zcr_diving:server:syncSettings', "waypoint", currentAreaNameChanced[1].x,currentAreaNameChanced[1].y, CoopDataClient.roomSetting.owneridentifier)
            TriggerServerEvent('zcr_diving:server:syncCoords', currentAreaNameChanced, areaName)
            for k, v in pairs(currentAreaNameChanced) do
                modelNames = CoopDataClient.roomSetting.Mission.ChangeModel['BeforeModel']
                local modelName = modelNames[math.random(#modelNames)]
                RequestModel(GetHashKey(modelName))
                while not HasModelLoaded(GetHashKey(modelName)) do
                    Citizen.Wait(0)
                end
                local object = CreateObject(GetHashKey(modelName), v.x, v.y, v.z, false, true, true)
                --local netId = NetworkGetNetworkIdFromEntity(object)
                seaChangedTable[k] = {
                    netId = netId or 0,
                    objectName = object,
                    changed = false,
                    coords = v,
                    objectCount = k,
                }


                FreezeEntityPosition(object, true)
                SetEntityHeading(object, v.w or 0)
                CoralChanged(seaChangedTable[k], k, areaName)
                TriggerServerEvent('zcr_diving:server:syncAddObject', netId,  modelName, v, areaName, CoopDataClient.roomSetting.owneridentifier, k)
            end
        elseif areaName == 'seaSuitCase' then
            currentAreaNameSuitCase = GetRandomCoordInCircle(DefaultareaCoords, areaDistance, areaCount)
            TriggerServerEvent('zcr_diving:server:syncCoords', currentAreaNameSuitCase, areaName)
            for k, v in pairs(currentAreaNameSuitCase) do
                modelNames = CoopDataClient.roomSetting.Mission.SuitCaseModel['BeforeModel']
                local modelName = modelNames[math.random(#modelNames)]
                RequestModel(GetHashKey(modelName))
                while not HasModelLoaded(GetHashKey(modelName)) do
                    Citizen.Wait(0)
                end
                local object = CreateObject(GetHashKey(modelName), v.x, v.y, v.z, false, true, true)
                SetEntityRotation(object, 270.0, 0.0, v.w or 0)
                seaSuitCaseTable[k] = {
                    netId = netId or 0,
                    objectName = object,
                    changed = false,
                    claimed = false,
                    coords = v,
                    objectCount = k,
                }

                FreezeEntityPosition(object, true)
                DrawTextForObjectSuitCase(seaSuitCaseTable[k], k, areaName)
                TriggerServerEvent('zcr_diving:server:syncAddObject', netId,  modelName, v, areaName, CoopDataClient.roomSetting.owneridentifier, k)
    
            end
        elseif areaName == 'seaClean' then

            currentAreaNameClean = GetRandomCoordInCircle(DefaultareaCoords, areaDistance, areaCount)
            TriggerServerEvent('zcr_diving:server:syncCoords', currentAreaNameClean, areaName)
            for k, v in pairs(currentAreaNameClean) do
                modelNames = CoopDataClient.roomSetting.Mission.CleanModel[1]
                local modelName = modelNames[math.random(#modelNames)]
                RequestModel(GetHashKey(modelName))
                while not HasModelLoaded(GetHashKey(modelName)) do
                    Citizen.Wait(0)
                end
                local object = CreateObject(GetHashKey(modelName), v.x, v.y, v.z, false, true, true)
                seaCleanTable[k] = {
                    netId = netId or 0,
                    objectName = object,
                    changed = false,
                    coords = v,
                    objectCount = k,
                }
                FreezeEntityPosition(object, true)
                SetEntityRotation(object, 90.0, 0.0, 0.0, 2, true)
                CleanTrash(seaCleanTable[k], k, areaName)
                TriggerServerEvent('zcr_diving:server:syncAddObject', netId,  modelName, v, areaName, CoopDataClient.roomSetting.owneridentifier, k)
            end
        end

    end
end

function MoveBoxUp(obj)
    if obj == nil or obj.raised then
        return
    end

    local targetZ = 0.5 -- Deniz seviyesi
    Citizen.CreateThread(function()
        while true do
            local waitTime = 0
            Citizen.Wait(waitTime)

            local boxCoords = GetEntityCoords(obj.objectName)
            if boxCoords.z >= targetZ then
                FreezeEntityPosition(obj.objectName, true)
                SetEntityCollision(obj.objectName, true, true)
                
                obj.raised = true
                break
            end
            -- Yükselme hızını ayarla
            local newZ = boxCoords.z + obj.riseSpeed
            SetEntityCoords(obj.objectName, boxCoords.x, boxCoords.y, newZ, false, false, false, true)
        end
    end)
end

function GetRandomBoxModel(boxModels)
    local bigboxWeight = boxModels.bigbox.random
    local smallboxWeight = boxModels.smallbox.random

    local totalWeight = bigboxWeight + smallboxWeight
    local randomWeight = math.random() * totalWeight

    if randomWeight < bigboxWeight then
        return boxModels.bigbox, "bigbox"
    else
        return boxModels.smallbox, "smallbox"
    end
end

RegisterNetEvent('zcr_diving:client:syncDeleteObject', function(areaName, objectCount)
    if areaName == "seaSuitCase" then
        if seaSuitCaseTable[objectCount] then
            local object = seaSuitCaseTable[objectCount].objectName
            if DoesEntityExist(object) then
                DeleteEntity(object)
            end
            seaSuitCaseTable[objectCount].claimed = true
        end
    elseif areaName == "seaClean" then
        if seaCleanTable[objectCount] then
            local object = seaCleanTable[objectCount].objectName
            if DoesEntityExist(object) then
                DeleteEntity(object)
            end
            seaCleanTable[objectCount].changed = true
        end
    elseif areaName == 'shipBox' then
        if ShipBoxTable[objectCount] then
            local object = ShipBoxTable[objectCount].objectName
            if DoesEntityExist(object) then
                DeleteEntity(object)
            end
            ShipBoxTable[objectCount].changed = true
        end
    end
end)

RegisterNetEvent('zcr_diving:client:syncInteracted', function(objectName, objectCount)
    for i, obj in pairs(BoxTable) do
        if obj == nil then
            break
        end
        if obj.objectCount == objectCount then
            MoveBoxUp(obj)
            obj.interacted = true
            break
        end
    end
end)

RegisterNetEvent('zcr_diving:client:syncAddLiftBag', function(boxObject, coords, objectCount, liftHeight, boxHeight)
    if LiftTable[objectCount] then
        return
    else
        for k, v in pairs(BoxTable) do
            if v.objectCount == objectCount then
                local liftbag = CreateObject(GetHashKey("liftbag"), coords.x, coords.y, coords.z + v.boxHeight, false, true, true)
                SetEntityRotation(liftbag, 90.0, 0.0, 0.0, 2, true)
                SetEntityCollision(liftbag, false, true)
                AttachEntityToEntity(liftbag, v.objectName, 0, 0.0, 0.0, v.selectedBoxModel.liftHeight, 90.0, 0.0, 0.0, true, true, false, true, 0, true)
                LiftTable[objectCount] = {
                    netId = netId or 0,
                    objectName = liftbag,
                    changed = false,
                    coords = coords,
                    objectCount = objectCount,
                    liftHeight = v.selectedBoxModel.liftHeight,
                }
                BoxTable[objectCount].liftbagBool = true
                BoxTable[objectCount].liftbagModel = liftbag
                BoxTable[objectCount].liftbagCount = objectCount
            end
        end

    end
end)

RegisterNetEvent('zcr_diving:client:syncAddBox', function(modelName, coords, objectCount, boxHeight, bigbox, selectedBoxModel)
    if BoxTable[objectCount] then
        return
    else
        local box = CreateObject(GetHashKey(modelName), coords.x, coords.y, coords.z, false, true, true)
        local netId = NetworkGetNetworkIdFromEntity(box)
        BoxTable[objectCount] = {
            netId = netId or 0,
            objectName = box,
            changed = false,
            coords = coords,
            objectCount = objectCount,
            raised = false,
            interacted = false,
            liftbagBool = false,
            boxHeight = boxHeight,
            BigBox = bigbox,
            selectedBoxModel = selectedBoxModel,
            riseSpeed = selectedBoxModel.riseSpeed or 0.02, -- Default rise speed
        }
        FreezeEntityPosition(box, true)
    end
end)

RegisterNetEvent('zcr_diving:client:syncAddObjectShip', function(modelName, boneIndex, crateOffsetX, crateOffsetY, crateOffsetZ, objectCount)
    if ShipBoxTable[objectCount] then
        return
    else
        if not DoesEntityExist(clientvehInfo) then
            local vehicleData =  TriggerCallback('zcr_diving:server:getVehicleData', CoopDataClient.roomSetting.owneridentifier)
            if vehicleData then
                clientvehInfo = NetToVeh(vehicleData)
            end
        end
        local crate = CreateObject(GetHashKey("gr_prop_gr_rsply_crate01a"), 0.0, 0.0, 0.0, false, true, true)
        AttachEntityToEntity(crate, clientvehInfo, boneIndex, crateOffsetX, crateOffsetY, crateOffsetZ, 0.0, 0.0, 0.0, true, true, false, true, 1, true)
        ShipBoxTable[objectCount] = {
            netId = netId or 0,
            objectName = object,
            changed = false,
            coords = coords,
            objectCount = objectCount,
        }
        totalboxnumber = totalboxnumber
    end

end)

RegisterNetEvent('zcr_diving:client:syncAddObject', function(netID, modelName, coords, areaName, objectCount)
    if areaName == "sealooting" then
        if BoxTable[objectCount] then
        else
            local object = CreateObject(GetHashKey(modelName), coords.x, coords.y, coords.z, false, true, true)
            BoxTable[objectCount] = {
                netId = netId or 0,
                objectName = object,
                changed = false,
                coords = coords,
                objectCount = objectCount,
            }
            FreezeEntityPosition(object, true)
            SetEntityHeading(object, coords.w)
        end
    elseif areaName == "seaChanged" then
        if seaChangedTable[objectCount] then
        else
            local object = CreateObject(GetHashKey(modelName), coords.x, coords.y, coords.z, false, true, true)
            seaChangedTable[objectCount] = {
                netId = netId or 0,
                objectName = object,
                changed = false,
                coords = coords,
                objectCount = objectCount,
            }
            
            FreezeEntityPosition(object, true)
            SetEntityHeading(object, coords.w)
            CoralChanged(seaChangedTable[objectCount], objectCount, areaName)
        end
    elseif areaName == "seaSuitCase" then
        if seaSuitCaseTable[objectCount] then
        else
            local object = CreateObject(GetHashKey(modelName), coords.x, coords.y, coords.z , false, true, true)
            seaSuitCaseTable[objectCount] = {
                netId = netId or 0,
                objectName = object,
                changed = false,
                claimed = false,
                coords = coords,
                objectCount = objectCount,
            }
            SetEntityRotation(object, 270.0, 0.0, coords.w or 0)
            FreezeEntityPosition(object, true)
            DrawTextForObjectSuitCase(seaSuitCaseTable[objectCount], objectCount, areaName)
        end
    elseif areaName == "seaClean" then
        if seaCleanTable[objectCount] then
        else
            local object = CreateObject(GetHashKey(modelName), coords.x, coords.y, coords.z , false, true, true)
            seaCleanTable[objectCount] = {
                netId = netId or 0,
                objectName = object,
                changed = false,
                coords = coords,
                objectCount = objectCount,
            }
            FreezeEntityPosition(object, true)
            SetEntityRotation(object, 90.0, 0.0, 0.0, 2, true)
            CleanTrash(seaCleanTable[objectCount], objectCount, areaName)
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        local waitTime = 1000 
        if CoopDataClient and CoopDataClient.roomSetting and CoopDataClient.roomSetting.finishJob then
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            if not DoesEntityExist(clientvehInfo) then
                local vehicleData = TriggerCallback('zcr_diving:server:getVehicleData', CoopDataClient.roomSetting.owneridentifier)
                if vehicleData then
                    clientvehInfo = NetToVeh(vehicleData)
                end
            else
                clientvehInfo = clientvehInfo
            end
            local vehicleCoords = GetEntityCoords(clientvehInfo)
            if CoopDataClient.roomSetting.carDelivered and CoopDataClient.roomSetting.finishJob then
                local jobDeliverCoords = CoopDataClient.roomSetting.Mission.jobDeliverTheVehicleCoords
                jobDeliverCoords = vector3(jobDeliverCoords.x, jobDeliverCoords.y, jobDeliverCoords.z)
                local distance = #(vehicleCoords - vector3(jobDeliverCoords.x, jobDeliverCoords.y, jobDeliverCoords.z))
                
                if distance < 15 then
                    DrawMarker(2, 
                    jobDeliverCoords.x, 
                    jobDeliverCoords.y, 
                    jobDeliverCoords.z + 1.0, 
                    0, -- Rengi (beyaz)
                    0.75, -- Boyutu X
                    0.75, -- Boyutu Y
                    0, 0, 0, 0.5, 0.5, 0.5, 255, 255, 255, 210, 0, 0.10, 0, 0, 0, 0.0, 0)
                    DrawText3D(jobDeliverCoords.x, jobDeliverCoords.y, jobDeliverCoords.z + 1.0, "Deliver Vehicle - [E]")
                    if IsControlJustPressed(0, 38) and IsPedInAnyVehicle(playerPed, false) and distance < 10 then
                        if totalboxnumber == 1 then
                            TriggerServerEvent('zcr_diving:server:LeaveVehicle', CoopDataClient.roomSetting.owneridentifier, 0)
                        else
                            totalboxnumber = totalboxnumber - 1
                            NuiMessage('showProgressBar', { label = totalboxnumber .. " Pieces of boxes being delivered", time = tonumber(totalboxnumber) or 2 })
                            Citizen.Wait(totalboxnumber * 2100)
                            TriggerServerEvent('zcr_diving:server:LeaveVehicle', CoopDataClient.roomSetting.owneridentifier, totalboxnumber)
                        end
                        break
                    end
                    waitTime = 0
                end
            end
        end
        Citizen.Wait(waitTime)
    end
end)


function CoralChanged(TABLE, k, areaName)
    Citizen.CreateThread(function()
        while true do
            local waitTime = 1000
            if CoopDataClient and CoopDataClient.roomSetting and CoopDataClient.roomSetting.startJob then
                local playerPed = PlayerPedId()
                local coords = GetEntityCoords(playerPed)
                local distance = #(coords - vector3(TABLE.coords.x, TABLE.coords.y, TABLE.coords.z))
                if distance < 45 and TABLE.changed ~= true then
                    waitTime = 0
                    DrawMarker(2, TABLE.coords.x, TABLE.coords.y, TABLE.coords.z + 4.0, 0, 0.50, 0, 0, 0, 0, 0.5, 0.5, 0.5, 255, 255, 255, 210, 0, 0.10, 0, 0, 0, 0.0, 0)                       
                    if distance < 2.2 and TABLE.changed ~= true then
                        DrawText3D(TABLE.coords.x, TABLE.coords.y, TABLE.coords.z + 2.0, "Take Coral - [E]")
                        if IsControlJustPressed(0, 38) then
                            if not TABLE.changed then
                                NuiMessage('showProgressBar', { label = "Take Coral", time = 2 })
                                ChangeObjectAnim('coral')
                                Citizen.Wait(2800)
                                if TABLE.changed then
                                    return
                                end
                                ClearPedTasks(playerPed)
                            end
                            DeleteEntity(TABLE.objectName)
                            local modelNames = CoopDataClient.roomSetting.Mission.ChangeModel['AfterModel']
                            local newModelName = modelNames[math.random(#modelNames)]
                            local object = CreateObject(GetHashKey(newModelName), TABLE.coords.x, TABLE.coords.y, TABLE.coords.z, false, true, true)
                            TABLE = nil
                            TABLE = {
                                netId = netId or 0,
                                objectName = object,
                                changed = true,
                                coords = seaChangedTable[k].coords,
                                objectCount = k,
                            }
                            seaChangedTable[k] = TABLE
                            FreezeEntityPosition(object, true)
                            SetEntityHeading(object, TABLE.coords.w)
                            TriggerServerEvent('zcr_diving:server:deleteModel', "seaChanged", CoopDataClient.roomSetting.owneridentifier)
                            TriggerServerEvent('zcr_diving:server:syncChangeObject', netId, newModelName, TABLE.coords, areaName, CoopDataClient.roomSetting.owneridentifier, k)
                            TriggerServerEvent('zcr_diving:server:addItem', 'coral')
                        end
                    end
                end
            end
            Citizen.Wait(waitTime)
        end
    end)
end

function DrawTextForObjectSuitCase(TABLE, k, areaName, onlyDelete)
    Citizen.CreateThread(function()
        while true do
            local waitTime = 1000
            if CoopDataClient and CoopDataClient.roomSetting then
                local playerPed = PlayerPedId()
                local coords = GetEntityCoords(playerPed)
                local distance = #(coords - vector3(TABLE.coords.x, TABLE.coords.y, TABLE.coords.z))
                if distance < 2.2 and TABLE.changed ~= true then
                    DrawText3D(TABLE.coords.x, TABLE.coords.y, TABLE.coords.z + 1.0, "Open Suitcase - [E]")
                    waitTime = 0
                    if distance < 2.0 and TABLE.changed ~= true then
                        if IsControlJustPressed(0, 38) then
                            local currentWeapon = GetSelectedPedWeapon(PlayerPedId())
                            local knifeHash = GetHashKey("WEAPON_KNIFE")
                            if currentWeapon == knifeHash then
                                if not TABLE.changed then
                                    NuiMessage('showProgressBar', { label = "Open SuitCase", time = 2 })
                                    ChangeObjectAnim('suitCase')
                                    Citizen.Wait(2800)
                                    if TABLE.changed then
                                        return
                                    end
                                    DeleteEntity(TABLE.objectName)
                                    Wait(100)

                                    local modelNames = CoopDataClient.roomSetting.Mission.SuitCaseModel["AfterModel"]
                                    local newModelName = modelNames[math.random(#modelNames)]
                                    local object = CreateObject(GetHashKey(newModelName), TABLE.coords.x, TABLE.coords.y, TABLE.coords.z, false, true, true)
                                    TABLE.objectName = object
                                    TABLE.changed = true
                                    seaSuitCaseTable[k] = TABLE
                                    FreezeEntityPosition(object, true)
                                    SetEntityHeading(object, TABLE.coords.w)
                                    LootSuitCase(seaSuitCaseTable[k], k, areaName)
                                    TriggerServerEvent('zcr_diving:server:syncChangeObject', netId, newModelName, TABLE.coords, areaName, CoopDataClient.roomSetting.owneridentifier, k)
                                end
                            else
                                sendNotification(Config.NotificationText['needKnife'].text, Config.NotificationText['needKnife'].type)
                            end
                        end
                    end
                end
            end
            Citizen.Wait(waitTime)
        end
    end)
end

function LootSuitCase(TABLE, k, areaName)
    Citizen.CreateThread(function()
        while true do
            local waitTime = 1000
            if CoopDataClient and CoopDataClient.roomSetting then
                local playerPed = PlayerPedId()
                local coords = GetEntityCoords(playerPed)
                local distance = #(coords - vector3(TABLE.coords.x, TABLE.coords.y, TABLE.coords.z))
                
                if distance < 4.0 and TABLE.changed == true and TABLE.claimed == false then
                    waitTime = 0
                    DrawText3D(TABLE.coords.x, TABLE.coords.y, TABLE.coords.z + 1.0, "Loot SuitCase - [E]")
                    if distance < 3.0 and TABLE.changed == true and TABLE.claimed == false then
                        if IsControlJustPressed(0, 38) then
                            NuiMessage('showProgressBar', { label = "Loot SuitCase", time = 2 })
                            ChangeObjectAnim('suitCase')
                            Citizen.Wait(2800)
                            TABLE.claimed = true
                            if TABLE.objectName then
                                DeleteEntity(TABLE.objectName)
                            end
                            TriggerServerEvent('zcr_diving:server:addItem', 'suitCase')
                            TriggerServerEvent('zcr_diving:server:syncDeleteObject', areaName, CoopDataClient.roomSetting.owneridentifier, k)
                            TABLE.claimed = true
                            seaSuitCaseTable[k] = TABLE
                        end
                    end
                end
            end
            Citizen.Wait(waitTime)
        end
    end)
end

function CleanTrash(TABLE, k, areaName)
    Citizen.CreateThread(function()
        while true do
            local waitTime = 1000
            if CoopDataClient and CoopDataClient.roomSetting and CoopDataClient.roomSetting.startJob then
                local playerPed = PlayerPedId()
                local coords = GetEntityCoords(playerPed)
                local distance = #(coords - vector3(TABLE.coords.x, TABLE.coords.y, TABLE.coords.z))
                if distance < 45 and TABLE.changed ~= true then
                    waitTime = 0
                    DrawMarker(2, TABLE.coords.x, TABLE.coords.y, TABLE.coords.z + 1.0, 0, 0.50, 0, 0, 0, 0, 0.5, 0.5, 0.5, 255, 255, 255, 210, 0, 0.10, 0, 0, 0, 0.0, 0)                       
                    if distance < 3 and TABLE.changed ~= true then
                        DrawText3D(TABLE.coords.x, TABLE.coords.y, TABLE.coords.z + 1.0, "Collect Trash - [E]")
                        if IsControlJustPressed(0, 38) then
                            if TABLE and  not TABLE.changed then
                                NuiMessage('showProgressBar', { label = "Collect Clean", time = 2 })
                                ChangeObjectAnim('trash')
                                Citizen.Wait(2800)
                                if TABLE.changed then
                                    return
                                end
                                ClearPedTasks(playerPed)
                                Wait(100)
                                if TABLE.objectName then
                                    DeleteEntity(TABLE.objectName)
                                end
                                TABLE.changed = true
                                TABLE = seaCleanTable[k]
                                TriggerServerEvent('zcr_diving:server:deleteModel', "seaClean", CoopDataClient.roomSetting.owneridentifier)
                                TriggerServerEvent('zcr_diving:server:syncDeleteObject', areaName, CoopDataClient.roomSetting.owneridentifier, k)
                                TriggerServerEvent('zcr_diving:server:addItem', 'trash')
                            end
                        end
                    end
                end
            end
            Citizen.Wait(waitTime)
        end
    end)
end

RegisterNetEvent('zcr_diving:client:FinishJob', function(jobData)
    CoopDataClient = jobData
    if CoopDataClient.roomSetting.carDelivered then 
        sendNotification(Config.NotificationText['deliverVehile'].text, Config.NotificationText['deliverVehile'].type)
        clearBlip()
        local blip = AddBlipForCoord(CoopDataClient.roomSetting.Mission.jobDeliverTheVehicleCoords.x, CoopDataClient.roomSetting.Mission.jobDeliverTheVehicleCoords.y)
        SetBlipSprite (blip, 1)
        SetBlipColour (blip, 29)
        SetBlipScale  (blip, 0.8)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString("DELIVER VEHICLE")
        EndTextCommandSetBlipName(blip)
        table.insert(FinishBlip, blip)
        SetNewWaypoint(CoopDataClient.roomSetting.Mission.jobDeliverTheVehicleCoords.x, CoopDataClient.roomSetting.Mission.jobDeliverTheVehicleCoords.y)
        TriggerServerEvent('zcr_diving:server:syncSettings', "waypoint",CoopDataClient.roomSetting.Mission.jobDeliverTheVehicleCoords.x, CoopDataClient.roomSetting.Mission.jobDeliverTheVehicleCoords.y, CoopDataClient.roomSetting.owneridentifier)
    end
end)

RegisterNetEvent('zcr_diving:client:LeaveVehicle', function(scoreAmount, awards)
    local playerPed = PlayerPedId()
    for k,v in pairs(BoxTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        BoxTable = {}
    end
    deleteGear()

    for k,v in pairs(seaChangedTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        seaChangedTable = {}
    end
    for k,v in pairs(seaCleanTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        seaCleanTable = {}
    end
    for k,v in pairs(seaSuitCaseTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        seaSuitCaseTable = {}
    end

    for k,v in pairs(LiftTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        LiftTable = {}
    end
    for k,v in pairs(ShipBoxTable) do 
        DetachEntity(v.objectName, true, true)
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        ShipBoxTable = {}
    end
    local playerData = TriggerCallback('zcr_diving:server:getPlayerData')
    local finishjobData = {
        name = playerData.name,
        scoreAmount = scoreAmount,
        money = awards.money,
        xp = awards.xp,
    }
    NuiMessage('FINISH_JOB', finishjobData)
    if clientvehInfo then
        DeleteEntity(clientvehInfo)
    end
    CoopDataClient = {}
    joobTaskClient = {}
    blip = {}
    ChangedBlip = {}
    CleanBlip = {}  
    ShipBoxTable = {}
    currentAreaNameChanced = {}
    currentAreaNameClean = {}
    currentAreaNameSuitCase = {}
    VehBlip = {}

    for i = 1, #VehBlip do
        RemoveBlip(VehBlip[i])
    end
    clearBlip()
    SetEnableScuba(PlayerPedId(), false)
    SetEnableScubaGearLight(PlayerPedId(), false)
    SetPedMaxTimeUnderwater(PlayerPedId(), 1.00)
end)

clearBlip = function()
    for i = 1, #blips do
        RemoveBlip(blips[i])
    end
    blips = {}
    for i = 1, #ChangedBlip do
        RemoveBlip(ChangedBlip[i])
    end

    for i = 1, #CleanBlip do
        RemoveBlip(CleanBlip[i])
    end
    for i = 1, #VehBlip do
        RemoveBlip(VehBlip[i])
    end

    VehBlip = {}
    blip = {}
    ChangedBlip = {}
    CleanBlip = {}


end

RegisterNetEvent('zcr_diving:client:syncChangeObject', function(netId, modelName, coords, areaName, objectCount, onylDelete)
    if areaName == "seaChanged" then
        if seaChangedTable[objectCount] then
            if seaChangedTable[objectCount].changed then
                --zaten silindi
            else
                DeleteEntity(seaChangedTable[objectCount].objectName)
                seaChangedTable[objectCount].changed = true
                seaChangedTable[objectCount] = nil
                local object = CreateObject(GetHashKey(modelName), coords.x, coords.y, coords.z, false, true, true)
                seaChangedTable[objectCount] = {
                    netId = netId or 0,
                    objectName = object,
                    changed = true,
                    coords = coords,
                    objectCount = objectCount,
                }
                seaChangedTable[objectCount].changed = true
                FreezeEntityPosition(object, true)
                SetEntityHeading(object, coords.w)
            end
        end

    elseif areaName == "seaSuitCase" then
        if seaSuitCaseTable[objectCount] then
            if seaSuitCaseTable[objectCount].changed then
            else
                if seaSuitCaseTable[objectCount].objectName then
                    DeleteEntity(seaSuitCaseTable[objectCount].objectName)
                end
                seaSuitCaseTable[objectCount].changed = true
                local modelNames = CoopDataClient.roomSetting.Mission.SuitCaseModel["AfterModel"]
                local newModelName = modelNames[math.random(#modelNames)]
                local object = CreateObject(GetHashKey(newModelName), coords.x, coords.y, coords.z, false, true, true)
                seaSuitCaseTable[objectCount].objectName = object
                seaSuitCaseTable[objectCount].changed = true
                FreezeEntityPosition(object, true)
                LootSuitCase(seaSuitCaseTable[objectCount], objectCount, areaName)
            end
        end
    end
end)

RegisterNetEvent('zcr_diving:client:resetjob', function()
    deleteGear()
    for k,v in pairs(BoxTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        BoxTable = {}
    end
    deleteGear()
    for k,v in pairs(LiftTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        LiftTable = {}
    end
    for k,v in pairs(TrashTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        TrashTable = {}
    end
    for k,v in pairs(seaCleanTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        seaCleanTable = {}
    end
    for k,v in pairs(seaChangedTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        seaChangedTable = {}
    end
    
    for k,v in pairs(seaSuitCaseTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        seaSuitCaseTable = {}
    end
    for k,v in pairs(ShipBoxTable) do 
        if v.objectName then
            DeleteEntity(v.objectName)
        end
        ShipBoxTable = {}
    end

    for i = 1, #blips do
        RemoveBlip(blips[i])
    end
    blips = {}
    for i = 1, #ChangedBlip do
        RemoveBlip(ChangedBlip[i])
    end
    for i = 1, #CleanBlip do
        RemoveBlip(CleanBlip[i])
    end
    for i = 1, #VehBlip do
        RemoveBlip(VehBlip[i])
    end
    for i = 1, #FinishBlip do
        RemoveBlip(FinishBlip[i])
    end

    for i = 1, #blips do
        RemoveBlip(blips[i])
    end
    blips = {}
    for i = 1, #ChangedBlip do
        RemoveBlip(ChangedBlip[i])
    end

    for i = 1, #CleanBlip do
        RemoveBlip(CleanBlip[i])
    end
    for i = 1, #VehBlip do
        RemoveBlip(VehBlip[i])
    end
    
    for i = 1, #FinishBlip do
        RemoveBlip(FinishBlip[i])
    end

    

    VehBlip = {}
    FinishBlip = {}

    ChangedBlip = {}
    CleanBlip = {}
    CoopDataClient = {}
    joobTaskClient = {}

    SetPedMaxTimeUnderwater(PlayerPedId(), 1.00)

    blip = {}
    ChangedBlip = {}
    CleanBlip = {}
    VehBlip = {}
    FinishBlip = {}
    CoopDataClient = {}
    joobTaskClient = {}
    if clientvehInfo then
        DeleteEntity(clientvehInfo)
    end
    NuiMessage('RESET_JOB')
    SetNuiFocus(false, false)
    SetEnableScuba(PlayerPedId(), false)
    SetEnableScubaGearLight(PlayerPedId(), false)
end)

local liftKey = 38
local crateOffsetX = 2.0
local crateOffsetY = -12.0
local crateOffsetZ = 1.0

Citizen.CreateThread(function()
    local sin, cos, atan2, abs, rad, deg = math.sin, math.cos, math.atan2, math.abs, math.rad, math.deg
    local DEFAULT_OPTIONS = {waitTime=0.5, grappleSpeed=15.0}

    Grapple = {}

    local function DirectionToRotation(dir, roll)
        local x, y, z
        z = -deg(atan2(dir.x, dir.y))
        local rotpos = vector3(dir.z, #vector2(dir.x, dir.y), 0.0)
        x = deg(atan2(rotpos.x, rotpos.y))
        y = roll
        return vector3(x, y, z)
    end

    local function RotationToDirection(rot)
        local rotZ = rad(rot.z)
        local rotX = rad(rot.x)
        local cosOfRotX = abs(cos(rotX))
        return vector3(-sin(rotZ) * cosOfRotX, cos(rotZ) * cosOfRotX, sin(rotX))
    end

    local function RayCastGamePlayCamera(dist)
        local camRot = GetGameplayCamRot()
        local camPos = GetGameplayCamCoord()
        local dir = RotationToDirection(camRot)
        local dest = camPos + (dir * dist)
        local ray = StartShapeTestRay(camPos, dest, 17, -1, 0)
        local _, hit, endPos, surfaceNormal, entityHit = GetShapeTestResult(ray)
        if hit == 0 then endPos = dest end
        return hit, endPos, entityHit, surfaceNormal
    end

    function GrappleCurrentAimPoint(dist)
        return RayCastGamePlayCamera(dist)
    end

    local function PinRope(rope, target, dest)
        PinRopeVertex(rope, 0, dest)
        PinRopeVertex(rope, GetRopeVertexCount(rope) - 1, GetEntityCoords(target))
    end

    function Grapple.new(dest, target, options, boxId, liftbagId)
        if graplleTarget ~= false then
            return
        end
        
        graplleTarget = true
        if not dest or not target then return end
        Grapple.ropeList = {}
        local self = {}
        options = options or {}
        local grappleId = math.random((-2^32)+1, 2^32-1)
        local start = dest
        local fromStartToDest = dest - start
        local dir = fromStartToDest / #fromStartToDest
        local length = #fromStartToDest
        local finished = false
        local rope
    
        RopeLoadTextures() -- load rope fix
        rope = AddRope(dest, 0.0, 0.0, 0.0, 0.0, 5, 0.0, 0.0, 2.0, false, false, false, 5.0, false)
    
        function self._handleRope(rope, target, dest)
            Citizen.CreateThread(function()
                while not finished do
                    PinRope(rope, target, dest)
                    Wait(0)
                end
                DeleteChildRope(rope)
                DeleteRope(rope)
                rope = nil
            end)
        end

        function self.activateSync()
            local distTraveled = 0.0
            local currentPos = start
            Wait(options.waitTime * 1000)
            while not finished and distTraveled < length do
                local fwdPerFrame = dir * options.grappleSpeed * GetFrameTime()
                distTraveled = distTraveled + #fwdPerFrame
                if distTraveled > length then
                    distTraveled = length
                    currentPos = dest
                else
                    currentPos = currentPos + fwdPerFrame
                end
                SetEntityCoords(target, currentPos)
                TriggerServerEvent('grapple:server:update', NetworkGetNetworkIdFromEntity(target), currentPos)
                if distTraveled >= length then
                    finished = true
                end
                Wait(0)
            end
            self.destroy()
        end
    
        function self.activate()
            CreateThread(self.activateSync)
        end
    
        function self.destroy()
            SetEntityCollision(target, false, true)
            FreezeEntityPosition(target, false)
    
            local hareketHizi = 0.05
            while true do
                if CoopDataClient then
                    if not DoesEntityExist(clientvehInfo) then
                        local vehicleData =  TriggerCallback('zcr_diving:server:getVehicleData', CoopDataClient.roomSetting.owneridentifier)
                        if vehicleData then
                            clientvehInfo = NetToVeh(vehicleData)
                        end
                    else
                        clientvehInfo = clientvehInfo
                    end
                end

                local pedCoords = GetEntityCoords(clientvehInfo) or GetEntityCoords(PlayerPedId())
                local objeKoordinat = GetEntityCoords(target)
                local distanceX = math.abs(pedCoords.x - objeKoordinat.x)
                local distanceY = math.abs(pedCoords.y - objeKoordinat.y)
                if distanceX < 1.0 and distanceY < 1.0 then
                    table.insert(Grapple.ropeList, rope)
            
                    for i, rope in ipairs(Grapple.ropeList) do
                        DeleteChildRope(rope)
                        DeleteRope(rope)
                    end
                    Grapple.ropeList = {} 
                    DeleteEntity(target)
                    DeleteEntity(LiftTable[liftbagId].objectName)
                    if BoxTable[boxId].liftbagModel then
                        DeleteObject(BoxTable[boxId].liftbagModel)
                    end
                    BoxTable[boxId] = nil
                    LiftTable[liftbagId] = nil
                    self._handleRope(rope, target, dest)
                    finished = true
                    graplleTarget = false
                    attempts = 0
                    TriggerServerEvent('grapple:server:end', NetworkGetNetworkIdFromEntity(target))
                    if totalboxnumber < 49 then
                        if not DoesEntityExist(clientvehInfo) then
                            local vehicleData =  TriggerCallback('zcr_diving:server:getVehicleData', CoopDataClient.roomSetting.owneridentifier)
                            if vehicleData then
                                clientvehInfo = NetToVeh(vehicleData)
                            end
                        end
                        local crate = CreateObject(GetHashKey("gr_prop_gr_rsply_crate01a"), 0.0, 0.0, 0.0, false, true, true)
                        AttachEntityToEntity(crate, clientvehInfo, 0, crateOffsetX, crateOffsetY, crateOffsetZ, 0.0, 0.0, 0.0, true, true, false, true, 1, true)
                        ShipBoxTable[totalboxnumber] = {
                            objectName = crate,
                            changed = false,
                            coords = GetEntityCoords(crate),
                            objectCount = totalboxnumber,
                        }
                        TriggerServerEvent('zcr_diving:server:syncAddObjectShip', crate, boneIndex,  crateOffsetX, crateOffsetY, crateOffsetZ, totalboxnumber, CoopDataClient.roomSetting.owneridentifier)
                        if totalboxnumber % 6 == 0 and totalboxnumber % 24 ~= 0 then
                            crateOffsetX = 2.0
                            crateOffsetZ = crateOffsetZ + 0.1
                            crateOffsetY = crateOffsetY - 0.8
                        elseif totalboxnumber % 24 == 0 then
                            crateOffsetX = 2.0
                            crateOffsetZ = 1.6
                            crateOffsetY = -12.0
                        else
                            crateOffsetX = crateOffsetX - 0.8 
                        end
                        totalboxnumber = totalboxnumber + 1
                    end
                    break
                end
            
                local farkX = objeKoordinat.x - pedCoords.x
                local farkY = objeKoordinat.y - pedCoords.y
            
                local mesafe = math.sqrt(farkX^2 + farkY^2) 
                local normalizeX = farkX / mesafe
                local normalizeY = farkY / mesafe
            
                objeKoordinat = vector3(
                    objeKoordinat.x - normalizeX * hareketHizi,
                    objeKoordinat.y - normalizeY * hareketHizi,
                    objeKoordinat.z
                )
            
                SetEntityCoords(target, objeKoordinat.x, objeKoordinat.y, objeKoordinat.z, false, false, false, false)
            
                Citizen.Wait(10)
            end
        end
    
        self._handleRope(rope, target, dest)
    
        return self
    end

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(0)
            local playerCoords = GetEntityCoords(PlayerPedId())
            if CoopDataClient and CoopDataClient.roomSetting then
                for i, obj in pairs(BoxTable) do
                    if obj == nil then
                        break
                    end
                    local boxCoords = GetEntityCoords(obj.objectName)
                    local distance = #(playerCoords - boxCoords)
                    if distance < 50.0 then
                        if not obj.interacted and obj.liftbagBool and distance < 2.0 then
                            DrawText3D(boxCoords.x, boxCoords.y, boxCoords.z + 1.0, "Raise Box - [E]")
    
                            if IsControlJustPressed(0, liftKey) and not obj.interacted then
                                obj.interacted = true
                                MoveBoxUp(obj)
                                TriggerServerEvent('zcr_diving:server:syncInteracted', obj.objectName, obj.objectCount, CoopDataClient.roomSetting.owneridentifier)
                            end
                        elseif obj.raised and graplleTarget == false then
                            local hit, pos, entityHit, _ = GrappleCurrentAimPoint(4000)
                            if hit == 1 and entityHit == obj.objectName and not IsPedSwimming(PlayerPedId()) and not IsPedInAnyVehicle(PlayerPedId()) then
                                DrawText3D(boxCoords.x, boxCoords.y, boxCoords.z + 1.0, "Throw Rope - [E]")
                            end
                            if IsControlJustPressed(0, 38) and not IsPedSwimming(PlayerPedId()) and not IsPedInAnyVehicle(PlayerPedId()) and entityHit == obj.objectName then
                                local anchorstate = TriggerCallback('zcr_diving:server:getVehicleAnchor', CoopDataClient.roomSetting.owneridentifier)
                                anchorstate = anchorstate or "true"
                                if anchorstate  == "true" then
                                    if graplleTarget == false then
                                        local grapple
                                        if attempts < 1 then
                                            if not DoesEntityExist(clientvehInfo) then
                                                local vehicleData =  TriggerCallback('zcr_diving:server:getVehicleData', CoopDataClient.roomSetting.owneridentifier)
                                                if vehicleData then
                                                    clientvehInfo = NetToVeh(vehicleData)
                                                    local clientvehInfoCoords = GetEntityCoords(clientvehInfo)
                                                    grapple = Grapple.new(clientvehInfoCoords, obj.objectName, {waitTime = 1.5}, obj.objectCount, obj.liftbagCount)
                                                    sendNotification(Config.NotificationText['towedrope'].text, Config.NotificationText['towedrope'].type)
                                                    TriggerServerEvent('grapple:server:start', clientvehInfoCoords, obj.objectName, {waitTime = 1.5}, obj.objectCount, obj.liftbagCount, CoopDataClient.roomSetting.owneridentifier )
                                                    attempts = attempts + 1
                                                end
                                            else
                                                local clientvehInfoCoords = GetEntityCoords(clientvehInfo)
                                                sendNotification(Config.NotificationText['towedrope'].text, Config.NotificationText['towedrope'].type)
                                                grapple = Grapple.new(clientvehInfoCoords, obj.objectName, {waitTime = 1.5}, obj.objectCount, obj.liftbagCount)
                                                TriggerServerEvent('grapple:server:start', clientvehInfoCoords, obj.objectName, {waitTime = 1.5}, obj.objectCount, obj.liftbagCount, CoopDataClient.roomSetting.owneridentifier )
                                                attempts = attempts + 1
                                            end
    
                                        end
                                        if grapple then
                                            grapple.activate()
                                        end
                                    end
                                else
                                    sendNotification(Config.NotificationText['needAnchor'].text, Config.NotificationText['needAnchor'].type)
                                    sendNotification(Config.NotificationText['pressanchor'].text, Config.NotificationText['pressanchor'].type)
                                end
                            end
                        elseif not obj.interacted and not obj.liftbagBool and distance < 2.0 then
                            DrawText3D(boxCoords.x, boxCoords.y, boxCoords.z + 1.0, "Connect Liftbag - [E]")
                            if IsControlJustPressed(0, liftKey) then
                                local hasItem = exports.ox_inventory:Search('count', 'liftbag')
                                if hasItem > 0 then
                                    if currentGear.oxygen > 0 then
                                        if currentGear.oxygen < 20 then
                                            sendNotification(Config.NotificationText['oxygenLow'].text, Config.NotificationText['oxygenLow'].type)
                                        end
                                        TriggerServerEvent('zcr_diving:server:removeItem', 'liftbag', 1)
                                        currentGear.oxygen = currentGear.oxygen - 2
                                        NuiMessage('OXYGEN_LEVEL', {oxygen = currentGear.oxygen, level = currentGear.level})
                                        FreezeEntityPosition(PlayerPedId(), true)
                                        NuiMessage('showProgressBar', { label = "Connect Liftbag", time = 2 })
                                        ChangeObjectAnim('box')
                                        Citizen.Wait(2800)
                                        local liftbag = CreateObject(GetHashKey("liftbag"), boxCoords.x, boxCoords.y, boxCoords.z + obj.boxHeight, false, true, true)
                                        SetEntityRotation(liftbag, 90.0, 0.0, 0.0, 2, true)
                                        SetEntityCollision(liftbag, false, true)
                                        AttachEntityToEntity(liftbag, obj.objectName, 0, 0.0, 0.0, obj.selectedBoxModel.liftHeight, 90.0, 0.0, 0.0, true, true, false, true, 0, true)
                                        LiftTable[i] = {
                                            objectName = liftbag,
                                            changed = false,
                                            coords = boxCoords,
                                            objectCount = i,
                                        }
                                        obj.liftbagModel = liftbag
                                        obj.liftbagCount = i
                                        obj.liftbagBool = true
                                        TriggerServerEvent('zcr_diving:server:syncAddLiftBag', obj.objectName, boxCoords, i, obj.selectedBoxModel.liftHeight, obj.boxHeight, CoopDataClient.roomSetting.owneridentifier) 
                                        FreezeEntityPosition(PlayerPedId(), false)
                                    else
                                        sendNotification(Config.NotificationText['needoxygen'].text, Config.NotificationText['needoxygen'].type)
                                    end
                                else
                                    sendNotification(Config.NotificationText['needoliftbag'].text, Config.NotificationText['needoliftbag'].type)
                                end
                            end
                        end
                    end
                end
            end

        end
    end)

    RegisterNetEvent('grapple:client:start')
    AddEventHandler('grapple:client:start', function(destination, target, options, boxId, liftbagId)
        for k, v in pairs(BoxTable) do
            if boxId == v.objectCount then
                local grapple = Grapple.new(destination, v.objectName, {waitTime = 1.5}, v.objectCount, v.liftbagCount)
                if grapple then
                    grapple.activate()
                end
            end
        end

    end)

    RegisterNetEvent('grapple:client:update')
    AddEventHandler('grapple:client:update', function(targetNetId, position)
        local target = NetworkGetEntityFromNetworkId(targetNetId)
        if DoesEntityExist(target) then
            SetEntityCoords(target, position)
        end
    end)

    RegisterNetEvent('grapple:client:end')
    AddEventHandler('grapple:client:end', function(targetNetId)
        local target = NetworkGetEntityFromNetworkId(targetNetId)
        if DoesEntityExist(target) then
            SetEntityCollision(target, false, true)
            FreezeEntityPosition(target, false)
        end
    end)
end)

RegisterCommand(Config.AnchorCommand, function ()
    local playerPed = PlayerPedId()
    local playerVeh = GetVehiclePedIsIn(playerPed, false)
    if not DoesEntityExist(clientvehInfo) then
        local vehicleData =  TriggerCallback('zcr_diving:server:getVehicleData', CoopDataClient.roomSetting.owneridentifier)
        if vehicleData then
            clientvehInfo = NetToVeh(vehicleData)
        end
    end
    if DoesEntityExist(clientvehInfo) and playerVeh == clientvehInfo then
        TriggerServerEvent('zcr_diving:server:syncSettings', "anchor", CoopDataClient.roomSetting.anchor, nil, CoopDataClient.roomSetting.owneridentifier)
    end
end)

RegisterCommand(Config.JobResetCommand, function ()
    if CoopDataClient and CoopDataClient.roomSetting then
        TriggerServerEvent('zcr_diving:server:resetJobButton', CoopDataClient.roomSetting.owneridentifier)
    end
end)