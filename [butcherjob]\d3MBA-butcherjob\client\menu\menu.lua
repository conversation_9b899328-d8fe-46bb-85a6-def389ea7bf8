local Framework = exports['d3MBA-lib']:GetFrameworkObject()

RegisterNetEvent("d3MBA-butcher:client:OpenLaptopMenu", function()
	local data = Framework.TriggerServerCallback('d3-butcherJob:server:getData')
	Wait(50)
	if data == false then 
		Wait(50)
		TriggerEvent("d3MBA-butcher:client:OpenLaptopMenu")
		return
	end 

	local menuHeaderOptions = {
		title = Config.Menu["office"],
		ox_title = Config.Menu["office"] .. " " ..Config.Menu["menu"],
		icon = "fas fa-laptop",
	}

	local menuFooterOptions = {
		title = Config.Menu["exit"],
		icon = "fas fa-circle-xmark", 
	}

	local menuOptions = {}

	table.insert(menuOptions,  {
		title = string.format(Config.Menu["catching_experience_title"], data.catching),
		context = Config.Menu["catching_experience_context"],
		icon = "fas fa-database",
	})
	
	table.insert(menuOptions,  {
		title = string.format(Config.Menu["processing_experience_title"], data.processing),
		context = Config.Menu["processing_experience_context"],
		icon = "fas fa-database",
	})

	if data.current_order == nil and FindingOrder == false then  
		table.insert(menuOptions, {
			title = Config.Menu["find_customers_title"],
			context = Config.Menu["find_customers_context"],
			event = "d3MBA-butcherjob:client:StartDelivery", 
			icon = "fas fa-truck-fast",
		})
	elseif data.current_order ~= nil then
		table.insert(menuOptions, {
			title = Config.Menu["see_order_details_title"],
			context = Config.Menu["see_order_details_context"],
			event = "d3MBA-butcherjob:client:OpenOrderDetailsMenu", 
			icon = "fas fa-box-open",
		})
	end 	

	table.insert(menuOptions,  {
		title = Config.Menu["tips_for_selling_title"],
		context = Config.Menu["tips_for_selling_context"],
		event = "d3MBA-butcher:client:OpenTipsMenu",
		icon = "fas fa-brain",
	})

	TriggerEvent("d3MBA-lib:client:OpenMenu", Framework.Menu, menuHeaderOptions, menuOptions, menuFooterOptions)
end)

-- Order Details Menu
RegisterNetEvent("d3MBA-butcherjob:client:OpenOrderDetailsMenu", function()
	local data = Framework.TriggerServerCallback('d3-butcherJob:server:getData')
	Wait(50)

	-- Ensure current_order is correctly decoded from JSON
	if not data or not data.current_order then
		TriggerEvent('d3MBA-lib:sendNotification', source, "No active order found!", Framework.NotificationsSettings.Error, 5000)
		return
	end

	-- Decode current_order JSON if necessary
	local order = data.current_order and json.decode(data.current_order) or nil

	if not order then
		TriggerEvent('d3MBA-lib:sendNotification', source, "Order data is corrupted or missing!", Framework.NotificationsSettings.Error, 5000)
		return
	end

	local menuHeaderOptions = {
		title = Config.Menu["order_details_title"],
		ox_title = Config.Menu["order_title"],
		icon = "fas fa-box-open",
	}

	local menuFooterOptions = {
		title = Config.Menu["back"],
		event = "d3MBA-butcher:client:OpenOfficeMenu",
		icon = "fas fa-circle-arrow-left", 
	}

	local menuOptions = {}

	-- Convert order.Items to the format expected by the callback
	local itemsToCheck = {}
	for _, item in ipairs(order.Items) do
		itemsToCheck[item.ItemName] = item.Amount
	end

	local hasItems = Framework.TriggerServerCallback("d3MBA-lib:server:CheckPlayerItemsTable", itemsToCheck)
	Wait(50)

	for i = 1, #order.Items do 
		local item = order.Items[i]
		local itemName = item.ItemName

		-- Find the corresponding entry in hasItems
		local hasItem = false
		for j = 1, #hasItems do
			if hasItems[j].ItemName == itemName then
				hasItem = hasItems[j].HasItem
				break
			end
		end

		local symbol = hasItem and "✅" or "❌"

		local itemLabel = Framework.GetItemLabel(itemName)
		local itemAmount = item.Amount

		table.insert(menuOptions, {
			title = string.format(Config.Menu["item_format"], itemLabel, itemAmount) .. " " .. symbol,
			icon = Framework.ConvertImageFormat(Framework.GetItemImg(itemName), 45), 
			image = Framework.ConvertImageFormat(Framework.GetItemImg(itemName), 45),
		})
	end
		
	if Config.Delivery.DistanceMultiplier.Use == true then 
		table.insert(menuOptions, {
			title = string.format(Config.Menu["customer_distance"], math.floor(order.Distance + 0.5)),
			icon = "fas fa-arrows-alt-h",
			disabled = true,
		})
	end 

	table.insert(menuOptions, {
		title = string.format(Config.Menu["total_price"], order.TotalPrice),
		icon = "fas fa-dollar-sign",
		disabled = true,
	})

	TriggerEvent("d3MBA-lib:client:OpenMenu", Framework.Menu, menuHeaderOptions, menuOptions, menuFooterOptions)
end)



RegisterNetEvent("d3MBA-butcher:client:OpenTipsMenu", function()
	local menuHeaderOptions = {
		title = Config.Menu["tips_title"], 
		ox_title = Config.Menu["tips_ox_title"],
		icon = "fas fa-brain",
	}

	local menuFooterOptions = {
		title = Config.Menu["back"],
		icon = "fas fa-circle-arrow-left", 
		event = "d3MBA-butcher:client:OpenLaptopMenu"
	}

	local menuOptions = {}

	table.insert(menuOptions,  {
			title = Config.Menu["sell_directly_title"],
			context = Config.Menu["sell_directly_context"],
			icon = "fas fa-dollar-sign",
	})
	
	table.insert(menuOptions,  {
			title = Config.Menu["deliver_title"],
			context = Config.Menu["deliver_context"],
			icon = "fas fa-dollar-sign",
	})

	TriggerEvent("d3MBA-lib:client:OpenMenu", Framework.Menu, menuHeaderOptions, menuOptions, menuFooterOptions)
end)


RegisterNetEvent("d3MBA-butcher:client:OpenLineControlsMenu", function()
	local menuHeaderOptions = {
        title = Config.Menu["line_controls_title"], 
		ox_title = Config.Menu["controls_title"],  
		icon = "fas fa-cog", 
	}

	local menuFooterOptions = {
		title = Config.Menu["exit"],
		icon = "fas fa-circle-xmark", 
	}

	local menuOptions = {}

	table.insert(menuOptions,  {
		title = string.format(Config.Menu["chickens_on_line"], ChickensOnLine),
		context = Config.Menu["chickens_on_line_context"],
		icon = "fas fa-arrows-alt-h",
	})
	
	table.insert(menuOptions,  {
		title = string.format(Config.Menu["chicken_legs_in_box"], LegsBoxCount),
		context = Config.Menu["chicken_legs_in_box_context"],
		icon = "fas fa-drumstick-bite",
	})
	
	table.insert(menuOptions,  {
		title = string.format(Config.Menu["plucked_chickens_in_meat_box"], MeatBoxCount),
		context = Config.Menu["plucked_chickens_in_meat_box_context"],
		icon = "fas fa-box-open",
	})

	table.insert(menuOptions,  {
		title = Config.Menu["refresh_title"],
		context = Config.Menu["refresh_context"],
		icon = "fas fa-sync",
		event = "d3MBA-butcher:client:OpenLineControlsMenu"
	})

    TriggerEvent("d3MBA-lib:client:OpenMenu", Framework.Menu, menuHeaderOptions, menuOptions, menuFooterOptions)
end)

RegisterNetEvent("d3MBA-butcher:client:OpenGrinderMenu", function(GrinderIndex)
	local menuHeaderOptions = {
        title = Config.Menu["meat_grinder_title"], 
		ox_title = Config.Menu["grinder_title"],  
		icon = "fas fa-cog", 
	}

	local menuFooterOptions = {
		title = Config.Menu["exit"],
		icon = "fas fa-circle-xmark", 
	}

	local menuOptions = {}

	-- Create a sorted keys table
	local sortedKeys = {}

	for k in pairs(Config.GrindingMeat.GrinderMenu) do 
		table.insert(sortedKeys, tonumber(k))
	end

	-- Sort the keys
	table.sort(sortedKeys)

	-- Use the sorted keys to create the menu options
	for _, k in ipairs(sortedKeys) do
		table.insert(menuOptions,  {
			title = string.format(Config.Menu["grind_meat"], k),
			context = Config.Menu["grind_meat_context"], 
			icon = Framework.ConvertImageFormat(Framework.GetItemImg(Config.Items.GroundChicken), 45),
			image = Framework.ConvertImageFormat(Framework.GetItemImg(Config.Items.GroundChicken), 45), -- 45 is the image size
			event = "d3MBA-butcher:client:GrindMeatMenu",
			args = {
				GrinderIndex = GrinderIndex,
				MeatAmount = k
			}
		})
	end 

    TriggerEvent("d3MBA-lib:client:OpenMenu", Framework.Menu, menuHeaderOptions, menuOptions, menuFooterOptions)
end)

-- Grinding Meat Menu Item Check
RegisterNetEvent("d3MBA-butcher:client:GrindMeatMenu", function(data)
	local menuHeaderOptions = {
		title = Config.Menu["meat_grinder_title"], 
		ox_title = Config.Menu["grinder_title"],  
		icon = "fas fa-cog", 
	}

	local menuFooterOptions = {
		title = Config.Menu["back"],
		event = "d3MBA-butcher:client:OpenGrinderMenu",
		args = data.grinderCoords,
		icon = "fas fa-circle-arrow-left", 
	}

	local menuOptions = {}

	table.insert(menuOptions, {
		title = string.format(Config.Menu["start_grinding_meat"], data.MeatAmount),
		icon = "fas fa-power-off", 
		event = "d3MBA-butcher:server:GrindMeat", 
		isServer = true, 
		args = {
			grinderCoords = data.grinderCoords,
			MeatAmount = data.MeatAmount,
		}
	})

	local hasItems = Framework.TriggerServerCallback("d3MBA-lib:server:CheckPlayerItemsTable", Config.GrindingMeat.GrinderMenu[tostring(data.MeatAmount)])
	Wait(50)

	for i = 1, #hasItems do 
		local symbol = hasItems[i].HasItem and "✅" or "❌"

		table.insert(menuOptions, {
			title = Framework.GetItemLabel(hasItems[i].ItemName) .. " - " .. hasItems[i].Amount .. "x - " .. symbol,
			icon = Framework.ConvertImageFormat(Framework.GetItemImg(hasItems[i].ItemName), 45), -- 45 is the image size
			image = Framework.ConvertImageFormat(Framework.GetItemImg(hasItems[i].ItemName), 45), -- 45 is the image size
		})
	end 

	TriggerEvent("d3MBA-lib:client:OpenMenu", Framework.Menu, menuHeaderOptions, menuOptions, menuFooterOptions)
end)

-- Instant sell menu 
RegisterNetEvent("d3MBA-butcher:client:OpenInstantSellMenu", function()
    local prices = Framework.TriggerServerCallback("d3MBA-butcher:server:GetDynamicPrices")
	Wait(20)

    local menuHeaderOptions = {
        title = Config.Menu["butcher_shop_title"], 
        ox_title = Config.Menu["butcher_shop_menu"],  
    }

    local menuFooterOptions = {
        title = Config.Menu["exit"],
        icon = "fas fa-circle-xmark", 
    }

    local menuOptions = {}


	if Config.InstantSell.DisableQuickAllSell == false then 
		table.insert(menuOptions, {
			title = Config.Menu["quick_sell_all"],
			context = Config.Menu["quick_sell_all_context"],
			icon = "fas fa-dollar-sign",
			event = "d3MBA-butcher:client:InstantSellAllItems",
		})
	end

    -- Create a table to hold the items we want to check
    local itemsToCheck = {}
    local ShopItems = Config.InstantSell.SellMenu
    for i = 1, #ShopItems do  
        itemsToCheck[ShopItems[i].ItemName] = 1 -- We check if the player has at least 1 of each item
    end

    -- Use a callback to check the player's items all at once
    local hasItems = Framework.TriggerServerCallback("d3MBA-lib:server:CheckPlayerItemsTable", itemsToCheck)
    Wait(50)

    -- Build the menu options
    for i = 1, #ShopItems do  
        local item = ShopItems[i]
        local itemName = item.ItemName
		local price = prices[itemName] or item.Price

        -- Determine if the player has the item
        local hasItem = false
        for j = 1, #hasItems do
            if hasItems[j].ItemName == itemName then
                hasItem = hasItems[j].HasItem
                break
            end
        end

        -- Add the item to the menu and disable it if the player doesn't have it
        table.insert(menuOptions,  {
            title = string.format(Config.Menu["sell_label"], Framework.GetItemLabel(item.ItemName)),
            context = string.format(Config.Menu["price_context"], item.Price),
			icon = Framework.ConvertImageFormat(Framework.GetItemImg(item.ItemName), 45), -- 45 is the image size
			image = Framework.ConvertImageFormat(Framework.GetItemImg(item.ItemName), 45), -- 45 is the image size
            event = "d3MBA-butcher:client:InstantSellInputAmount",
			disabled = not hasItem, -- Disable if the player doesn't have the item
            args = {
                ItemName = item.ItemName,
                Price = item.Price,
            } 
        })
    end        

    -- Open the menu
    TriggerEvent("d3MBA-lib:client:OpenMenu", Framework.Menu, menuHeaderOptions, menuOptions, menuFooterOptions)
end)

-- Show Order Menu
RegisterNetEvent("d3MBA-butcherjob:client:ShowOrderMenu", function(order)
	if not order or not order.Items then
		TriggerEvent('d3MBA-lib:sendNotification', source, "No order found!", Framework.NotificationsSettings.Error, 5000)
		return
	end

	local menuHeaderOptions = {
		title = Config.Menu["start_packaging_title"],
		ox_title = Config.Menu["order_title"],
		event = "d3MBA-butcherjob:client:StartPackagingOrder",
		icon = "fas fa-box-open",
	}

	local menuFooterOptions = {
		title = Config.Menu["exit"],
		icon = "fas fa-circle-xmark", 
	}

	local menuOptions = {}

	-- Convert order.Items to the format expected by the callback
	local itemsToCheck = {}
	for _, item in ipairs(order.Items) do
		itemsToCheck[item.ItemName] = item.Amount
	end

	local hasItems = Framework.TriggerServerCallback("d3MBA-lib:server:CheckPlayerItemsTable", itemsToCheck)
	Wait(50)

	for i = 1, #order.Items do 
		local item = order.Items[i]
		local itemName = item.ItemName

		-- Find the corresponding entry in hasItems
		local hasItem = false
		for j = 1, #hasItems do
			if hasItems[j].ItemName == itemName then
				hasItem = hasItems[j].HasItem
				break
			end
		end

		local symbol = hasItem and "✅" or "❌"

		local itemLabel = Framework.GetItemLabel(itemName)
		local itemAmount = item.Amount

		table.insert(menuOptions, {
			title = string.format(Config.Menu["item_format"], itemLabel, itemAmount) .. " " .. symbol,
			icon = Framework.ConvertImageFormat(Framework.GetItemImg(itemName), 45), 
			image = Framework.ConvertImageFormat(Framework.GetItemImg(itemName), 45),
		})
	end

	table.insert(menuOptions, {
		title = string.format(Config.Menu["total_price_title"], order.TotalPrice),
		icon = "fas fa-dollar-sign",
		disabled = true,
	})

	TriggerEvent("d3MBA-lib:client:OpenMenu", Framework.Menu, menuHeaderOptions, menuOptions, menuFooterOptions)
end)