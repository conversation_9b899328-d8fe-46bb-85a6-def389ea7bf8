if Config.Framework ~= "QBCORE" then
    return
end

Citizen.CreateThreadNow(function()
    if Config.Framework == 'QBCORE' then
        QBCore = exports['qb-core']:GetCoreObject()
    end
end)

function GetPlayers()
    local xPlayers = QBCore.Functions.GetPlayers()
    return xPlayers
end

function GetPlayeridentifier(source)
    local Player = QBCore.Functions.GetPlayer(source) or false
    if Player then
        return Player.PlayerData.citizenid
    end
end

function GetPlayerId(source)
    local Player = QBCore.Functions.GetPlayer(source)
    return Player
end

function GetItemCountFromInventory(source, item)
    local xPlayer = GetPlayerId(source)
    local ItemData = xPlayer.Functions.GetItemByName(item)
    if ItemData then
        return ItemData.amount
    end
end

function AddInventoryItem(src, item, quantity)
    local xPlayer = GetPlayerId(src)
    xPlayer.Functions.AddItem(item, quantity)
end

function RemoveInventoryItem(item, quantity)
    local xPlayer = GetPlayerId(source)
    xPlayer.Functions.RemoveItem(item, quantity)
end

function AddMoney(source, price) --MODIFICACION
    if source then
        local xPlayer = GetPlayerId(source) or false
        if xPlayer then
            xPlayer.Functions.AddMoney('cash', price)
        end
    end
end

function RemoveMoney(price, src)
    if src then
        local xPlayer = GetPlayerId(src)
        xPlayer.Functions.RemoveMoney('cash', price)
    else
        local xPlayer = GetPlayerId(source)
        xPlayer.Functions.RemoveMoney('cash', price)
    end
end

function GetMoney(source)
    local xPlayer = GetPlayerId(source)
    local money = xPlayer.PlayerData.money["cash"]
    return money
end

function RegisterServerCallback(name, cb)
    QBCore.Functions.CreateCallback(name, cb)
end

function GetPlayerFromIdentifier(identifier) --MODIFICACION
    local xPlayer = QBCore.Functions.GetSource(identifier)
    if xPlayer then
        return xPlayer
    else
        return nil
    end
end
