Config = {}

Config.trucker_logistics = {					-- Settings related to the link with the Truck Logistics script
	['enable'] = false,							-- Set this as true if you own the Truck Logistics script and want to link the jobs created in the Hire Deliveryman page with the truckers
	['quick_jobs_page'] = false,					-- true: The jobs created will appear in the Quick jobs page in the trucker logistics (it uses a rented truck). false: They will appear in the Freights page (it requires an owned truck)
	['available_trucks'] = {					-- List of trucks that are generated in contracts
		"actros","man","daf","vnl780"
	},
	['available_trailers'] = {					-- List of trailers that are generated in contracts
		"trailers", "trailers2", "trailers3"
	}
}

Config.max_stores_per_player = 1				-- Maximum number of stores that each player can have
Config.max_stores_employed = 1					-- Maximum number of stores that each player can be employed
Config.has_stock_when_unowed = false				-- If true, the store stock will be full stock when there is no owner. If false, the store stock will be empty when there is no owner
Config.max_jobs = 20							-- Max amount of jobs that each store can create
Config.disable_rename_business = false 			-- Set this to true if you want to disable the function to rename the business
Config.group_map_blips = true					-- true: will group all the blips into a single category in the map. false: all the blips will be grouped just by the name and icon
Config.charge_import_money_before = false 		-- true: The money will be deducted from the store balance when the player starts the job. false: The money will be deducted only after the player finishes the job

-- Here are the places where the person can open the market menu
-- You can add as many locations as you want, just use the location already created as an example
Config.market_locations = {
	["utools"] = {															    -- ID
		['buy_price'] = 30000,											    -- Price to buy this market

		['sell_price'] = 0,												        -- Price to sell this market
		['coord'] = {2739.7654, 3463.0034, 55.7105},		                    -- Coordinate to open the menu
		['garage_coord'] = {2700.6533, 3449.6228, 55.7897, 157.9668},			-- Garage coordinates, where the trucks will spawn (coordinates composed of x, y, z, h)
		['truck_parking_location'] = {2700.6533, 3449.6228, 55.7897, 157.9668},					        -- Price to sell this market
		['map_blip_coord'] = {2755.4761, 3469.4087, 55.7515},			-- Location that the trucks from Trucker Logistics script will park when delivering cargo for this store
		['sell_blip_coords'] = {												-- The coordinates where customes will buy things on this store (coordinates composed of x, y, z)
			{2735.3533, 3463.6460, 55.8096}

		},
		['deliveryman_coord'] = {2733.5295, 3467.9807, 55.6960},				-- Coord where the deliveryman will take the jobs you've created
		['type'] = 'youtools', 													-- Insert here the market type ID
		['account'] = {															-- Account settings for this store
			['item'] = {														-- Account that the money should be debited when buying item in the market (player can choose between 2)
				[1] = {
					['icon'] = 'img/credit_card.png',
					['account'] = 'bank'
				},
				[2] = {
					['icon'] = 'img/cash.png',
					['account'] = 'cash'
				}
			},
			['store'] = 'bank',													-- Account that should be used with store expenses (owner)
		}
	},

	["ironlegion_guns"] = {															    -- ID
		['buy_price'] = 30000,											    -- Price to buy this market
		['sell_price'] = 0,												        -- Price to sell this market
		['coord'] = {550.5881, -2772.3813, 5.6452},		                    	-- Coordinate to open the menu
		['garage_coord'] = {556.4835, -2763.6208, 6.0580, 235.8787},			-- Garage coordinates, where the trucks will spawn (coordinates composed of x, y, z, h)
		['truck_parking_location'] = {556.4835, -2763.6208, 6.0580, 235.8787}, -- Location that the trucks from Trucker Logistics script will park when delivering cargo for this store
		['map_blip_coord'] = {505.3028, -2758.5881, 3.0812},	
		['sell_blip_coords'] = {												-- The coordinates where customers will buy things on this store (coordinates composed of x, y, z)
			{505.3028, -2758.5881, 3.0812}

		},
		['deliveryman_coord'] = {556.9430, -2771.1934, 6.0907},				-- Coord where the deliveryman will take the jobs you've created
		['type'] = 'illegalguns', 													-- Insert here the market type ID
		['account'] = {
			['item'] = {
				[1] = {
					['icon'] = 'img/black_money.png',
					['account'] = 'black_money'
				},
				[2] = {
					['icon'] = 'img/black_money.png',
					['account'] = 'black_money'
				}
			},
			['store'] = 'black_money',
		}
	},

	["robbery"] = {															    -- ID
		['buy_price'] = 30000,											    -- Price to buy this market
		['sell_price'] = 0,												        -- Price to sell this market
		['coord'] = {563.9795, -2780.9312, 5.8779},		                    	-- Coordinate to open the menu
		['garage_coord'] = {588.4863, -2780.5903, 6.0580, 241.2684},			-- Garage coordinates, where the trucks will spawn (coordinates composed of x, y, z, h)
		['truck_parking_location'] = {588.4863, -2780.5903, 6.0580, 241.2684},			-- Location that the trucks from Trucker Logistics script will park when delivering cargo for this store				-- The coordinates where customes will buy things on this store (coordinates composed of x, y, z)
		['map_blip_coord'] = {507.6541, -2754.5320, 3.1054},
		['sell_blip_coords'] = {								
			{507.6541, -2754.5320, 3.1054}
		},
		['deliveryman_coord'] = {564.0535, -2775.4177, 6.0907},				-- Coord where the deliveryman will take the jobs you've created
		['type'] = 'blackmarket', 													-- Insert here the market type ID
		['account'] = {															-- Account settings for this store
			['item'] = {
				[1] = {
					['icon'] = 'img/black_money.png',
					['account'] = 'black_money'
				},
				[2] = {
					['icon'] = 'img/black_money.png',
					['account'] = 'black_money'
				}
			},
			['store'] = 'black_money',													-- Account that should be used with store expenses (owner)
		}
	},
	
	["zion_gunshop"] = {
		['buy_price'] = 30000,
		['sell_price'] = 0,
		['stock_capacity'] = 1500000,
		['coord'] = {7.3230, -1110.8857, 29.6511},
		['garage_coord'] = {-10.0469, -1116.8212, 27.9476, 155.9557},
		['truck_parking_location'] = {-10.0469, -1116.8212, 27.9476, 155.9557},
		['map_blip_coord'] = {21.7877, -1108.2366, 30.1902},
		['sell_blip_coords'] = {
			{21.7877, -1108.2366, 30.1902}
		},
		['deliveryman_coord'] = {15.9941, -1112.6742, 29.8003},
		['type'] = 'ammunation',
		['account'] = {
			['item'] = {
				[1] = {
					['icon'] = 'img/credit_card.png',
					['account'] = 'bank'
				},
				[2] = {
					['icon'] = 'img/cash.png',
					['account'] = 'cash'
				}
			},
			['store'] = 'bank',
		}
	},

	["jade"] = {
		['buy_price'] = 30000,
		['sell_price'] = 0,
		['coord'] = {233.1001, 3995.7312, 50.6945},
		['garage_coord'] = {253.7194, 3944.6167, 49.8381, 297.5166},
		['truck_parking_location'] = {253.7194, 3944.6167, 49.8381, 297.5166},
		['map_blip_coord'] = {230.0743, 3998.9021, 49.9704},
		['sell_blip_coords'] = {
			{230.0743, 3998.9021, 49.9704}
		},
		['deliveryman_coord'] = {240.7036, 3992.3633, 50.1509},
		['type'] = 'jade_cafe',
		['account'] = {
			['item'] = {
				[1] = {
					['icon'] = 'img/credit_card.png',
					['account'] = 'bank'
				},
				[2] = {
					['icon'] = 'img/cash.png',
					['account'] = 'cash'
				}
			},
			['store'] = 'bank',
		}
	},

	["zion_nightclub"] = {
		['buy_price'] = 30000,
		['sell_price'] = 0,
		['coord'] = {247.1667, -3160.2117, -0.1160},
		['garage_coord'] = {278.9128, -3145.2993, 5.7902, 358.4903},
		['truck_parking_location'] = {278.9128, -3145.2993, 5.7902, 358.4903},
		['map_blip_coord'] = {248.2868, -3162.8162, -0.3962},
		['sell_blip_coords'] = {
			{248.2868, -3162.8162, -0.3962}
		},
		['deliveryman_coord'] = {251.2693, -3157.2981, -0.1864},
		['type'] = 'nightclub',
		['account'] = {
			['item'] = {
				[1] = { 
					['icon'] = 'img/credit_card.png',
					['account'] = 'bank'
				},
				[2] = {
					['icon'] = 'img/cash.png',
					['account'] = 'cash'
				}
			},
			['store'] = 'bank',
		}
	},

	["illegalstore"] = {
		['buy_price'] = 30000,
		['sell_price'] = 0,
		['coord'] = {1643.2343, 4848.8306, 45.0322},
		['garage_coord'] = {1640.8654, 4835.6694, 42.0237, 188.1814},
		['truck_parking_location'] = {1640.8654, 4835.6694, 42.0237, 188.1814},
		['map_blip_coord'] = {1656.6161, 4850.9971, 42.0112},
		['sell_blip_coords'] = {
			{1650.5127, 4849.1519, 41.8930}
		},
		['deliveryman_coord'] = {1649.5876, 4849.4043, 45.3480},
		['type'] = 'drugsales',
		['account'] = {
			['item'] = {
				[1] = {
					['icon'] = 'img/black_money.png',
					['account'] = 'black_money'
				},
				[2] = {
					['icon'] = 'img/black_money.png',
					['account'] = 'black_money'
				}
			},
			['store'] = 'black_money',
		}
	},

	-- ["mixer_blr"] = {
	-- 	['buy_price'] = 30000,
	-- 	['sell_price'] = 0,
	-- 	['coord'] = {-149.7650, 6287.9087, 31.5780},
	-- 	['garage_coord'] = {-135.5181, 6278.6011, 31.3468, 315.4100},
	-- 	['truck_parking_location'] = {-135.5181, 6278.6011, 31.3468, 315.4100},
	-- 	['map_blip_coord'] = {-150.1341, 6300.1929, 31.5230},
	-- 	['sell_blip_coords'] = {
	-- 		{-150.1341, 6300.1929, 31.5230}
	-- 	},
	-- 	['deliveryman_coord'] = {-148.1516, 6307.0522, 31.5460},
	-- 	['type'] = 'mixer',
	-- 	['account'] = {
	-- 		['item'] = {
	-- 			[1] = {
	-- 				['icon'] = 'img/credit_card.png',
	-- 				['account'] = 'bank'
	-- 			},
	-- 			[2] = {
	-- 				['icon'] = 'img/cash.png',
	-- 				['account'] = 'cash'
	-- 			}
	-- 		},
	-- 		['store'] = 'bank',
	-- 	}
	-- },

	["labs"] = {
		['buy_price'] = 30000,
		['sell_price'] = 0,
		['coord'] = {-1826.2601, -383.2724, 49.4052},
		['garage_coord'] = {705.7866, -987.3450, 24.0875, 276.6223},
		['truck_parking_location'] = {705.7866, -987.3450, 24.0875, 276.6223},
		['map_blip_coord'] = {-1830.9800, -380.7344, 49.3006},
		['sell_blip_coords'] = {
			{-1830.9800, -380.7344, 49.3006},
		},
		['deliveryman_coord'] = {712.7935, -973.9830, 30.3953},
		['type'] = 'medicines',
		['account'] = {
			['item'] = {
				[1] = {
					['icon'] = 'img/credit_card.png',
					['account'] = 'bank'
				},
				[2] = {
					['icon'] = 'img/cash.png',
					['account'] = 'cash'
				}
			},
			['store'] = 'bank',
		}
	},

	-- ["medimport"] = {
	-- 	['buy_price'] = 30000,
	-- 	['sell_price'] = 0,
	-- 	['coord'] = {707.0480, -967.9009, 30.4294},
	-- 	['garage_coord'] = {703.0744, -987.9644, 24.0738, 273.4126},
	-- 	['truck_parking_location'] = {703.0744, -987.9644, 24.0738, 273.4126},
	-- 	['map_blip_coord'] = {716.3892, -963.3223, 30.5319},
	-- 	['sell_blip_coords'] = {
	-- 		{716.3892, -963.3223, 30.5319},
	-- 	},
	-- 	['deliveryman_coord'] = {708.7571, -959.0824, 30.3953},
	-- 	['type'] = 'rawmedi',
	-- 	['account'] = {
	-- 		['item'] = {
	-- 			[1] = {
	-- 				['icon'] = 'img/credit_card.png',
	-- 				['account'] = 'bank'
	-- 			},
	-- 			[2] = {
	-- 				['icon'] = 'img/cash.png',
	-- 				['account'] = 'cash'
	-- 			}
	-- 		},
	-- 		['store'] = 'bank',
	-- 	}
	-- },

	-- ["grocerystore"] = {
	-- 	['buy_price'] = 30000,
	-- 	['sell_price'] = 0,
	-- 	['coord'] = {-1119.3320, -1352.5450, 5.1551},
	-- 	['garage_coord'] = {-1054.9617, -1396.9224, 5.4254, 69.8288},
	-- 	['truck_parking_location'] = {-1054.9617, -1396.9224, 5.4254, 69.8288},
	-- 	['map_blip_coord'] = {-1113.1561, -1366.9105, 5.0668},
	-- 	['sell_blip_coords'] = {
	-- 		{-1112.4698, -1363.5652, 4.3268},
	-- 	},
	-- 	['deliveryman_coord'] = {-1109.0635, -1359.7269, 5.0620},
	-- 	['type'] = 'gstore',
	-- 	['account'] = {
	-- 		['item'] = {
	-- 			[1] = {
	-- 				['icon'] = 'img/credit_card.png',
	-- 				['account'] = 'bank'
	-- 			},
	-- 			[2] = {
	-- 				['icon'] = 'img/cash.png',
	-- 				['account'] = 'cash'
	-- 			}
	-- 		},
	-- 		['store'] = 'bank',
	-- 	}
	-- },

	["burgershot"] = {
		['buy_price'] = 30000,
		['sell_price'] = 0,
		['coord'] = {-1201.2109, -895.5609, 13.7137},
		['garage_coord'] = {-1205.7192, -900.9031, 13.3844, 33.1885},
		['truck_parking_location'] = {-1205.7192, -900.9031, 13.3844, 33.1885},
		['map_blip_coord'] = {-1181.2360, -886.0742, 13.8875},
		['sell_blip_coords'] = {
			{-1195.4259, -893.0286, 13.7873},
		},
		['deliveryman_coord'] = {-1175.9053, -892.3986, 13.8041},
		['type'] = 'foodburger',
		['account'] = {
			['item'] = {
				[1] = {
					['icon'] = 'img/credit_card.png',
					['account'] = 'bank'
				},
				[2] = {
					['icon'] = 'img/cash.png',
					['account'] = 'cash'
				}
			},
			['store'] = 'bank',
		}
	},

	-- ["ramenshop"] = {
	-- 	['buy_price'] = 30000,
	-- 	['sell_price'] = 0,
	-- 	['coord'] = {-1238.3571, -269.1920, 37.1041},
	-- 	['garage_coord'] = {-1244.5394, -256.7892, 39.0455, 298.4922},
	-- 	['truck_parking_location'] = {-1244.5394, -256.7892, 39.0455, 298.4922},
	-- 	['map_blip_coord'] = {-1229.8110, -285.9413, 37.7375},
	-- 	['sell_blip_coords'] = {
	-- 		{-1236.9015, -270.8861, 37.5145},
	-- 	},
	-- 	['deliveryman_coord'] = {-1234.7383, -288.5889, 37.6532},
	-- 	['type'] = 'ramen',
	-- 	['account'] = {
	-- 		['item'] = {
	-- 			[1] = {
	-- 				['icon'] = 'img/credit_card.png',
	-- 				['account'] = 'bank'
	-- 			},
	-- 			[2] = {
	-- 				['icon'] = 'img/cash.png',
	-- 				['account'] = 'cash'
	-- 			}
	-- 		},
	-- 		['store'] = 'bank',
	-- 	}
	-- },

	["grindus"] = {
		['buy_price'] = 30000,
		['sell_price'] = 0,
		['coord'] = {-1081.9127, -245.8277, 37.6383},
		['garage_coord'] = {-1057.8396, -225.0571, 38.2778, 120.7932},
		['truck_parking_location'] = {-1057.8396, -225.0571, 38.2778, 120.7932},
		['map_blip_coord'] = {-1066.9178, -239.3154, 43.7819},
		['sell_blip_coords'] = {
			{-1045.1794, -241.2475, 37.9922}
		},
		['deliveryman_coord'] = {-1384.8243, -591.8856, 30.3200},
		['type'] = 'importexport',
		['account'] = {
			['item'] = {
				[1] = {
					['icon'] = 'img/credit_card.png',
					['account'] = 'bank'
				},
				[2] = {
					['icon'] = 'img/cash.png',
					['account'] = 'cash'
				}
			},
			['store'] = 'bank',
		}
	}
}

-- Here you configure each type of market available to buy
Config.market_types = {
	['jade_cafe'] = {							-- Market type ID
		['stock_capacity'] = 150000,		-- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 10000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 120000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 110000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'speedo',
			[1] = 'gburrito',
			[2] = 'mule',
			[3] = 'mule3',
			[4] = 'pounder',
			[5] = 'pounder'	
		},
		['max_purchasable_categories'] = 5,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"qupid", "chocolates", "plushes"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"qupid", "chocolates", "plushes"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 279,						-- Blip ID [Set this value 0 to dont have blip]
			['name'] = "LUX RESORT",			-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 1,						-- Blip Color
			['scale'] = 0.6,					-- Blip Scale					-- Blip Scale
		}
	},
	['drugsales'] = {							-- Market type ID
		['stock_capacity'] = 150000,		-- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 10000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 120000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 110000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'shadbus',
			[1] = 'shadbus',
			[2] = 'shadbus',
			[3] = 'shadbus',
			[4] = 'shadbus',
			[5] = 'shadbus'	
		},
		['max_purchasable_categories'] = 2,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"breakingbad", "drugs"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"breakingbad", "drugs"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 140,						-- Blip ID [Set this value 0 to dont have blip]
			['name'] = "K&K Pvt.Ltd.",			-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 4,						-- Blip Color
			['scale'] = 0.1,					-- Blip Scale					-- Blip Scale
		}
	},

	['importexport'] = {							-- Market type ID
		['stock_capacity'] = 500000,			-- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {'heez'},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 10000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 120000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 110000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'speedo',
			[1] = 'gburrito',
			[2] = 'mule',
			[3] = 'mule3',
			[4] = 'pounder',
			[5] = 'pounder'		
		},
		['max_purchasable_categories'] = 3,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"industries"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"industries"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 50,						-- Blip ID [Set this value 0 to dont have blip]
			['name'] = "Nautty Enterprises",			-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 4,						-- Blip Color
			['scale'] = 0.7,					-- Blip Scale					-- Blip Scale
		}
	},

	-- ['restaurant'] = {							-- Market type ID
	-- 	['stock_capacity'] = 1000000,			-- Max stock capacity
	-- 	['max_employees'] = 20,					-- Max employees
	-- 	['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
	-- 	['upgrades'] = {						-- Definition of each upgrade
	-- 		['stock'] = {						-- Stock capacity
	-- 			['price'] = 10000,				-- Price to upgrade
	-- 			['level_reward'] = {			-- Reward of each level (max level: 5)
	-- 				[0] = 0,
	-- 				[1] = 50,
	-- 				[2] = 100,
	-- 				[3] = 150,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['truck'] = {						-- Truck capacity
	-- 			['price'] = 120000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 25,
	-- 				[2] = 50,
	-- 				[3] = 100,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['relationship'] = {				-- Relationship
	-- 			['price'] = 110000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 2,
	-- 				[2] = 4,
	-- 				[3] = 6,
	-- 				[4] = 8,
	-- 				[5] = 10,
	-- 			}
	-- 		},
	-- 	},
	-- 	['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
	-- 		[0] = 'speedo',
	-- 		[1] = 'gburrito',
	-- 		[2] = 'mule',
	-- 		[3] = 'mule3',
	-- 		[4] = 'pounder',
	-- 		[5] = 'pounder'	
	-- 	},
	-- 	['max_purchasable_categories'] = 4,	
	-- 	['categories'] = {						-- Here you configure the categories available to purchase in your store
	-- 		"fooditems", "drinkitems"
	-- 	},
	-- 	['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
	-- 		"fooditems", "drinkitems"
	-- 	},
	-- 	['blips'] = {							-- Create the blips on map
	-- 		['id'] = 463,						-- Blip ID [Set this value 0 to dont have blip]
	-- 		['name'] = "Zenin Cafe",			-- Blip Name [Will be replaced when the owner rename the store]
	-- 		['color'] = 23,						-- Blip Color
	-- 		['scale'] = 0.7,					-- Blip Scale					-- Blip Scale
	-- 	}
	-- },

	-- ['rawmedi'] = {							-- Market type ID
	-- 	['stock_capacity'] = 1000000,			-- Max stock capacity
	-- 	['max_employees'] = 20,					-- Max employees
	-- 	['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
	-- 	['upgrades'] = {						-- Definition of each upgrade
	-- 		['stock'] = {						-- Stock capacity
	-- 			['price'] = 10000,				-- Price to upgrade
	-- 			['level_reward'] = {			-- Reward of each level (max level: 5)
	-- 				[0] = 0,
	-- 				[1] = 50,
	-- 				[2] = 100,
	-- 				[3] = 150,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['truck'] = {						-- Truck capacity
	-- 			['price'] = 120000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 25,
	-- 				[2] = 50,
	-- 				[3] = 100,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['relationship'] = {				-- Relationship
	-- 			['price'] = 110000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 2,
	-- 				[2] = 4,
	-- 				[3] = 6,
	-- 				[4] = 8,
	-- 				[5] = 10,
	-- 			}
	-- 		},
	-- 	},
	-- 	['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
	-- 		[0] = 'speedo',
	-- 		[1] = 'gburrito',
	-- 		[2] = 'mule',
	-- 		[3] = 'mule3',
	-- 		[4] = 'pounder',
	-- 		[5] = 'pounder'	
	-- 	},
	-- 	['max_purchasable_categories'] = 4,	
	-- 	['categories'] = {						-- Here you configure the categories available to purchase in your store
	-- 		"rawmaterials"
	-- 	},
	-- 	['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
	-- 		"rawmaterials"
	-- 	},
	-- 	['blips'] = {							-- Create the blips on map
	-- 		['id'] = 153,						-- Blip ID [Set this value 0 to dont have blip]
	-- 		['name'] = "Medicine Raw Materials",			-- Blip Name [Will be replaced when the owner rename the store]
	-- 		['color'] = 23,						-- Blip Color
	-- 		['scale'] = 0.6,					-- Blip Scale					-- Blip Scale
	-- 	}
	-- },

	['foodburger'] = {							-- Market type ID
		['stock_capacity'] = 60000,			-- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 10000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 120000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 110000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'speedo',
			[1] = 'gburrito',
			[2] = 'mule',
			[3] = 'mule3',
			[4] = 'pounder',
			[5] = 'pounder'	
		},
		['max_purchasable_categories'] = 4,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"burgerfood"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"burgerfood"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 52,						-- Blip ID [Set this value 0 to dont have blip]
			['name'] = "K&K Burgershot",			-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 23,						-- Blip Color
			['scale'] = 0.7,					-- Blip Scale					-- Blip Scale
		}
	},

	-- ['ramen'] = {							-- Market type ID
	-- 	['stock_capacity'] = 60000,			-- Max stock capacity
	-- 	['max_employees'] = 20,					-- Max employees
	-- 	['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
	-- 	['upgrades'] = {						-- Definition of each upgrade
	-- 		['stock'] = {						-- Stock capacity
	-- 			['price'] = 10000,				-- Price to upgrade
	-- 			['level_reward'] = {			-- Reward of each level (max level: 5)
	-- 				[0] = 0,
	-- 				[1] = 50,
	-- 				[2] = 100,
	-- 				[3] = 150,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['truck'] = {						-- Truck capacity
	-- 			['price'] = 120000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 25,
	-- 				[2] = 50,
	-- 				[3] = 100,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['relationship'] = {				-- Relationship
	-- 			['price'] = 110000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 2,
	-- 				[2] = 4,
	-- 				[3] = 6,
	-- 				[4] = 8,
	-- 				[5] = 10,
	-- 			}
	-- 		},
	-- 	},
	-- 	['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
	-- 		[0] = 'speedo',
	-- 		[1] = 'gburrito',
	-- 		[2] = 'mule',
	-- 		[3] = 'mule3',
	-- 		[4] = 'pounder',
	-- 		[5] = 'pounder'	
	-- 	},
	-- 	['max_purchasable_categories'] = 4,	
	-- 	['categories'] = {						-- Here you configure the categories available to purchase in your store
	-- 		"ramens"
	-- 	},
	-- 	['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
	-- 		"ramens"
	-- 	},
	-- 	['blips'] = {							-- Create the blips on map
	-- 		['id'] = 52,						-- Blip ID [Set this value 0 to dont have blip]
	-- 		['name'] = "Ramen shop",			-- Blip Name [Will be replaced when the owner rename the store]
	-- 		['color'] = 4,						-- Blip Color
	-- 		['scale'] = 0.7,					-- Blip Scale					-- Blip Scale
	-- 	}
	-- },

	-- ['gstore'] = {							-- Market type ID
	-- 	['stock_capacity'] = 50000,			-- Max stock capacity
	-- 	['max_employees'] = 20,					-- Max employees
	-- 	['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
	-- 	['upgrades'] = {						-- Definition of each upgrade
	-- 		['stock'] = {						-- Stock capacity
	-- 			['price'] = 10000,				-- Price to upgrade
	-- 			['level_reward'] = {			-- Reward of each level (max level: 5)
	-- 				[0] = 0,
	-- 				[1] = 50,
	-- 				[2] = 100,
	-- 				[3] = 150,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['truck'] = {						-- Truck capacity
	-- 			['price'] = 120000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 25,
	-- 				[2] = 50,
	-- 				[3] = 100,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['relationship'] = {				-- Relationship
	-- 			['price'] = 110000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 2,
	-- 				[2] = 4,
	-- 				[3] = 6,
	-- 				[4] = 8,
	-- 				[5] = 10,
	-- 			}
	-- 		},
	-- 	},
	-- 	['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
	-- 		[0] = 'speedo',
	-- 		[1] = 'gburrito',
	-- 		[2] = 'mule',
	-- 		[3] = 'mule3',
	-- 		[4] = 'pounder',
	-- 		[5] = 'pounder'		
	-- 	},
	-- 	['max_purchasable_categories'] = 4,	
	-- 	['categories'] = {						-- Here you configure the categories available to purchase in your store
	-- 		"vegetables", "meat", "essence", "other"
	-- 	},
	-- 	['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
	-- 		"vegetables", "meat", "essence", "other"
	-- 	},
	-- 	['blips'] = {							-- Create the blips on map
	-- 		['id'] = 468,						-- Blip ID [Set this value 0 to dont have blip]
	-- 		['name'] = "Grocery Store",	-- Blip Name [Will be replaced when the owner rename the store]
	-- 		['color'] = 23,						-- Blip Color
	-- 		['scale'] = 0.7,					-- Blip Scale
	-- 	}
	-- },

	['youtools'] = {							-- Market type ID
		['stock_capacity'] = 200000,		    -- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 8000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 500,
					[2] = 1000,
					[3] = 1500,
					[4] = 2000,
					[5] = 3000,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 12000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 11000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'dynastytools',
			[1] = 'dynastytools',
			[2] = 'dynastytools',
			[3] = 'dynastytools',
			[4] = 'dynastytools',
			[5] = 'dynastytools'	
		},
		['max_purchasable_categories'] = 8,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"electronics", "utilities", "gettowork", "cooking", "gardening", "fishing"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"electronics", "utilities", "gettowork", "cooking", "gardening", "fishing"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 52,						-- Blip ID [Set this value 0 to dont have blip]
			['name'] = "Zion Tools",				-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 4,						-- Blip Color
			['scale'] = 0.7,					-- Blip Scale					-- Blip Scale
		}
	},

	['blackmarket'] = {
		['stock_capacity'] = 185000,
		['max_employees'] = 20,
		['required_job'] = {},
		['upgrades'] = {
			['stock'] = {						-- Stock capacity
				['price'] = 10000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 120000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 110000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {
			[0] = 'crimbus',
			[1] = 'crimbus',
			[2] = 'crimbus',
			[3] = 'crimbus',
			[4] = 'crimbus',
			[5] = 'crimbus'
		},
		['max_purchasable_categories'] = 4,	
		['categories'] = {
			"robberyitems", "robberysell"
		},
		['default_categories'] = {
			"robberyitems", "robberysell"
		},
		['blips'] = {
			['id'] = 52,
			['name'] = "Black Market",
			['color'] = 4,
			['scale'] = 0.1,
		}
	},

	['nightclub'] = {							-- Market type ID
		['stock_capacity'] = 1850000,				-- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 10000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 120000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 110000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'speedo',
			[1] = 'gburrito',
			[2] = 'mule',
			[3] = 'mule3',
			[4] = 'pounder',
			[5] = 'pounder'
		},
		['max_purchasable_categories'] = 4,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"smokersparadise", "cheers", "vatt"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"smokersparadise", "cheers", "vatt"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 52,						-- Blip ID [Set this value 0 to dont have blip]
			['name'] = "Aurora Nightclub",				-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 4,						-- Blip Color
			['scale'] = 0.7,					-- Blip Scale					-- Blip Scale
		}
	},

	['medicines'] = {							-- Market type ID
		['stock_capacity'] = 1850000,				-- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 10000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 120000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 110000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'speedo',
			[1] = 'gburrito',
			[2] = 'mule',
			[3] = 'mule3',
			[4] = 'pounder',
			[5] = 'pounder'
		},
		['max_purchasable_categories'] = 4,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"pharmacy"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"pharmacy"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 52,						-- Blip ID [Set this value 0 to dont have blip]
			['name'] = "Pharmacy",				-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 4,						-- Blip Color
			['scale'] = 0.4,					-- Blip Scale
		}
	},

	['ammunation'] = {							-- Market type ID
		['stock_capacity'] = 7500000,			-- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 160000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 250000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 320000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'sbearcat',
			[1] = 'sbearcat',
			[2] = 'sbearcat',
			[3] = 'sbearcat',
			[4] = 'sbearcat',
			[5] = 'sbearcat'	
		},
		['max_purchasable_categories'] = 10,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"meleeweapons", "pistolwhite", "riflewhite", "otherweaponwhite", "throwables", "ammos", "gunmagazines"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"meleeweapons", "pistolwhite", "riflewhite", "otherweaponwhite", "throwables", "ammos","gunmagazines"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 110,
			['name'] = "ZION Gun Shop",			-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 4,						-- Blip Color
			['scale'] = 0.7,					-- Blip Scale					-- Blip Scale
		}
	},

	['illegalguns'] = {							-- Market type ID
		['stock_capacity'] = 10000,				-- Max stock capacity
		['max_employees'] = 20,					-- Max employees
		['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
		['upgrades'] = {						-- Definition of each upgrade
			['stock'] = {						-- Stock capacity
				['price'] = 160000,				-- Price to upgrade
				['level_reward'] = {			-- Reward of each level (max level: 5)
					[0] = 0,
					[1] = 50,
					[2] = 100,
					[3] = 150,
					[4] = 200,
					[5] = 300,
				}
			},
			['truck'] = {						-- Truck capacity
				['price'] = 250000,
				['level_reward'] = {
					[0] = 0,
					[1] = 25,
					[2] = 50,
					[3] = 100,
					[4] = 200,
					[5] = 300,
				}
			},
			['relationship'] = {				-- Relationship
				['price'] = 320000,
				['level_reward'] = {
					[0] = 0,
					[1] = 2,
					[2] = 4,
					[3] = 6,
					[4] = 8,
					[5] = 10,
				}
			},
		},
		['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
			[0] = 'ironbus',
			[1] = 'ironbus',
			[2] = 'ironbus',
			[3] = 'ironbus',
			[4] = 'ironbus',
			[5] = 'ironbus'
		},
		['max_purchasable_categories'] = 10,	
		['categories'] = {						-- Here you configure the categories available to purchase in your store
			"meleeweaponsblack", "pistolblack", "rifleblack", "weaponkits"
		},
		['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
			"meleeweaponsblack", "pistolblack", "rifleblack", "weaponkits"
		},
		['blips'] = {							-- Create the blips on map
			['id'] = 110,
			['name'] = "Iron Legion",			-- Blip Name [Will be replaced when the owner rename the store]
			['color'] = 4,						-- Blip Color
			['scale'] = 0.2,					-- Blip Scale					-- Blip Scale
		}
	}
	-- ['mixer'] = {							-- Market type ID
	-- 	['stock_capacity'] = 10000,				-- Max stock capacity
	-- 	['max_employees'] = 20,					-- Max employees
	-- 	['required_job'] = {},					-- Required job do purchase goods in this store (set to {} to dont require any job here, or put the job name there)
	-- 	['upgrades'] = {						-- Definition of each upgrade
	-- 		['stock'] = {						-- Stock capacity
	-- 			['price'] = 160000,				-- Price to upgrade
	-- 			['level_reward'] = {			-- Reward of each level (max level: 5)
	-- 				[0] = 0,
	-- 				[1] = 50,
	-- 				[2] = 100,
	-- 				[3] = 150,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['truck'] = {						-- Truck capacity
	-- 			['price'] = 250000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 25,
	-- 				[2] = 50,
	-- 				[3] = 100,
	-- 				[4] = 200,
	-- 				[5] = 300,
	-- 			}
	-- 		},
	-- 		['relationship'] = {				-- Relationship
	-- 			['price'] = 320000,
	-- 			['level_reward'] = {
	-- 				[0] = 0,
	-- 				[1] = 2,
	-- 				[2] = 4,
	-- 				[3] = 6,
	-- 				[4] = 8,
	-- 				[5] = 10,
	-- 			}
	-- 		},
	-- 	},
	-- 	['trucks'] = {							-- Trucks for each level when upgrade the truck cargo
	-- 		[0] = 'speedo',
	-- 		[1] = 'gburrito',
	-- 		[2] = 'mule',
	-- 		[3] = 'mule3',
	-- 		[4] = 'pounder',
	-- 		[5] = 'pounder'
	-- 	},
	-- 	['max_purchasable_categories'] = 10,	
	-- 	['categories'] = {						-- Here you configure the categories available to purchase in your store
	-- 		"blrmixer"
	-- 	},
	-- 	['default_categories'] = {				-- Here you can configure the categories available when the store has no owner
	-- 		"blrmixer"
	-- 	},
	-- 	['blips'] = {							-- Create the blips on map
	-- 		['id'] = 110,
	-- 		['name'] = "Distillery",			-- Blip Name [Will be replaced when the owner rename the store]
	-- 		['color'] = 4,						-- Blip Color
	-- 		['scale'] = 0.6,					-- Blip Scale					-- Blip Scale
	-- 	}
	-- }
}

-- Here you configure each category to purchase inside the markets
Config.market_categories = {
	['electronics'] = {
		['page_name'] = "Electronics",
		['page_desc'] = "Things that can electrocute you",
		['page_icon'] = '<i class="fa-solid fa-mobile-screen"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/electronics.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['radio'] = {						
				['name'] = "Radio",				
				['price_to_customer'] = 1800,	
				['price_to_customer_min'] = 1800,
				['price_to_customer_max'] = 2000,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 1000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'radio.png',			
			},
			['iphone'] = {						
				['name'] = "zPHONE Z1",				
				['price_to_customer'] = 17000,	
				['price_to_customer_min'] = 17000,
				['price_to_customer_max'] = 18000,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 13000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'iphone.png',			
			},
			['phone'] = {						
				['name'] = "zPHONE Z2",				
				['price_to_customer'] = 28000,	
				['price_to_customer_min'] = 27000,
				['price_to_customer_max'] = 28000,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 13000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'phone.png',			
			},
			['mediarem'] = {						
				['name'] = "Global Remote",				
				['price_to_customer'] = 15000,	
				['price_to_customer_min'] = 15000,
				['price_to_customer_max'] = 20000,
				['price_to_export'] = 15000,		
				['price_to_owner'] = 10000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'mediarem.png',		
			},
			['deliverytab'] = {						
				['name'] = "Delivery Tablet",				
				['price_to_customer'] = 13500,	
				['price_to_customer_min'] = 13500,
				['price_to_customer_max'] = 15500,
				['price_to_export'] = 10500,		
				['price_to_owner'] = 11500,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'deliverytab.png',			
			},
			['cctvcam'] = {						
				['name'] = "CCTV Camera",				
				['price_to_customer'] = 8000,	
				['price_to_customer_min'] = 8000,
				['price_to_customer_max'] = 9000,
				['price_to_export'] = 8000,		
				['price_to_owner'] = 4000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'cctvcam.png',			
			},
			['drone_flyer_1'] = {						
				['name'] = "Drone Flyer",				
				['price_to_customer'] = 1745600,	
				['price_to_customer_min'] = 1245600,
				['price_to_customer_max'] = 1295600,
				['price_to_export'] = 1245600,		
				['price_to_owner'] = 1045600,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'drone_flyer_1.png',
			},
			['laptop'] = {						
				['name'] = "Laptop",				
				['price_to_customer'] = 18500,	
				['price_to_customer_min'] = 18500,
				['price_to_customer_max'] = 20500,
				['price_to_export'] = 4200,		
				['price_to_owner'] = 13000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'laptop.png',			
			},
			['rccar'] = {						
				['name'] = "RC Car",				
				['price_to_customer'] = 57000,	
				['price_to_customer_min'] = 57000,
				['price_to_customer_max'] = 58000,
				['price_to_export'] = 57000,
				['price_to_owner'] = 37000,	
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 50,	
				['img'] = 'rccar.png',			
			},
			['camera2'] = {						
				['name'] = "Canon EOS M50",				
				['price_to_customer'] = 15950,	
				['price_to_customer_min'] = 18950,
				['price_to_customer_max'] = 20950, 
				['price_to_export'] = 20950,		
				['price_to_owner'] = 10950,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'camera2.png',				
			},
			['powerbank'] = {						
				['name'] = "Power Bank",				
				['price_to_customer'] = 8500,	
				['price_to_customer_min'] = 8500,
				['price_to_customer_max'] = 9500,
				['price_to_export'] = 8500,		
				['price_to_owner'] = 5500,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'powerbank.png',		
			}
		}
	},

	['utilities'] = {
		['page_name'] = "Utilities",
		['page_desc'] = "Enhance your lifestyle",
		['page_icon'] = '<i class="fa-solid fa-store"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/utilities.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['spray'] = {						
				['name'] = "Spray Can",				
				['price_to_customer'] = 15000,	
				['price_to_customer_min'] = 15000,
				['price_to_customer_max'] = 20000,
				['price_to_export'] = 15000,		
				['price_to_owner'] = 10000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'spray.png',			
			},
			['spray_remover'] = {						
				['name'] = "Spray Remover",				
				['price_to_customer'] = 6000,	
				['price_to_customer_min'] = 6000,
				['price_to_customer_max'] = 6500,
				['price_to_export'] = 6000,		
				['price_to_owner'] = 4000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'spray_remover.png',	
			},
			['parachute'] = {						
				['name'] = "Parachute",				
				['price_to_customer'] = 6200,	
				['price_to_customer_min'] = 6200,
				['price_to_customer_max'] = 8200,
				['price_to_export'] = 6200,		
				['price_to_owner'] = 4200,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'parachute.png',		
			},
			['repairkit'] = {						
				['name'] = "Repair Kit",				
				['price_to_customer'] = 9999,	
				['price_to_customer_min'] = 9999,
				['price_to_customer_max'] = 10999,
				['price_to_export'] = 9999,		
				['price_to_owner'] = 6999,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'repairkit.png',		
			},
			['photoalbum'] = {						
				['name'] = "Photo Album",				
				['price_to_customer'] = 6000,	
				['price_to_customer_min'] = 6000,
				['price_to_customer_max'] = 7000,
				['price_to_export'] = 6000,		
				['price_to_owner'] = 3000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'photoalbum.png',		
			},
			['paperbag2'] = {						
				['name'] = "Paper Bag",				
				['price_to_customer'] = 4500,	
				['price_to_customer_min'] = 4500,
				['price_to_customer_max'] = 5500,
				['price_to_export'] = 4500,		
				['price_to_owner'] = 2500,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'paperbag2.png',		
			},
			-- ['document'] = {						
			-- 	['name'] = "Document",				
			-- 	['price_to_customer'] = 7000,	
			-- 	['price_to_customer_min'] = 7000,
			-- 	['price_to_customer_max'] = 7500,
			-- 	['price_to_export'] = 7000,		
			-- 	['price_to_owner'] = 7000,		
			-- 	['amount_to_owner'] = 500,		
			-- 	['amount_to_delivery'] = 3000,	
			-- 	['img'] = 'document.png',		
			-- },
			['ducttape'] = {						
				['name'] = "Duct Tape",				
				['price_to_customer'] = 7900,	
				['price_to_customer_min'] = 7900,
				['price_to_customer_max'] = 8900,
				['price_to_export'] = 7900,		
				['price_to_owner'] = 4900,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'ducttape.png',		
			},
			['camp'] = {						
				['name'] = "Camping Kit",				
				['price_to_customer'] = 9999,	
				['price_to_customer_min'] = 8999,
				['price_to_customer_max'] = 10499,
				['price_to_export'] = 9999,		
				['price_to_owner'] = 6999,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'camp.png',			
			},
			['binoculars'] = {						
				['name'] = "Binoculars",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 11300,
				['price_to_export'] = 10000,		
				['price_to_owner'] = 8000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'binoculars.png',	
			},
			['watergun'] = {						
				['name'] = "Water Gun",				
				['price_to_customer'] = 16100,	
				['price_to_customer_min'] = 16100,
				['price_to_customer_max'] = 20100,
				['price_to_export'] = 16100,		
				['price_to_owner'] = 13100,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'watergun.png',		
			},
			['scuba_set'] = {						
				['name'] = "Scuba Set",				
				['price_to_customer'] = 17999,	
				['price_to_customer_min'] = 17999,
				['price_to_customer_max'] = 19999,
				['price_to_export'] = 17999,		
				['price_to_owner'] = 14999,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'scuba_set.png',		
			},
			['scuba_fins'] = {						
				['name'] = "Scuba Fins",				
				['price_to_customer'] = 15999,	
				['price_to_customer_min'] = 15999,
				['price_to_customer_max'] = 17999,
				['price_to_export'] = 15999,		
				['price_to_owner'] = 12999,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'scuba_fins.png',		
			},
			-- ['scuba_tube_l1'] = {						
			-- 	['name'] = "Scuba Gear Level 1",				
			-- 	['price_to_customer'] = 18999,	
			-- 	['price_to_customer_min'] = 18999,
			-- 	['price_to_customer_max'] = 20999,
			-- 	['price_to_export'] = 18999,		
			-- 	['price_to_owner'] = 18999,		
			-- 	['amount_to_owner'] = 500,		
			-- 	['amount_to_delivery'] = 3000,	
			-- 	['img'] = 'scuba_tube_l1.png',		
			-- },
			-- ['scuba_tube_l2'] = {						
			-- 	['name'] = "Scuba Gear Level 2",				
			-- 	['price_to_customer'] = 23999,	
			-- 	['price_to_customer_min'] = 23999,
			-- 	['price_to_customer_max'] = 24999,
			-- 	['price_to_export'] = 19999,		
			-- 	['price_to_owner'] = 19999,		
			-- 	['amount_to_owner'] = 500,		
			-- 	['amount_to_delivery'] = 3000,	
			-- 	['img'] = 'scuba_tube_l2.png',		
			-- },
			-- ['scuba_tube_l3'] = {						
			-- 	['name'] = "Scuba Gear Level 3",				
			-- 	['price_to_customer'] = 26000,	
			-- 	['price_to_customer_min'] = 26000,
			-- 	['price_to_customer_max'] = 27000,
			-- 	['price_to_export'] = 22000,		
			-- 	['price_to_owner'] = 22000,		
			-- 	['amount_to_owner'] = 500,		
			-- 	['amount_to_delivery'] = 3000,	
			-- 	['img'] = 'scuba_tube_l3.png',		
			-- },
			['scuba_gear'] = {						
				['name'] = "Scuba Mask",				
				['price_to_customer'] = 10689,	
				['price_to_customer_min'] = 10689,
				['price_to_customer_max'] = 11689,
				['price_to_export'] = 10689,		
				['price_to_owner'] = 7689,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'scuba_gear.png',		
			},
			['towing_rope'] = {						
				['name'] = "Winch",				
				['price_to_customer'] = 8000,	
				['price_to_customer_min'] = 8000,
				['price_to_customer_max'] = 10000,
				['price_to_export'] = 8000,		
				['price_to_owner'] = 4000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'towing_rope.png',		
			},
			['rope'] = {						
				['name'] = "Rope",				
				['price_to_customer'] = 5000,	
				['price_to_customer_min'] = 5000,
				['price_to_customer_max'] = 8000,
				['price_to_export'] = 8000,		
				['price_to_owner'] = 2000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'rope.png',		
			},
			['liftbag'] = {						
				['name'] = "Lift Bag",				
				['price_to_customer'] = 5999,	
				['price_to_customer_min'] = 5999,
				['price_to_customer_max'] = 6999,
				['price_to_export'] = 5999,		
				['price_to_owner'] = 5999,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'liftbag.png',		
			},
			['awl'] = {						
				['name'] = "AWL Tool",				
				['price_to_customer'] = 11000,	
				['price_to_customer_min'] = 11000,
				['price_to_customer_max'] = 14000,
				['price_to_export'] = 8000,		
				['price_to_owner'] = 7000,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'awl.png',		
			},
			['bcutter'] = {						
				['name'] = "Break Cutter",				
				['price_to_customer'] = 13999,	
				['price_to_customer_min'] = 13999,
				['price_to_customer_max'] = 14999,
				['price_to_export'] = 8000,		
				['price_to_owner'] = 9999,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'bcutter.png',		
			}
		}
	},

	['gettowork'] = {
		['page_name'] = "Get to Work",
		['page_desc'] = "Things that you can use to make money",
		['page_icon'] = '<i class="fa-solid fa-briefcase"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/gettowork.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['huntingbait'] = {						
				['name'] = "Hunting Bait",				
				['price_to_customer'] = 80,	
				['price_to_customer_min'] = 80,
				['price_to_customer_max'] = 100,
				['price_to_export'] = 20,		
				['price_to_owner'] = 30,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 5000,	
				['img'] = 'huntingbait.png',		
			},
			['ls_shears'] = {						
				['name'] = "Shears",				
				['price_to_customer'] = 5500,	
				['price_to_customer_min'] = 3000,
				['price_to_customer_max'] = 8500,
				['price_to_export'] = 1000,		
				['price_to_owner'] = 2000,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 5000,	
				['img'] = 'ls_shears.png',		
			},
			['ls_baking_soda'] = {						
				['name'] = "Baking Soda",				
				['price_to_customer'] = 800,	
				['price_to_customer_min'] = 600,
				['price_to_customer_max'] = 1200,
				['price_to_export'] = 200,		
				['price_to_owner'] = 300,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 5000,	
				['img'] = 'ls_baking_soda.png',		
			},
			-- ['huntingknife'] = {						
			-- 	['name'] = "Hunting Knife",				
			-- 	['price_to_customer'] = 3800,	
			-- 	['price_to_customer_min'] = 2800,
			-- 	['price_to_customer_max'] = 4800,
			-- 	['price_to_export'] = 2800,		
			-- 	['price_to_owner'] = 2800,		
			-- 	['amount_to_owner'] = 500,		
			-- 	['amount_to_delivery'] = 800,	
			-- 	['img'] = 'huntingknife.png',	
			-- },
			-- ['churn'] = {						
			-- 	['name'] = "Churn",				
			-- 	['price_to_customer'] = 200,
			-- 	['price_to_customer_min'] = 200,
			-- 	['price_to_customer_max'] = 450,
			-- 	['price_to_export'] = 200,
			-- 	['price_to_owner'] = 200,
			-- 	['amount_to_owner'] = 100,
			-- 	['amount_to_delivery'] = 100,
			-- 	['img'] = 'churn.png',
			-- },
			['animal_tracker'] = {						
				['name'] = "Animal Tracker",				
				['price_to_customer'] = 200,	
				['price_to_customer_min'] = 200,
				['price_to_customer_max'] = 300,
				['price_to_export'] = 100,		
				['price_to_owner'] = 100,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 250,	
				['img'] = 'animal_tracker.png',	
			}
		}
	},

	-- ['fooditems'] = {
	-- 	['page_name'] = "Food",
	-- 	['page_desc'] = "Please your apetite",
	-- 	['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/fooditems.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['donut'] = {						
	-- 			['name'] = "Donut",				
	-- 			['price_to_customer'] = 160,	
	-- 			['price_to_customer_min'] = 160,
	-- 			['price_to_customer_max'] = 190,
	-- 			['price_to_export'] = 160,		
	-- 			['price_to_owner'] = 160,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'donut.png',		
	-- 		},				
	-- 		['burger_home'] = {						
	-- 			['name'] = "Home Burger",				
	-- 			['price_to_customer'] = 200,	
	-- 			['price_to_customer_min'] = 200,
	-- 			['price_to_customer_max'] = 250,
	-- 			['price_to_export'] = 200,		
	-- 			['price_to_owner'] = 200,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'burger_home.png',		
	-- 		},
	-- 		['pizza'] = {						
	-- 			['name'] = "Pizza Slice",				
	-- 			['price_to_customer'] = 250,	
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 300,
	-- 			['price_to_export'] = 250,		
	-- 			['price_to_owner'] = 250,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'pizza.png',		
	-- 		},
	-- 		['fchicken'] = {						
	-- 			['name'] = "Fried Chicken",				
	-- 			['price_to_customer'] = 350,	
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 550,
	-- 			['price_to_export'] = 250,		
	-- 			['price_to_owner'] = 5000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'fchicken.png',		
	-- 		},
	-- 		['burger_aish'] = {						
	-- 			['name'] = "Donut",				
	-- 			['price_to_customer'] = 320,	
	-- 			['price_to_customer_min'] = 220,
	-- 			['price_to_customer_max'] = 420,
	-- 			['price_to_export'] = 220,		
	-- 			['price_to_owner'] = 5000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'burger_aish.png',		
	-- 		},
	-- 		['burger_splat'] = {						
	-- 			['name'] = "Splat Burger",				
	-- 			['price_to_customer'] = 500,	
	-- 			['price_to_customer_min'] = 500,
	-- 			['price_to_customer_max'] = 550,
	-- 			['price_to_export'] = 500,		
	-- 			['price_to_owner'] = 500,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'burger_splat.png',		
	-- 		},
	-- 		['burger_kidilan'] = {						
	-- 			['name'] = "Special Burger",				
	-- 			['price_to_customer'] = 900,	
	-- 			['price_to_customer_min'] = 700,
	-- 			['price_to_customer_max'] = 1100,
	-- 			['price_to_export'] = 700,		
	-- 			['price_to_owner'] = 5000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'burger_kidilan.png',		
	-- 		},
	-- 		['burger_fish'] = {						
	-- 			['name'] = "Fish Burger",				
	-- 			['price_to_customer'] = 320,	
	-- 			['price_to_customer_min'] = 220,
	-- 			['price_to_customer_max'] = 520,
	-- 			['price_to_export'] = 220,		
	-- 			['price_to_owner'] = 5000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'burger_fish.png',		
	-- 		},
	-- 		['kanji'] = {						
	-- 			['name'] = "Kanji",				
	-- 			['price_to_customer'] = 250,	
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 500,
	-- 			['price_to_export'] = 250,		
	-- 			['price_to_owner'] = 5000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'kanji.png',		
	-- 		},
	-- 		['moz_sticks'] = {						
	-- 			['name'] = "Mozarella Sticks",				
	-- 			['price_to_customer'] = 250,	
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 250,		
	-- 			['price_to_owner'] = 5000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'moz_sticks.png',		
	-- 		},
	-- 		['taco'] = {						
	-- 			['name'] = "Taco",				
	-- 			['price_to_customer'] = 200,	
	-- 			['price_to_customer_min'] = 200,
	-- 			['price_to_customer_max'] = 400,
	-- 			['price_to_export'] = 200,		
	-- 			['price_to_owner'] = 5000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'taco.png',		
	-- 		},
	-- 		['hotdog'] = {						
	-- 			['name'] = "Hotdog",				
	-- 			['price_to_customer'] = 400,	
	-- 			['price_to_customer_min'] = 400,
	-- 			['price_to_customer_max'] = 600,
	-- 			['price_to_export'] = 400,		
	-- 			['price_to_owner'] = 5000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'hotdog.png',		
	-- 		}
	-- 	}
	-- },

	['burgerfood'] = {
		['page_name'] = "Food",
		['page_desc'] = "Please your apetite",
		['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/fooditems.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['smg_dfburger'] = {						
				['name'] = "Dragon Fire",				
				['price_to_customer'] = 820,	
				['price_to_customer_min'] = 750,
				['price_to_customer_max'] = 1550,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_tcburger.png',		
			},	
			['smg_tcburger'] = {						
				['name'] = "Teriyaki Chicken",				
				['price_to_customer'] = 520,	
				['price_to_customer_min'] = 500,
				['price_to_customer_max'] = 1200,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_tcburger.png',		
			},	
			['smg_bbcburger'] = {						
				['name'] = "Beef & Bok Choy",				
				['price_to_customer'] = 350,	
				['price_to_customer_min'] = 350,
				['price_to_customer_max'] = 1150,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_bbcburger.png',		
			},	
			['smg_cwrap'] = {						
				['name'] = "Chicken Wrap",				
				['price_to_customer'] = 300,	
				['price_to_customer_min'] = 250,
				['price_to_customer_max'] = 1050,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_cwrap.png',		
			},	
			['smg_ffries'] = {						
				['name'] = "Fortune Fries",				
				['price_to_customer'] = 250,	
				['price_to_customer_min'] = 200,
				['price_to_customer_max'] = 1000,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_ffries.png',		
			},
			['smg_gtdstick'] = {						
				['name'] = "Gen Tso Drumstick",				
				['price_to_customer'] = 200,	
				['price_to_customer_min'] = 150,
				['price_to_customer_max'] = 800,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_gtdstick.png',		
			},
			['smg_wsoup'] = {						
				['name'] = "Wonton Soup",				
				['price_to_customer'] = 280,	
				['price_to_customer_min'] = 200,
				['price_to_customer_max'] = 1100,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_wsoup.png',		
			},
			['smg_jitea'] = {						
				['name'] = "Jasmine Iced Tea",				
				['price_to_customer'] = 800,	
				['price_to_customer_min'] = 650,
				['price_to_customer_max'] = 1400,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_jitea.png',		
			},
			['smg_lsoda'] = {						
				['name'] = "Lychee Soda",				
				['price_to_customer'] = 320,	
				['price_to_customer_min'] = 250,
				['price_to_customer_max'] = 1250,
				['price_to_export'] = 10,		
				['price_to_owner'] = 5000,		
				['amount_to_owner'] = 10,		
				['amount_to_delivery'] = 10,	
				['img'] = 'smg_lsoda.png',		
			}
		}
	},

	-- ['ramens'] = {
	-- 	['page_name'] = "Ramen Noodles",
	-- 	['page_desc'] = "Please your apetite",
	-- 	['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/fooditems.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['shoyuramen'] = {						
	-- 			['name'] = "Shoyu Ramen",				
	-- 			['price_to_customer'] = 3000,	
	-- 			['price_to_customer_min'] = 300,
	-- 			['price_to_customer_max'] = 3800,
	-- 			['price_to_export'] = 250,		
	-- 			['price_to_owner'] = 3500,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'shoyuramen.png',		
	-- 		},	
	-- 		['zenramen'] = {						
	-- 			['name'] = "Zen Ramen",				
	-- 			['price_to_customer'] = 2500,	
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 3300,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 3000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'zenramen.png',		
	-- 		},	
	-- 		['specialramen'] = {						
	-- 			['name'] = "Special Ramen",				
	-- 			['price_to_customer'] = 3500,	
	-- 			['price_to_customer_min'] = 350,
	-- 			['price_to_customer_max'] = 4300,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 4000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'specialramen.png',		
	-- 		},	
	-- 		['seafoodramen'] = {						
	-- 			['name'] = "Seafood Ramen",				
	-- 			['price_to_customer'] = 3600,	
	-- 			['price_to_customer_min'] = 360,
	-- 			['price_to_customer_max'] = 4400,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 4100,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'seafoodramen.png',		
	-- 		},	
	-- 		['veganramen'] = {						
	-- 			['name'] = "Vegan Ramen",				
	-- 			['price_to_customer'] = 2000,	
	-- 			['price_to_customer_min'] = 200,
	-- 			['price_to_customer_max'] = 2800,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 2500,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'veganramen.png',		
	-- 		},
	-- 		['sake'] = {						
	-- 			['name'] = "Sake",				
	-- 			['price_to_customer'] = 2000,	
	-- 			['price_to_customer_min'] = 200,
	-- 			['price_to_customer_max'] = 2800,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 2500,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'sake.png',		
	-- 		},
	-- 		['ramunesoda'] = {						
	-- 			['name'] = "Ramune Soda",				
	-- 			['price_to_customer'] = 2000,	
	-- 			['price_to_customer_min'] = 200,
	-- 			['price_to_customer_max'] = 2800,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 2500,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'ramunesoda.png',		
	-- 		},
	-- 		['dorayaki'] = {						
	-- 			['name'] = "Dorayaki",				
	-- 			['price_to_customer'] = 2500,	
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 3300,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 3000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'dorayaki.png',		
	-- 		},
	-- 		['takoyaki'] = {						
	-- 			['name'] = "Takoyaki",				
	-- 			['price_to_customer'] = 2700,	
	-- 			['price_to_customer_min'] = 270,
	-- 			['price_to_customer_max'] = 3500,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 3200,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'takoyaki.png',		
	-- 		}
	-- 	}
	-- },

	-- ['jadefoods'] = {
	-- 	['page_name'] = "Food",
	-- 	['page_desc'] = "Please your apetite",
	-- 	['page_icon'] = '<i class="fa-solid fa-martini-glass"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/fooditems.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['goldporota'] = {						
	-- 			['name'] = "Golden പൊറോട്ട",				
	-- 			['price_to_customer'] = 25000,	
	-- 			['price_to_customer_min'] = 25000,
	-- 			['price_to_customer_max'] = 30000,
	-- 			['price_to_export'] = 25000,		
	-- 			['price_to_owner'] = 25000,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'goldporota.png',		
	-- 		},	
	-- 		['ppori'] = {						
	-- 			['name'] = "പഴംപൊരി",				
	-- 			['price_to_customer'] = 750,	
	-- 			['price_to_customer_min'] = 650,
	-- 			['price_to_customer_max'] = 850,
	-- 			['price_to_export'] = 650,		
	-- 			['price_to_owner'] = 650,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'ppori.png',		
	-- 		},	
	-- 		['porotabeef'] = {						
	-- 			['name'] = "പൊറോട്ട & ബീഫ്",				
	-- 			['price_to_customer'] = 1250,	
	-- 			['price_to_customer_min'] = 1150,
	-- 			['price_to_customer_max'] = 1350,
	-- 			['price_to_export'] = 1150,		
	-- 			['price_to_owner'] = 1150,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'porotabeef.png',		
	-- 		},	
	-- 		['muttonbiri'] = {						
	-- 			['name'] = "Mutton Biriyani",				
	-- 			['price_to_customer'] = 1650,	
	-- 			['price_to_customer_min'] = 1550,
	-- 			['price_to_customer_max'] = 1750,
	-- 			['price_to_export'] = 1550,		
	-- 			['price_to_owner'] = 1550,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'muttonbiri.png',		
	-- 		},	
	-- 		['uzhunvada'] = {						
	-- 			['name'] = "ഉഴുന്ന് വട",				
	-- 			['price_to_customer'] = 250,	
	-- 			['price_to_customer_min'] = 150,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 150,		
	-- 			['price_to_owner'] = 150,		
	-- 			['amount_to_owner'] = 100,		
	-- 			['amount_to_delivery'] = 100,	
	-- 			['img'] = 'uzhunvada.png',		
	-- 		}
	-- 	}
	-- },

	['qupid'] = {
		['page_name'] = "Qupid Store",
		['page_desc'] = "Qupid Store",
		['page_icon'] = '<i class="fa-solid fa-martini-glass"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/perktitles.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['guitar'] = {						
				['name'] = "Guitar",				
				['price_to_customer'] = 1500000,	
				['price_to_customer_min'] = 150000,
				['price_to_customer_max'] = 1600000,
				['price_to_export'] = 200,		
				['price_to_owner'] = 140000,		
				['amount_to_owner'] = 20,		
				['amount_to_delivery'] = 20,	
				['img'] = 'guitar.png',			
			},				
			['teddy'] = {						
				['name'] = "Teddy Bear",				
				['price_to_customer'] = 100000,	
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 130000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 8000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'teddy.png',			
			},	
			['rose'] = {						
				['name'] = "Rose",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 20000,
				['price_to_export'] = 100,		
				['price_to_owner'] = 700,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'rose.png',			
			},	
			['balloon'] = {						
				['name'] = "Balloon",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 20000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 800,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'balloon.png',			
			},
			['bubble'] = {						
				['name'] = "Bubbles",				
				['price_to_customer'] = 150000,	
				['price_to_customer_min'] = 150000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 12000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'bubble.png',			
			},
			['heart'] = {						
				['name'] = "Heart",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 20000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 800,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'heart.png',			
			}
		}
	},

	['chocolates'] = {
		['page_name'] = "Chocolates",
		['page_desc'] = "Chocolates",
		['page_icon'] = '<i class="fa-solid fa-candy-cane"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/chocolate.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['bubbly'] = {						
				['name'] = "Dairy Milk Bubbly",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 20000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 800,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'bubbly.png',			
			},
			['wholenut'] = {						
				['name'] = "Dairy Milk Wholenut",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 20000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 800,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'wholenut.png',			
			},
			['lovemilk'] = {						
				['name'] = "Dairy Milk Silk",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 20000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 800,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'lovemilk.png',			
			}
		}
	},

	['plushes'] = {
		['page_name'] = "Plushes",
		['page_desc'] = "Plushes",
		['page_icon'] = '<i class="fa-regular fa-face-smile"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/plush.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['uwupurpleplush'] = {						
				['name'] = "Purple Plush",				
				['price_to_customer'] = 500000,	
				['price_to_customer_min'] = 500000,
				['price_to_customer_max'] = 530000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 80000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'uwupurpleplush.png',			
			},	
			['uwugreenplush'] = {						
				['name'] = "Green Plush",				
				['price_to_customer'] = 500000,	
				['price_to_customer_min'] = 500000,
				['price_to_customer_max'] = 530000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 80000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'uwugreenplush.png',			
			},
			['uwublueplush'] = {						
				['name'] = "Blue Plush",				
				['price_to_customer'] = 550000,	
				['price_to_customer_min'] = 550000,
				['price_to_customer_max'] = 580000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 100000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'uwublueplush.png',			
			},
			['uwubrownplush'] = {						
				['name'] = "Brown Plush",				
				['price_to_customer'] = 550000,	
				['price_to_customer_min'] = 550000,
				['price_to_customer_max'] = 580000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 100000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'uwubrownplush.png',			
			},
			['uwuyellowplush'] = {						
				['name'] = "Yellow Plush",				
				['price_to_customer'] = 700000,	
				['price_to_customer_min'] = 700000,
				['price_to_customer_max'] = 720000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 180000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'uwuyellowplush.png',			
			},
			['uwuredplush'] = {						
				['name'] = "Red Plush",				
				['price_to_customer'] = 700000,	
				['price_to_customer_min'] = 700000,
				['price_to_customer_max'] = 720000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 180000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'uwuredplush.png',			
			},
			['uwugreenrareplush'] = {						
				['name'] = "Rare Green Plush",				
				['price_to_customer'] = 800000,	
				['price_to_customer_min'] = 800000,
				['price_to_customer_max'] = 820000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 280000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'uwugreenrareplush.png',			
			},
			['uwupinkrareplush'] = {						
				['name'] = "Rare Pink Plush",				
				['price_to_customer'] = 800000,	
				['price_to_customer_min'] = 800000,
				['price_to_customer_max'] = 820000,
				['price_to_export'] = 150,		
				['price_to_owner'] = 280000,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 100,	
				['img'] = 'uwupinkrareplush.png',			
			}
		}
	},

	['cooking'] = {
		['page_name'] = "Cooking",
		['page_desc'] = "Be a chef wherever you want",
		['page_icon'] = '<i class="fa-solid fa-bowl-rice"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/cooking.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['stove1'] = {						
				['name'] = "Stove",				
				['price_to_customer'] = 1456,	
				['price_to_customer_min'] = 1956,
				['price_to_customer_max'] = 2456,
				['price_to_export'] = 1956,		
				['price_to_owner'] = 1056,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'stove1.png',			
			},
			['bbq'] = {						
				['name'] = "BBQ Stove",				
				['price_to_customer'] = 2156,	
				['price_to_customer_min'] = 1156,
				['price_to_customer_max'] = 3156,
				['price_to_export'] = 1156,		
				['price_to_owner'] = 856,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'bbq.png',		
			},
			['capsicum'] = {						
				['name'] = "Capsicum",				
				['price_to_customer'] = 90,	
				['price_to_customer_min'] = 60,
				['price_to_customer_max'] = 120,
				['price_to_export'] = 60,		
				['price_to_owner'] = 30,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'capsicum.png',		
			},
			['tuna'] = {						
				['name'] = "Tuna",				
				['price_to_customer'] = 65,	
				['price_to_customer_min'] = 55,
				['price_to_customer_max'] = 85,
				['price_to_export'] = 55,		
				['price_to_owner'] = 35,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'tuna.png',			
			},
			['chicken'] = {						
				['name'] = "Chicken",				
				['price_to_customer'] = 106,	
				['price_to_customer_min'] = 106,
				['price_to_customer_max'] = 126,
				['price_to_export'] = 55,		
				['price_to_owner'] = 45,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'chicken.png',			
			},
			['garlic'] = {						
				['name'] = "Garlic",				
				['price_to_customer'] = 120,	
				['price_to_customer_min'] = 120,
				['price_to_customer_max'] = 140,
				['price_to_export'] = 55,		
				['price_to_owner'] = 80,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'garlic.png',			
			},
			['masala'] = {						
				['name'] = "Masala",				
				['price_to_customer'] = 230,	
				['price_to_customer_min'] = 230,
				['price_to_customer_max'] = 250,
				['price_to_export'] = 55,		
				['price_to_owner'] = 180,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'masala.png',			
			},
			['salt'] = {						
				['name'] = "Salt",				
				['price_to_customer'] = 12,	
				['price_to_customer_min'] = 12,
				['price_to_customer_max'] = 20,
				['price_to_export'] = 5,		
				['price_to_owner'] = 5,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'salt.png',			
			},
			['butter'] = {						
				['name'] = "Butter",				
				['price_to_customer'] = 86,	
				['price_to_customer_min'] = 86,
				['price_to_customer_max'] = 96,
				['price_to_export'] = 55,		
				['price_to_owner'] = 60,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'butter.png',			
			},
			['oil'] = {						
				['name'] = "Oil",				
				['price_to_customer'] = 25,	
				['price_to_customer_min'] = 25,
				['price_to_customer_max'] = 35,
				['price_to_export'] = 5,		
				['price_to_owner'] = 8,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'oil.png',			
			},
			['lemon'] = {						
				['name'] = "Lemon",				
				['price_to_customer'] = 140,	
				['price_to_customer_min'] = 140,
				['price_to_customer_max'] = 150,
				['price_to_export'] = 55,		
				['price_to_owner'] = 90,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'lemon.png',			
			}
		}
	},

	['gardening'] = {
		['page_name'] = "Gardening",
		['page_desc'] = "Plant a seed. Grow a life.",
		['page_icon'] = '<i class="fa-solid fa-plant-wilt"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/gardening.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			-- ['trowel'] = {						
			-- 	['name'] = "Trowel",				
			-- 	['price_to_customer'] = 1200,	
			-- 	['price_to_customer_min'] = 800,
			-- 	['price_to_customer_max'] = 1800,
			-- 	['price_to_export'] = 800,		
			-- 	['price_to_owner'] = 800,		
			-- 	['amount_to_owner'] = 500,		
			-- 	['amount_to_delivery'] = 10000,	
			-- 	['img'] = 'trowel.png',	
			-- },
			-- ['pumpkinseed'] = {						
			-- 	['name'] = "Pumpkin Seed",				
			-- 	['price_to_customer'] = 65,	
			-- 	['price_to_customer_min'] = 30,
			-- 	['price_to_customer_max'] = 100,
			-- 	['price_to_export'] = 30,		
			-- 	['price_to_owner'] = 30,		
			-- 	['amount_to_owner'] = 100,		
			-- 	['amount_to_delivery'] = 25000,	
			-- 	['img'] = 'pumpkinseed.png',	
			-- },
			-- ['melonseed'] = {						
			-- 	['name'] = "Melon Seed",				
			-- 	['price_to_customer'] = 65,	
			-- 	['price_to_customer_min'] = 30,
			-- 	['price_to_customer_max'] = 100,
			-- 	['price_to_export'] = 30,		
			-- 	['price_to_owner'] = 30,		
			-- 	['amount_to_owner'] = 100,		
			-- 	['amount_to_delivery'] = 25000,	
			-- 	['img'] = 'melonseed.png',	
			-- },
			['mixing_kit'] = {
				['name'] = "Mixing Kit",
				['price_to_customer'] = 15500,
				['price_to_customer_min'] = 12000,
				['price_to_customer_max'] = 19000,
				['price_to_export'] = 3000,
				['price_to_owner'] = 6000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 5000,
				['img'] = 'mixing_kit.png',
			},
			['planter_kit'] = {
				['name'] = "Planter Kit",
				['price_to_customer'] = 15500,
				['price_to_customer_min'] = 12000,
				['price_to_customer_max'] = 19000,
				['price_to_export'] = 3000,
				['price_to_owner'] = 6000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 5000,
				['img'] = 'planter_kit.png',
			},
			['ls_fertilizer'] = {						
				['name'] = "Fertilizer",				
				['price_to_customer'] = 250,	
				['price_to_customer_min'] = 250,
				['price_to_customer_max'] = 350,
				['price_to_export'] = 250,		
				['price_to_owner'] = 100,		
				['amount_to_owner'] = 25000,		
				['amount_to_delivery'] = 5000,	
				['img'] = 'ls_fertilizer.png',		
			},
			['ls_watering_can'] = {						
				['name'] = "Watering Can",				
				['price_to_customer'] = 300,	
				['price_to_customer_min'] = 250,
				['price_to_customer_max'] = 500,
				['price_to_export'] = 250,		
				['price_to_owner'] = 150,		
				['amount_to_owner'] = 25000,		
				['amount_to_delivery'] = 5000,	
				['img'] = 'ls_watering_can.png',	
			}
			-- ['raker'] = {						
			-- 	['name'] = "Raker",				
			-- 	['price_to_customer'] = 1200,	
			-- 	['price_to_customer_min'] = 800,
			-- 	['price_to_customer_max'] = 1800,
			-- 	['price_to_export'] = 800,		
			-- 	['price_to_owner'] = 800,		
			-- 	['amount_to_owner'] = 500,		
			-- 	['amount_to_delivery'] = 10000,	
			-- 	['img'] = 'raker.png',	
			-- },
			-- ['shovel'] = {						
			-- 	['name'] = "Shovel",				
			-- 	['price_to_customer'] = 100,	
			-- 	['price_to_customer_min'] = 50,
			-- 	['price_to_customer_max'] = 300,
			-- 	['price_to_export'] = 50,		
			-- 	['price_to_owner'] = 50,		
			-- 	['amount_to_owner'] = 500,		
			-- 	['amount_to_delivery'] = 10000,	
			-- 	['img'] = 'shovel.png',		
			-- }
		}
	},

	['fishing'] = {
		['page_name'] = "Fishing Items",
		['page_desc'] = "Cast your line and connect with nature on a peaceful fishing adventure.",
		['page_icon'] = '<i class="fa-solid fa-fish"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/fishing.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['rod_box'] = {						
				['name'] = "Rod Box",				
				['price_to_customer'] = 8000,	
				['price_to_customer_min'] = 8000,
				['price_to_customer_max'] = 13000,
				['price_to_export'] = 8000,		
				['price_to_owner'] = 6000,		
				['amount_to_owner'] = 1000,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'rod_box.png',	
			},
			-- ['fishing_net'] = {						
			-- 	['name'] = "Fishing Net",				
			-- 	['price_to_customer'] = 9000,	
			-- 	['price_to_customer_min'] = 9000,
			-- 	['price_to_customer_max'] = 14000,
			-- 	['price_to_export'] = 8000,		
			-- 	['price_to_owner'] = 6000,		
			-- 	['amount_to_owner'] = 1000,		
			-- 	['amount_to_delivery'] = 3000,	
			-- 	['img'] = 'fishing_net.png',	
			-- },
			['earthworm'] = {						
				['name'] = "Earthworm",				
				['price_to_customer'] = 400,	
				['price_to_customer_min'] = 400,
				['price_to_customer_max'] = 550,
				['price_to_export'] = 100,		
				['price_to_owner'] = 240,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'earthworm.png',		
			},
			['breadball'] = {						
				['name'] = "Bread Ball",				
				['price_to_customer'] = 460,	
				['price_to_customer_min'] = 460,
				['price_to_customer_max'] = 600,
				['price_to_export'] = 100,		
				['price_to_owner'] = 340,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'breadball.png',		
			},
			['corn'] = {						
				['name'] = "Corn",				
				['price_to_customer'] = 520,	
				['price_to_customer_min'] = 520,
				['price_to_customer_max'] = 640,
				['price_to_export'] = 100,		
				['price_to_owner'] = 380,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'corn.png',		
			},
			['maggots'] = {						
				['name'] = "Maggots",				
				['price_to_customer'] = 600,	
				['price_to_customer_min'] = 600,
				['price_to_customer_max'] = 800,
				['price_to_export'] = 100,		
				['price_to_owner'] = 440,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'maggots.png',		
			},
			['minnow'] = {						
				['name'] = "Live Minnow",				
				['price_to_customer'] = 700,	
				['price_to_customer_min'] = 700,
				['price_to_customer_max'] = 900,
				['price_to_export'] = 100,		
				['price_to_owner'] = 480,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'minnow.png',		
			},
			['nightcrawler'] = {						
				['name'] = "Night Crawler",				
				['price_to_customer'] = 650,	
				['price_to_customer_min'] = 650,
				['price_to_customer_max'] = 850,
				['price_to_export'] = 100,		
				['price_to_owner'] = 440,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'nightcrawler.png',		
			},
			['bloodworm'] = {						
				['name'] = "Blood Worm",				
				['price_to_customer'] = 800,	
				['price_to_customer_min'] = 800,
				['price_to_customer_max'] = 1000,
				['price_to_export'] = 100,		
				['price_to_owner'] = 540,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'bloodworm.png',		
			},
			['magnet'] = {						
				['name'] = "Fishing Magnet",				
				['price_to_customer'] = 1000,	
				['price_to_customer_min'] = 1000,
				['price_to_customer_max'] = 1200,
				['price_to_export'] = 100,		
				['price_to_owner'] = 540,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'magnet.png',		
			},
			['bobber'] = {						
				['name'] = "Basic Bobber",				
				['price_to_customer'] = 4000,	
				['price_to_customer_min'] = 4000,
				['price_to_customer_max'] = 6000,
				['price_to_export'] = 100,		
				['price_to_owner'] = 3000,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'bobber.png',		
			},
			['spinner'] = {						
				['name'] = "Spinner Lure",				
				['price_to_customer'] = 5300,	
				['price_to_customer_min'] = 5300,
				['price_to_customer_max'] = 7300,
				['price_to_export'] = 100,		
				['price_to_owner'] = 4040,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'spinner.png',		
			},
			['sinker_set'] = {						
				['name'] = "Professional Sinker Set",				
				['price_to_customer'] = 6400,	
				['price_to_customer_min'] = 6400,
				['price_to_customer_max'] = 8400,
				['price_to_export'] = 100,		
				['price_to_owner'] = 5040,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'sinker_set.png',		
			},
			['premium_tackle'] = {						
				['name'] = "Premium Tackle Kit",				
				['price_to_customer'] = 7500,	
				['price_to_customer_min'] = 7500,
				['price_to_customer_max'] = 9500,
				['price_to_export'] = 100,		
				['price_to_owner'] = 6040,		
				['amount_to_owner'] = 3000,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'premium_tackle.png',		
			}
		}
	},

	['robberyitems'] = {
		['page_name'] = "Robbery Items",
		['page_desc'] = "Items that are using to give ZCPD some headaches",
		['page_icon'] = '<i class="fa-solid fa-sack-dollar"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/robberyitems.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['bag'] = {						
				['name'] = "Side Bag",				
				['price_to_customer'] = 1800,	
				['price_to_customer_min'] = 1800,
				['price_to_customer_max'] = 2800,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 1800,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'bag.png',		
			},
			['drill'] = {						
				['name'] = "Drill",				
				['price_to_customer'] = 22500,	
				['price_to_customer_min'] = 22500,
				['price_to_customer_max'] = 23500,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 22500,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'drill.png',		
			},
			['hack_usb'] = {						
				['name'] = "Trojan USB",				
				['price_to_customer'] = 13600,	
				['price_to_customer_min'] = 13600,
				['price_to_customer_max'] = 14600,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 13600,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'hack_usb.png',		
			},
			['thermite_bomb'] = {						
				['name'] = "Thermite Bomb",				
				['price_to_customer'] = 13800,	
				['price_to_customer_min'] = 13800,
				['price_to_customer_max'] = 14800,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 13800,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'thermite_bomb.png',		
			},
			['thermite'] = {						
				['name'] = "Thermite",				
				['price_to_customer'] = 13800,	
				['price_to_customer_min'] = 13800,
				['price_to_customer_max'] = 14800,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 13800,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'thermite.png',		
			},
			['c4_bomb'] = {						
				['name'] = "C4 Bomb",				
				['price_to_customer'] = 16800,	
				['price_to_customer_min'] = 16800,
				['price_to_customer_max'] = 17800,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 16800,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'c4_bomb.png',		
			},
			['lockpick'] = {						
				['name'] = "Lockpick",				
				['price_to_customer'] = 6000,	
				['price_to_customer_min'] = 6000,
				['price_to_customer_max'] = 6500,
				['price_to_export'] = 180,		
				['price_to_owner'] = 6000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'lockpick.png',		
			},
			['cutter'] = {						
				['name'] = "Cutter",				
				['price_to_customer'] = 25000,	
				['price_to_customer_min'] = 25000,
				['price_to_customer_max'] = 26000,
				['price_to_export'] = 1800,		
				['price_to_owner'] = 25000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'cutter.png',		
			}
		}
	},

	['robberysell'] = {
		['page_name'] = "Robbery Items",
		['page_desc'] = "Items that are using to give ZCPD some headaches",
		['page_icon'] = '<i class="fa-solid fa-sack-dollar"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/robberyitems.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['jewels'] = {						
				['name'] = "Jewels",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 230,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'jewels.png',		
			},
			['coke_pooch'] = {						
				['name'] = "Cocaine Pouch",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 130,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'coke_pooch.png',		
			},
			['weed_pooch'] = {						
				['name'] = "Weed Pouch",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 130,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'weed_pooch.png',		
			},
			['statue'] = {						
				['name'] = "Statue",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 17000,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'statue.png',		
			},
			['gold'] = {						
				['name'] = "Gold Bar",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 680,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'gold.png',		
			},
			['rolex'] = {						
				['name'] = "Rolex",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 2400,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'rolex.png',		
			},
			['ring'] = {						
				['name'] = "Ring",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 690,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'ring.png',		
			},
			['diamond_mb'] = {						
				['name'] = "Diamond Box",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 2850,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'diamond_mb.png',		
			},
			['diamond'] = {						
				['name'] = "Diamond",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 1550,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'diamond.png',		
			},
			['necklace'] = {						
				['name'] = "Necklace",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 1890,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'necklace.png',		
			},
			['mia_painting'] = {						
				['name'] = "Mia Painting",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 9600,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'mia_painting.png',		
			},
			['sun_painting'] = {						
				['name'] = "Sun Painting",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 9500,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'sun_painting.png',		
			},
			['itachi_painting'] = {						
				['name'] = "Itachi Painting",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 7150,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'itachi_painting.png',		
			},
			['joker_painting'] = {						
				['name'] = "Joker Painting",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 7600,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'joker_painting.png',		
			},
			['xx_painting'] = {						
				['name'] = "XXXten Painting",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 12900,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'xx_painting.png',		
			},
			['shammy_painting'] = {						
				['name'] = "Shammy Painting",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 9400,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'shammy_painting.png',		
			},
			['quinn_painting'] = {						
				['name'] = "Quinn Painting",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 8300,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'quinn_painting.png',		
			},
			['dead_painting'] = {						
				['name'] = "Dead Painting",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 8900,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'dead_painting.png',		
			},
			-- ['turtle'] = {						
			-- 	['name'] = "Turtle",				
			-- 	['price_to_customer'] = 1800000,	
			-- 	['price_to_customer_min'] = 1800000,
			-- 	['price_to_customer_max'] = 2800000,
			-- 	['price_to_export'] = 720,		
			-- 	['price_to_owner'] = 1800000,		
			-- 	['amount_to_owner'] = 50,		
			-- 	['amount_to_delivery'] = 100,	
			-- 	['img'] = 'turtle.png',		
			-- },
			['pogo'] = {						
				['name'] = "Pogo Sculpture",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 3100,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'pogo.png',		
			},
			['bottle'] = {						
				['name'] = "Bottle",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 1600,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'bottle.png',		
			},
			['panther'] = {						
				['name'] = "Panther",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 3200,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'panther.png',		
			},
			['artgun'] = {						
				['name'] = "Art Gun",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 3300,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'artgun.png',		
			},
			['artskull'] = {						
				['name'] = "Art Skull",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 3250,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'artskull.png',		
			},
			['artegg'] = {						
				['name'] = "Art Egg",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 2700,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'artegg.png',		
			},
			['artlamp'] = {						
				['name'] = "Art Lamp",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 1900,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'artlamp.png',		
			},
			['arthorse'] = {						
				['name'] = "Art Horse",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 2200,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'arthorse.png',		
			},
			['deer_horn'] = {						
				['name'] = "Deer Horn",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 1675,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'deer_horn.png',		
			},
			['boar_tusk'] = {						
				['name'] = "Boar Tusk",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 1550,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 1000,	
				['img'] = 'boar_tusk.png',		
			},
			['coyote_fur'] = {						
				['name'] = "Coyote Fur",				
				['price_to_customer'] = 1800000,	
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2800000,
				['price_to_export'] = 1850,		
				['price_to_owner'] = 1800000,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'coyote_fur.png',		
			},
			--Fish kuntham black
			['great_white_shark'] = {
				['name'] = "Great White Shark",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 89341,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'great_white_shark.png',
			},
			['gold_coin'] = {
				['name'] = "Gold Doubloon",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 24318,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'gold_coin.png',
			},
			['silver_necklace'] = {
				['name'] = "Silver Necklace",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 26247,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'silver_necklace.png',
			},
			['treasure_chest'] = {
				['name'] = "Small Treasure Chest",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 29468,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'treasure_chest.png',
			},
			['ancient_statue'] = {
				['name'] = "Ancient Statue",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 27421,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ancient_statue.png',
			},
			['pearl'] = {
				['name'] = "Giant Pearl",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 29234,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'pearl.png',
			},
			['saw_shark'] = {
				['name'] = "Saw Shark",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 36217,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'saw_shark.png',
			},
			['hammerhead_shark'] = {
				['name'] = "Hammerhead Shark",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 32412,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'hammerhead_shark.png',
			},
			['whale_shark'] = {
				['name'] = "Whale Shark",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 46499,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'whale_shark.png',
			}			
		}
	},

	-- ['craftitems'] = {
	-- 	['page_name'] = "Craft items",
	-- 	['page_desc'] = "Get your items that you need for crafting",
	-- 	-- ['page_icon'] = '<i class="fa-sharp fa-solid fa-sign-posts-wrench"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	['page_icon'] = '<i class="fa-sharp fa-solid fa-sign-posts-wrench"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/craft.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['water'] = {						
	-- 			['name'] = "Water",				
	-- 			['price_to_customer'] = 80,	
	-- 			['price_to_customer_min'] = 80,
	-- 			['price_to_customer_max'] = 110,
	-- 			['price_to_export'] = 15600,		
	-- 			['price_to_owner'] = 40,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 500,	
	-- 			['img'] = 'water.png',		
	-- 		},
	-- 		['ethanol'] = {						
	-- 			['name'] = "Ethanol",				
	-- 			['price_to_customer'] = 80,	
	-- 			['price_to_customer_min'] = 80,
	-- 			['price_to_customer_max'] = 110,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 40,		
	-- 			['amount_to_owner'] = 1250,		
	-- 			['amount_to_delivery'] = 500,	
	-- 			['img'] = 'ethanol.png',		
	-- 		},
	-- 		['ocb_paper'] = {						
	-- 			['name'] = "Ocb Paper",				
	-- 			['price_to_customer'] = 80,	
	-- 			['price_to_customer_min'] = 80,
	-- 			['price_to_customer_max'] = 110,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 40,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 500,	
	-- 			['img'] = 'ocb_paper.png',		
	-- 		},
	-- 		['cytoxan'] = {						
	-- 			['name'] = "Cytoxan",				
	-- 			['price_to_customer'] = 80,	
	-- 			['price_to_customer_min'] = 80,
	-- 			['price_to_customer_max'] = 110,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 40,		
	-- 			['amount_to_owner'] = 1250,		
	-- 			['amount_to_delivery'] = 500,	
	-- 			['img'] = 'cytoxan.png',		
	-- 		},
	-- 		['yeast'] = {						
	-- 			['name'] = "Yeast",				
	-- 			['price_to_customer'] = 80,	
	-- 			['price_to_customer_min'] = 80,
	-- 			['price_to_customer_max'] = 110,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 40,		
	-- 			['amount_to_owner'] = 1250,		
	-- 			['amount_to_delivery'] = 500,	
	-- 			['img'] = 'yeast.png',		
	-- 		},
	-- 		['min_water'] = {						
	-- 			['name'] = "Mineral Water",				
	-- 			['price_to_customer'] = 140,	
	-- 			['price_to_customer_min'] = 140,
	-- 			['price_to_customer_max'] = 160,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 70,		
	-- 			['amount_to_owner'] = 1250,		
	-- 			['amount_to_delivery'] = 1000,	
	-- 			['img'] = 'min_water.png',		
	-- 		},
	-- 		['pmetal'] = {						
	-- 			['name'] = "Processed metal",				
	-- 			['price_to_customer'] = 440,	
	-- 			['price_to_customer_min'] = 440,
	-- 			['price_to_customer_max'] = 460,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 400,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 500,	
	-- 			['img'] = 'pmetal.png',		
	-- 		},
	-- 		['glass'] = {						
	-- 			['name'] = "Glass",				
	-- 			['price_to_customer'] = 490,	
	-- 			['price_to_customer_min'] = 490,
	-- 			['price_to_customer_max'] = 510,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 420,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 500,	
	-- 			['img'] = 'glass.png',		
	-- 		}
	-- 	}
	-- },

	['breakingbad'] = {
		['page_name'] = "Breaking Bad",
		['page_desc'] = "Items that makes even Walter White look good",
		['page_icon'] = '<i class="fa-solid fa-flask"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/breakingbad.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['methlab'] = {						
				['name'] = "M Lab",				
				['price_to_customer'] = 2960,	
				['price_to_customer_min'] = 2960,
				['price_to_customer_max'] = 4960,
				['price_to_export'] = 2960,		
				['price_to_owner'] = 2960,		
				['amount_to_owner'] = 50,		
				['amount_to_delivery'] = 100,	
				['img'] = 'methlab.png',		
			},
			['lithium'] = {						
				['name'] = "Lithium Battery",				
				['price_to_customer'] = 690,	
				['price_to_customer_min'] = 590,
				['price_to_customer_max'] = 890,
				['price_to_export'] = 590,		
				['price_to_owner'] = 590,		
				['amount_to_owner'] = 5000,		
				['amount_to_delivery'] = 5000,	
				['img'] = 'lithium.png',		
			},
			['acetone'] = {						
				['name'] = "Acetone",				
				['price_to_customer'] = 350,	
				['price_to_customer_min'] = 350,
				['price_to_customer_max'] = 400,
				['price_to_export'] = 150,		
				['price_to_owner'] = 350,		
				['amount_to_owner'] = 5000,		
				['amount_to_delivery'] = 2500,	
				['img'] = 'acetone.png',		
			},
			['ls_shovel'] = {						
				['name'] = "Plant Shovel",				
				['price_to_customer'] = 4820,	
				['price_to_customer_min'] = 4820,
				['price_to_customer_max'] = 5820,
				['price_to_export'] = 4820,		
				['price_to_owner'] = 4820,		
				['amount_to_owner'] = 100,		
				['amount_to_delivery'] = 250,	
				['img'] = 'ls_shovel.png',		
			},
			['ls_plant_pot'] = {						
				['name'] = "Plant Pot",				
				['price_to_customer'] = 600,	
				['price_to_customer_min'] = 600,
				['price_to_customer_max'] = 900,
				['price_to_export'] = 600,		
				['price_to_owner'] = 600,		
				['amount_to_owner'] = 500,		
				['amount_to_delivery'] = 2000,	
				['img'] = 'ls_plant_pot.png',		
			},
			['ls_rolling_paper'] = {						
				['name'] = "Rolling Paper",				
				['price_to_customer'] = 500,	
				['price_to_customer_min'] = 500,
				['price_to_customer_max'] = 600,
				['price_to_export'] = 550,		
				['price_to_owner'] = 550,		
				['amount_to_owner'] = 2000,		
				['amount_to_delivery'] = 10000,	
				['img'] = 'ls_rolling_paper.png',		
			},
			['ls_empty_baggy'] = {						
				['name'] = "Empty Baggy",				
				['price_to_customer'] = 500,	
				['price_to_customer_min'] = 500,
				['price_to_customer_max'] = 600,
				['price_to_export'] = 550,		
				['price_to_owner'] = 550,		
				['amount_to_owner'] = 2000,		
				['amount_to_delivery'] = 10000,	
				['img'] = 'ls_empty_baggy.png',		
			},
			['ls_coke_table'] = {						
				['name'] = "Coke Table",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 12000,
				['price_to_export'] = 550,		
				['price_to_owner'] = 8000,		
				['amount_to_owner'] = 2000,		
				['amount_to_delivery'] = 10000,	
				['img'] = 'ls_coke_table.png',		
			}

		}
	},

	['smokersparadise'] = {
		['page_name'] = "Smoker's Paradise",
		['page_desc'] = "Smoke your life away to death.",
		['page_icon'] = '<i class="fa-solid fa-smoking"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/smokersparadise.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['lighter'] = {						
				['name'] = "Lighter",				
				['price_to_customer'] = 350,	
				['price_to_customer_min'] = 250,
				['price_to_customer_max'] = 450,
				['price_to_export'] = 250,		
				['price_to_owner'] = 250,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'lighter.png',		
			},
			['marlborocig'] = {						
				['name'] = "Marlboro Cig",				
				['price_to_customer'] = 195,	
				['price_to_customer_min'] = 185,
				['price_to_customer_max'] = 205,
				['price_to_export'] = 85,		
				['price_to_owner'] = 85,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'marlborocig.png',	
			},
			['redwcig'] = {						
				['name'] = "Redwood Cig",				
				['price_to_customer'] = 125,	
				['price_to_customer_min'] = 115,
				['price_to_customer_max'] = 135,
				['price_to_export'] = 115,		
				['price_to_owner'] = 115,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'redwcig.png',		
			},
			['marlboro'] = {						
				['name'] = "Marlboro Pack",				
				['price_to_customer'] = 990,	
				['price_to_customer_min'] = 850,
				['price_to_customer_max'] = 1050,
				['price_to_export'] = 850,		
				['price_to_owner'] = 850,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'marlboro.png',		
			},
			['redw'] = {						
				['name'] = "Redwood Pack",				
				['price_to_customer'] = 1200,	
				['price_to_customer_min'] = 1100,
				['price_to_customer_max'] = 1300,
				['price_to_export'] = 1100,		
				['price_to_owner'] = 1100,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'redw.png',		
			},
			['vape'] = {						
				['name'] = "Vape",				
				['price_to_customer'] = 280,	
				['price_to_customer_min'] = 280,
				['price_to_customer_max'] = 380,
				['price_to_export'] = 280,		
				['price_to_owner'] = 280,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'vape.png',		
			},
			['liquid'] = {						
				['name'] = "Liquid",				
				['price_to_customer'] = 480,	
				['price_to_customer_min'] = 400,
				['price_to_customer_max'] = 550,
				['price_to_export'] = 400,		
				['price_to_owner'] = 400,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'liquid.png',		
			},
			['cubancigar'] = {						
				['name'] = "Cuban Cigar",				
				['price_to_customer'] = 490,	
				['price_to_customer_min'] = 400,
				['price_to_customer_max'] = 550,
				['price_to_export'] = 400,		
				['price_to_owner'] = 400,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'cubancigar.png',		
			},
			['davidoffcigar'] = {						
				['name'] = "Davidoff Cigar",				
				['price_to_customer'] = 385,	
				['price_to_customer_min'] = 345,
				['price_to_customer_max'] = 485,
				['price_to_export'] = 345,		
				['price_to_owner'] = 345,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'davidoffcigar.png',	
			},
			['og_kush_joint'] = {						
				['name'] = "OG Kush Joint",				
				['price_to_customer'] = 128,	
				['price_to_customer_min'] = 128,
				['price_to_customer_max'] = 145,
				['price_to_export'] = 128,		
				['price_to_owner'] = 128,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'og_kush_joint.png',	
			},
			['blue_dream_joint'] = {						
				['name'] = "Blue Dream Joint",				
				['price_to_customer'] = 195,	
				['price_to_customer_min'] = 145,
				['price_to_customer_max'] = 280,
				['price_to_export'] = 145,		
				['price_to_owner'] = 145,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'blue_dream_joint.png',	
			},
			['purple_haze_joint'] = {						
				['name'] = "Purple Haze Joint",				
				['price_to_customer'] = 275,	
				['price_to_customer_min'] = 175,
				['price_to_customer_max'] = 325,
				['price_to_export'] = 175,		
				['price_to_owner'] = 175,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'purple_haze_joint.png',	
			},
			['banana_kush_joint'] = {						
				['name'] = "Banana Kush Joint",				
				['price_to_customer'] = 185,	
				['price_to_customer_min'] = 145,
				['price_to_customer_max'] = 280,
				['price_to_export'] = 145,		
				['price_to_owner'] = 145,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'banana_kush_joint.png',		
			}
		}
	},

	['cheers'] = {
		['page_name'] = "Cheers!!",
		['page_desc'] = "Glass clinking and liver melting!!",
		['page_icon'] = '<i class="fa-solid fa-champagne-glasses"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/cheers.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['piswasser'] = {						
				['name'] = "Beer",				
				['price_to_customer'] = 1540,	
				['price_to_customer_min'] = 1540,
				['price_to_customer_max'] = 2540,
				['price_to_export'] = 1540,		
				['price_to_owner'] = 1540,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'piswasser.png',		
			},
			['mount_whisky'] = {						
				['name'] = "Mount Whiskey",				
				['price_to_customer'] = 2000,	
				['price_to_customer_min'] = 2000,
				['price_to_customer_max'] = 2440,
				['price_to_export'] = 2000,		
				['price_to_owner'] = 2000,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'mount_whisky.png',	
			},
			['tequila'] = {						
				['name'] = "Tequila",				
				['price_to_customer'] = 5440,	
				['price_to_customer_min'] = 5440,
				['price_to_customer_max'] = 7440,
				['price_to_export'] = 5440,		
				['price_to_owner'] = 5440,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'tequila.png',		
			},
			['costa_del_perro'] = {						
				['name'] = "Costa Del Perro",				
				['price_to_customer'] = 3500,	
				['price_to_customer_min'] = 3500,
				['price_to_customer_max'] = 4500,
				['price_to_export'] = 3500,		
				['price_to_owner'] = 3500,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'costa_del_perro.png',
			},
			['nogo_vodka'] = {						
				['name'] = "Vodka",				
				['price_to_customer'] = 3000,	
				['price_to_customer_min'] = 2540,
				['price_to_customer_max'] = 4540,
				['price_to_export'] = 2540,		
				['price_to_owner'] = 2540,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'nogo_vodka.png',		
			},
			['raine'] = {						
				['name'] = "Jinja",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 9740,
				['price_to_customer_max'] = 13740,
				['price_to_export'] = 9740,		
				['price_to_owner'] = 9000,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'raine.png',		
			},
			['rockford_hill'] = {						
				['name'] = "Rockford Hills",				
				['price_to_customer'] = 7500,	
				['price_to_customer_min'] = 6500,
				['price_to_customer_max'] = 8500,
				['price_to_export'] = 6500,		
				['price_to_owner'] = 7500,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'rockford_hill.png',	
			},
			['vinewood_red'] = {						
				['name'] = "Vinewood Red",				
				['price_to_customer'] = 5500,	
				['price_to_customer_min'] = 5500,
				['price_to_customer_max'] = 6500,
				['price_to_export'] = 5500,		
				['price_to_owner'] = 5500,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'vinewood_red.png',	
			},
			['vinewood_blanc'] = {						
				['name'] = "Vinewood Blanc",				
				['price_to_customer'] = 5500,	
				['price_to_customer_min'] = 5500,
				['price_to_customer_max'] = 6500,
				['price_to_export'] = 5500,		
				['price_to_owner'] = 5500,		
				['amount_to_owner'] = 2500,		
				['amount_to_delivery'] = 3000,	
				['img'] = 'vinewood_blanc.png',		
			}
		}
	},

	['vatt'] = {
		['page_name'] = "Vatt",
		['page_desc'] = "Nadan items with power",
		['page_icon'] = '<i class="fa-solid fa-wine-bottle"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/vatt.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 250, 		-- Price to sell the category
		['items'] = {
			['paili_vat'] = {						
				['name'] = "Moonlit Sting",				
				['price_to_customer'] = 12500,	
				['price_to_customer_min'] = 12500,
				['price_to_customer_max'] = 15000,
				['price_to_export'] = 100,		
				['price_to_owner'] = 8000,		
				['amount_to_owner'] = 200,		
				['amount_to_delivery'] = 10,	
				['img'] = 'paili_vat.png',			
			},
			['vbos'] = {						
				['name'] = "Starlit Sip",				
				['price_to_customer'] = 10000,	
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 13500,
				['price_to_export'] = 100,		
				['price_to_owner'] = 6000,		
				['amount_to_owner'] = 300,		
				['amount_to_delivery'] = 15,	
				['img'] = 'vbos.png',					
			}
		}
	},

	['meleeweapons'] = {
		['page_name'] = "Melee weapons",
		['page_desc'] = "A melee weapon is any handheld weapon used in hand-to-hand combat",
		['page_icon'] = '<i class="fa-solid fa-hand-fist"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/meleeweapons.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['WEAPON_KNIFE'] = {
				['name'] = "KNIFE",
				['price_to_customer'] = 12400,
				['price_to_customer_min'] = 12400,
				['price_to_customer_max'] = 15800,
				['price_to_export'] = 124,
				['price_to_owner'] = 12400,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_KNIFE.png',
			},		
			['WEAPON_KNUCKLE'] = {
				['name'] = "Knucle Dusters",
				['price_to_customer'] = 121,
				['price_to_customer_min'] = 12100,
				['price_to_customer_max'] = 15500,
				['price_to_export'] = 12100,
				['price_to_owner'] = 12100,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_KNUCKLE.png',
			},
			['WEAPON_SLEDGEHAMMER'] = {
				['name'] = "Sledgehammer",
				['price_to_customer'] = 18500,
				['price_to_customer_min'] = 18500,
				['price_to_customer_max'] = 22000,
				['price_to_export'] = 185,
				['price_to_owner'] = 18500,
				['amount_to_owner'] = 200,
				['amount_to_delivery'] = 15,
				['img'] = 'WEAPON_SLEDGEHAMMER.png',
			},
			['WEAPON_GOLFCLUB'] = {
				['name'] = "Golf Club",
				['price_to_customer'] = 8500,
				['price_to_customer_min'] = 8500,
				['price_to_customer_max'] = 12000,
				['price_to_export'] = 85,
				['price_to_owner'] = 8500,
				['amount_to_owner'] = 300,
				['amount_to_delivery'] = 20,
				['img'] = 'WEAPON_GOLFCLUB.png',
			},
			['helmet'] = {
				['name'] = "HELMET",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 10000,
				['price_to_customer_max'] = 12000,
				['price_to_export'] = 60,
				['price_to_owner'] = 8000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 50,
				['img'] = 'helmet.png',
			},	
			['WEAPON_FLASHLIGHT'] = {
				['name'] = "Flashlight",
				['price_to_customer'] = 11450,
				['price_to_customer_min'] = 11450,
				['price_to_customer_max'] = 15550,
				['price_to_export'] = 114,
				['price_to_owner'] = 11450,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_FLASHLIGHT.png',
			},
			['WEAPON_SWITCHBLADE'] = {
				['name'] = "Switchblade",
				['price_to_customer'] = 24950,
				['price_to_customer_min'] = 24950,
				['price_to_customer_max'] = 29650,
				['price_to_export'] = 249,
				['price_to_owner'] = 24950,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_SWITCHBLADE.png',
			},
			['WEAPON_KEYBOARD'] = {
				['name'] = "Keyboard",
				['price_to_customer'] = 17200,
				['price_to_customer_min'] = 17200,
				['price_to_customer_max'] = 19700,
				['price_to_export'] = 172,
				['price_to_owner'] = 17200,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_KEYBOARD.png',
			},
			['WEAPON_BRAWLCHAIR'] = {
				['name'] = "Brawl Chair",
				['price_to_customer'] = 25000,
				['price_to_customer_min'] = 25000,
				['price_to_customer_max'] = 30000,
				['price_to_export'] = 250,
				['price_to_owner'] = 25000,
				['amount_to_owner'] = 100,
				['amount_to_delivery'] = 15,
				['img'] = 'WEAPON_BRAWLCHAIR.png',
			}
		}
	},

	['pistolwhite'] = {
		['page_name'] = "Pistols",
		['page_desc'] = "Ranged weapon is any weapon capable of engaging targets at a distance beyond immediate physical contact",
		['page_icon'] = '<i class="fa-solid fa-gun"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/pistols.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['WEAPON_PISTOL_MK2'] = {
				['name'] = "Pistol",
				['price_to_customer'] = 55500,
				['price_to_customer_min'] = 50500,
				['price_to_customer_max'] = 62500,
				['price_to_export'] = 325,
				['price_to_owner'] = 50500,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_PISTOL_MK2.png',
			},
			['WEAPON_COMBATPISTOL'] = {
				['name'] = "JW2 Combat Master",
				['price_to_customer'] = 102500,
				['price_to_customer_min'] = 90500,
				['price_to_customer_max'] = 107650,
				['price_to_export'] = 525,
				['price_to_owner'] = 90500,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_COMBATPISTOL.png',
			},
			['WEAPON_HEAVYPISTOL'] = {
				['name'] = "Heavy Pistol",
				['price_to_customer'] = 110000,
				['price_to_customer_min'] = 110000,
				['price_to_customer_max'] = 120000,
				['price_to_export'] = 682,
				['price_to_owner'] = 100200,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_HEAVYPISTOL.png',
			},
			['WEAPON_AUTOMATICPISTOL'] = {
				['name'] = "Automatic Pistol",
				['price_to_customer'] = 110000,
				['price_to_customer_min'] = 90000,
				['price_to_customer_max'] = 115000,
				['price_to_export'] = 580,
				['price_to_owner'] = 90000,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_AUTOMATICPISTOL.png',
			},
			['WEAPON_SNSPISTOL'] = {
				['name'] = "SNS Pistol",
				['price_to_customer'] = 755850,
				['price_to_customer_min'] = 65850,
				['price_to_customer_max'] = 90850,
				['price_to_export'] = 458,
				['price_to_owner'] = 65850,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_SNSPISTOL.png',
			},
			['WEAPON_DEAGLE'] = {
				['name'] = "Desert Eagle",
				['price_to_customer'] = 120000,
				['price_to_customer_min'] = 105500,
				['price_to_customer_max'] = 130500,
				['price_to_export'] = 778,
				['price_to_owner'] = 105500,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_DEAGLE.png',
			}
		}
	},
	['riflewhite'] = {
		['page_name'] = "Rifles",
		['page_desc'] = "Ranged weapon is any weapon capable of engaging targets at a distance beyond immediate physical contact",
		['page_icon'] = '<i class="fa-solid fa-person-rifle"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/rifle.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {		
			['WEAPON_KILO'] = {
				['name'] = "KILO",
				['price_to_customer'] = 3850000,
				['price_to_customer_min'] = 3850000,
				['price_to_customer_max'] = 4050000,
				['price_to_export'] = 5,
				['price_to_owner'] = 3650000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_KILO.png',	
			},
			['WEAPON_AKBULL'] = {
				['name'] = "AKBULL",
				['price_to_customer'] = 3450000,
				['price_to_customer_min'] = 3450000,
				['price_to_customer_max'] = 3750000,
				['price_to_export'] = 5,
				['price_to_owner'] = 3450000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_AKBULL.png',
			},
			['WEAPON_LBRS'] = {
				['name'] = "LBRS Battle Rifle",
				['price_to_customer'] = 3600000,
				['price_to_customer_min'] = 3600000,
				['price_to_customer_max'] = 3900000,
				['price_to_export'] = 5,
				['price_to_owner'] = 3400000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_LBRS.png',
			},
			['WEAPON_LR25'] = {
				['name'] = "LR25 Sniper Rifle",
				['price_to_customer'] = 4200000,
				['price_to_customer_min'] = 4200000,
				['price_to_customer_max'] = 4500000,
				['price_to_export'] = 5,
				['price_to_owner'] = 4000000,
				['amount_to_owner'] = 3,
				['amount_to_delivery'] = 3,
				['img'] = 'WEAPON_LR25.png',
			}
		}
	},
	['pharmacy'] = {
		['page_name'] = "Pharmacy",
		['page_desc'] = "Medicines",
		['page_icon'] = '<i class="fa-solid fa-hospital"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/pharmacy.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {		
			['molnupiravir'] = {
				['name'] = "Molnupiravir",
				['price_to_customer'] = 500,
				['price_to_customer_min'] = 500,
				['price_to_customer_max'] = 550,
				['price_to_export'] = 7,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'molnupiravir.png',
			},
			['gingermedicine'] = {
				['name'] = "Ginger Medicine",
				['price_to_customer'] = 550,
				['price_to_customer_min'] = 550,
				['price_to_customer_max'] = 600,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'gingermedicine.png',
			},
			['nasuamedicine'] = {
				['name'] = "Nausea Medicine",
				['price_to_customer'] = 570,
				['price_to_customer_min'] = 570,
				['price_to_customer_max'] = 620,
				['price_to_export'] = 4,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'nasuamedicine.png',
			},
			['coughmedicine'] = {
				['name'] = "Cough Medicine",
				['price_to_customer'] = 480,
				['price_to_customer_min'] = 480,
				['price_to_customer_max'] = 530,
				['price_to_export'] = 6,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'coughmedicine.png',
			},
			['multivitamins'] = {
				['name'] = "Eternal Multi Vitamins",
				['price_to_customer'] = 670,
				['price_to_customer_min'] = 670,
				['price_to_customer_max'] = 720,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 25,
				['amount_to_delivery'] = 50,
				['img'] = 'multivitamins.png',
			},
			['diatabs'] = {
				['name'] = "Diatabs",
				['price_to_customer'] = 530,
				['price_to_customer_min'] = 530,
				['price_to_customer_max'] = 580,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 25,
				['amount_to_delivery'] = 50,
				['img'] = 'diatabs.png',
			},
			['acetaminophen'] = {
				['name'] = "Acetaminophen",
				['price_to_customer'] = 460,
				['price_to_customer_min'] = 460,
				['price_to_customer_max'] = 510,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 25,
				['amount_to_delivery'] = 50,
				['img'] = 'acetaminophen.png',
			},
			['offlotion'] = {
				['name'] = "Off Lotion",
				['price_to_customer'] = 720,
				['price_to_customer_min'] = 720,
				['price_to_customer_max'] = 770,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 25,
				['amount_to_delivery'] = 50,
				['img'] = 'offlotion.png',
			},
			['antihistamine'] = {
				['name'] = "Anti Histamine",
				['price_to_customer'] = 680,
				['price_to_customer_min'] = 680,
				['price_to_customer_max'] = 730,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 25,
				['amount_to_delivery'] = 50,
				['img'] = 'antihistamine.png',
			},
			['ambroxol'] = {
				['name'] = "Ambroxol",
				['price_to_customer'] = 540,
				['price_to_customer_min'] = 540,
				['price_to_customer_max'] = 590,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 25,
				['amount_to_delivery'] = 50,
				['img'] = 'ambroxol.png',
			},
			['baraclude'] = {
				['name'] = "Baraclude",
				['price_to_customer'] = 490,
				['price_to_customer_min'] = 490,
				['price_to_customer_max'] = 540,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 25,
				['amount_to_delivery'] = 50,
				['img'] = 'baraclude.png',
			},
			['metronidazole'] = {
				['name'] = "Metronidazole",
				['price_to_customer'] = 580,
				['price_to_customer_min'] = 580,
				['price_to_customer_max'] = 630,
				['price_to_export'] = 3,
				['price_to_owner'] = 350000000,
				['amount_to_owner'] = 25,
				['amount_to_delivery'] = 50,
				['img'] = 'metronidazole.png',
			}
		}
	},

	-- ['rawmaterials'] = {
	-- 	['page_name'] = "Raw Materials For Medicines",
	-- 	['page_desc'] = "Medicines Making items",
	-- 	['page_icon'] = '<i class="fa-solid fa-hospital"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/snipers.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['sodium_hydroxide'] = {						
	-- 			['name'] = "Sodium Hydroxide",				
	-- 			['price_to_customer'] = 200,	
	-- 			['price_to_customer_min'] = 200,
	-- 			['price_to_customer_max'] = 210,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 150,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'sodium_hydroxide.png',		
	-- 		},
	-- 		['acetic_acid'] = {						
	-- 			['name'] = "Acetic Acid",				
	-- 			['price_to_customer'] = 210,	
	-- 			['price_to_customer_min'] = 210,
	-- 			['price_to_customer_max'] = 220,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 160,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'acetic_acid.png',		
	-- 		},
	-- 		['catalyst'] = {						
	-- 			['name'] = "Catalyst",				
	-- 			['price_to_customer'] = 250,	
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 260,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 180,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'catalyst.png',		
	-- 		},
	-- 		['nucleoside'] = {						
	-- 			['name'] = "Nucleoside",				
	-- 			['price_to_customer'] = 260,	
	-- 			['price_to_customer_min'] = 260,
	-- 			['price_to_customer_max'] = 270,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 180,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'nucleoside.png',		
	-- 		},
	-- 		['phosphorus'] = {						
	-- 			['name'] = "Phosphorus",				
	-- 			['price_to_customer'] = 300,	
	-- 			['price_to_customer_min'] = 300,
	-- 			['price_to_customer_max'] = 310,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 230,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'phosphorus.png',		
	-- 		},
	-- 		['aminoprop'] = {						
	-- 			['name'] = "Aminoprop",				
	-- 			['price_to_customer'] = 190,	
	-- 			['price_to_customer_min'] = 190,
	-- 			['price_to_customer_max'] = 200,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 120,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'aminoprop.png',		
	-- 		},
	-- 		['hydrochloric_acid'] = {						
	-- 			['name'] = "Hydrochloric Acid",				
	-- 			['price_to_customer'] = 240,	
	-- 			['price_to_customer_min'] = 240,
	-- 			['price_to_customer_max'] = 250,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 190,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'hydrochloric_acid.png',		
	-- 		},
	-- 		['bromhexine'] = {						
	-- 			['name'] = "Brom hexine",				
	-- 			['price_to_customer'] = 260,	
	-- 			['price_to_customer_min'] = 260,
	-- 			['price_to_customer_max'] = 270,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 200,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'bromhexine.png',		
	-- 		},
	-- 		['chlorpheniramine'] = {						
	-- 			['name'] = "Chloro pheniramine",				
	-- 			['price_to_customer'] = 290,	
	-- 			['price_to_customer_min'] = 290,
	-- 			['price_to_customer_max'] = 310,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 220,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'chlorpheniramine.png',		
	-- 		},
	-- 		['diphenhydramine'] = {						
	-- 			['name'] = "Diphen hydramine",				
	-- 			['price_to_customer'] = 410,	
	-- 			['price_to_customer_min'] = 410,
	-- 			['price_to_customer_max'] = 420,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 370,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'diphenhydramine.png',		
	-- 		},
	-- 		['essential_oil'] = {						
	-- 			['name'] = "Essential Oil",				
	-- 			['price_to_customer'] = 290,	
	-- 			['price_to_customer_min'] = 290,
	-- 			['price_to_customer_max'] = 310,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 220,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'essential_oil.png',		
	-- 		},
	-- 		['coconut_oil'] = {						
	-- 			['name'] = "Coconut Oil",				
	-- 			['price_to_customer'] = 190,	
	-- 			['price_to_customer_min'] = 190,
	-- 			['price_to_customer_max'] = 210,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 120,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'coconut_oil.png',		
	-- 		},
	-- 		['acetic_anhydride'] = {						
	-- 			['name'] = "Acetic Anhydride",				
	-- 			['price_to_customer'] = 340,	
	-- 			['price_to_customer_min'] = 340,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 290,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'acetic_anhydride.png',		
	-- 		},
	-- 		['aminophenol'] = {						
	-- 			['name'] = "Amino Phenol",				
	-- 			['price_to_customer'] = 250,	
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 260,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 200,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'aminophenol.png',		
	-- 		},
	-- 		['pectin'] = {						
	-- 			['name'] = "Pectin",				
	-- 			['price_to_customer'] = 190,	
	-- 			['price_to_customer_min'] = 190,
	-- 			['price_to_customer_max'] = 210,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 120,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'pectin.png',		
	-- 		},
	-- 		['loperamide'] = {						
	-- 			['name'] = "Loper Amide",				
	-- 			['price_to_customer'] = 230,	
	-- 			['price_to_customer_min'] = 230,
	-- 			['price_to_customer_max'] = 250,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 170,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'loperamide.png',		
	-- 		},
	-- 		['moringa'] = {						
	-- 			['name'] = "Moringa",				
	-- 			['price_to_customer'] = 80,	
	-- 			['price_to_customer_min'] = 80,
	-- 			['price_to_customer_max'] = 100,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 30,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'moringa.png',		
	-- 		},
	-- 		['spirulina'] = {						
	-- 			['name'] = "Spirulina",				
	-- 			['price_to_customer'] = 260,	
	-- 			['price_to_customer_min'] = 260,
	-- 			['price_to_customer_max'] = 280,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 200,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'spirulina.png',		
	-- 		},
	-- 		['thyme'] = {						
	-- 			['name'] = "Thyme",				
	-- 			['price_to_customer'] = 180,	
	-- 			['price_to_customer_min'] = 180,
	-- 			['price_to_customer_max'] = 200,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 140,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'thyme.png',		
	-- 		},
	-- 		['lemon_juice'] = {						
	-- 			['name'] = "Lemon Juice",				
	-- 			['price_to_customer'] = 270,	
	-- 			['price_to_customer_min'] = 270,
	-- 			['price_to_customer_max'] = 280,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 220,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'lemon_juice.png',		
	-- 		},
	-- 		['peppermint_leaves'] = {						
	-- 			['name'] = "Peppermint Leaves",				
	-- 			['price_to_customer'] = 50,	
	-- 			['price_to_customer_min'] = 50,
	-- 			['price_to_customer_max'] = 70,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 20,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'peppermint_leaves.png',		
	-- 		},
	-- 		['ginger'] = {						
	-- 			['name'] = "Ginger",				
	-- 			['price_to_customer'] = 230,	
	-- 			['price_to_customer_min'] = 230,
	-- 			['price_to_customer_max'] = 250,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 180,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'ginger.png',		
	-- 		},
	-- 		['honey'] = {						
	-- 			['name'] = "Honey",				
	-- 			['price_to_customer'] = 300,	
	-- 			['price_to_customer_min'] = 300,
	-- 			['price_to_customer_max'] = 320,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 260,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'honey.png',		
	-- 		},
	-- 		['cyanogen'] = {						
	-- 			['name'] = "Cyanogen",				
	-- 			['price_to_customer'] = 340,	
	-- 			['price_to_customer_min'] = 340,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 280,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'cyanogen.png',		
	-- 		},
	-- 		['methylnitro'] = {						
	-- 			['name'] = "Methyl Nitro",				
	-- 			['price_to_customer'] = 390,	
	-- 			['price_to_customer_min'] = 390,
	-- 			['price_to_customer_max'] = 410,
	-- 			['price_to_export'] = 12200,		
	-- 			['price_to_owner'] = 320,		
	-- 			['amount_to_owner'] = 125,		
	-- 			['amount_to_delivery'] = 80,	
	-- 			['img'] = 'methylnitro.png',		
	-- 		}
	-- 	}
	-- },
	['otherweaponwhite'] = {
		['page_name'] = "Other Guns",
		['page_desc'] = "Mixed, compact, lightweight, and highly versatile firearms designed for close-quarter combat and rapid-fire shooting",
		['page_icon'] = '<i class="fa-solid fa-briefcase"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/smg.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['WEAPON_M700'] = {
				['name'] = "Remington M700",
				['price_to_customer'] = 425000,
				['price_to_customer_min'] = 400000,
				['price_to_customer_max'] = 475000,
				['price_to_export'] = 425,
				['price_to_owner'] = 425000,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_M700.png',
			},
			['WEAPON_MP7F'] = {
				['name'] = "MP7F",
				['price_to_customer'] = 1850000,
				['price_to_customer_min'] = 1850000,
				['price_to_customer_max'] = 2150000,
				['price_to_export'] = 5,
				['price_to_owner'] = 1650000,
				['amount_to_owner'] = 8,
				['amount_to_delivery'] = 8,
				['img'] = 'WEAPON_MP7F.png',
			},
			['WEAPON_MCX'] = {
				['name'] = "SIG MCX",
				['price_to_customer'] = 3200000,
				['price_to_customer_min'] = 3200000,
				['price_to_customer_max'] = 3500000,
				['price_to_export'] = 5,
				['price_to_owner'] = 3000000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_MCX.png',
			},
			['WEAPON_SIG'] = {
				['name'] = "SIG P320-M18",
				['price_to_customer'] = 87500,
				['price_to_customer_min'] = 77500,
				['price_to_customer_max'] = 98500,
				['price_to_export'] = 778,
				['price_to_owner'] = 77500,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 25,
				['img'] = 'WEAPON_SIG.png',
			}
		}
	},

	['meleeweaponsblack'] = {
		['page_name'] = "Melee weapons",
		['page_desc'] = "A melee weapon is any handheld weapon used in hand-to-hand combat",
		['page_icon'] = '<i class="fa-solid fa-hand-fist"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/meleeweapons.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['WEAPON_BAT'] = {
				['name'] = "Wooden Bat",
				['price_to_customer'] = 146500,
				['price_to_customer_min'] = 146500,
				['price_to_customer_max'] = 152500,
				['price_to_export'] = 146,
				['price_to_owner'] = 146500,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_BATS.png',
			},
			['WEAPON_BATTLEAXE'] = {
				['name'] = "BATTLEAXE",
				['price_to_customer'] = 397000,
				['price_to_customer_min'] = 397000,
				['price_to_customer_max'] = 402000,
				['price_to_export'] = 397,
				['price_to_owner'] = 397000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_BATTLEAXE.png',
			},
			['WEAPON_BOTTLE'] = {
				['name'] = "Bottle",
				['price_to_customer'] = 397000,
				['price_to_customer_min'] = 397000,
				['price_to_customer_max'] = 402000,
				['price_to_export'] = 397,
				['price_to_owner'] = 397000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_BOTTLE.png',
			},
			['WEAPON_CROWBAR'] = {
				['name'] = "CROWBAR",
				['price_to_customer'] = 397000,
				['price_to_customer_min'] = 397000,
				['price_to_customer_max'] = 402000,
				['price_to_export'] = 397,
				['price_to_owner'] = 397000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_CROWBAR.png',
			},
			['WEAPON_HATCHET'] = {
				['name'] = "Stone Hatchet",
				['price_to_customer'] = 397000,
				['price_to_customer_min'] = 397000,
				['price_to_customer_max'] = 402000,
				['price_to_export'] = 397,
				['price_to_owner'] = 397000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_HATCHET.png',
			},
			['WEAPON_HAMMER'] = {
				['name'] = "HAMMER",
				['price_to_customer'] = 397000,
				['price_to_customer_min'] = 397000,
				['price_to_customer_max'] = 402000,
				['price_to_export'] = 397,
				['price_to_owner'] = 397000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_HAMMER.png',
			},
			['WEAPON_MACHETE'] = {
				['name'] = "MACHETE",
				['price_to_customer'] = 397000,
				['price_to_customer_min'] = 397000,
				['price_to_customer_max'] = 402000,
				['price_to_export'] = 397,
				['price_to_owner'] = 397000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_MACHETE.png',
			}
		}
	},

	['pistolblack'] = {
		['page_name'] = "Pistols",
		['page_desc'] = "Handheld pistols of the underworld",
		['page_icon'] = '<i class="fa-solid fa-gun"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/pistols.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['WEAPON_UTGLOCK'] = {
				['name'] = "Utopia Glock",
				['price_to_customer'] = 325000,
				['price_to_customer_min'] = 325000,
				['price_to_customer_max'] = 340000,
				['price_to_export'] = 325,
				['price_to_owner'] = 325000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_UTGLOCK.png',
			},
			['WEAPON_PYTHON'] = {
				['name'] = "Python Revolver",
				['price_to_customer'] = 225000,
				['price_to_customer_min'] = 225000,
				['price_to_customer_max'] = 245000,
				['price_to_export'] = 225,
				['price_to_owner'] = 225000,
				['amount_to_owner'] = 50,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_PYTHON.png',
			}
		}
	},

	['rifleblack'] = {
		['page_name'] = "Rifles",
		['page_desc'] = "Rifle guns",
		['page_icon'] = '<i class="fa-solid fa-person-rifle"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/rifles.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['WEAPON_UTOPIA'] = {
				['name'] = "UTOPIA",
				['price_to_customer'] = 2500000,
				['price_to_customer_min'] = 2300000,
				['price_to_customer_max'] = 2700000,
				['price_to_export'] = 5,
				['price_to_owner'] = 2300000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_UTOPIA.png',	
			},	
			['WEAPON_G36'] = {
				['name'] = "HK G36",
				['price_to_customer'] = 2200000,
				['price_to_customer_min'] = 2000000,
				['price_to_customer_max'] = 2400000,
				['price_to_export'] = 5,
				['price_to_owner'] = 2000000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_G36.png',	
			},	
			['WEAPON_M762'] = {
				['name'] = "FB Beryl M762",
				['price_to_customer'] = 2100000,
				['price_to_customer_min'] = 1900000,
				['price_to_customer_max'] = 2300000,
				['price_to_export'] = 5,
				['price_to_owner'] = 1900000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_M762.png',	
			},	
			['WEAPON_MK47FM'] = {
				['name'] = "MK 47 Mutant",
				['price_to_customer'] = 2000000,
				['price_to_customer_min'] = 1800000,
				['price_to_customer_max'] = 2200000,
				['price_to_export'] = 5,
				['price_to_owner'] = 1800000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_MK47FM.png',	
			},	
			['WEAPON_GROZA'] = {
				['name'] = "OTs-14 Groza",
				['price_to_customer'] = 2300000,
				['price_to_customer_min'] = 2100000,
				['price_to_customer_max'] = 2500000,
				['price_to_export'] = 5,
				['price_to_owner'] = 2100000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_GROZA.png',	
			},	
			['WEAPON_MDR'] = {
				['name'] = "Desert Tech MDR",
				['price_to_customer'] = 1500000,
				['price_to_customer_min'] = 1300000,
				['price_to_customer_max'] = 1700000,
				['price_to_export'] = 5,
				['price_to_owner'] = 1300000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_MDR.png',	
			},	
			['WEAPON_M6IC'] = {
				['name'] = "LWRC M6IC",
				['price_to_customer'] = 2400000,
				['price_to_customer_min'] = 2200000,
				['price_to_customer_max'] = 2600000,
				['price_to_export'] = 5,
				['price_to_owner'] = 2200000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_M6IC.png',	
			},	
			['WEAPON_AKM'] = {
				['name'] = "AKM",
				['price_to_customer'] = 2300000,
				['price_to_customer_min'] = 2100000,
				['price_to_customer_max'] = 2500000,
				['price_to_export'] = 5,
				['price_to_owner'] = 2100000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_AKM.png',	
			},
			['WEAPON_AK47'] = {
				['name'] = "AK47",
				['price_to_customer'] = 1900000,
				['price_to_customer_min'] = 1700000,
				['price_to_customer_max'] = 2100000,
				['price_to_export'] = 5,
				['price_to_owner'] = 1900000,
				['amount_to_owner'] = 5,
				['amount_to_delivery'] = 5,
				['img'] = 'WEAPON_AK47.png',	
			}
		}
	},

	['weaponkits'] = {
		['page_name'] = "Gun Kits",
		['page_desc'] = "Extend the capability of your guns",
		['page_icon'] = '<i class="fa-solid fa-prescription-bottle"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/gunmagazines.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['kt_akm_ske'] = {
				['name'] = "AKM SKELETON KIT",
				['price_to_customer'] = 1600,
				['price_to_customer_min'] = 1600,
				['price_to_customer_max'] = 2600,
				['price_to_export'] = 160,
				['price_to_owner'] = 1600,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'kt_akm_ske.png',
			},
			['kt_akm_tac'] = {
				['name'] = "AKM TACTICAL KIT",
				['price_to_customer'] = 1600,
				['price_to_customer_min'] = 1600,
				['price_to_customer_max'] = 2600,
				['price_to_export'] = 160,
				['price_to_owner'] = 1600,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'kt_akm_tac.png',
			},
			['kt_akm_reb'] = {
				['name'] = "AKM REBEL KIT",
				['price_to_customer'] = 1600,
				['price_to_customer_min'] = 1600,
				['price_to_customer_max'] = 2600,
				['price_to_export'] = 160,
				['price_to_owner'] = 1600,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'kt_akm_reb.png',
			}
		}
	},

	-- ['lumberitems'] = {
	-- 	['page_name'] = "Lumber Items",
	-- 	['page_desc'] = "Items for Crafting",
	-- 	['page_icon'] = '<i class="fa-solid fa-flask"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/smelting.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['lbarrel'] = {
	-- 			['name'] = "Barrel",
	-- 			['price_to_customer'] = 950,
	-- 			['price_to_customer_min'] = 950,
	-- 			['price_to_customer_max'] = 1000,
	-- 			['price_to_export'] = 1,
	-- 			['price_to_owner'] = 120000000,
	-- 			['amount_to_owner'] = 1,
	-- 			['amount_to_delivery'] = 1,
	-- 			['img'] = 'lbarrel.png',
	-- 		},
	-- 		['lbuttplate'] = {
	-- 			['name'] = "Butt Plate",
	-- 			['price_to_customer'] = 950,
	-- 			['price_to_customer_min'] = 950,
	-- 			['price_to_customer_max'] = 1000,
	-- 			['price_to_export'] = 1,
	-- 			['price_to_owner'] = 120000000,
	-- 			['amount_to_owner'] = 1,
	-- 			['amount_to_delivery'] = 1,
	-- 			['img'] = 'lbuttplate.png',
	-- 		},
	-- 		['lreartrunnion'] = {
	-- 			['name'] = "Rear Trunnion",
	-- 			['price_to_customer'] = 950,
	-- 			['price_to_customer_min'] = 950,
	-- 			['price_to_customer_max'] = 1000,
	-- 			['price_to_export'] = 1,
	-- 			['price_to_owner'] = 120000000,
	-- 			['amount_to_owner'] = 1,
	-- 			['amount_to_delivery'] = 1,
	-- 			['img'] = 'lreartrunnion.png',
	-- 		},
	-- 		['cork'] = {
	-- 			['name'] = "Cork",
	-- 			['price_to_customer'] = 280,
	-- 			['price_to_customer_min'] = 280,
	-- 			['price_to_customer_max'] = 300,
	-- 			['price_to_export'] = 1,
	-- 			['price_to_owner'] = 120000000,
	-- 			['amount_to_owner'] = 1,
	-- 			['amount_to_delivery'] = 1,
	-- 			['img'] = 'cork.png',
	-- 		},
	-- 		['pbottle'] = {
	-- 			['name'] = "Plain Bottle",
	-- 			['price_to_customer'] = 280,
	-- 			['price_to_customer_min'] = 280,
	-- 			['price_to_customer_max'] = 300,
	-- 			['price_to_export'] = 1,
	-- 			['price_to_owner'] = 120000000,
	-- 			['amount_to_owner'] = 1,
	-- 			['amount_to_delivery'] = 1,
	-- 			['img'] = 'pbottle.png',
	-- 		}
	-- 	}
	-- },

	['sniperblack'] = {
		['page_name'] = "Snipers",
		['page_desc'] = "Long Ranged weapons",
		['page_icon'] = '<i class="fa-solid fa-crosshairs"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/snipers.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			-- ['WEAPON_SR25'] = {
			-- 	['name'] = "Stoner Rifle-25",
			-- 	['price_to_customer'] = 8500000,
			-- 	['price_to_customer_min'] = 8500000,
			-- 	['price_to_customer_max'] = 8535000,
			-- 	['price_to_export'] = 850,
			-- 	['price_to_owner'] = 8500000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'WEAPON_SR25.png',
			-- },
			-- ['WEAPON_VICTUSXMR'] = {
			-- 	['name'] = "Victus XMR",
			-- 	['price_to_customer'] = 15000000,
			-- 	['price_to_customer_min'] = 15000000,
			-- 	['price_to_customer_max'] = 15050000,
			-- 	['price_to_export'] = 150,
			-- 	['price_to_owner'] = 15000000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'WEAPON_VICTUSXMR.png',
			-- }	
		}
	},

	['otherweaponblack'] = {
		['page_name'] = "Other Weapons",
		['page_desc'] = "Weapons that do not fit in any other category",
		['page_icon'] = '<i class="fa-solid fa-person-military-rifle"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/otherweapons.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			-- ['WEAPON_bullpupshotgun'] = {
			-- 	['name'] = "Bullpup Shotgun",
			-- 	['price_to_customer'] = 6500000,
			-- 	['price_to_customer_min'] = 6500000,
			-- 	['price_to_customer_max'] = 6525000,
			-- 	['price_to_export'] = 650,
			-- 	['price_to_owner'] = 6500000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'WEAPON_bullpupshotgun.png',
			-- },
			-- ['helmet'] = {
			-- 	['name'] = "Helmet",
			-- 	['price_to_customer'] = 25000,
			-- 	['price_to_customer_min'] = 25000,
			-- 	['price_to_customer_max'] = 27500,
			-- 	['price_to_export'] = 250,
			-- 	['price_to_owner'] = 25000,
			-- 	['amount_to_owner'] = 500,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'helmet.png',
			-- }
		}
	},

	['throwables'] = {
		['page_name'] = "Throwables",
		['page_desc'] = "Improvised projectile weapons for various situations",
		['page_icon'] = '<i class="fa-solid fa-bomb"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/throwables.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category

		['items'] = {
			['WEAPON_BOOK'] = {
				['name'] = "Book",
				['price_to_customer'] = 150,
				['price_to_customer_min'] = 150,
				['price_to_customer_max'] = 200,
				['price_to_export'] = 15,
				['price_to_owner'] = 100,
				['amount_to_owner'] = 1000,
				['amount_to_delivery'] = 100,
				['img'] = 'WEAPON_BOOK.png',
			},
			['WEAPON_BRICK'] = {
				['name'] = "Brick",
				['price_to_customer'] = 300,
				['price_to_customer_min'] = 300,
				['price_to_customer_max'] = 400,
				['price_to_export'] = 30,
				['price_to_owner'] = 200,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 50,
				['img'] = 'WEAPON_BRICK.png',
			},
			['WEAPON_SHOE'] = {
				['name'] = "Shoe",
				['price_to_customer'] = 250,
				['price_to_customer_min'] = 250,
				['price_to_customer_max'] = 350,
				['price_to_export'] = 25,
				['price_to_owner'] = 150,
				['amount_to_owner'] = 750,
				['amount_to_delivery'] = 75,
				['img'] = 'WEAPON_SHOE.png',
			}
			-- ['WEAPON_FLASHBANG'] = {
			-- 	['name'] = "Flashbang",
			-- 	['price_to_customer'] = 15000,
			-- 	['price_to_customer_min'] = 15000,
			-- 	['price_to_customer_max'] = 16500,
			-- 	['price_to_export'] = 150,
			-- 	['price_to_owner'] = 15000,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'WEAPON_FLASHBANG.png',
			-- },
			-- ['WEAPON_FIREEXTINGUISHER'] = {
			-- 	['name'] = "Fire Extinguisher",
			-- 	['price_to_customer'] = 14120,
			-- 	['price_to_customer_min'] = 14120,
			-- 	['price_to_customer_max'] = 28120,
			-- 	['price_to_export'] = 142,
			-- 	['price_to_owner'] = 14120,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'WEAPON_FIREEXTINGUISHER.png',
			-- }
		}
	},

	['ammos'] = {
		['page_name'] = "Ammo's",
		['page_desc'] = "Ammo's",
		['page_icon'] = '<i class="fa-solid fa-bullseye"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/ammos.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['ammo-9'] = {
				['name'] = "9mm",
				['price_to_customer'] = 50,
				['price_to_customer_min'] = 50,
				['price_to_customer_max'] = 55,
				['price_to_export'] = 5,
				['price_to_owner'] = 10,
				['amount_to_owner'] = 10000,
				['amount_to_delivery'] = 5000,
				['img'] = 'ammo-9.png',
			},

			-- ['ammo-22'] = {
			-- 	['name'] = ".22 Rifle",
			-- 	['price_to_customer'] = 45,
			-- 	['price_to_customer_min'] = 45,
			-- 	['price_to_customer_max'] = 46,
			-- 	['price_to_export'] = 4,
			-- 	['price_to_owner'] = 22,
			-- 	['amount_to_owner'] = 10000,
			-- 	['amount_to_delivery'] = 5000,
			-- 	['img'] = 'ammo-22.png',
			-- },

			['ammo-44'] = {
				['name'] = ".44 Magnum",
				['price_to_customer'] = 56,
				['price_to_customer_min'] = 50,
				['price_to_customer_max'] = 56,
				['price_to_export'] = 1,
				['price_to_owner'] = 16,
				['amount_to_owner'] = 10000,
				['amount_to_delivery'] = 5000,
				['img'] = 'ammo-44.png',
			},		
			['ammo-45'] = {
				['name'] = ".45 ACP",
				['price_to_customer'] = 57,
				['price_to_customer_min'] = 50,
				['price_to_customer_max'] = 57,
				['price_to_export'] = 8,
				['price_to_owner'] = 18,
				['amount_to_owner'] = 10000,
				['amount_to_delivery'] = 5000,
				['img'] = 'ammo-45.png',
			},
			['ammo-50'] = {
				['name'] = ".50 AE",
				['price_to_customer'] = 120,
				['price_to_customer_min'] = 120,
				['price_to_customer_max'] = 122,
				['price_to_export'] = 12,
				['price_to_owner'] = 60,
				['amount_to_owner'] = 10000,
				['amount_to_delivery'] = 5000,
				['img'] = 'ammo-50.png',
			},	
			-- ['ammo-38'] = {
			-- 	['name'] = ".38 Long Colt",
			-- 	['price_to_customer'] = 45,
			-- 	['price_to_customer_min'] = 45,
			-- 	['price_to_customer_max'] = 46,
			-- 	['price_to_export'] = 4,
			-- 	['price_to_owner'] = 25,
			-- 	['amount_to_owner'] = 10000,
			-- 	['amount_to_delivery'] = 5000,
			-- 	['img'] = 'ammo-38.png',
			-- },
			-- ['ammo-shotgun'] = {
			-- 	['name'] = "12 Guage",
			-- 	['price_to_customer'] = 350,
			-- 	['price_to_customer_min'] = 350,
			-- 	['price_to_customer_max'] = 352,
			-- 	['price_to_export'] = 35,
			-- 	['price_to_owner'] = 350,
			-- 	['amount_to_owner'] = 10000,
			-- 	['amount_to_delivery'] = 5000,
			-- 	['img'] = 'ammo-shotgun.png',
			-- },	
			['ammo-rifle'] = {
				['name'] = "5.56",
				['price_to_customer'] = 330,
				['price_to_customer_min'] = 330,
				['price_to_customer_max'] = 340,
				['price_to_export'] = 32,
				['price_to_owner'] = 260,
				['amount_to_owner'] = 25000,
				['amount_to_delivery'] = 5000,
				['img'] = 'ammo-rifle.png',
			},	
			['ammo-rifle2'] = {
				['name'] = "7.62",
				['price_to_customer'] = 370,
				['price_to_customer_min'] = 370,
				['price_to_customer_max'] = 380,
				['price_to_export'] = 35,
				['price_to_owner'] = 300,
				['amount_to_owner'] = 25000,
				['amount_to_delivery'] = 5000,
				['img'] = 'ammo-rifle2.png',
			},	
			-- ['ammo-sniper'] = {
			-- 	['name'] = ".338 Lapua",
			-- 	['price_to_customer'] = 950,
			-- 	['price_to_customer_min'] = 950,
			-- 	['price_to_customer_max'] = 953,
			-- 	['price_to_export'] = 95,
			-- 	['price_to_owner'] = 910,
			-- 	['amount_to_owner'] = 10000,
			-- 	['amount_to_delivery'] = 5000,
			-- 	['img'] = 'ammo-sniper.png',
			-- },	
			['ammo-sniper2'] = {
				['name'] = ".308 Winchester",
				['price_to_customer'] = 525,
				['price_to_customer_min'] = 525,
				['price_to_customer_max'] = 528,
				['price_to_export'] = 52,
				['price_to_owner'] = 480,
				['amount_to_owner'] = 10000,
				['amount_to_delivery'] = 5000,
				['img'] = 'ammo-sniper2.png',
			}
		}
	},

	['gunmagazines'] = {
		['page_name'] = "Gun Attachments",
		['page_desc'] = "Extend the capability of your guns",
		['page_icon'] = '<i class="fa-solid fa-prescription-bottle"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/gunmagazines.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['at_clip_extended_pistol'] = {
				['name'] = "Extended Pistol Clip",
				['price_to_customer'] = 2600,
				['price_to_customer_min'] = 2600,
				['price_to_customer_max'] = 7000,
				['price_to_export'] = 260,
				['price_to_owner'] = 3000,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_clip_extended_pistol.png',
			},
			['at_clip_extended_smg'] = {
				['name'] = "Extended SMG Clip",
				['price_to_customer'] = 4200,
				['price_to_customer_min'] = 4200,
				['price_to_customer_max'] = 8500,
				['price_to_export'] = 420,
				['price_to_owner'] = 4200,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_clip_extended_smg.png',
			},
			['at_clip_extended_rifle'] = {
				['name'] = "Extended Rifle Clip",
				['price_to_customer'] = 4900,
				['price_to_customer_min'] = 4900,
				['price_to_customer_max'] = 9000,
				['price_to_export'] = 490,
				['price_to_owner'] = 4900,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_clip_extended_rifle.png',
			},
			-- ['at_clip_extended_shotgun'] = {
			-- 	['name'] = "Extended Shotgun Clip",
			-- 	['price_to_customer'] = 4900,
			-- 	['price_to_customer_min'] = 4900,
			-- 	['price_to_customer_max'] = 5100,
			-- 	['price_to_export'] = 490,
			-- 	['price_to_owner'] = 4900,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'at_clip_extended_shotgun.png',
			-- },
			-- ['at_clip_extended_mg'] = {
			-- 	['name'] = "Extended MG Clip",
			-- 	['price_to_customer'] = 13400,
			-- 	['price_to_customer_min'] = 13400,
			-- 	['price_to_customer_max'] = 14600,
			-- 	['price_to_export'] = 1340,
			-- 	['price_to_owner'] = 13400,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'at_clip_extended_mg.png',
			-- },
			-- ['at_clip_extended_sniper'] = {
			-- 	['name'] = "Extended Sniper Clip",
			-- 	['price_to_customer'] = 15400,
			-- 	['price_to_customer_min'] = 15400,
			-- 	['price_to_customer_max'] = 15800,
			-- 	['price_to_export'] = 1540,
			-- 	['price_to_owner'] = 15400,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'at_clip_extended_sniper.png',
			-- },
			-- ['at_clip_drum_smg'] = {
			-- 	['name'] = "Drum Smg Clip",
			-- 	['price_to_customer'] = 15400,
			-- 	['price_to_customer_min'] = 15400,
			-- 	['price_to_customer_max'] = 15800,
			-- 	['price_to_export'] = 1040,
			-- 	['price_to_owner'] = 15400,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'at_clip_drum_smg.png',
			-- },
			-- ['at_clip_drum_rifle'] = {
			-- 	['name'] = "Drum Rifle Clip",
			-- 	['price_to_customer'] = 15400,
			-- 	['price_to_customer_min'] = 15400,
			-- 	['price_to_customer_max'] = 15800,
			-- 	['price_to_export'] = 1040,
			-- 	['price_to_owner'] = 15400,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'at_clip_drum_rifle.png',
			-- },
			-- ['at_clip_drum_shotgun'] = {
			-- 	['name'] = "Drum Shotgun Clip",
			-- 	['price_to_customer'] = 15400,
			-- 	['price_to_customer_min'] = 15400,
			-- 	['price_to_customer_max'] = 15800,
			-- 	['price_to_export'] = 1040,
			-- 	['price_to_owner'] = 15400,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'at_clip_drum_shotgun.png',
			-- },
			['at_suppressor_light'] = {
				['name'] = "Suppressor",
				['price_to_customer'] = 10400,
				['price_to_customer_min'] = 10400,
				['price_to_customer_max'] = 10600,
				['price_to_export'] = 940,
				['price_to_owner'] = 10400,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_suppressor_light.png',
			},
			['at_suppressor_heavy'] = {
				['name'] = "Tactical Suppressor",
				['price_to_customer'] = 14400,
				['price_to_customer_min'] = 14400,
				['price_to_customer_max'] = 14800,
				['price_to_export'] = 1240,
				['price_to_owner'] = 14400,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_suppressor_heavy.png',
			},
			-- ['at_barrel'] = {
			-- 	['name'] = "Heavy Barrel",
			-- 	['price_to_customer'] = 14400,
			-- 	['price_to_customer_min'] = 14400,
			-- 	['price_to_customer_max'] = 14800,
			-- 	['price_to_export'] = 1240,
			-- 	['price_to_owner'] = 14400,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'at_barrel.png',
			-- },
			-- ['at_scope_macro'] = {
			-- 	['name'] = "Macro Scope",
			-- 	['price_to_customer'] = 3600,
			-- 	['price_to_customer_min'] = 3600,
			-- 	['price_to_customer_max'] = 3800,
			-- 	['price_to_export'] = 260,
			-- 	['price_to_owner'] = 3600,
			-- 	['amount_to_owner'] = 250,
			-- 	['amount_to_delivery'] = 500,
			-- 	['img'] = 'at_scope_macro.png',
			-- },
			['at_scope_small'] = {
				['name'] = "Small Scope",
				['price_to_customer'] = 6600,
				['price_to_customer_min'] = 6600,
				['price_to_customer_max'] = 6800,
				['price_to_export'] = 460,
				['price_to_owner'] = 6600,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_scope_small.png',
			},
			['at_scope_medium'] = {
				['name'] = "Medium Scope",
				['price_to_customer'] = 8850,
				['price_to_customer_min'] = 8850,
				['price_to_customer_max'] = 8950,
				['price_to_export'] = 680,
				['price_to_owner'] = 8850,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_scope_medium.png',
			},
			['at_scope_large'] = {
				['name'] = "Large Scope",
				['price_to_customer'] = 32810,
				['price_to_customer_min'] = 32810,
				['price_to_customer_max'] = 33010,
				['price_to_export'] = 2680,
				['price_to_owner'] = 32810,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_scope_large.png',
			},
			['at_scope_holo'] = {
				['name'] = "Holographic Sight",
				['price_to_customer'] = 6000,
				['price_to_customer_min'] = 6000,
				['price_to_customer_max'] = 6400,
				['price_to_export'] = 500,
				['price_to_owner'] = 6000,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_scope_holo.png',
			},
			['at_stock2'] = {
				['name'] = "Light Stock",
				['price_to_customer'] = 6000,
				['price_to_customer_min'] = 6000,
				['price_to_customer_max'] = 6400,
				['price_to_export'] = 500,
				['price_to_owner'] = 6000,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_stock2.png',
			},
			['at_stock'] = {
					['name'] = "Heavy Stock",
					['price_to_customer'] = 6000,
					['price_to_customer_min'] = 6000,
					['price_to_customer_max'] = 6400,
					['price_to_export'] = 500,
					['price_to_owner'] = 6000,
					['amount_to_owner'] = 250,
					['amount_to_delivery'] = 500,
					['img'] = 'at_stock.png',
			},
			['at_grip'] = {
					['name'] = "Grip",
					['price_to_customer'] = 6000,
					['price_to_customer_min'] = 6000,
					['price_to_customer_max'] = 6400,
					['price_to_export'] = 500,
					['price_to_owner'] = 6000,
					['amount_to_owner'] = 250,
					['amount_to_delivery'] = 500,
					['img'] = 'at_grip.png',
			},
			['at_flashlight'] = {
				['name'] = "Tactical Flashlight",
				['price_to_customer'] = 6000,
				['price_to_customer_min'] = 6000,
				['price_to_customer_max'] = 6400,
				['price_to_export'] = 500,
				['price_to_owner'] = 6000,
				['amount_to_owner'] = 250,
				['amount_to_delivery'] = 500,
				['img'] = 'at_flashlight.png',
			}
		}
	},

	['weaponskins'] = {
		['page_name'] = "Weapon Skins",
		['page_desc'] = "Change colors. Make it fancy.",
		['page_icon'] = '<i class="fa-solid fa-palette"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/weaponskins.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			-- ['at_skin_camo'] = {
			-- 	['name'] = "Tactical",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 10000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1000,
			-- 	['price_to_owner'] = 10000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_camo.png',
			-- },
			-- ['at_skin_brushstroke'] = {
			-- 	['name'] = "Brushstroke Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_brushstroke.png',
			-- },
			-- ['at_skin_woodland'] = {
			-- 	['name'] = "Woodland Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_woodland.png',
			-- },
			-- ['at_skin_skull'] = {
			-- 	['name'] = "Skull Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_skull.png',
			-- },
			-- ['at_skin_sessanta'] = {
			-- 	['name'] = "Sessanta Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_sessanta.png',
			-- },
			-- ['at_skin_perseus'] = {
			-- 	['name'] = "Perseus Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_perseus.png',
			-- },
			-- ['at_skin_leopard'] = {
			-- 	['name'] = "Leopard Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_leopard.png',
			-- },
			-- ['at_skin_zebra'] = {
			-- 	['name'] = "Zebra Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_zebra.png',
			-- },
			-- ['at_skin_geometric'] = {
			-- 	['name'] = "Geometric Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_geometric.png',
			-- },
			-- ['at_skin_boom'] = {
			-- 	['name'] = "Boom Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_boom.png',
			-- },
			-- ['at_skin_patriotic'] = {
			-- 	['name'] = "Patriotic Weapon Kit",
			-- 	['price_to_customer'] = 12000,
			-- 	['price_to_customer_min'] = 12000,
			-- 	['price_to_customer_max'] = 17500,
			-- 	['price_to_export'] = 1200,
			-- 	['price_to_owner'] = 12000,
			-- 	['amount_to_owner'] = 25,
			-- 	['amount_to_delivery'] = 50,
			-- 	['img'] = 'at_skin_patriotic.png',
			-- }
		}
	},

	['drugs'] = {
		['page_name'] = "Drugs",
		['page_desc'] = "Narcotics is a dirty business.",
		['page_icon'] = '<i class="fa-solid fa-cannabis"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/marijuana.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['meth'] = {
				['name'] = "Meth",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 360,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'meth.png',
			},
			['tweak'] = {
				['name'] = "Tweak",
				['price_to_customer'] = 120000,
				['price_to_customer_min'] = 120000,
				['price_to_customer_max'] = 220000,
				['price_to_export'] = 2500,
				['price_to_owner'] = 120000,
				['amount_to_owner'] = 5000,
				['amount_to_delivery'] = 4000,
				['img'] = 'tweak.png',
			},
			-- ['idukki_gold'] = {
			-- 	['name'] = "Idukki Gold",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 4200,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 4000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'idukki_gold.png',
			-- },
			-- ['weed_lemonhaze'] = {
			-- 	['name'] = "Lemonhaze Weed",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 4000,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 4000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'weed_lemonhaze.png',
			-- },
			-- ['cocaine'] = {
			-- 	['name'] = "Cocaine",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 4800,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 4000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'cocaine.png',
			-- },
			-- ['coca'] = {
			-- 	['name'] = "Cocaine Leaves",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 600,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 4000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'coca.png',
			-- },
			['coke_pooch'] = {
				['name'] = "Cocaine Pouch",
				['price_to_customer'] = 146200,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 300,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'coke_pooch.png',
			},
			['weed_packaged'] = {
				['name'] = "Packaged Weed",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 3850,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'weed_packaged.png',
			},
			['meth_packaged'] = {
				['name'] = "Packaged Meth",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 4220,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'meth_packaged.png',
			},
			['cocaine_packaged'] = {
				['name'] = "Packaged Cocaine",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 5200,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'cocaine_packaged.png',
			},
			['inside-weed'] = {
				['name'] = "Special Weed",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 1500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'inside-weed.png',
			},
			-- ['inside-meth'] = {
			-- 	['name'] = "Packaged Weed",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 1750,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 4000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'inside-meth.png',
			-- },
			['inside-coke'] = {
				['name'] = "Special Coke",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 2950,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'inside-coke.png',
			},
			['ls_cocaine_bag'] = {
				['name'] = "Cocaine Bag",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 12500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_cocaine_bag.png',
			},
			['ls_crack_bag'] = {
				['name'] = "Crack Bag",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 19500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_crack_bag.png',
			},
			['ls_plain_jane_joint'] = {
				['name'] = "Plain Jane Joint",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 5300,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_plain_jane_joint.png',
			},
			['ls_banana_kush_joint'] = {
				['name'] = "Banana Kush Joint",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 5400,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_banana_kush_joint.png',
			},
			['ls_purple_haze_joint'] = {
				['name'] = "Purple Haze Joint",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 5500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 5500,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_purple_haze_joint.png',
			},
			['ls_orange_crush_joint'] = {
				['name'] = "Orange Crush Joint",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 5600,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 5000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_orange_crush_joint.png',
			},
			['ls_cosmic_kush_joint'] = {
				['name'] = "Cosmic Kush Joint",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 5700,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 5000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_cosmic_kush_joint.png',
			},
			['ls_blue_dream_joint'] = {
				['name'] = "Blue Dream Joint",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 5550,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_blue_dream_joint.png',
			},
			['ls_plain_jane_bag'] = {
				['name'] = "Plain Jane Bag",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 43000,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_plain_jane_bag.png',
			},
			['ls_banana_kush_bag'] = {
				['name'] = "Banana Kush Bag",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 45500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_banana_kush_bag.png',
			},
			['ls_purple_haze_bag'] = {
				['name'] = "Purple Haze Bag",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 47500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 5000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_purple_haze_bag.png',
			},
			['ls_orange_crush_bag'] = {
				['name'] = "Orange Crush Bag",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 48500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 5000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_orange_crush_bag.png',
			},
			['ls_cosmic_kush_bag'] = {
				['name'] = "Cosmic Kush Bag",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 49500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 5000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_cosmic_kush_bag.png',
			},
			['ls_blue_dream_bag'] = {
				['name'] = "Blue Dream Bag",
				['price_to_customer'] = 100000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 46500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 4000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ls_blue_dream_bag.png',
			}
		}
	},

	['industries'] = {
		['page_name'] = "Import Exporters",
		['page_desc'] = "Import and Export Business.",
		['page_icon'] = '<i class="fa-solid fa-file-export"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
		-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
		['page_img'] = 'img/categories/industriess.png',	-- This is the category image in the page to buy categories
		['category_buy_price'] = 2500, 			-- Price to buy the category
		['category_sell_price'] = 1250, 		-- Price to sell the category
		['items'] = {
			['skin_deer_ruined'] = {
				['name'] = "Tattered Deer Pelt",
				['price_to_customer'] = 500,
				['price_to_customer_min'] = 500,
				['price_to_customer_max'] = 500,
				['price_to_export'] = 600,
				['price_to_owner'] = 600,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_deer_ruined.png',
			},
			['skin_deer_low'] = {
				['name'] = "Worn Deer Pelt",
				['price_to_customer'] = 580,
				['price_to_customer_min'] = 580,
				['price_to_customer_max'] = 580,
				['price_to_export'] = 680,
				['price_to_owner'] = 680,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_deer_low.png',
			},
			['skin_deer_medium'] = {
				['name'] = "Supple Deer Pelt",
				['price_to_customer'] = 680,
				['price_to_customer_min'] = 680,
				['price_to_customer_max'] = 680,
				['price_to_export'] = 780,
				['price_to_owner'] = 780,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_deer_medium.png',
			},
			['skin_deer_good'] = {
				['name'] = "Prime Deer Pelt",
				['price_to_customer'] = 760,
				['price_to_customer_min'] = 760,
				['price_to_customer_max'] = 760,
				['price_to_export'] = 860,
				['price_to_owner'] = 860,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_deer_good.png',
			},
			['skin_deer_perfect'] = {
				['name'] = "Flawless Boar Meat",
				['price_to_customer'] = 900,
				['price_to_customer_min'] = 900,
				['price_to_customer_max'] = 900,
				['price_to_export'] = 1000,
				['price_to_owner'] = 1000,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_deer_perfect.png',
			},

			['skin_boar_ruined'] = {
				['name'] = "Tattered Boar Pelt",
				['price_to_customer'] = 500,
				['price_to_customer_min'] = 500,
				['price_to_customer_max'] = 500,
				['price_to_export'] = 600,
				['price_to_owner'] = 600,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_boar_ruined.png',
			},
			['skin_boar_low'] = {
				['name'] = "Worn Boar Pelt",
				['price_to_customer'] = 560,
				['price_to_customer_min'] = 560,
				['price_to_customer_max'] = 560,
				['price_to_export'] = 660,
				['price_to_owner'] = 660,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_boar_low.png',
			},
			['skin_boar_medium'] = {
				['name'] = "Supple Boar Pelt",
				['price_to_customer'] = 680,
				['price_to_customer_min'] = 680,
				['price_to_customer_max'] = 680,
				['price_to_export'] = 780,
				['price_to_owner'] = 780,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_boar_medium.png',
			},
			['skin_boar_good'] = {
				['name'] = "Prime Boar Pelt",
				['price_to_customer'] = 760,
				['price_to_customer_min'] = 760,
				['price_to_customer_max'] = 760,
				['price_to_export'] = 860,
				['price_to_owner'] = 860,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_boar_good.png',
			},
			['skin_boar_perfect'] = {
				['name'] = "Flawless Boar Meat",
				['price_to_customer'] = 900,
				['price_to_customer_min'] = 900,
				['price_to_customer_max'] = 900,
				['price_to_export'] = 1000,
				['price_to_owner'] = 1000,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_boar_perfect.png',
			},

			['skin_coyote_ruined'] = {
				['name'] = "Tattered Coyote Pelt",
				['price_to_customer'] = 480,
				['price_to_customer_min'] = 480,
				['price_to_customer_max'] = 480,
				['price_to_export'] = 580,
				['price_to_owner'] = 580,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_coyote_ruined.png',
			},
			['skin_coyote_low'] = {
				['name'] = "Worn Coyote Pelt",
				['price_to_customer'] = 500,
				['price_to_customer_min'] = 500,
				['price_to_customer_max'] = 500,
				['price_to_export'] = 600,
				['price_to_owner'] = 600,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_coyote_low.png',
			},
			['skin_coyote_medium'] = {
				['name'] = "Supple Coyote Pelt",
				['price_to_customer'] = 530,
				['price_to_customer_min'] = 530,
				['price_to_customer_max'] = 530,
				['price_to_export'] = 630,
				['price_to_owner'] = 630,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_coyote_medium.png',
			},
			['skin_coyote_good'] = {
				['name'] = "Prime Coyote Pelt",
				['price_to_customer'] = 580,
				['price_to_customer_min'] = 580,
				['price_to_customer_max'] = 580,
				['price_to_export'] = 680,
				['price_to_owner'] = 680,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_coyote_good.png',
			},
			['skin_coyote_perfect'] = {
				['name'] = "Flawless Coyote Meat",
				['price_to_customer'] = 800,
				['price_to_customer_min'] = 800,
				['price_to_customer_max'] = 800,
				['price_to_export'] = 900,
				['price_to_owner'] = 900,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'skin_coyote_perfect.png',
			},

			['boarmeat'] = {
				['name'] = "Boar Meat",
				['price_to_customer'] = 1900,
				['price_to_customer_min'] = 1900,
				['price_to_customer_max'] = 1900,
				['price_to_export'] = 2400,
				['price_to_owner'] = 2400,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'boarmeat.png',
			},
			['boartusk'] = {
				['name'] = "Boar Tusk",
				['price_to_customer'] = 1900,
				['price_to_customer_min'] = 1900,
				['price_to_customer_max'] = 1900,
				['price_to_export'] = 2400,
				['price_to_owner'] = 2400,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'boartusk.png',
			},
			['deermeat'] = {
				['name'] = "Deer Meat",
				['price_to_customer'] = 1650,
				['price_to_customer_min'] = 1650,
				['price_to_customer_max'] = 1650,
				['price_to_export'] = 1950,
				['price_to_owner'] = 1950,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'deermeat.png',
			},
			['deerskin'] = {
				['name'] = "Deer Skin",
				['price_to_customer'] = 1100,
				['price_to_customer_min'] = 1100,
				['price_to_customer_max'] = 1100,
				['price_to_export'] = 1600,
				['price_to_owner'] = 1600,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'deerskin.png',
			},
			['coyotemeat'] = {
				['name'] = "Coyote Meat",
				['price_to_customer'] = 1900,
				['price_to_customer_min'] = 1900,
				['price_to_customer_max'] = 1900,
				['price_to_export'] = 2400,
				['price_to_owner'] = 2400,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'coyotemeat.png',
			},
			['coyotefur'] = {
				['name'] = "Coyote Fur",
				['price_to_customer'] = 2700,
				['price_to_customer_min'] = 2700,
				['price_to_customer_max'] = 2700,
				['price_to_export'] = 3100,
				['price_to_owner'] = 3100,
				['amount_to_owner'] = 500,
				['amount_to_delivery'] = 500,
				['img'] = 'coyotefur.png',
			},
			['raw_meat'] = {
				['name'] = "Raw Meat",
				['price_to_customer'] = 320,
				['price_to_customer_min'] = 320,
				['price_to_customer_max'] = 320,
				['price_to_export'] = 420,
				['price_to_owner'] = 420,
				['amount_to_owner'] = 100,
				['amount_to_delivery'] = 100,
				['img'] = 'raw_meat.png',
			},
			-- ['sardine'] = {
			-- 	['name'] = "Sardine Fish",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 720,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'sardine.png',
			-- },
			-- ['mackerel'] = {
			-- 	['name'] = "Mackerel Fish",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 720,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'mackerel.png',
			-- },
			-- ['prawn'] = {
			-- 	['name'] = "Prawns",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 700,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'prawn.png',
			-- },
			-- ['tunafish'] = {
			-- 	['name'] = "Tuna Fish",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 740,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'tunafish.png',
			-- },
			-- ['anchovy'] = {
			-- 	['name'] = "Anchovy",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 720,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'anchovy.png',
			-- },
			-- ['squid'] = {
			-- 	['name'] = "Squid",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 730,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'squid.png',
			-- },
			-- ['crab'] = {
			-- 	['name'] = "Crab",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 700,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'crab.png',
			-- },
			-- ['seer'] = {
			-- 	['name'] = "Seer Fish",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 670,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'seer.png',
			-- },
			-- ['shark'] = {
			-- 	['name'] = "Shark",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 1600,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'shark.png',
			-- },

			['potato'] = {
				['name'] = "Potato",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 360,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'potato.png',
			},
			['tomato'] = {
				['name'] = "Tomato",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 380,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'tomato.png',
			},
			['orange'] = {
				['name'] = "Orange",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 400,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'orange.png',
			},
			['cabbage'] = {
				['name'] = "Cabbage",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 380,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'cabbage.png',
			},
			['coffee_beans'] = {
				['name'] = "Coffee Beans",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 260,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'coffee_beans.png',
			},	
			-- ['pumpkin'] = {
			-- 	['name'] = "Pumpkin",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 400,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'pumpkin.png',
			-- },
			-- ['melon'] = {
			-- 	['name'] = "Watermelon",
			-- 	['price_to_customer'] = 10000,
			-- 	['price_to_customer_min'] = 100000,
			-- 	['price_to_customer_max'] = 200000,
			-- 	['price_to_export'] = 420,
			-- 	['price_to_owner'] = 100000,
			-- 	['amount_to_owner'] = 3000,
			-- 	['amount_to_delivery'] = 3000,
			-- 	['img'] = 'melon.png',
			-- },	
			-- mining
			['coal_ore'] = {
				['name'] = "Coal Ore",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 480,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'coal_ore.png',
			},	
			['flint'] = {
				['name'] = "Flint",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 480,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'flint.png',
			},
			['sulfur_chunk'] = {
				['name'] = "Sulfur Chunk",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 500,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'sulfur_chunk.png',
			},
			['gold_nugget'] = {
				['name'] = "Gold Nugget",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 550,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'gold_nugget.png',
			},
			['gold_dust'] = {
				['name'] = "Gold Dust",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 580,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'gold_dust.png',
			},
			['quartz_crystal'] = {
				['name'] = "Quartz Crystal",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 600,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'quartz_crystal.png',
			},
			['emerald_crystal'] = {
				['name'] = "Emerald Crystal",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 650,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'emerald_crystal.png',
			},
			['beryl_chunk'] = {
				['name'] = "Beryl Chunk",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 680,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'beryl_chunk.png',
			},
			['green_garnet'] = {
				['name'] = "Green Garnet",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 700,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'green_garnet.png',
			},
			['ruby_crystal'] = {
				['name'] = "Ruby Crystal",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 780,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ruby_crystal.png',
			},
			['corundum_chunk'] = {
				['name'] = "Corundum Chunk",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 780,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'corundum_chunk.png',
			},
			['pink_sapphire'] = {
				['name'] = "Pink Sapphire",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 800,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'pink_sapphire.png',
			},
			['amethyst_geode'] = {
				['name'] = "Amethyst Geode",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 880,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'amethyst_geode.png',
			},
			['purple_quartz'] = {
				['name'] = "Purple Quartz",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 950,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'purple_quartz.png',
			},
			['clear_crystal'] = {
				['name'] = "Clear Crystal",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 980,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'clear_crystal.png',
			},
			['diamond_crystal'] = {
				['name'] = "Diamond Crystal",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 1080,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'diamond_crystal.png',
			},
			['graphite_chunk'] = {
				['name'] = "Graphite Chunk",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 1180,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'graphite_chunk.png',
			},
			['blue_diamond'] = {
				['name'] = "Blue Diamond",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 1480,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'blue_diamond.png',
			},
			['dendrogyra_coral'] = {
				['name'] = "Dendrogyra",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 900,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'dendrogyra_coral.png',
			},
			['antipatharia_coral'] = {
				['name'] = "Antipatharia",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 950,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'antipatharia_coral.png',
			},
			['montastraea_coral'] = {
				['name'] = "Montastraea",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 1060,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'montastraea_coral.png',
			},
			['keshi_pearl'] = {
				['name'] = "Keshi Pearls",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 1100,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'keshi_pearl.png',
			},
			['cortez_pearl'] = {
				['name'] = "Cortez Pearls",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 1230,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'cortez_pearl.png',
			},
			['marine_fossil'] = {
				['name'] = "Marine Fossil",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 1380,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'marine_fossil.png',
			},
			['copper'] = {
				['name'] = "Copper",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 610,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'copper.png',
			},
			['iron'] = {
				['name'] = "Iron",
				['price_to_customer'] = 10000,
				['price_to_customer_min'] = 100000,
				['price_to_customer_max'] = 200000,
				['price_to_export'] = 630,
				['price_to_owner'] = 100000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'iron.png',
			},
			-- Fishes

			['bitterling'] = {
				['name'] = "Bitterling",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 20457,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'bitterling.png',
			},
			['pale_chub'] = {
				['name'] = "Pale Chub",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 22546,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'pale_chub.png',
			},
			['dace'] = {
				['name'] = "Dace",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 18784,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'dace.png',
			},
			['carp'] = {
				['name'] = "Carp",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 25994,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'carp.png',
			},
			['goldfish'] = {
				['name'] = "Goldfish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 15452,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'goldfish.png',
			},
			['killifish'] = {
				['name'] = "Killifish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 17681,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'killifish.png',
			},
			['crawfish'] = {
				['name'] = "Crawfish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 18433,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'crawfish.png',
			},
			['tadpole'] = {
				['name'] = "Tadpole",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 15846,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'tadpole.png',
			},
			['frog'] = {
				['name'] = "Frog",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 19624,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'frog.png',
			},
			['freshwater_goby'] = {
				['name'] = "Freshwater Goby",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 19123,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'freshwater_goby.png',
			},
			['loach'] = {
				['name'] = "Loach",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 22894,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'loach.png',
			},
			['bluegill'] = {
				['name'] = "Bluegill",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 24738,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'bluegill.png',
			},
			['yellow_perch'] = {
				['name'] = "Yellow Perch",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 25974,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'yellow_perch.png',
			},
			['black_bass'] = {
				['name'] = "Black Bass",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 26731,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'black_bass.png',
			},
			['tilapia'] = {
				['name'] = "Tilapia",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 23428,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'tilapia.png',
			},
			['pond_smelt'] = {
				['name'] = "Pond Smelt",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 16399,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'pond_smelt.png',
			},
			['sweetfish'] = {
				['name'] = "Sweetfish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 18748,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'sweetfish.png',
			},
			['horse_mackerel'] = {
				['name'] = "Horse Mackerel",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 16784,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'horse_mackerel.png',
			},
			['sea_bass'] = {
				['name'] = "Sea Bass",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 26730,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'sea_bass.png',
			},
			['dab'] = {
				['name'] = "Dab",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 18856,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'dab.png',
			},
			['olive_flounder'] = {
				['name'] = "Olive Flounder",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 25394,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'olive_flounder.png',
			},
			['koi'] = {
				['name'] = "Koi",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 33375,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'koi.png',
			},
			['pop_eyed_goldfish'] = {
				['name'] = "Pop-eyed Goldfish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 28464,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'pop_eyed_goldfish.png',
			},
			['ranchu_goldfish'] = {
				['name'] = "Ranchu Goldfish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 30471,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ranchu_goldfish.png',
			},
			['angelfish'] = {
				['name'] = "Angel Fish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 31897,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'angelfish.png',
			},
			['betta'] = {
				['name'] = "Betta",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 28594,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'betta.png',
			},
			['neon_tetra'] = {
				['name'] = "Neon Tetra",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 35828,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'neon_tetra.png',
			},
			['rainbowfish'] = {
				['name'] = "Rainbow Fish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 33649,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'rainbowfish.png',
			},
			['sea_butterfly'] = {
				['name'] = "Sea Butterfly",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 35493,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'sea_butterfly.png',
			},
			['seahorse'] = {
				['name'] = "Seahorse",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 34565,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'seahorse.png',
			},
			['clownfish'] = {
				['name'] = "Clown Fish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 30542,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'clownfish.png',
			},
			['surgeonfish'] = {
				['name'] = "Surgeon Fish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 27893,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'surgeonfish.png',
			},
			['butterfly_fish'] = {
				['name'] = "Butterfly Fish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 36334,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'butterfly_fish.png',
			},
			['zebra_turkeyfish'] = {
				['name'] = "Zebra Turkeyfish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 29536,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'zebra_turkeyfish.png',
			},
			['barred_knifejaw'] = {
				['name'] = "Barred Knifejaw",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 38937,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'barred_knifejaw.png',
			},
			['red_snapper'] = {
				['name'] = "Red Snapper",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 45766,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'red_snapper.png',
			},
			['moray_eel'] = {
				['name'] = "Moray Eel",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 32132,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'moray_eel.png',
			},
			['ribbon_eel'] = {
				['name'] = "Ribbon Eel",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 32673,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ribbon_eel.png',
			},
			['sturgeon'] = {
				['name'] = "Sturgeon",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 60847,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'sturgeon.png',
			},
			['giant_snakehead'] = {
				['name'] = "Giant Snakehead",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 65812,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'giant_snakehead.png',
			},
			['golden_trout'] = {
				['name'] = "Golden Trout",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 50431,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'golden_trout.png',
			},
			['stringfish'] = {
				['name'] = "String Fish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 50362,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'stringfish.png',
			},
			['king_salmon'] = {
				['name'] = "King Salmon",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 55664,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'king_salmon.png',
			},
			['napoleonfish'] = {
				['name'] = "Napoleon Fish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 62558,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'napoleonfish.png',
			},
			['dorado'] = {
				['name'] = "Dorado",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 54877,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'dorado.png',
			},
			['gar'] = {
				['name'] = "Gar",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 55499,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'gar.png',
			},
			['arapaima'] = {
				['name'] = "Arapaima",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 61372,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'arapaima.png',
			},
			['tuna_fish'] = {
				['name'] = "Tuna",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 53843,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'tuna_fish.png',
			},
			['blue_marlin'] = {
				['name'] = "Blue Marlin",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 70644,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'blue_marlin.png',
			},
			['giant_trevally'] = {
				['name'] = "Giant Trevally",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 60446,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'giant_trevally.png',
			},
			['mahi_mahi'] = {
				['name'] = "Mahi Mahi",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 60526,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'mahi_mahi.png',
			},
			['ray'] = {
				['name'] = "Ray",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 45487,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ray.png',
			},
			['ocean_sunfish'] = {
				['name'] = "Ocean Sunfish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 85988,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'ocean_sunfish.png',
			},
			['oarfish'] = {
				['name'] = "Oar Fish",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 116238,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'oarfish.png',
			},
			['coelacanth'] = {
				['name'] = "Coelacanth",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 180213,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'coelacanth.png',
			},
			['barreleye'] = {
				['name'] = "Barrel Eye",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 205811,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'barreleye.png',
			},
			['old_boot'] = {
				['name'] = "Old Boot",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 35945,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'old_boot.png',
			},
			['rusty_anchor'] = {
				['name'] = "Rusty Anchor",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 45628,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'rusty_anchor.png',
			},
			['broken_bottle'] = {
				['name'] = "Antique Bottle",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 60541,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'broken_bottle.png',
			},
			['diving_watch'] = {
				['name'] = "Vintage Diving Watch",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 45978,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'diving_watch.png',
			},
			['shipwreck_plank'] = {
				['name'] = "Shipwreck Fragment",
				['price_to_customer'] = 1000000,
				['price_to_customer_min'] = 1000000,
				['price_to_customer_max'] = 2000000,
				['price_to_export'] = 40472,
				['price_to_owner'] = 1000000,
				['amount_to_owner'] = 3000,
				['amount_to_delivery'] = 3000,
				['img'] = 'shipwreck_plank.png',
			}									
		}
	},
	
	-- ['vegetables'] = {
	-- 	['page_name'] = "Vegetable",
	-- 	['page_desc'] = "Get all your Vegetables Here.",
	-- 	['page_icon'] = '<i class="fa-solid fa-file-export"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/cooking.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['onion'] = {
	-- 			['name'] = "Onion",
	-- 			['price_to_customer'] = 130,
	-- 			['price_to_customer_min'] = 130,
	-- 			['price_to_customer_max'] = 230,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 130,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'onion.png',
	-- 		},
	-- 		['tomato'] = {
	-- 			['name'] = "Tomato",
	-- 			['price_to_customer'] = 150,
	-- 			['price_to_customer_min'] = 150,
	-- 			['price_to_customer_max'] = 200,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 150,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'tomato.png',
	-- 		},
	-- 		['spinach'] = {
	-- 			['name'] = "Spinach",
	-- 			['price_to_customer'] = 160,
	-- 			['price_to_customer_min'] = 160,
	-- 			['price_to_customer_max'] = 260,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 160,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'spinach.png',
	-- 		},
	-- 		['lettuce'] = {
	-- 			['name'] = "Lettuce",
	-- 			['price_to_customer'] = 180,
	-- 			['price_to_customer_min'] = 180,
	-- 			['price_to_customer_max'] = 280,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 180,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'lettuce.png',
	-- 		},
	-- 		['olives'] = {
	-- 			['name'] = "Olives",
	-- 			['price_to_customer'] = 120,
	-- 			['price_to_customer_min'] = 120,
	-- 			['price_to_customer_max'] = 220,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 120,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'olives.png',
	-- 		},
	-- 		['seaweed'] = {
	-- 			['name'] = "Sea Weed",
	-- 			['price_to_customer'] = 230,
	-- 			['price_to_customer_min'] = 230,
	-- 			['price_to_customer_max'] = 230,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 230,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'seaweed.png',
	-- 		},
	-- 		['bambooshoots'] = {
	-- 			['name'] = "Bamboo Shoots",
	-- 			['price_to_customer'] = 190,
	-- 			['price_to_customer_min'] = 190,
	-- 			['price_to_customer_max'] = 290,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 190,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'bambooshoots.png',
	-- 		},
	-- 		['beansprouts'] = {
	-- 			['name'] = "Bean Sprouts",
	-- 			['price_to_customer'] = 120,
	-- 			['price_to_customer_min'] = 120,
	-- 			['price_to_customer_max'] = 220,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 120,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'beansprouts.png',
	-- 		},
	-- 		['chilli'] = {
	-- 			['name'] = "Chilli",
	-- 			['price_to_customer'] = 130,
	-- 			['price_to_customer_min'] = 130,
	-- 			['price_to_customer_max'] = 230,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 130,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'chilli.png',
	-- 		},
	-- 		['garlic'] = {
	-- 			['name'] = "Garlic",
	-- 			['price_to_customer'] = 140,
	-- 			['price_to_customer_min'] = 140,
	-- 			['price_to_customer_max'] = 240,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 140,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'garlic.png',
	-- 		},
	-- 		['mushroom'] = {
	-- 			['name'] = "Mushroom",
	-- 			['price_to_customer'] = 200,
	-- 			['price_to_customer_min'] = 200,
	-- 			['price_to_customer_max'] = 300,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 200,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'mushroom.png',
	-- 		},
	-- 		['wheat'] = {
	-- 			['name'] = "Wheat",
	-- 			['price_to_customer'] = 160,
	-- 			['price_to_customer_min'] = 160,
	-- 			['price_to_customer_max'] = 260,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 160,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'wheat.png',
	-- 		},
	-- 		['potato'] = {
	-- 			['name'] = "Potato",
	-- 			['price_to_customer'] = 150,
	-- 			['price_to_customer_min'] = 150,
	-- 			['price_to_customer_max'] = 220,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 150,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'potato.png',
	-- 		},
	-- 		['scallions'] = {
	-- 			['name'] = "Scallions",
	-- 			['price_to_customer'] = 75,
	-- 			['price_to_customer_min'] = 75,
	-- 			['price_to_customer_max'] = 120,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 75,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'scallions.png',
	-- 		},
	-- 		['lemon'] = {
	-- 			['name'] = "Lemon",
	-- 			['price_to_customer'] = 150,
	-- 			['price_to_customer_min'] = 150,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 150,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'lemon.png',
	-- 		},
	-- 		['ginger'] = {
	-- 			['name'] = "Ginger",
	-- 			['price_to_customer'] = 250,
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 250,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'ginger.png',
	-- 		},
	-- 		['sweetcorn'] = {
	-- 			['name'] = "Sweet Corn",
	-- 			['price_to_customer'] = 300,
	-- 			['price_to_customer_min'] = 300,
	-- 			['price_to_customer_max'] = 400,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 300,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'sweetcorn.png',
	-- 		}
	-- 	}
	-- },
	-- ['meat'] = {
	-- 	['page_name'] = "Meat",
	-- 	['page_desc'] = "Get all your Meat Here.",
	-- 	['page_icon'] = '<i class="fa-solid fa-palette"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/cooking.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['pork'] = {
	-- 			['name'] = "Pork",
	-- 			['price_to_customer'] = 250,
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 250,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'pork.png',
	-- 		},
	-- 		['egg'] = {
	-- 			['name'] = "Egg",
	-- 			['price_to_customer'] = 270,
	-- 			['price_to_customer_min'] = 270,
	-- 			['price_to_customer_max'] = 370,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 270,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'egg.png',
	-- 		},
	-- 		['squid'] = {
	-- 			['name'] = "Squid",
	-- 			['price_to_customer'] = 325,
	-- 			['price_to_customer_min'] = 325,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 325,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'squid.png',
	-- 		},
	-- 		['chicken'] = {						
	-- 			['name'] = "Chicken Pieces",				
	-- 			['price_to_customer'] = 126,	
	-- 			['price_to_customer_min'] = 126,
	-- 			['price_to_customer_max'] = 212,
	-- 			['price_to_export'] = 126,		
	-- 			['price_to_owner'] = 126,		
	-- 			['amount_to_owner'] = 3000,		
	-- 			['amount_to_delivery'] = 3000,	
	-- 			['img'] = 'chicken.png',		
	-- 		},
	-- 		['beef'] = {						
	-- 			['name'] = "Beef",				
	-- 			['price_to_customer'] = 226,	
	-- 			['price_to_customer_min'] = 226,
	-- 			['price_to_customer_max'] = 326,
	-- 			['price_to_export'] = 226,		
	-- 			['price_to_owner'] = 226,		
	-- 			['amount_to_owner'] = 3000,		
	-- 			['amount_to_delivery'] = 3000,	
	-- 			['img'] = 'beef.png',		
	-- 		},
	-- 		['prawn'] = {
	-- 			['name'] = "Prawn",
	-- 			['price_to_customer'] = 280,
	-- 			['price_to_customer_min'] = 280,
	-- 			['price_to_customer_max'] = 340,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 280,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'prawn.png',
	-- 		},
	-- 		['crab'] = {
	-- 			['name'] = "Crab",
	-- 			['price_to_customer'] = 340,
	-- 			['price_to_customer_min'] = 340,
	-- 			['price_to_customer_max'] = 380,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 340,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'crab.png',
	-- 		}
	-- 	}
	-- },
	-- ['essence'] = {
	-- 	['page_name'] = "Essence",
	-- 	['page_desc'] = "Get all your Essence Here.",
	-- 	['page_icon'] = '<i class="fa-solid fa-wand-magic-sparkles"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/cooking.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['fcolour'] = {
	-- 			['name'] = "Food Colour",
	-- 			['price_to_customer'] = 130,
	-- 			['price_to_customer_min'] = 130,
	-- 			['price_to_customer_max'] = 230,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 130,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'fcolour.png',
	-- 		},
	-- 		['masala'] = {						
	-- 			['name'] = "Masala",				
	-- 			['price_to_customer'] = 250,	
	-- 			['price_to_customer_min'] = 200,
	-- 			['price_to_customer_max'] = 300,
	-- 			['price_to_export'] = 200,		
	-- 			['price_to_owner'] = 200,		
	-- 			['amount_to_owner'] = 500,		
	-- 			['amount_to_delivery'] = 3000,	
	-- 			['img'] = 'masala.png',			
	-- 		},
	-- 		['vanilla'] = {
	-- 			['name'] = "Vanilla Extract",
	-- 			['price_to_customer'] = 150,
	-- 			['price_to_customer_min'] = 150,
	-- 			['price_to_customer_max'] = 250,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 150,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'vanilla.png',
	-- 		},
	-- 		['flavour'] = {
	-- 			['name'] = "Flavour",
	-- 			['price_to_customer'] = 230,
	-- 			['price_to_customer_min'] = 230,
	-- 			['price_to_customer_max'] = 330,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 230,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'flavour.png',
	-- 		}
	-- 	}
	-- },
	-- ['other'] = {
	-- 	['page_name'] = "Other",
	-- 	['page_desc'] = "Get all your Grocery Here.",
	-- 	['page_icon'] = '<i class="fa-solid fa-bowl-rice"></i>', -- Its the icon that will appear in the page tab. "padding:12px" means that the image will be 15px smaller, use it to resize the image if needed. Tip: You can get nice images here: https://icon-icons.com/search/icons/
	-- 	-- ['page_icon'] = '<i class="fa-solid fa-burger"></i>', -- As an alternative to the page icon, you can use icons from here (https://fontawesome.com/search?m=free&s=solid)
	-- 	['page_img'] = 'img/categories/cooking.png',	-- This is the category image in the page to buy categories
	-- 	['category_buy_price'] = 2500, 			-- Price to buy the category
	-- 	['category_sell_price'] = 1250, 		-- Price to sell the category
	-- 	['items'] = {
	-- 		['bun'] = {
	-- 			['name'] = "Bun",
	-- 			['price_to_customer'] = 210,
	-- 			['price_to_customer_min'] = 210,
	-- 			['price_to_customer_max'] = 310,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 210,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'bun.png',
	-- 		},
	-- 		['sugar'] = {
	-- 			['name'] = "Sugar",
	-- 			['price_to_customer'] = 110,
	-- 			['price_to_customer_min'] = 110,
	-- 			['price_to_customer_max'] = 210,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 110,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'sugar.png',
	-- 		},
	-- 		['salt'] = {						
	-- 			['name'] = "Salt",				
	-- 			['price_to_customer'] = 20,	
	-- 			['price_to_customer_min'] = 20,
	-- 			['price_to_customer_max'] = 75,
	-- 			['price_to_export'] = 20,		
	-- 			['price_to_owner'] = 20,		
	-- 			['amount_to_owner'] = 500,		
	-- 			['amount_to_delivery'] = 3000,	
	-- 			['img'] = 'salt.png',			
	-- 		},
	-- 		['ketchup'] = {						
	-- 			['name'] = "Ketchup",				
	-- 			['price_to_customer'] = 40,	
	-- 			['price_to_customer_min'] = 40,
	-- 			['price_to_customer_max'] = 120,
	-- 			['price_to_export'] = 0,		
	-- 			['price_to_owner'] = 40,		
	-- 			['amount_to_owner'] = 3000,		
	-- 			['amount_to_delivery'] = 3000,	
	-- 			['img'] = 'ketchup.png',			
	-- 		},
	-- 		['mayonnaise'] = {						
	-- 			['name'] = "Mayonnaise",				
	-- 			['price_to_customer'] = 50,	
	-- 			['price_to_customer_min'] = 50,
	-- 			['price_to_customer_max'] = 160,
	-- 			['price_to_export'] = 0,		
	-- 			['price_to_owner'] = 50,		
	-- 			['amount_to_owner'] = 3000,		
	-- 			['amount_to_delivery'] = 3000,	
	-- 			['img'] = 'mayonnaise.png',			
	-- 		},
	-- 		['butter'] = {						
	-- 			['name'] = "Butter",				
	-- 			['price_to_customer'] = 96,	
	-- 			['price_to_customer_min'] = 96,
	-- 			['price_to_customer_max'] = 146,
	-- 			['price_to_export'] = 0,		
	-- 			['price_to_owner'] = 96,		
	-- 			['amount_to_owner'] = 2000,		
	-- 			['amount_to_delivery'] = 2000,	
	-- 			['img'] = 'butter.png',			
	-- 		},
	-- 		['cheese'] = {						
	-- 			['name'] = "Cheese",				
	-- 			['price_to_customer'] = 126,	
	-- 			['price_to_customer_min'] = 126,
	-- 			['price_to_customer_max'] = 226,
	-- 			['price_to_export'] = 0,		
	-- 			['price_to_owner'] = 126,		
	-- 			['amount_to_owner'] = 2000,		
	-- 			['amount_to_delivery'] = 2000,	
	-- 			['img'] = 'cheese.png',			
	-- 		},
	-- 		['oil'] = {						
	-- 			['name'] = "Oil",				
	-- 			['price_to_customer'] = 35,	
	-- 			['price_to_customer_min'] = 35,
	-- 			['price_to_customer_max'] = 135,
	-- 			['price_to_export'] = 0,		
	-- 			['price_to_owner'] = 35,		
	-- 			['amount_to_owner'] = 2000,		
	-- 			['amount_to_delivery'] = 2000,	
	-- 			['img'] = 'oil.png',			
	-- 		},
	-- 		['rice'] = {
	-- 			['name'] = "Rice",
	-- 			['price_to_customer'] = 250,
	-- 			['price_to_customer_min'] = 250,
	-- 			['price_to_customer_max'] = 350,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 250,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'rice.png',
	-- 		},
	-- 		['soda'] = {
	-- 			['name'] = "Soda",
	-- 			['price_to_customer'] = 90,
	-- 			['price_to_customer_min'] = 90,
	-- 			['price_to_customer_max'] = 190,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 90,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'soda.png',
	-- 		},
	-- 		['teapowder'] = {
	-- 			['name'] = "Tea Leaf",
	-- 			['price_to_customer'] = 50,
	-- 			['price_to_customer_min'] = 50,
	-- 			['price_to_customer_max'] = 150,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 50,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'teapowder.png',
	-- 		},
	-- 		['ethanol'] = {
	-- 			['name'] = "Ethanol",
	-- 			['price_to_customer'] = 110,
	-- 			['price_to_customer_min'] = 110,
	-- 			['price_to_customer_max'] = 210,
	-- 			['price_to_export'] = 0,
	-- 			['price_to_owner'] = 110,
	-- 			['amount_to_owner'] = 2000,
	-- 			['amount_to_delivery'] = 2000,
	-- 			['img'] = 'ethanol.png',
	-- 		}
	-- 	}
	-- }
 }
-- Here you can configure the permissions for each employee role
-- 1 = Basic Access
-- 2 = Advanced Access
-- 3 = Full Access
-- When setting a permission to 1, any employee will be able to access that function/page
-- When setting a permission to 2, only the employees with the advanced access and full access will be able to access that function/page
-- When setting a permission to 3, only the employees with the full access will be able to access that function/page
-- When setting a permission to 4, only the owner will be able to access that function/page
Config.roles_permissions = {
	['functions'] = {				-- These are the actions (when a button is clicked)
		['createJob'] = 2,
		['deleteJob'] = 2,
		['renameMarket'] = 3,
		['setPrice'] = 2,
		['buyUpgrade'] = 2,
		['hideBalance'] = 2,
		['showBalance'] = 2,
		['withdrawMoney'] = 3,
		['depositMoney'] = 3,
		['sellMarket'] = 4,
		['storeProductFromInventory'] = 1,
		['hirePlayer'] = 3,
		['firePlayer'] = 3,
		['changeRole'] = 4,
		['giveComission'] = 4,
		['startImportJob'] = 1,
		['startExportJob'] = 1,
		['buyCategory'] = 3,
		['sellCategory'] = 3,
	},
	['ui_pages'] = {				-- These are the UI pages
		['main'] = 1,
		['goods'] = 1,
		['hire'] = 2,
		['employee'] = 3,
		['upgrades'] = 2,
		['bank'] = 3
	}
}
-- Setting to remove inactive stores
Config.clear_stores = {
	['active'] = false,				-- Set to false to disable this function
	['min_stock_amount'] = 30,		-- Minimum percentage of stock to consider an inactive store. Stores that have been inactive for a long time will be removed
	['min_stock_variety'] = 70,		-- Minimum percentage of variety of products in stock to consider an inactive store. Stores that have been inactive for a long time will be removed
	['cooldown'] = 72				-- Time (in hours) that the store needs to be below the minimum amount of stock to be removed
}

Config.route_blip = {				-- The blip style that will appear when doing the store jobs
	['id'] = 478,					-- Blip id
	['color'] = 5					-- Blip color
}

-- Cargo delivery locations (vector3)
Config.delivery_locations = {
	{ -952.31, -1077.87, 2.48 },
	{ -978.23, -1108.09, 2.16 },
	{ -1024.49, -1139.6, 2.75 },
	{ -1063.76, -1159.88, 2.56 },
	{ -1001.68, -1218.78, 5.75 },
	{ -1171.54, -1575.61, 4.51 },
	{ -1097.94, -1673.36, 8.4 },
	{ -1286.17, -1267.12, 4.52 },
	{ -1335.75, -1146.55, 6.74 },
	{ -1750.47, -697.09, 10.18 },
	{ -1876.84, -584.39, 11.86 },
	{ -1772.23, -378.12, 46.49 },
	{ -1821.38, -404.97, 46.65 },
	{ -1965.33, -296.96, 41.1 },
	{ -3089.24, 221.49, 14.07 },
	{ -3088.62, 392.3, 11.45 },
	{ -3077.98, 658.9, 11.67 },
	{ -3243.07, 931.84, 17.23 },
	{ 1230.8, -1590.97, 53.77 },
	{ 1286.53, -1604.26, 54.83 },
	{ 1379.38, -1515.23, 58.24 },
	{ 1379.38, -1515.23, 58.24 },
	{ 1437.37, -1492.53, 63.63 },
	{ 450.04, -1863.49, 27.77 },
	{ 403.75, -1929.72, 24.75 },
	{ 430.16, -1559.31, 32.8 },
	{ 446.06, -1242.17, 30.29 },
	{ 322.39, -1284.7, 30.57 },
	{ 369.65, -1194.79, 31.34 },
	{ 474.27, -635.05, 25.65 },
	{ 158.87, -1215.65, 29.3 },
	{ 154.68, -1335.62, 29.21 },
	{ 215.54, -1461.67, 29.19 },
	{ 167.46, -1709.3, 29.3 },
	{ -444.47, 287.68, 83.3 },
	{ -179.56, 314.25, 97.88 },
	{ -16.07, 216.7, 106.75 },
	{ 164.02, 151.87, 105.18 },
	{ 840.2, -181.93, 74.19 },
	{ 952.27, -252.17, 67.77 },
	{ 1105.27, -778.84, 58.27 },
	{ 1099.59, -345.68, 67.19 },
	{ -1211.12, -401.56, 38.1 },
	{ -1302.69, -271.32, 40.0 },
	{ -1468.65, -197.3, 48.84 },
	{ -1583.18, -265.78, 48.28 },
	{ -603.96, -774.54, 25.02 },
	{ -805.14, -959.54, 18.13 },
	{ -325.07, -1356.35, 31.3 },
	{ -321.94, -1545.74, 31.02 },
	{ -428.95, -1728.13, 19.79 },
	{ -582.38, -1743.65, 22.44 },
	{ -670.43, -889.09, 24.5 },
	{ 1691.4, 3866.21, 34.91 },
	{ 1837.93, 3907.12, 33.26 },
	{ 1937.08, 3890.89, 32.47},
	{ 2439.7, 4068.45, 38.07 },
	{ 2592.26, 4668.98, 34.08 },
	{ 1961.53, 5184.91, 47.98 },
	{ 2258.59, 5165.84, 59.12 },
	{ 1652.7, 4746.1, 42.03 },
	{ -359.09, 6062.05, 31.51 },
	{ -160.13, 6432.2, 31.92 },
	{ 33.33, 6673.27, 32.19 },
	{ 175.03, 6643.14, 31.57 },
	{ 22.8, 6488.44, 31.43 },
	{ 64.39, 6309.17, 31.38 },
	{ 122.42, 6406.02, 31.37 },
	{ 1681.2, 6429.11, 32.18 },
	{ 2928.01, 4474.74, 48.04 },
	{ 2709.92, 3454.83, 56.32 },
	{ -688.75, 5788.9, 17.34 },
	{ -436.13, 6154.93, 31.48 },
	{ -291.09, 6185.0, 31.49 },
	{ 405.31, 6526.38, 27.69 },
	{ -20.38, 6567.13, 31.88 },
	{ -368.06, 6341.4, 29.85 },
	{ 1842.89, 3777.72, 33.16 },
	{ 1424.82, 3671.73, 34.18 },
	{ 996.54, 3575.55, 34.62 },
	{ 1697.52, 3596.14, 35.56 },
	{ 2415.05, 5005.35, 46.68 },
	{ 2336.21, 4859.41, 41.81},
	{ 1800.9, 4616.07, 37.23 },
	{ -453.3, 6336.9, 13.11 },
	{ -425.4, 6356.43, 13.33 },
	{ -481.9, 6276.47, 13.42 },
	{ -693.92, 5761.95, 17.52 },
	{ -682.03, 5770.96, 17.52 },
	{ -379.44, 6062.07, 31.51 },
	{ -105.68, 6528.7, 30.17 },
	{ 35.02, 6662.61, 32.2 },
	{ 126.41, 6353.64, 31.38 },
	{ 48.15, 6305.99, 31.37 },
	{ 1417.68, 6343.83, 24.01 },
	{ 1510.21, 6326.28, 24.61 },
	{ 1698.22, 6425.66, 32.77 },
	{ 2434.69, 5011.7, 46.84 },
	{ 1718.88, 4677.32, 43.66 },
	{ 1673.21, 4958.09, 42.35 },
	{ 1364.33, 4315.43, 37.67 },
	{ -1043.6, 5326.84, 44.58 },
	{ -329.63, 6150.58, 32.32 },
	{ -374.41, 6191.04, 31.73 },
	{ -356.63, 6207.34, 31.85 },
	{ -347.15, 6224.69, 31.7 },
	{ -315.61, 6194.0, 31.57 },
	{ -33.3, 6455.87, 31.48 },
	{ 405.52, 6526.59, 27.7 },
	{ 1470.41, 6513.71, 21.23 },
	{ 1593.73, 6460.56, 25.32 },
	{ 1741.31, 6420.19, 35.05 },
}

-- Product export locations (vector3)
Config.export_locations = {
    {-758.14, 5540.96, 33.49},
    {-3046.19, 143.27, 11.6},
    {-1153.01, 2672.99, 18.1},
    {622.67, 110.27, 92.59},
    {-574.62, -1147.27, 22.18},
    {376.31, 2638.97, 44.5},
    {1738.32, 3283.89, 41.13},
    {1419.98, 3618.63, 34.91},
    {1452.67, 6552.02, 14.89},
    {3472.4, 3681.97, 33.79},
    {2485.73, 4116.13, 38.07},
    {65.02, 6345.89, 31.22},
    {-303.28, 6118.17, 31.5},
    {-63.41, -2015.25, 18.02},
    {-46.35, -2112.38, 16.71},
    {-746.6, -1496.67, 5.01},
    {369.54, 272.07, 103.11},
    {907.61, -44.12, 78.77},
    {-1517.31, -428.29, 35.45},
    {235.04, -1520.18, 29.15},
    {34.8, -1730.13, 29.31},
    {350.4, -2466.9, 6.4},
    {1213.97, -1229.01, 35.35},
    {1395.7, -2061.38, 52.0},
    {729.09, -2023.63, 29.31},
    {840.72, -1952.59, 28.85},
    -- {551.76, -1840.26, 25.34},
    {723.78, -1372.08, 26.29},
    -- {-339.92, -1284.25, 31.32},
    {1137.23, -1285.05, 34.6},
    -- {466.93, -1231.55, 29.95},
    {442.28, -584.28, 28.5},
    {1560.52, 888.69, 77.46},
    {2561.78, 426.67, 108.46},
    {569.21, 2730.83, 42.07},
    {2665.4, 1700.63, 24.49},
    {1120.1, 2652.5, 38.0},
    {2004.23, 3071.87, 47.06},
    {2038.78, 3175.7, 45.09},
    {1635.54, 3562.84, 35.23},
    {2744.55, 3412.43, 56.57},
    {1972.17, 3839.16, 32.0},
    {1980.59, 3754.65, 32.18},
    {1716.0, 4706.41, 42.69},
    {1691.36, 4918.42, 42.08},
    {1971.07, 5165.12, 47.64},
    {1908.78, 4932.06, 48.97},
    {140.79, -1077.85, 29.2},
    {-323.98, -1522.86, 27.55},
    {-1060.53, -221.7, 37.84},
    {2471.47, 4463.07, 35.3},
    {2699.47, 3444.81, 55.8},
    {-1060.53, -221.7, 37.84},
    {2655.38, 3281.01, 55.24},
    {2730.39, 2778.2, 36.01},
    {-2966.68, 363.37, 14.77},
    {2788.89, 2816.49, 41.72},
    {-604.45, -1212.24, 14.95},
    {2534.83, 2589.08, 37.95},
    {-143.31, 205.88, 92.12},
    {2347.04, 2633.25, 46.67},
    {860.47, -896.87, 25.53},
    {973.34, -1038.19, 40.84},
    {-409.04, 1200.44, 325.65},
    {-1617.77, 3068.17, 32.27},
    {-71.8, -1089.98, 26.56},
    {-409.04, 1200.44, 325.65},
    {-1617.77, 3068.17, 32.27},
    {1246.34, 1860.78, 79.47},
    {-1777.63, 3082.36, 32.81},
    {-1775.87, 3088.13, 32.81},
    {-1827.5, 2934.11, 32.82},
    {-2123.69, 3270.14, 32.82},
    {-2444.59, 2981.63, 32.82},
    {-2448.59, 2962.8, 32.82},
    {-2277.86, 3176.57, 32.81},
    {-2969.0, 366.46, 14.77},
    {-1637.61, -814.53, 10.17},
    {-1494.72, -891.67, 10.11},
    {-902.27, -1528.42, 5.03},
    {-1173.93, -1749.87, 3.97},
    {-1087.8, -2047.55, 13.23},
    {-1133.74, -2035.99, 13.21},
    {-1234.4, -2092.3, 13.93},
    {-1025.97, -2223.62, 8.99},
    {850.42, 2197.69, 51.93},
    {42.61, 2803.45, 57.88},
    {-1193.54, -2155.4, 13.2},
    {-1184.37, -2185.67, 13.2},
    {2041.76, 3172.26, 44.98},
    {-465.48, -2169.09, 10.01},
    {-3150.77, 1086.55, 20.7},
    {-433.69, -2277.29, 7.61},
    {-395.18, -2182.97, 10.29},
    {-3029.7, 591.68, 7.79},
    {-3029.7, 591.68, 7.79},
    {-1007.29, -3021.72, 13.95},
    {-61.32, -1832.75, 26.8},
    {822.72, -2134.28, 29.29},
    {942.22, -2487.76, 28.34},
    {729.29, -2086.53, 29.3},
    {783.08, -2523.98, 20.51},
    {717.8, -2111.18, 29.22},
    {787.05, -1612.38, 31.17},
    {913.52, -1556.87, 30.74},
    {777.64, -2529.46, 20.13},
    {846.71, -2496.12, 28.34},
    {711.79, -1395.19, 26.35},
    {723.38, -1286.3, 26.3},
    {983.0, -1230.77, 25.38},
    {818.01, -2422.85, 23.6},
    {885.53, -1166.38, 24.99},
    {700.85, -1106.93, 22.47},
    {882.26, -2384.1, 28.0},
    {977.83, -1821.21, 31.17},
    {-1138.73, -759.77, 18.87},
    {938.71, -1154.36, 25.38},
    {973.0, -1156.18, 25.43},
    {689.41, -963.48, 23.49},
    {140.72, -375.29, 43.26},
    {-497.09, -62.13, 39.96},
    {-606.34, 187.43, 70.01},
    {117.12, -356.15, 42.59},
    {53.91, -1571.07, 29.47},
    {1528.1, 1719.32, 109.97},
    -- {1411.29, 1060.33, 114.34},
    {1145.76, -287.73, 68.96},
    {1117.71, -488.25, 65.25},
    {874.28, -949.16, 26.29},
    {829.28, -874.08, 25.26},
    {725.37, -874.53, 24.67},
    {693.66, -1090.43, 22.45},
    {977.51, -1013.67, 41.32},
    {901.89, -1129.9, 24.08},
    {911.7, -1258.04, 25.58},
    {847.06, -1397.72, 26.14},
    {830.67, -1409.13, 26.16},
    {130.47, -1066.12, 29.2},
    -- {-52.79, -1078.65, 26.93},
    {-131.74, -1097.38, 21.69},
    {-621.47, -1106.05, 22.18},
    {-668.65, -1182.07, 10.62},
    {-111.84, -956.71, 27.27},
    {-1323.51, -1165.11, 4.73},
    {-1314.65, -1254.96, 4.58},
    {-1169.18, -1768.78, 3.87},
    {-1343.38, -744.02, 22.28},
    {-1532.84, -578.16, 33.63},
    {-1461.4, -362.39, 43.89},
    {-1457.25, -384.15, 38.51},
    {-1544.42, -411.45, 41.99},
    {-1432.72, -250.32, 46.37},
    {-1040.24, -499.88, 36.07},
    {346.43, -1107.19, 29.41},
    {523.99, -2112.7, 5.99},
    {977.19, -2539.34, 28.31},
    {1101.01, -2405.39, 30.76},
    {1591.9, -1714.0, 88.16},
    {1693.41, -1497.45, 113.05},
    {1029.44, -2501.31, 28.43},
    {2492.55, -320.89, 93.0},
    {2846.31, 1463.1, 24.56},
    {3631.05, 3768.61, 28.52},
    {3572.5, 3665.53, 33.9},
    {2919.03, 4337.85, 50.31},
    {2521.47, 4203.47, 39.95},
    {2926.2, 4627.28, 48.55},
    {3808.59, 4463.22, 4.37},
    {3323.71, 5161.1, 18.4},
    -- {2133.06, 4789.57, 40.98},
    {1900.83, 4913.82, 48.87},
    {381.06, 3591.37, 33.3},
    {642.8, 3502.47, 34.09},
    {277.33, 2884.71, 43.61},
    {-60.3, 1961.45, 190.19},
    {225.63, 1244.33, 225.46},
    {-1136.15, 4925.14, 220.01},
    {-519.96, 5243.84, 79.95},
    {-602.87, 5326.63, 70.46},
    {-797.95, 5400.61, 34.24},
    {-776.0, 5579.11, 33.49},
    {-704.2, 5772.55, 17.34},
    {-299.24, 6300.27, 31.5},
    {402.52, 6619.61, 28.26},
    {-247.72, 6205.46, 31.49},
    {-267.5, 6043.45, 31.78},
    {-16.29, 6452.44, 31.4},
    -- {2204.73, 5574.04, 53.74},
    {1638.98, 4840.41, 42.03},
    {1961.26, 4640.93, 40.71},
    {1776.61, 4584.67, 37.65},
    -- {137.29, 281.73, 109.98},
    {588.37, 127.87, 98.05},
    {199.8, 2788.78, 45.66},
    {708.58, -295.1, 59.19},
    {581.28, 2799.43, 42.1},
    {1296.73, 1424.35, 100.45},
    {955.85, -22.89, 78.77}
}

-- Config for npcs that will stand in the stores
Config.NPCs = {
	{
		['model'] = 'mp_m_shopkeep_01',				-- Ped model (https://docs.fivem.net/docs/game-references/ped-models/)
		['emote'] = 'WORLD_HUMAN_STAND_MOBILE',		-- Ped emote (https://wiki.rage.mp/index.php?title=Scenarios)
		['pos'] = {									-- Ped locations (vector4)
			{-2988.9944, 853.2300, -118.6631, 347.2358},

		}
	},
	{
		['model'] = 's_m_y_ammucity_01',
		['emote'] = 'WORLD_HUMAN_COP_IDLES',
		['pos'] = {
			{23.0, -1105.67, 29.8, 162.91},
		}
	},
}

Config.create_table = true