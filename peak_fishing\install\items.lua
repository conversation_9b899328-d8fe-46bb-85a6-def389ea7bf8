return {
    -- Fish Items

    bitterling = {
        label = 'Bitterling',
        description = 'A small freshwater fish common in calm waters.',
        stack = true,
    },

    pale_chub = {
        label = 'Pale Chub',
        description = 'A modest-sized fish found in streams and rivers.',
        stack = true,
    },

    dace = {
        label = 'Dace',
        description = 'A river fish most active during evening and early morning.',
        stack = true,
    },

    carp = {
        label = 'Carp',
        description = 'A large pond-dwelling fish found year-round.',
        stack = true,
    },

    goldfish = {
        label = 'Goldfish',
        description = 'A classic small pond fish.',
        stack = true,
    },

    killifish = {
        label = 'Killifish',
        description = 'A small pond fish found during spring and summer.',
        stack = true,
    },

    crawfish = {
        label = 'Crawfish',
        description = 'A crustacean found in ponds during spring and summer.',
        stack = true,
    },
    
    tadpole = {
        label = 'Tadpole',
        description = 'A young amphibian found in ponds during spring.',
        stack = true,
    },
    
    frog = {
        label = 'Frog',
        description = 'An amphibian found in ponds during late spring and summer.',
        stack = true,
    },

    freshwater_goby = {
        label = 'Freshwater Goby',
        description = 'A small river fish most active during evening hours.',
        stack = true,
    },

    loach = {
        label = 'Loach',
        description = 'A river fish found during spring.',
        stack = true,
    },

    bluegill = {
        label = 'Bluegill',
        description = 'A small river fish active during daytime.',
        stack = true,
    },

    yellow_perch = {
        label = 'Yellow Perch',
        description = 'A river fish found during winter and early spring.',
        stack = true,
    },

    black_bass = {
        label = 'Black Bass',
        description = 'A large and common river fish found year-round.',
        stack = true,
    },

    tilapia = {
        label = 'Tilapia',
        description = 'A river fish found during summer months.',
        stack = true,
    },

    pond_smelt = {
        label = 'Pond Smelt',
        description = 'A small river fish found in winter months.',
        stack = true,
    },

    sweetfish = {
        label = 'Sweetfish',
        description = 'A river fish found during summer months.',
        stack = true,
    },

    anchovy = {
        label = 'Anchovy',
        description = 'A small, silvery marine fish often found in schools.',
        stack = true,
    },
    
    horse_mackerel = {
        label = 'Horse Mackerel',
        description = 'A common marine fish found in large schools.',
        stack = true,
    },

    sea_bass = {
        label = 'Sea Bass',
        description = 'A common large marine fish found year-round.',
        stack = true,
    },

    dab = {
        label = 'Dab',
        description = 'A flatfish found in marine environments.',
        stack = true,
    },
    
    olive_flounder = {
        label = 'Olive Flounder',
        description = 'A large flatfish found in marine environments.',
        stack = true,
    },

    squid = {
        label = 'Squid',
        description = 'A marine cephalopod with a distinctive elongated body.',
        stack = true,
    },

    koi = {
        label = 'Koi',
        description = 'A colorful and sought-after ornamental carp.',
        stack = true,
    },

    pop_eyed_goldfish = {
        label = 'Pop-eyed Goldfish',
        description = 'A unique goldfish variant with prominent eyes.',
        stack = true,
    },

    ranchu_goldfish = {
        label = 'Ranchu Goldfish',
        description = 'A round-bodied goldfish breed.',
        stack = true,
    },

    angelfish = {
        label = 'Angelfish',
        description = 'A tropical river fish with distinctive shape.',
        stack = true,
    },

    betta = {
        label = 'Betta',
        description = 'A colorful river fish known for its vibrant fins.',
        stack = true,
    },

    neon_tetra = {
        label = 'Neon Tetra',
        description = 'A small, brightly colored river fish.',
        stack = true,
    },

    rainbowfish = {
        label = 'Rainbowfish',
        description = 'A colorful river fish active during late spring and summer.',
        stack = true,
    },

    sea_butterfly = {
        label = 'Sea Butterfly',
        description = 'A delicate marine creature found in winter seas.',
        stack = true,
    },

    seahorse = {
        label = 'Seahorse',
        description = 'A unique marine fish with a distinctive shape.',
        stack = true,
    },

    clownfish = {
        label = 'Clownfish',
        description = 'A small, brightly colored tropical marine fish.',
        stack = true,
    },

    surgeonfish = {
        label = 'Surgeonfish',
        description = 'A colorful marine fish with distinctive markings.',
        stack = true,
    },

    butterfly_fish = {
        label = 'Butterfly Fish',
        description = 'A vibrant tropical marine fish with unique patterns.',
        stack = true,
    },

    zebra_turkeyfish = {
        label = 'Zebra Turkeyfish',
        description = 'A unique marine fish with striking striped patterns.',
        stack = true,
    },

    barred_knifejaw = {
        label = 'Barred Knifejaw',
        description = 'A distinctive marine fish with unique markings.',
        stack = true,
    },

    red_snapper = {
        label = 'Red Snapper',
        description = 'A prized marine fish with distinctive red coloration.',
        stack = true,
    },

    moray_eel = {
        label = 'Moray Eel',
        description = 'A serpentine marine creature found in rocky areas.',
        stack = true,
    },

    ribbon_eel = {
        label = 'Ribbon Eel',
        description = 'A colorful and unique marine eel species.',
        stack = true,
    },

    sturgeon = {
        label = 'Sturgeon',
        description = 'An ancient species known for its size and caviar.',
        stack = true,
    },

    giant_snakehead = {
        label = 'Giant Snakehead',
        description = 'A large and distinctive pond fish.',
        stack = true,
    },

    golden_trout = {
        label = 'Golden Trout',
        description = 'A rare and beautiful trout with golden coloring.',
        stack = true,
    },

    stringfish = {
        label = 'Stringfish',
        description = 'A large clifftop river fish found in winter.',
        stack = true,
    },
    
    king_salmon = {
        label = 'King Salmon',
        description = 'The largest and most prestigious salmon species.',
        stack = true,
    },

    napoleonfish = {
        label = 'Napoleonfish',
        description = 'A large, distinctive marine fish found during summer.',
        stack = true,
    },

    dorado = {
        label = 'Dorado',
        description = 'A powerful predatory river fish from South America.',
        stack = true,
    },

    gar = {
        label = 'Gar',
        description = 'A prehistoric-looking fish found in ponds during summer.',
        stack = true,
    },

    arapaima = {
        label = 'Arapaima',
        description = 'A massive river fish from the Amazon basin.',
        stack = true,
    },

    tuna = {
        label = 'Tuna',
        description = 'A large, powerful marine fish prized by anglers.',
        stack = true,
    },

    blue_marlin = {
        label = 'Blue Marlin',
        description = 'A massive and powerful oceanic predator.',
        stack = true,
    },

    giant_trevally = {
        label = 'Giant Trevally',
        description = 'A large and aggressive marine game fish.',
        stack = true,
    },

    mahi_mahi = {
        label = 'Mahi-Mahi',
        description = 'A colorful and fast-swimming tropical marine fish.',
        stack = true,
    },

    ray = {
        label = 'Ray',
        description = 'A flat marine creature gliding through the waters.',
        stack = true,
    },

    saw_shark = {
        label = 'Saw Shark',

        description = 'A unique shark species with a distinctive saw-like snout.',
        stack = true,
    },

    hammerhead_shark = {
        label = 'Hammerhead Shark',
        description = 'A shark with a uniquely shaped head resembling a hammer.',
        stack = true,
    },

    whale_shark = {
        label = 'Whale Shark',
        description = 'The largest fish species in the world, despite being a gentle giant.',
        stack = true,
    },

    ocean_sunfish = {
        label = 'Ocean Sunfish',
        description = 'A massive and unique marine creature with a distinctive fin.',
        stack = true,
    },

    oarfish = {
        label = 'Oarfish',
        description = 'A long, mysterious deep-sea creature rarely seen.',
        stack = true,
    },

    great_white_shark = {
        label = 'Great White Shark',
        description = 'The ocean\'s apex predator. A legendary catch.',
        stack = true,
    },

    coelacanth = {
        label = 'Coelacanth',
        description = 'A prehistoric fish thought to be extinct, only catchable during rain.',
        stack = true,
    },
    
    barreleye = {
        label = 'Barreleye',
        description = 'A unique deep-sea fish with a transparent head.',
        stack = true,
    },

    -- Fishing Equipment

    fishing_rod = {
        label = 'Fishing Rod',
        weight = 1000,
        stack = false,
        description = 'A tool for catching fish',
        server = {
            export = 'peak_fishing.useFishingRod',
        },
        buttons = {
            {
                label = 'Open Bait Storage',
                action = function(slot)
                    TriggerServerEvent('peak_fishing:server:openBaitStorage', slot)
                end
            },
            {
                label = 'Open Tackle Storage',
                action = function(slot)
                    TriggerServerEvent('peak_fishing:server:openTackleStorage', slot)
                end
            },
        },
    },

    fishing_net = {
        label = 'Fishing Net',
        weight = 2000,
        description = 'A sturdy fishing net used to catch multiple fish at once. Ideal for deep water or large catches.',
        stack = false,
        server = {
            export = 'peak_fishing.useFishingNet',
        }
    },  

    -- Tackle Items

    bobber = {
        label = 'Basic Bobber',
        weight = 50,
        description = 'Improves bite detection and helps stabilize your fishing line.',
        stack = true,
    },
    
    spinner = {
        label = 'Spinner Lure',
        weight = 75,
        description = 'Attracts predatory fish with its flashing movements.',
        stack = true,
    },
    
    sinker_set = {
        label = 'Professional Sinker Set',
        weight = 120,
        description = 'High-quality weights for precise depth control and better stability in currents.',
        stack = true,
    },
    
    premium_tackle = {
        label = 'Premium Tackle Kit',
        weight = 200,
        description = 'High-quality line and hooks for better control and reduced chance of losing fish.',
        stack = true,
    },

    -- Bait items

    earthworm = {
        label = 'Earthworm',
        weight = 10,
        stack = true,
        description = 'A classic natural bait, perfect for beginners.',
    },

    bread = {
        label = 'Bread Ball',
        weight = 10,
        stack = true,
        description = 'Simple but effective bait for surface fishing.',
    },

    corn = {
        label = 'Sweet Corn',
        weight = 10,
        stack = true,
        description = 'Popular bait for freshwater fishing.',
    },

    maggots = {
        label = 'Maggots',
        weight = 10,
        stack = true,
        description = 'Small but highly effective natural bait.',
    },

    minnow = {
        label = 'Live Minnow',
        weight = 30,
        stack = true,
        description = 'Fresh live bait, very attractive to predatory fish.',
    },

    nightcrawler = {
        label = 'Nightcrawler',
        weight = 20,
        stack = true,
        description = 'Large worms, excellent for night fishing.',
    },

    bloodworm = {
        label = 'Bloodworm',
        weight = 15,
        stack = true,
        description = 'Premium marine bait, highly effective in saltwater.',
    },

    magnet = {
        label = 'Fishing Magnet',
        weight = 200,
        description = 'A specialized magnet for treasure hunting. Unlikely to catch fish, but great for finding metal treasures.',
        stack = true,
    },

    -- Treasure Items

    old_boot = {
        label = 'Old Boot',
        weight = 500,
        description = 'A worn-out leather boot. Not valuable, but a fishing classic.',
        stack = true,
    },
    
    rusty_anchor = {
        label = 'Rusty Anchor',
        weight = 5000,
        description = 'A small, corroded ship anchor. Might be of interest to collectors.',
        stack = true,
    },
    
    broken_bottle = {
        label = 'Antique Bottle',
        weight = 300,
        description = 'An old glass bottle with faded markings. Could be centuries old.',
        stack = true,
    },
    
    gold_coin = {
        label = 'Gold Doubloon',
        weight = 20,
        description = 'A well-preserved gold coin from a bygone era. Quite valuable!',
        stack = true,
    },
    
    silver_necklace = {
        label = 'Silver Necklace',
        weight = 50,
        description = 'A tarnished silver necklace with an intricate pendant.',
        stack = true,
    },
    
    treasure_chest = {
        label = 'Small Treasure Chest',
        weight = 2000,
        description = 'A miniature wooden chest containing a few valuables. What a find!',
        stack = true,
    },
    
    ancient_statue = {
        label = 'Ancient Statue',
        weight = 1000,
        description = 'A small stone figurine of unknown origin. Archaeologists might be interested.',
        stack = true,
    },
    
    pearl = {
        label = 'Giant Pearl',
        weight = 50,
        description = 'An unusually large and lustrous pearl. Extremely rare.',
        stack = true,
    },
    
    diving_watch = {
        label = 'Vintage Diving Watch',
        weight = 150,
        description = 'A high-quality waterproof watch, somehow still in working condition.',
        stack = true,
    },
    
    shipwreck_plank = {
        label = 'Shipwreck Fragment',
        weight = 2000,
        description = 'A piece of wood with ornate carvings, likely from an old shipwreck.',
        stack = true,
    },
} 