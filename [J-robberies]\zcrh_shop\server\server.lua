CanRobbery = true
CurrentIndex = nil
CurrentRobber = nil
local playerRewards = {} -- Track actual rewards per player

-- ESX reference for compatibility
ESX = Core

function SendNotify(Number, source)
    if source ~= nil and source ~= 0 then
        notification(source, Config.Notify[Number][1], Config.Notify[Number][2], Config.Notify[Number][3], Config.Notify[Number][4])
    end
end

RESCB('zcrh_shop:server:getItem',function(source,cb,item)
    if GetItemCount(source, item) > 0 then
        RemoveItem(source, item, 1)
        cb(true)
    else
        cb(false)
    end
end)

RESCB("zcrh_shop:server:PossibleRobbery",function(source,cb)
	Citizen.Wait(1)

    local policeOnline = #ESX.GetExtendedPlayers('job', Config.CopsJobs[1])

	if policeOnline >= Config.RequiredCopsCount then
        if CanRobbery then
            cb(true)
        else
            if CurrentRobber ~= nil then
                SendNotify(1, source)
            elseif CurrentRobber == nil then
                SendNotify(8, source)
            end
            cb(false)
        end
	else
        SendNotify(2, source)
        cb(false)
	end
end)

RegisterServerEvent('zcrh_shop:server:sync')
AddEventHandler('zcrh_shop:server:sync', function(type, index, playerleft)
    if type == 'START ROBBERY' then
        CanRobbery = false
        CurrentIndex = index
        CurrentRobber = source
        TriggerClientEvent('zcrh_shop:client:sync', -1, type, index)
        -- Notify HMS that heist started
        TriggerEvent('hms:heistStarted', 'shopheist')
    elseif type == 'RESET ROBBERY' then
        -- Show heist completion with actual rewards if player completed it
        if CurrentRobber and playerRewards[CurrentRobber] and Config.SuccessRobbery.Use then
            local rewards = {}

            if playerRewards[CurrentRobber].totalMoney > 0 then
                table.insert(rewards, {
                    stat = "Cash Stolen",
                    value = "~g~$" .. playerRewards[CurrentRobber].totalMoney
                })
            end

            if playerRewards[CurrentRobber].itemDetails ~= "" then
                table.insert(rewards, {
                    stat = "Items Stolen",
                    value = "~g~" .. playerRewards[CurrentRobber].itemDetails
                })
            end

            if #rewards > 0 then
                TriggerClientEvent('heist-notify:show', CurrentRobber, Config.SuccessRobbery, rewards, 7, true)
            end

            playerRewards[CurrentRobber] = nil -- Clear rewards data
        end

        CurrentIndex = nil
        CurrentRobber = nil
        TriggerClientEvent('zcrh_shop:client:sync', -1, type, index)
        Citizen.CreateThread(function()
            Citizen.Wait(1000*60*Config.Cooldown)
            CanRobbery = true
        end)
        -- Notify HMS that heist ended
        TriggerEvent('hms:heistEnded', 'shopheist')
    elseif type == 'NPC SYNC' then
        TriggerClientEvent('zcrh_shop:client:sync', -1, type, index)
    end
end)

RegisterServerEvent('zcrh_shop:server:PoliceAlert')
AddEventHandler('zcrh_shop:server:PoliceAlert', function(coords, index)
    local players = ESX.GetExtendedPlayers('job', Config.CopsJobs[1])
    for i = 1, #players do
        TriggerClientEvent('zcrh_shop:client:PoliceAlert', players[i].source, coords, index)
    end
end)

RegisterServerEvent('zcrh_shop:server:AddMoney')
AddEventHandler('zcrh_shop:server:AddMoney', function(type, index)
    if source == CurrentRobber then
        Money = 0
        if type == 'SAFE' then
            Money = math.random(Config.ShopRobberys[index].SafeMoney[1], Config.ShopRobberys[index].SafeMoney[2])
        elseif type == 'CASH REGISTER' then
            Money = math.random(Config.ShopRobberys[index].CashRegisterMoney[1], Config.ShopRobberys[index].CashRegisterMoney[2])
        end
        AddMoneyFunction(source, Money)

        -- Track actual rewards
        if not playerRewards[source] then
            playerRewards[source] = {
                totalMoney = 0,
                totalItems = 0,
                itemDetails = ""
            }
        end
        playerRewards[source].totalMoney = playerRewards[source].totalMoney + Money

        -- Webhook log for money
        local playerName = PlayerNameFunction(source)
        local playerIdentifier = GetIdentifierFunction(source)
        local shopName = Config.ShopRobberys[index].ShopName
        local message = string.format("**Player:** %s\n**Identifier:** %s\n**Shop:** %s\n**Money Received:** %d %s", playerName, playerIdentifier, shopName, Money, Config.MoneyForm)
        SendWebhookLog('robberyProcess', message)

        TriggerClientEvent('zcrh_shop:client:sync', CurrentRobber, 'RECEIVED MONEY', index, Money)
    else
        print('^1HACKER ^2>>>^7 ID: ^4'.. source ..' ^1[^4'.. GetIdentifierFunction(source) ..'^1] try to create money with the "zcrh_shop" trigger!')
    end
end)

RegisterServerEvent('zcrh_shop:server:AddItem')
AddEventHandler('zcrh_shop:server:AddItem', function(type, index)
    if source == CurrentRobber then
        if type == 'SAFE' then
            local Text = ''
            local Items = {}
            for k, v in pairs(Config.ShopRobberys[index].SafeRewardItems) do
                local randomCount = math.random(v.count[1], v.count[2])

                if Text == '' then
                    Text = randomCount..'x '..v.label
                else
                    Text = Text..', '..randomCount..'x '..v.label
                end

                table.insert(Items, {label = v.label, count = randomCount})
                AddItem(source, v.item, randomCount)
            end

            -- Track actual rewards
            if not playerRewards[source] then
                playerRewards[source] = {
                    totalMoney = 0,
                    totalItems = 0,
                    itemDetails = ""
                }
            end
            playerRewards[source].itemDetails = Text

            notification(source, Config.Notify[10][1], Config.Notify[10][2]..' '..Text, Config.Notify[10][3], Config.Notify[10][4])
            TriggerClientEvent('zcrh_shop:client:sync', CurrentRobber, 'RECEIVED ITEMS', index, Items)

            -- Webhook log for items
            local playerName = PlayerNameFunction(source)
            local playerIdentifier = GetIdentifierFunction(source)
            local shopName = Config.ShopRobberys[index].ShopName
            local itemDetails = Text
            local message = string.format("**Player:** %s\n**Identifier:** %s\n**Shop:** %s\n**Items Received:** %s", playerName, playerIdentifier, shopName, itemDetails)
            SendWebhookLog('robberyProcess', message)
        else
            print('^1HACKER ^2>>>^7 ID: ^4'.. source ..' ^1[^4'.. GetIdentifierFunction(source) ..'^1] try to create money with the "zcrh_shop" trigger!')
        end
    else
        print('^1HACKER ^2>>>^7 ID: ^4'.. source ..' ^1[^4'.. GetIdentifierFunction(source) ..'^1] try to create money with the "zcrh_shop" trigger!')
    end
end)

RegisterServerEvent('zcrh_shop:server:SellItems')
AddEventHandler('zcrh_shop:server:SellItems', function(index)
    local Text = ''
    local AllGotMoney = 0
    for k,v in pairs(Config.ShopRobberys) do
        if k == index then
            for _k, _v in pairs(v.SafeRewardItems) do
                local itemCount = GetItemCount(source, _v.item)
                if itemCount > 0 then
                    if Text == '' then
                        Text = itemCount..'x '.._v.label
                    else
                        Text = Text..', '..itemCount..'x '.._v.label
                    end

                    RemoveItem(source, _v.item, itemCount)
                    AllGotMoney += itemCount*_v.sellPrice
                end
            end
            if AllGotMoney > 0 then
                AddMoneyFunction(source, AllGotMoney)
                notification(source, Config.Notify[12][1], Config.Notify[12][2]..' '..Text..', '.. Config.Notify[10][2] ..' '.. AllGotMoney ..' '..Config.MoneyForm, Config.Notify[12][3], Config.Notify[12][4])
            else
                SendNotify(13, source)
            end
        end
    end
end)

-----------------------------------------------------------
----------------------| player drop |----------------------
-----------------------------------------------------------

AddEventHandler('playerDropped', function()
	if CurrentRobber == source then
        TriggerEvent('zcrh_shop:server:sync', 'RESET ROBBERY', CurrentIndex, true)
    end
end)