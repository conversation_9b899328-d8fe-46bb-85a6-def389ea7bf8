-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               11.8.2-MariaDB - mariadb.org binary distribution
-- Server OS:                    Win64
-- HeidiSQL Version:             12.10.0.7000
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- Dumping database structure for s36_zion-main
CREATE DATABASE IF NOT EXISTS `s36_zion-main` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci */;
USE `s36_zion-main`;

-- Dumping structure for table s36_zion-main.job_grades
CREATE TABLE IF NOT EXISTS `job_grades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_name` varchar(50) DEFAULT NULL,
  `grade` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `label` varchar(50) NOT NULL,
  `salary` int(11) DEFAULT 100,
  `type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT 'lspd',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6357 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table s36_zion-main.job_grades: ~405 rows (approximately)
INSERT INTO `job_grades` (`id`, `job_name`, `grade`, `name`, `label`, `salary`, `type`) VALUES
	(1, 'unemployed', 0, 'unemployed', 'Freelancer', 625, 'lspd'),
	(2, 'ambulance', 0, 'student', 'Medical Student', 32500, 'lspd'),
	(3, 'ambulance', 1, 'intern', 'Intern', 37500, 'lspd'),
	(4, 'ambulance', 4, 'jresident', 'Junior Resident', 55000, 'lspd'),
	(5, 'ambulance', 5, 'sresident', 'Senior Resident', 67500, 'lspd'),
	(9, 'cardealer', 0, 'recruit', ' Recruit', 250, 'lspd'),
	(10, 'cardealer', 1, 'novice', 'Novice', 250, 'lspd'),
	(11, 'cardealer', 2, 'experienced', 'Experienced', 250, 'lspd'),
	(12, 'cardealer', 3, 'boss', 'Boss', 250, 'lspd'),
	(25, 'mechanic', 0, 'recruit', 'Recruit', 7500, 'lspd'),
	(26, 'mechanic', 1, 'novice', 'Novice', 8750, 'lspd'),
	(27, 'mechanic', 2, 'experienced', 'Experienced', 10000, 'lspd'),
	(28, 'mechanic', 3, 'chief', 'Chief', 11250, 'lspd'),
	(29, 'mechanic', 4, 'boss', 'Boss', 12500, 'lspd'),
	(40, 'taxi', 0, 'boss', 'Driver', 250, 'lspd'),
	(1001, 'weazelnews', 0, 'recrue', 'Recruit', 12500, 'lspd'),
	(1002, 'weazelnews', 6, 'boss', 'Boss', 37500, 'lspd'),
	(1024, 'police', 0, 'constable', 'PO', 30000, 'lspd'),
	(1025, 'police', 1, 'headconst', 'Constable', 37500, 'lspd'),
	(1026, 'police', 2, 'asi', 'Senior Constable', 42500, 'lspd'),
	(1027, 'police', 3, 'si', 'Corporal', 47500, 'lspd'),
	(1028, 'police', 4, 'circle', 'Sergeant', 52500, 'lspd'),
	(1029, 'police', 5, 'dysp', 'Inspector', 57500, 'lspd'),
	(1030, 'police', 6, 'super', 'Lieutenant', 62500, 'lspd'),
	(1033, 'prisoner', 0, 'inmate', 'Inmate', 250, 'lspd'),
	(1042, 'banker', 0, 'boss', 'Manager', 250, 'lspd'),
	(1056, 'realestateagent', 0, 'recruit', 'Advertiser', 250, 'lspd'),
	(1057, 'realestateagent', 1, 'novice', 'Expert Seller', 250, 'lspd'),
	(1058, 'realestateagent', 2, 'experienced', 'Co-Owner', 250, 'lspd'),
	(1059, 'realestateagent', 3, 'boss', 'Owner', 250, 'lspd'),
	(1060, 'offmechanic', 1, 'novice', 'Novice', 250, 'lspd'),
	(1061, 'offmechanic', 2, 'experienced', 'Experienced', 250, 'lspd'),
	(1062, 'offmechanic', 3, 'chief', 'Chief', 250, 'lspd'),
	(1063, 'offmechanic', 4, 'boss', 'Boss', 250, 'lspd'),
	(1064, 'offmechanic', 0, 'recrue', 'Recruit', 250, 'lspd'),
	(1085, 'weazelnews', 1, 'novice', 'Writer', 15000, 'lspd'),
	(1086, 'weazelnews', 5, 'experienced', 'M.D', 37500, 'lspd'),
	(1093, 'nclub', 0, 'recruit', 'Bartender', 250, 'lspd'),
	(1094, 'nclub', 1, 'novice', 'Club-DJ', 250, 'lspd'),
	(1095, 'nclub', 2, 'experienced', 'Security', 250, 'lspd'),
	(1096, 'nclub', 3, 'chief', 'Manager', 250, 'lspd'),
	(1097, 'nclub', 4, 'boss', 'Owner', 250, 'lspd'),
	(1107, 'rzer', 0, 'recruit', 'Recruit', 3750, 'lspd'),
	(1108, 'rzer', 6, 'officer', 'Customer Support', 6500, 'lspd'),
	(1121, 'uchiha', 0, 'recruit', 'Cashier', 250, 'lspd'),
	(1122, 'uchiha', 1, 'novice', 'Chef', 250, 'lspd'),
	(1123, 'uchiha', 2, 'experienced', 'Sous Chef', 250, 'lspd'),
	(1124, 'uchiha', 3, 'chief', 'Master Chef', 250, 'lspd'),
	(1125, 'uchiha', 4, 'boss', 'Owner', 250, 'lspd'),
	(1131, 'pub', 0, 'recruit', 'Team', 250, 'lspd'),
	(1141, 'shelby', 0, 'recruit', 'Salesman', 250, 'lspd'),
	(1142, 'shelby', 1, 'novice', 'Collector', 250, 'lspd'),
	(1143, 'shelby', 2, 'experienced', 'Manager', 250, 'lspd'),
	(1144, 'shelby', 3, 'chief', 'Co-Owner', 250, 'lspd'),
	(1145, 'shelby', 4, 'boss', 'Owner', 250, 'lspd'),
	(1146, 'casino', 0, 'recruit', 'Gambler', 25, 'lspd'),
	(1147, 'casino', 1, 'novice', 'Security', 25, 'lspd'),
	(1148, 'casino', 2, 'experienced', 'Receptionist', 25, 'lspd'),
	(1149, 'casino', 3, 'sergeant', 'Manager', 25, 'lspd'),
	(1150, 'casino', 4, 'intendent', 'Special Security', 25, 'lspd'),
	(1151, 'casino', 5, 'lieutenant', 'CFO', 25, 'lspd'),
	(1152, 'casino', 6, 'chief', 'Co-Owner', 250, 'lspd'),
	(1153, 'casino', 7, 'boss', 'Owner', 2500, 'lspd'),
	(1154, 'vat', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(1155, 'vat', 1, 'novice', 'Staff', 250, 'lspd'),
	(1156, 'vat', 2, 'experienced', 'Manager', 250, 'lspd'),
	(1157, 'vat', 3, 'chief', 'Co-Owner', 250, 'lspd'),
	(1158, 'vat', 4, 'boss', 'Owner', 250, 'lspd'),
	(1164, 'hell', 0, 'recruit', 'Thug', 250, 'lspd'),
	(1165, 'hell', 1, 'novice', 'Guardian', 250, 'lspd'),
	(1166, 'hell', 2, 'experienced', 'Family', 250, 'lspd'),
	(1167, 'hell', 3, 'chief', 'Co-Leader', 250, 'lspd'),
	(1168, 'hell', 4, 'boss', 'Leader', 250, 'lspd'),
	(1176, 'vagos', 0, 'recruit', 'Thug', 250, 'lspd'),
	(1177, 'vagos', 1, 'novice', 'Guardian', 250, 'lspd'),
	(1178, 'vagos', 2, 'experienced', 'Family', 250, 'lspd'),
	(1179, 'vagos', 3, 'chief', 'Co-Leader', 250, 'lspd'),
	(1180, 'vagos', 4, 'boss', 'Leader', 250, 'lspd'),
	(1181, 'fbi', 0, 'recruit', 'Security', 250, 'lspd'),
	(1182, 'fbi', 1, 'novice', 'CID', 250, 'lspd'),
	(1183, 'fbi', 2, 'experienced', 'Chief Detective', 250, 'lspd'),
	(1184, 'fbi', 3, 'chief', 'Chief', 250, 'lspd'),
	(1185, 'fbi', 4, 'boss', 'Boss', 250, 'lspd'),
	(1191, 'lawyer', 0, 'recruit', 'Associate', 250, 'lspd'),
	(1192, 'lawyer', 1, 'novice', 'Lawyer', 250, 'lspd'),
	(1193, 'lawyer', 2, 'experienced', 'Public Prosecutor', 250, 'lspd'),
	(1194, 'lawyer', 3, 'chief', 'Attorney', 250, 'lspd'),
	(1195, 'lawyer', 4, 'boss', 'Chief Judge', 250, 'lspd'),
	(1201, 'ktm', 0, 'recruit', 'Thug', 250, 'lspd'),
	(1202, 'ktm', 1, 'novice', 'Guardian', 250, 'lspd'),
	(1203, 'ktm', 2, 'experienced', 'Family', 250, 'lspd'),
	(1204, 'ktm', 3, 'chief', 'Co-Leader', 250, 'lspd'),
	(1205, 'ktm', 4, 'boss', 'Leader', 250, 'lspd'),
	(1211, 'godf', 0, 'recruit', 'Security', 2500, 'lspd'),
	(1212, 'godf', 1, 'novice', 'Henchman', 5000, 'lspd'),
	(1213, 'godf', 2, 'experienced', 'Sr. Henchman', 10000, 'lspd'),
	(1214, 'godf', 3, 'chief', 'Right Hand', 20000, 'lspd'),
	(1215, 'godf', 4, 'boss', 'Godfather', 25000, 'lspd'),
	(1216, 'rental', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(1217, 'rental', 1, 'novice', 'Employee', 250, 'lspd'),
	(1218, 'rental', 2, 'experienced', 'Supervisor', 250, 'lspd'),
	(1219, 'rental', 3, 'chief', 'Manager', 250, 'lspd'),
	(1220, 'rental', 4, 'boss', 'Owner', 250, 'lspd'),
	(1226, 'mrx', 0, 'recruit', 'Thug', 250, 'lspd'),
	(1227, 'mrx', 1, 'novice', 'Guardian', 250, 'lspd'),
	(1228, 'mrx', 2, 'experienced', 'Family', 250, 'lspd'),
	(1229, 'mrx', 3, 'chief', 'Co - Leader', 250, 'lspd'),
	(1230, 'mrx', 4, 'boss', 'Leader', 250, 'lspd'),
	(1231, 'govt', 0, 'recruit', 'SPG Gunman', 25000, 'lspd'),
	(1232, 'govt', 2, 'itax', 'Officer', 30000, 'lspd'),
	(1233, 'govt', 3, 'major', 'Black Cats', 32500, 'lspd'),
	(1234, 'govt', 5, 'assistant', 'Command In Chief', 37500, 'lspd'),
	(1235, 'govt', 6, 'sergeant', 'PA', 40000, 'lspd'),
	(1236, 'govt', 1, 'driver', 'Chauffeur', 27500, 'lspd'),
	(1241, 'rzer', 3, 'novice', 'Event Planner', 2000, 'lspd'),
	(5555, 'ambulance', 6, 'doctor', 'Doctor', 80000, 'lspd'),
	(5556, 'ambulance', 7, 'chiefdoc', 'Chief Doctor', 95000, 'lspd'),
	(5557, 'ambulance', 10, 'surgeon', 'Surgeon', 125000, 'lspd'),
	(5558, 'ambulance', 11, 'cmo', 'CMO', 132500, 'lspd'),
	(5569, 'madealer', 0, 'recurit', 'Delivery', 250, 'lspd'),
	(5570, 'madealer', 1, 'novice', 'Sales Man', 250, 'lspd'),
	(5571, 'madealer', 2, 'experienced', 'Manager', 250, 'lspd'),
	(5572, 'madealer', 3, 'chief', 'Partner', 250, 'lspd'),
	(5573, 'madealer', 4, 'boss', 'Owner', 250, 'lspd'),
	(5579, 'bm', 0, 'recruit', 'Thug', 250, 'lspd'),
	(5580, 'bm', 1, 'novice', 'Guardian', 250, 'lspd'),
	(5581, 'bm', 2, 'experienced', 'Family', 250, 'lspd'),
	(5582, 'bm', 3, 'chief', 'Co - Leader', 250, 'lspd'),
	(5583, 'bm', 4, 'boss', 'Leader', 250, 'lspd'),
	(5584, 'md', 0, 'recruit', 'Recruit', 7500, 'lspd'),
	(5585, 'md', 1, 'novice', 'Employee', 8750, 'lspd'),
	(5586, 'md', 2, 'experienced', 'Manager', 10000, 'lspd'),
	(5587, 'md', 3, 'chief', 'Co - Owner', 11250, 'lspd'),
	(5588, 'md', 4, 'boss', 'Owner', 12500, 'lspd'),
	(5604, 'dealer', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(5605, 'dealer', 1, 'novice', 'Employee', 250, 'lspd'),
	(5606, 'dealer', 2, 'experienced', 'Manager', 250, 'lspd'),
	(5607, 'dealer', 3, 'chief', 'Co-Owner', 250, 'lspd'),
	(5608, 'dealer', 4, 'boss', 'Owner', 250, 'lspd'),
	(5614, 'dho', 0, 'recruit', 'Thug', 250, 'lspd'),
	(5615, 'dho', 1, 'novice', 'Guardian', 250, 'lspd'),
	(5616, 'dho', 2, 'experienced', 'Family', 250, 'lspd'),
	(5617, 'dho', 3, 'chief', 'Co-Leader', 250, 'lspd'),
	(5618, 'dho', 4, 'boss', 'Leader', 250, 'lspd'),
	(6000, 'police', 7, 'dig', 'Captain', 67500, 'lspd'),
	(6001, 'police', 8, 'ig', 'Major', 75000, 'lspd'),
	(6002, 'police', 9, 'adgp', 'Superintendent', 87500, 'lspd'),
	(6003, 'police', 10, 'astcom', 'Assistant Commissioner', 100000, 'lspd'),
	(6004, 'offpolice', 0, 'recruit', 'PO', 500, 'lspd'),
	(6005, 'offpolice', 1, 'const', 'Constable', 500, 'lspd'),
	(6006, 'offpolice', 2, 'headconst', 'Senior Constable', 500, 'lspd'),
	(6007, 'offpolice', 3, 'asistant', 'Corporal', 500, 'lspd'),
	(6008, 'offpolice', 4, 'superint', 'Sergeant', 500, 'lspd'),
	(6009, 'offpolice', 5, 'chiefoff', 'Inspector', 500, 'lspd'),
	(6010, 'offpolice', 6, 'officer', 'Lieutenant', 500, 'lspd'),
	(6011, 'offpolice', 7, 'sergeant', 'Captain', 500, 'lspd'),
	(6012, 'offpolice', 8, 'intendent', 'Major', 500, 'lspd'),
	(6013, 'offpolice', 9, 'lieutenant', 'Superintendent', 500, 'lspd'),
	(6014, 'offpolice', 10, 'chef', 'Assistant Commissioner', 500, 'lspd'),
	(6015, 'smg', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6016, 'smg', 1, 'novice', 'Guardian', 250, 'lspd'),
	(6017, 'smg', 2, 'experienced', 'Family', 250, 'lspd'),
	(6018, 'smg', 3, 'chief', 'Co-Leader', 250, 'lspd'),
	(6019, 'smg', 4, 'boss', 'Leader', 250, 'lspd'),
	(6020, 'bwc', 0, 'recruit', 'Recruit', 2000, 'lspd'),
	(6021, 'bwc', 2, 'novice', 'Panther', 3000, 'lspd'),
	(6022, 'bwc', 6, 'indentent', 'Panther Co-Head', 4000, 'lspd'),
	(6023, 'bwc', 10, 'peta', 'Panther Head', 5000, 'lspd'),
	(6024, 'bwc', 17, 'lieutenant', 'Commander', 10000, 'lspd'),
	(6025, 'bwc', 21, 'chief', 'Co-Leader', 14000, 'lspd'),
	(6026, 'bwc', 22, 'boss', 'Leader', 15000, 'lspd'),
	(6037, 'unknown', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6038, 'unknown', 1, 'novice', 'Guardian', 250, 'lspd'),
	(6039, 'unknown', 2, 'experienced', 'Family', 250, 'lspd'),
	(6040, 'unknown', 3, 'chief', 'Co-Leader', 250, 'lspd'),
	(6041, 'unknown', 4, 'boss', 'Leader', 250, 'lspd'),
	(6047, 'tenz', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6048, 'tenz', 1, 'officer', 'Guardian', 250, 'lspd'),
	(6049, 'tenz', 2, 'sergeant', 'Family', 250, 'lspd'),
	(6050, 'tenz', 3, 'chef', 'Co-Leader', 250, 'lspd'),
	(6051, 'tenz', 4, 'boss', 'Leader', 250, 'lspd'),
	(6062, 'bwc', 11, 'zeta', 'Sierra Head', 5000, 'lspd'),
	(6063, 'bwc', 7, 'ceta', 'Sierra Co-Head', 4000, 'lspd'),
	(6064, 'bwc', 3, 'theta', 'Sierra', 3000, 'lspd'),
	(6065, 'bwc', 9, 'gamma', 'Armada Head', 5000, 'lspd'),
	(6066, 'bwc', 5, 'beta', 'Armada Co-Head', 4000, 'lspd'),
	(6067, 'bwc', 1, 'alpha', 'Armada', 3000, 'lspd'),
	(6069, 'bwc', 15, 'meta', 'Operations', 8000, 'lspd'),
	(6074, 'weazelnews', 3, 'noviceee', 'Cheif Editor', 8000, 'lspd'),
	(6075, 'weazelnews', 2, 'novicee', 'Reporter', 7000, 'lspd'),
	(6076, 'police', 12, 'chief', 'DGP', 60000, 'lspd'),
	(6082, 'drv', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6083, 'drv', 1, 'novice', 'Guardian', 250, 'lspd'),
	(6084, 'drv', 2, 'experienced', 'Family', 250, 'lspd'),
	(6085, 'drv', 3, 'chief', 'Co - Leader', 250, 'lspd'),
	(6086, 'drv', 4, 'boss', 'Leader', 250, 'lspd'),
	(6087, 'zee', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6088, 'zee', 1, 'novice', 'Worker', 250, 'lspd'),
	(6089, 'zee', 2, 'experienced', 'Partner', 250, 'lspd'),
	(6090, 'zee', 3, 'chief', 'MD', 250, 'lspd'),
	(6091, 'zee', 4, 'boss', 'CEO', 250, 'lspd'),
	(6092, 'solo', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6093, 'solo', 1, 'novice', 'Worker', 250, 'lspd'),
	(6094, 'solo', 2, 'experienced', 'Manager', 250, 'lspd'),
	(6095, 'solo', 3, 'chief', 'Co - Owner', 250, 'lspd'),
	(6096, 'solo', 4, 'boss', 'Owner', 250, 'lspd'),
	(6097, 'pmm', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6098, 'pmm', 1, 'officer', 'Experienced', 250, 'lspd'),
	(6099, 'pmm', 2, 'sergeant', 'Manager', 250, 'lspd'),
	(6100, 'pmm', 3, 'lieutenant', 'Co-Owner', 250, 'lspd'),
	(6101, 'pmm', 4, 'boss', 'Owner', 250, 'lspd'),
	(6102, 'kkp', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6103, 'kkp', 1, 'officer', 'Experienced', 250, 'lspd'),
	(6104, 'kkp', 2, 'sergeant', 'Manager', 250, 'lspd'),
	(6105, 'kkp', 3, 'lieutenant', 'Co-Owner', 250, 'lspd'),
	(6106, 'kkp', 4, 'boss', 'Owner', 250, 'lspd'),
	(6107, 'holo', 0, 'recruit', 'Dealers', 250, 'lspd'),
	(6112, 'tmg', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6113, 'tmg', 1, 'officer', 'Guardian', 250, 'lspd'),
	(6114, 'tmg', 2, 'sergeant', 'Family', 250, 'lspd'),
	(6115, 'tmg', 3, 'lieutenant', 'Co-Leader', 250, 'lspd'),
	(6116, 'tmg', 4, 'boss', 'Leader', 250, 'lspd'),
	(6117, 'jade', 0, 'recruit', 'Table Boy', 250, 'lspd'),
	(6118, 'jade', 1, 'officer', 'Chef', 250, 'lspd'),
	(6119, 'jade', 2, 'sergeant', 'Manager', 250, 'lspd'),
	(6121, 'jade', 3, 'chief', 'Co-Owner', 250, 'lspd'),
	(6122, 'jade', 4, 'boss', 'Owner', 250, 'lspd'),
	(6123, 'gre', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6124, 'gre', 1, 'officer', 'Experienced', 250, 'lspd'),
	(6125, 'gre', 2, 'sergeant', 'Manager', 250, 'lspd'),
	(6126, 'gre', 3, 'itendent', 'Co-Owner', 250, 'lspd'),
	(6127, 'gre', 4, 'lieutenant', 'Owner', 250, 'lspd'),
	(6133, 'lc', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6134, 'lc', 1, 'novice', 'Guardian', 250, 'lspd'),
	(6135, 'lc', 2, 'experienced', 'Family', 250, 'lspd'),
	(6136, 'lc', 3, 'chief', 'Co-Leader', 250, 'lspd'),
	(6137, 'lc', 4, 'boss', 'Leader', 250, 'lspd'),
	(6138, 'brc', 0, 'recruit', 'Receptionist', 250, 'lspd'),
	(6139, 'brc', 1, 'novice', 'Sales Consultant', 250, 'lspd'),
	(6140, 'brc', 2, 'experienced', 'Management', 250, 'lspd'),
	(6141, 'brc', 3, 'chief', 'Director Board', 250, 'lspd'),
	(6142, 'brc', 4, 'boss', 'Boss', 250, 'lspd'),
	(6144, 'gang', 0, 'recruit', 'ZionyX', 250, 'lspd'),
	(6150, 'govt', 4, 'experienced', 'SPG Major', 14000, 'lspd'),
	(6151, 'ambulance', 2, 'nursing', 'Nursing Assistant', 17500, 'lspd'),
	(6152, 'ambulance', 3, 'nurse', 'Nurse', 20000, 'lspd'),
	(6164, 'onyx', 0, 'recruit', 'Staff', 250, 'lspd'),
	(6165, 'onyx', 1, 'officer', 'Caterer', 250, 'lspd'),
	(6166, 'onyx', 2, 'experienced', 'Manager', 250, 'lspd'),
	(6167, 'onyx', 3, 'chief', 'Co-Owner', 250, 'lspd'),
	(6168, 'onyx', 4, 'boss', 'Owner', 250, 'lspd'),
	(6169, 'vv', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6170, 'vv', 1, 'novice', 'Guardian', 250, 'lspd'),
	(6171, 'vv', 2, 'experienced', 'Family', 250, 'lspd'),
	(6172, 'vv', 3, 'chief', 'Co - Leader', 250, 'lspd'),
	(6173, 'vv', 4, 'boss', 'Leader', 250, 'lspd'),
	(6178, 'mower', 0, 'gardener', 'Gardener', 250, 'lspd'),
	(6179, 'trucker', 0, 'driver', 'Driver', 250, 'lspd'),
	(6181, 'bwc', 19, 'general', 'General', 12000, 'lspd'),
	(6187, 'rzer', 5, 'intended', 'Event Coordinator', 2400, 'lspd'),
	(6189, 'rzer', 13, 'chief', 'MD', 4500, 'lspd'),
	(6190, 'weazelnews', 4, 'noviceeee', 'Chief Marketing Officer', 9000, 'lspd'),
	(6191, 'ritems', 0, 'recruit', 'Dealer', 250, 'lspd'),
	(6192, 'gym', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6193, 'gym', 1, 'officer', 'Experienced', 250, 'lspd'),
	(6194, 'gym', 2, 'sergeant', 'Manager', 250, 'lspd'),
	(6195, 'gym', 3, 'chief', 'Co-Owner', 250, 'lspd'),
	(6196, 'gym', 4, 'boss', 'Owner', 250, 'lspd'),
	(6197, 'poultry', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6198, 'poultry', 1, 'officer', 'Experienced', 250, 'lspd'),
	(6199, 'poultry', 2, 'sergeant', 'Manager', 250, 'lspd'),
	(6200, 'poultry', 3, 'chief', 'Co-Owner', 250, 'lspd'),
	(6201, 'poultry', 4, 'boss', 'Owner', 250, 'lspd'),
	(6202, 'zcpd', 0, 'rookie', 'Rookie', 500, 'lspd'),
	(6203, 'zcpd', 1, 'mvd', 'MVD', 500, 'lspd'),
	(6204, 'zcpd', 2, 'handler', 'Handler', 500, 'lspd'),
	(6205, 'zcpd', 3, 'swat', 'SWAT', 500, 'lspd'),
	(6206, 'zcpd', 4, 'turf', 'Turf Squad', 500, 'lspd'),
	(6207, 'blacklist', 0, 'recruit', 'Racer', 250, 'lspd'),
	(6208, 'blacklist', 1, 'officer', 'Keepers', 250, 'lspd'),
	(6209, 'blacklist', 2, 'sergeant', 'High Table', 250, 'lspd'),
	(6210, 'blacklist', 3, 'boss', 'The Boss', 250, 'lspd'),
	(6216, 'govt', 7, 'chief', 'Right Hand', 17000, 'lspd'),
	(6217, 'govt', 8, 'boss', 'Mayor', 20000, 'lspd'),
	(6223, 'zsl', 0, 'boss', 'Organizer', 250, 'lspd'),
	(6226, 'atx', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6227, 'atx', 1, 'novice', 'Guardian', 250, 'lspd'),
	(6228, 'atx', 2, 'experienced', 'Family', 250, 'lspd'),
	(6229, 'atx', 3, 'chief', 'Co - Leader', 250, 'lspd'),
	(6230, 'atx', 4, 'boss', 'Leader', 250, 'lspd'),
	(6231, 'triad', 0, 'recruit', 'Thug', 250, 'lspd'),
	(6232, 'triad', 1, 'novice', 'Guardian', 250, 'lspd'),
	(6233, 'triad', 2, 'experienced', 'Family', 250, 'lspd'),
	(6234, 'triad', 3, 'chief', 'Co - Leader', 250, 'lspd'),
	(6235, 'triad', 4, 'boss', 'Leader', 250, 'lspd'),
	(6236, 'erp', 0, 'recruit', 'Couple', 250, 'lspd'),
	(6237, 'ambulance', 8, 'physician', 'Physician', 42000, 'lspd'),
	(6238, 'ambulance', 9, 'consultant', 'Consultant', 45000, 'lspd'),
	(6242, 'happy', 0, 'recruit', 'Organizer', 250, 'lspd'),
	(6243, 'happy', 1, 'officer', 'Planner', 250, 'lspd'),
	(6244, 'happy', 2, 'sergeant', 'Manager', 250, 'lspd'),
	(6245, 'happy', 3, 'chief', 'CFO', 250, 'lspd'),
	(6246, 'happy', 4, 'boss', 'CEO', 250, 'lspd'),
	(6247, 'rzer', 1, 'intendedd', 'Driver', 1600, 'lspd'),
	(6248, 'rzer', 2, 'recruitt', 'Builder', 1800, 'lspd'),
	(6249, 'rzer', 4, 'officerr', 'Designer', 2200, 'lspd'),
	(6251, 'rzer', 14, 'boss', 'Director', 5000, 'lspd'),
	(6252, 'rzer', 7, 'oofficerr', 'Rental Head', 2800, 'lspd'),
	(6253, 'rzer', 8, 'novicee', 'Stage Events Head', 2800, 'lspd'),
	(6254, 'rzer', 9, 'noviceee', 'Activity Head', 2800, 'lspd'),
	(6255, 'rzer', 10, 'nooviceee', 'Budget Analyst', 3000, 'lspd'),
	(6256, 'rzer', 11, 'experienced', 'Manager', 3500, 'lspd'),
	(6257, 'rzer', 12, 'experiencedd', 'CEO', 4000, 'lspd'),
	(6258, 'psy', 0, 'recruit', 'Assistant Psychiatrist', 250, 'lspd'),
	(6259, 'psy', 1, 'chief', 'Junior Psychiatrist', 250, 'lspd'),
	(6260, 'psy', 2, 'boss', 'Senior Psychiatrist', 250, 'lspd'),
	(6261, 'grocery', 0, 'recruit', 'Staff', 250, 'lspd'),
	(6266, 'utools', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6267, 'utools', 1, 'novice', 'Worker', 250, 'lspd'),
	(6268, 'utools', 2, 'experienced', 'Partner', 250, 'lspd'),
	(6269, 'utools', 3, 'chief', 'MD', 250, 'lspd'),
	(6270, 'utools', 4, 'boss', 'CEO', 250, 'lspd'),
	(6271, 'import', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6272, 'import', 1, 'officer', 'Experienced', 250, 'lspd'),
	(6273, 'import', 2, 'sergeant', 'Manager', 250, 'lspd'),
	(6274, 'import', 3, 'chief', 'Co-Owner', 250, 'lspd'),
	(6275, 'import', 4, 'boss', 'Owner', 250, 'lspd'),
	(6276, 'bike', 0, 'recruit', 'Delivery', 250, 'lspd'),
	(6277, 'bike', 1, 'novice', 'Sales Man', 250, 'lspd'),
	(6278, 'bike', 2, 'experienced', 'Manager', 250, 'lspd'),
	(6279, 'bike', 3, 'chief', 'Partner', 250, 'lspd'),
	(6280, 'bike', 4, 'boss', 'Owner', 250, 'lspd'),
	(6281, 'ambulance', 12, 'hsadmin', 'Hospital Administration', 55000, 'lspd'),
	(6282, 'ambulance', 14, 'chief', 'Head Of Department', 65000, 'lspd'),
	(6283, 'ambulance', 15, 'boss', 'Medical Director', 90000, 'lspd'),
	(6285, 'ambulance', 13, 'administration', 'Administration', 60000, 'lspd'),
	(6287, 'offambulance', 0, 'student', 'Medical Student', 300, 'lspd'),
	(6288, 'offambulance', 1, 'intern', 'Intern', 300, 'lspd'),
	(6290, 'offambulance', 2, 'nursing', 'Nursing Assistant', 380, 'lspd'),
	(6291, 'offambulance', 4, 'jresident', 'Junior Resident', 350, 'lspd'),
	(6292, 'offambulance', 3, 'nurse', 'Nurse', 400, 'lspd'),
	(6293, 'offambulance', 5, 'sresident', 'Senior Resident', 425, 'lspd'),
	(6294, 'offambulance', 6, 'doctor', 'Doctor', 450, 'lspd'),
	(6295, 'offambulance', 7, 'chiefdoc', 'Chief Doctor', 500, 'lspd'),
	(6296, 'offambulance', 8, 'physician', 'Physician', 550, 'lspd'),
	(6297, 'offambulance', 9, 'consultant', 'Consultant', 550, 'lspd'),
	(6298, 'offambulance', 10, 'surgeon', 'Surgeon', 550, 'lspd'),
	(6299, 'offambulance', 11, 'cmo', 'CMO', 600, 'lspd'),
	(6300, 'offambulance', 12, 'hsadmin', 'Hospital Administration', 500, 'lspd'),
	(6301, 'offambulance', 13, 'administration', 'Administration', 750, 'lspd'),
	(6302, 'offambulance', 14, 'chief', 'Head Of Department', 800, 'lspd'),
	(6303, 'offambulance', 15, 'boss', 'Medical Director', 850, 'lspd'),
	(6304, 'ammu', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6305, 'ammu', 1, 'novice', 'Worker', 250, 'lspd'),
	(6306, 'ammu', 2, 'experienced', 'Manager', 250, 'lspd'),
	(6307, 'ammu', 3, 'chief', 'Co - Owner', 250, 'lspd'),
	(6308, 'ammu', 4, 'boss', 'Owner', 250, 'lspd'),
	(6309, 'police', 11, 'com', 'Commissioner', 50000, 'lspd'),
	(6310, 'offpolice', 11, 'com', 'Commissioner', 500, 'lspd'),
	(6311, 'offpolice', 12, 'boss', 'DGP', 500, 'lspd'),
	(6312, 'ramen', 0, 'recruit', 'Waiter', 250, 'lspd'),
	(6313, 'ramen', 1, 'linecook', 'Line Cook', 250, 'lspd'),
	(6314, 'ramen', 2, 'souschef', 'Sous Chef', 250, 'lspd'),
	(6315, 'ramen', 3, 'masterchef', 'Master Chef', 250, 'lspd'),
	(6316, 'ramen', 4, 'boss', 'Manager', 250, 'lspd'),
	(6317, 'burger', 0, 'recruit', 'Waiter', 250, 'lspd'),
	(6318, 'burger', 1, 'linecook', 'Line Cook', 250, 'lspd'),
	(6319, 'burger', 2, 'souschef', 'Sous Chef', 250, 'lspd'),
	(6320, 'burger', 3, 'chief', 'Master Chef', 250, 'lspd'),
	(6321, 'burger', 4, 'boss', 'Manager', 250, 'lspd'),
	(6322, 'income', 0, 'recruit', 'Officer', 4500, 'lspd'),
	(6323, 'income', 1, 'officer', 'Assistant Commissioner', 5500, 'lspd'),
	(6324, 'income', 2, 'chief', 'Deputy Commissioner', 7500, 'lspd'),
	(6325, 'income', 3, 'boss', 'Chief', 12500, 'lspd'),
	(6326, 'bwc', 20, 'advisor', 'Advisor', 13000, 'lspd'),
	(6327, 'bwc', 18, 'chaphead', 'Chapter Head', 11000, 'lspd'),
	(6328, 'bwc', 16, 'marshal', 'Marshal', 9000, 'lspd'),
	(6329, 'bwc', 14, 'guardman', 'Guardsman', 7000, 'lspd'),
	(6330, 'bwc', 13, 'overseer', 'Overseer', 6000, 'lspd'),
	(6331, 'bwc', 12, 'echohead', 'Echo Head', 5000, 'lspd'),
	(6332, 'bwc', 8, 'echocohead', 'Echo Co-Head', 4000, 'lspd'),
	(6333, 'bwc', 4, 'echo', 'Echo', 3000, 'lspd'),
	(6334, 'trades', 0, 'recruit', 'Recruit', 250, 'lspd'),
	(6335, 'trades', 1, 'novice', 'Staff', 250, 'lspd'),
	(6336, 'trades', 2, 'experienced', 'Manager', 250, 'lspd'),
	(6337, 'trades', 3, 'chief', 'Co - Owner', 250, 'lspd'),
	(6338, 'trades', 4, 'boss', 'Owner', 250, 'lspd'),
	(6339, 'iron', 0, 'recruit', 'Syndicate Member', 250, 'lspd'),
	(6340, 'crimson', 0, 'recruit', 'Syndicate Member', 250, 'lspd'),
	(6341, 'shadow', 0, 'recruit', 'Syndicate Member', 250, 'lspd'),
	(6342, 'bgun', 0, 'recruit', 'Dealer', 250, 'lspd'),
	(6343, 'wgun', 0, 'recruit', 'Dealer', 250, 'lspd'),
	(6344, 'bitems', 0, 'recruit', 'Dealer', 250, 'lspd'),
	(6345, 'wgun', 1, 'boss', 'Manager', 250, 'lspd'),
	(6346, 'mixer', 0, 'recruit', 'Mixer', 250, 'lspd'),
	(6347, 'lumber', 0, 'recruit', 'Worker', 250, 'lspd'),
	(6348, 'labs', 0, 'recruit', 'Chemist', 250, 'lspd'),
	(6349, 'police', 13, 'boss', 'Chief', 70000, 'lspd'),
	(6350, 'marine_arch', 0, 'marine_arch', 'Marine Archaeologist', 250, 'lspd'),
	(6351, 'motosport', 0, 'recruit', 'Mechanic', 250, 'lspd'),
	(6352, 'motosport', 1, 'novice', 'Engineer ', 250, 'lspd'),
	(6353, 'motosport', 2, 'experienced', 'Co-Driver', 250, 'lspd'),
	(6354, 'motosport', 3, 'lieutenant', 'Driver', 250, 'lspd'),
	(6355, 'motosport', 4, 'chief', 'Manager', 250, 'lspd'),
	(6356, 'motosport', 5, 'boss', 'Race Marshal', 250, 'lspd');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
