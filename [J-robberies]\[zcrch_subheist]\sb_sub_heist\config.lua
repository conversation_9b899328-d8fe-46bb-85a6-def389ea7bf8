Config = {}

-- For QB CORE - exports['qb-core']:GetCoreObject()
--For ESX - exports['es_extended']:getSharedObject()

Config.Framework_core = exports['es_extended']:getSharedObject()
	
Config.submarineheist = {

    Framework = 'ESX', -- ESX OR QB

    startheist ={ 
        pos = vector3(-1657.4, -982.56, 8.16),
        peds = {
            {pos = vector3(-1657.4, -982.56, 8.16), heading = 50.92, ped = 'a_m_m_hasjew_01'},
        }
    },
	
	Cooldown = 60, -- in miniutes
	
	submarineped ={ 
        pos = vector3(-338.76, -2444.68, 7.28),
        peds = {
            {pos = vector3(-338.76, -2444.68, 7.28), heading = 58.8, ped = 'cs_floyd'},
        }
    },
	
	rewardbot ={ 
        pos = vector3(3817.9438, 4441.8516, 2.8108),
        peds = {
            {pos = vector3(3817.9438, 4441.8516, 2.8108), heading = 286.0312, ped = 'a_m_m_hasjew_01'},
        }
    },
	

	Payphone = {pos = vector3(-2186.144, -406.404144, 13.1099062)},
	Telescope = {pos = vector3(72.56264, -2258.21558, 5.950937)},
	Shiplayout = {pos = vector3(423.88, -2211.08, 14.88)},
	Chair = {pos = vector3(-181.2, -2274.48, 28.52)},
	
	droner = {pos = vector3(414.64, -2207.96, 19.36)},
	hacker = {pos = vector3(419.84, -2207.68, 19.36)},
	bomber = {pos = vector3(430.28, -2210.44, 19.36)},
	shooter = {pos = vector3(428.16, -2211.68, 19.36)},
	diver = {pos = vector3(-133.16, -2242.88, 7.8)},
	drone = {pos = vector3(-66.8, -2269.52, 7.8)},
	dronedrive = {pos = vector3(-114.64, -2218.08, 7.8)},
	Container = {pos = vector3(-120.16, -2322.0, -17.0)},
	Dronecomputer = {pos = vector3(-113.8, -2219.8, 7.8)},

    Dronegames = {
        fuse = {
        {pos = vector3(-132.35054, -2240.039, 7.78804),heading = 0, inuse = false},
        {pos = vector3(-86.57245, -2233.28345, 7.775835),heading = -180.0, inuse = false},
        {pos = vector3(-57.43818, -2255.512, 7.795495),heading = 0, inuse = false},
        {pos = vector3(15.6437111, -2258.13037, 7.751312),heading = 180.0, inuse = false},
    },
    },
	
    Plantbomb = {
        Bombs = {
        {pos = vector3(-85.8, -2364.36, 14.28),heading = 176.56 , inuse = false},
        {pos = vector3(-124.28, -2365.52, 9.32),heading = 92.08 , inuse = false},
        {pos = vector3(-183.72, -2372.72, 9.32),heading = 88.8 , inuse = false},
        {pos = vector3(-183.48, -2358.52, 9.32),heading = 85.52 , inuse = false},
        {pos = vector3(-212.8, -2362.68, 17.32),heading = 87.64 , inuse = false},
        {pos = vector3(-212.8, -2371.4, 17.32),heading = 90.0 , inuse = false},
    },
    },

	Reward_black = false,
	
	Oxygen_tank_timer = 1, -- in minutes
	
	blacmoney_name = 'black_money',
	
	Resetheist_admin = true,  
	
	Bomberlogoutenable = false, -- under development dont use this
	
	Resetheist_command = 'smcancel', -- automatically the heist will be reset after cooldown / if admins need to reset use this command
		
	
}

Config.Characteranimations = {
Droner = {dict = "misscommon@response", name = "bring_it_on"},
Controller = {dict = "anim@mp_player_intcelebrationfemale@shadow_boxing", name = "shadow_boxing"},
Bomber = {dict = "anim@mp_player_intupperthumbs_up", name = "idle_a"},
Diver = {dict = "anim@mp_player_intcelebrationfemale@cut_throat", name = "cut_throat"},
}

Config.police = {
Count = 0,
Policejobname = 'police',
Bliptime = 1, -- in miniutes
Blipcoords = vector3(-124.64, -2366.0, 9.32),
}

Config.adminlist = { -- admin license to reset /
'license:4b708bf6ace28eb4d2d92985ee6cceb17efb8120',
}

Config.submarineheistitems = {
    Subcard = { item = 'subcard', req_amount = 1, give = 1 },
    Detonate = { item = 'detonator', req_amount = 1, give = 1 },
    Drone = { item = 'drone_sh', req_amount = 1, give = 1 },  
    C4 = { item = 'c4', req_amount = 1, give = 6 },
    Ocygenmask = { item = 'oxy_mask_sh', req_amount = 1, give = 1 },
}


Config.Bomberlogoutlogs = {
    
	Enable = false,
	Maincolor = 3066993,
	Savecolor = 65280,
	Clearcolor = 16711680,
	Restorecolor = 3447003,
}

Config.rewards = {							-- INCREASED 7.5x for heist economy
{item = 'subcard' , price = 37500},			-- was 5000
}

Config.submarinesecuritypeds = {
	[1] = {pos = {-189.88, -2376.6, 9.32, 3.48},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100},
	[2] = {pos = {-190.08, -2367.96, 9.32, 2.16},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100},
	[3] = {pos = {-187.2, -2353.44, 9.32, 343.52},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100},
	[4] = {pos = {-183.24, -2353.16, 9.32, 0.84},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[5] = {pos = {-174.32, -2353.68, 9.32, 4.8},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
}

Config.submarinesecuritypeds1 = {
    [1] = {pos = {-167.68, -2353.16, 9.32, 0.92},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[2] = {pos = {-162.52, -2353.72, 9.32, 354.4},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[3] = {pos = {-155.72, -2353.44, 9.32, 354.04},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[4] = {pos = {-156.2, -2359.96, 23.44, 101.04},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },	
	[5] = {pos = {-137.72, -2370.28, 20.64, 113.4},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
}

Config.submarinesecuritypeds2 = {
	
	[1] = {pos = {-92.92, -2373.88, 14.28, 108},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[2] = {pos = {-93.88, -2358.12, 14.28, 342.04},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100},
	[3] = {pos = {-118.92, -2377.36, 9.32, 181.24},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[4] = {pos = {-131.08, -2377.52, 9.32, 170.44},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[5] = {pos = {-147.24, -2377.56, 9.32, 146.44},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
}

Config.submarinesecuritypeds3 = {
    [1] = {pos = {-163.52, -2377.64, 9.32, 181.28},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[2] = {pos = {-224.24, -2379.32, 13.32, 208},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[3] = {pos = {-212.48, -2377.32, 17.32, 244.28},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[4] = {pos = {-211.76, -2355.72, 17.32, 342.08},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[5] = {pos = {-215.52, -2371.88, 29.68, 250.8},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
}

Config.submarinesecuritypeds4 = {		
    [1] = {pos = {-215.76, -2360.96, 29.68, 350.28},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },	
	[2] = {pos = {-139.56, -2378.4, 9.32, 174.56},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[3] = {pos = {-156.24, -2378.36, 9.32, 179.72},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[4] = {pos = {-172.4, -2378.52, 9.32, 183.32},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },
	[5] = {pos = {-185.56, -2377.84, 9.32, 181.16},pedname = 's_m_m_chemsec_01',ped_weapon = 'WEAPON_ASSAULTRIFLE',ped_armour = 100, ped_accuracy = 100 },	
}



Languages = {	
["afterblast_bomb"] = "A Submarine will be in a blue container go and plant the bomb in it",
["signalcame_msg"] = "Signals are jamming the controller's signal again. Let the controller know to crack it.",
["droner"] = "Look for the GPS location on the map and head over there.",
["hacker"] = "Head to the controller room and wait for the droner and diver to wrap up their tasks.",
["bomber"] = "Be prepared to neutralize the armed shooters on the ship before planting the bomb",
["diver"] = "Wait for the ship to explode, then proceed to plant a C4 in the container.",
["telescope_exit"] = "Exit Telescope",
["deli_msg"] = "Assist the controller in navigating the submarine",
["jammers_sidabled"] = "Now that all the jammers are disabled, make your way to the drone control room.",
["oxytimer_on"] = "Oxygen tank timer started",
["oxytimer_off"] = "Oxygen tank timer stopped",
["start_heist"] = "Press ~INPUT_PICKUP~ to Start heist",
["signal_cleared"] = "Signal Cleared.",
["complete_bomb"] = "All explosives have been set. ~b~Notify the Controller to initiate detonation.",
["not_planted"] = "No bombs planted",
["start_character"] = "Let's proceed with selecting your role.",
["no_c4"] = "No c4",
["no_drone"] = "No Drone",
["sell_subcars"] = "Press ~INPUT_PICKUP~ to Sell Subcard",
["inspect_drone"] = "Press ~INPUT_PICKUP~ to Inspect Drone",
["succes_deliver"] = "The Submarine has been successfully delivered. ~b~Proceed to the Dealer to claim your reward.",
["inspectdrone"] = "Inspect the drone now.",
["go_lay"] = "Press ~INPUT_PICKUP~ to See the Plan",
["place_drone"] = "Press ~INPUT_PICKUP~ to place Drone",
["plant_bomb"] = "Press ~INPUT_PICKUP~ to plant Bomb",
["finish_fuse"] = "It appears there are jammers in the vicinity, ~b~Disable them to get your Drone flying.",
["telescope"] = "Press ~INPUT_PICKUP~ to Scout",
["start_msg_sh"] = 'You have been assigned a Job. Proceed to the designated location to respond to the call.',
["scout"] = "Survey the ship and make your way to the ~b~planning room.",
["failed"] = "Mission Failed",
["phone_msg"] = "Hey, got a job for you and your crew. Round 'em up and head over to the marked spot.",
["talk"] = "Press ~INPUT_CONTEXT~ to Talk",
["select_character"] = "Press ~INPUT_CONTEXT~ to ~b~Select character",
["hack"] = "Press ~INPUT_CONTEXT~ to Hack",
["control"] = "Press ~INPUT_CONTEXT~ to ~b~Control submarine",
["take"] = "Press ~INPUT_CONTEXT~ to Take",
["inspect"] = "Press ~INPUT_CONTEXT~ to inspect",
["sit"] = "Press ~INPUT_CONTEXT~ to sit",
["drone_finish"] = "Alright, all tasks are completed. Now, ~b~let's wait for the controller to take over.",
["crack_signal"] = "Press ~INPUT_CONTEXT~ to Crack signal",
["control_submarine"] = "Press ~INPUT_CONTEXT~ to Control submarine",
["use_card"] = "Press ~INPUT_CONTEXT~ to Use Card",
["get_money"] = "Press ~INPUT_CONTEXT~ to Get your Reward.",
["recent_robbed"] = "The task has recently been initiated by someone else. Please try again later.",
["no_police"] = "Not enough cops to start.",
["no_card"] = "Missing card",
["no_bag"] = "Missing Bag",
["person_nearby"] = "Person Nearby",
["police_alert"] = "Suspicious activity in Cargo Ship",
['received'] = ' You Received ',
['success_reseted'] = 'Submarine Heist is reset now',
['not_admin'] = 'Not an admin',
['command_enabled'] = 'Command not enabled',
['blip_name'] = 'Suspicious Activity',
['auc_failed'] = 'Submarine Heist Failed',
['auc_start'] = 'Submarine will be starting soon',
['bomb_attached'] = 'C4 Attached',
['no_c4'] = 'No C4 ',
['not_enough'] = 'Not Enough Items',
['ship_blasttime'] = 'Explosion',
['control_sub'] = 'Use Your ~r~Arrow Keys to control',	
}

Config.nearbydsistance = 1.5

Config.Discordlogs = true
Config.WebhookURL_Reward = ""
Config.WebhookURL_Start = ""
Config.Logoutwebhook = ""

Config.frameworkCallback = function(event, callback, ...)
    local framework = Config.submarineheist.Framework

    if framework == 'ESX' then
        ESX.TriggerServerCallback(event, callback, ...)
    elseif framework == 'QB' then
        QBCore.Functions.TriggerCallback(event, callback, ...)
    elseif framework == 'CUSTOM' then
        -- Handle your custom framework here	
    end
end
		
