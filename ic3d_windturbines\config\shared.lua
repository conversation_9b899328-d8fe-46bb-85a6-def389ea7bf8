Config = {}

Config.RewardMin = 18000

Config.RewardMax = 29000

Config.Cooldown = 60000 -- msec

Config.skillDifficulty = {{ areaSize = 50, speedMultiplier = 5.0 }, { areaSize = 75, speedMultiplier = 4.0 }, { areaSize = 90, speedMultiplier = 8.0 }} -- easy: { areaSize: 50, speedMultiplier: 1 } ; medium: { areaSize: 40, speedMultiplier: 1.5 } ; hard: { areaSize: 25, speedMultiplier: 1.75 } // https://overextended.dev/ox_lib/Modules/Interface/Client/skillcheck

Config.Station = {
    {
    pedcoords = vec4(2086.5811, 2340.8572, 93.2704, 89.3918),
    zone = vec3(2086.5811, 2340.8572, 94.1704),  
    model = 's_m_y_dockwork_01',
    carModel = 'caddy3',
    spawnPoint = vec3(2082.9341, 2340.8667, 94.2745),
    deletePoint = vec3(2082.6936, 2346.5425, 94.2409),
    heading = 181.8469
    }
}

Config.Uniforms = {
	
		male = {
			['tshirt_1'] = 59,  ['tshirt_2'] = 0,
            ['torso_1'] = 273,   ['torso_2'] = 0,
            ['decals_1'] = 0,   ['decals_2'] = 0,
            ['arms'] = 19,
            ['pants_1'] = 36,   ['pants_2'] = 0,
            ['shoes_1'] = 63,   ['shoes_2'] = 0,
            ['helmet_1'] = -1,  ['helmet_2'] = 0,
            ['chain_1'] = 0,    ['chain_2'] = 0,
            ['ears_1'] = 0,     ['ears_2'] = 0
		},
		female = {
			['tshirt_1'] = 10,  ['tshirt_2'] = 0,
			['torso_1'] = 48,   ['torso_2'] = 0,
			['decals_1'] = 0,   ['decals_2'] = 0,
			['arms'] = 31,
			['pants_1'] = 32,   ['pants_2'] = 0,
			['shoes_1'] = 24,   ['shoes_2'] = 0,
			['helmet_1'] = 10,  ['helmet_2'] = 2,
			['chain_1'] = 0,    ['chain_2'] = 0,
			['ears_1'] = 2,     ['ears_2'] = 0
		}
}	

Config.Points = {
    {
		coords = vec3(2066.1423, 2273.4048, 93.3196),
		size = vec3(1, 9.0, 2),
		rotation = 70
    },
    {
        coords = vec3(2068.5488, 2355.3113, 97.0026),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },
    {
        coords = vec3(2116.3286, 2397.8347, 100.7456),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },
    {
        coords = vec3(2154.8438, 2332.8538, 109.9009),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },
    {
        coords = vec3(2096.4668, 2492.6992, 90.8040),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },
    {
        coords = vec3(2200.3223, 2486.4607, 88.3869),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },
    {
        coords = vec3(2165.7478, 2263.1721, 106.5850),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },
    {
        coords = vec3(2123.2834, 2233.9946, 106.0293),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(1974.4879, 2267.2236, 93.2927),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(1988.3101, 2197.6658, 105.0777),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2178.6978, 2165.7896, 117.3075),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2090.3328, 2151.7520, 110.2219),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2043.8793, 2117.1782, 93.4970),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2236.4963, 2043.2474, 130.8249),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2285.7500, 2074.9841, 122.8875),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2329.3779, 2050.7292, 103.9179),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2329.4956, 2114.7366, 108.2882),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2368.9319, 2183.6968, 102.9797),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2346.3384, 2233.4939, 99.3168),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2361.8101, 2287.8477, 94.2513),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2270.5535, 1991.7990, 132.1641),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2308.8364, 1968.7311, 131.3314),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2396.7979, 2032.1171, 91.3390),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2424.7871, 1989.1907, 84.5958),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2270.8315, 1991.9525, 132.1665),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2308.9219, 1968.7006, 131.3314),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2395.2900, 1270.3823, 62.1850),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2404.8430, 1421.4541, 46.5369),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2359.8184, 1391.5892, 58.7665),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2317.4429, 1328.6832, 69.7963),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2280.7698, 1408.5582, 74.9855),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2206.9380, 1399.8167, 81.5356),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2200.3044, 1491.3165, 82.6622),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2239.4534, 1529.1395, 74.5296),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2203.1494, 1648.2155, 83.6910),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2078.3989, 1687.1184, 103.1158),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2320.7913, 1448.0229, 63.2399),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2359.0549, 1508.0959, 54.3235),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2318.6487, 1608.6819, 57.9446),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2359.8423, 1669.2039, 48.6798),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2124.0261, 1747.9531, 103.2983),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2026.8354, 1839.0095, 95.7494),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2059.7400, 1892.1068, 92.8641),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2119.9258, 1869.2524, 94.7381),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2191.6570, 1872.2041, 102.2342),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2168.9224, 1932.0671, 98.6853),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2132.5054, 1987.1324, 96.1573),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2052.5752, 1998.3809, 86.2960),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2000.7041, 1928.0995, 92.4409),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(1959.9026, 2068.2961, 84.7337),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2191.4226, 2095.0701, 127.6619),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2184.3589, 1786.5876, 107.5410),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },
   {
        coords = vec3(2239.7761, 1828.2434, 109.1337),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2265.2224, 1914.1709, 123.2953),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },
   {
        coords = vec3(2301.2170, 1853.1594, 106.9971),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


    {
        coords = vec3(2355.5840, 1836.9011, 102.3368),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },

    {
        coords = vec3(2280.0200, 1567.9915, 65.8086),
        size = vec3(1, 9.0, 2),
        rotation = 70
    },


}