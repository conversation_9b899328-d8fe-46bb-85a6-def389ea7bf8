if Config.Framework ~= "ESX" then
    return
end

Citizen.CreateThreadNow(function ()
    if Config.Framework == "ESX" then
        while ESX == nil do
            ESX = exports["es_extended"]:getSharedObject()
        end
        while ESX.GetPlayerData().job == nil do
            Wait(10)
        end
    end
    PlayerData = ESX.GetPlayerData()
    PlayerJob = PlayerData.job
    if Config.RequireJob then
        if Config.MinerJob[PlayerData.job.name] then
            CanInteract = true
        else
            CanInteract = false
        end
    else
        CanInteract = true
    end
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
    ESX.PlayerLoaded = true
    PlayerLoaded = true
    for _, object in pairs(AllObjects) do
        if object then
            DeleteEntity(object)
        end
    end
    if Peds.minerPed and Peds.sellerPed and Peds.questPed and Peds.leaderboardPed then
        DeleteEntity(Peds.minerPed)
        DeleteEntity(Peds.sellerPed)
        DeleteEntity(Peds.questPed)
        DeleteEntity(Peds.leaderboardPed)
    end
    Wait(2000)
    -- CreateStones()
    CreateObjects()
    LoadBlips()
    -- LoadBlips()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
    PlayerData.job = job
    PlayerJob = job
    for _, blip in pairs(CreatedBlips) do
        RemoveBlip(blip)
    end
    if Config.RequireJob then
        if Config.MinerJob[PlayerData.job.name] then
            CanInteract = true
        else
            CanInteract = false
        end
    else
        CanInteract = true
    end
    UpdateTargetOptions()
    LoadedBlips = nil
    LoadBlips()
end)

function TriggerServerCallback(name, cb, ...)
    ESX.TriggerServerCallback(name, cb, ...)
end