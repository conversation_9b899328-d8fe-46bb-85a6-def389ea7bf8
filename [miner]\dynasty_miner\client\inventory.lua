function GetPickaxeFromInventory()
    local item, pickaxeType, currentDurability, maxDurability
    if Config.InventoryPickaxeScript == "ox" then
        item = exports.ox_inventory:GetSlotWithItem(Player.pickaxeType, { type = Player.pickaxeID }, false)
        if not item then
            return
        end
        currentDurability = item.metadata.durability
        pickaxeType = item.name
        maxDurability = Config.PickaxeLevel[pickaxeType].durability
        item = item.slot
        return item, currentDurability, maxDurability
    end
end