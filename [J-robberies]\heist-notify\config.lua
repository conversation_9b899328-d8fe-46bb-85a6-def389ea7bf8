Config = {}

-- HMS Configuration
Config.HMS = {
    Command = 'hms',
    RequiredJob = nil, -- Set to 'police' to restrict to police only, or nil for everyone
    
    -- Heist Resources Configuration
    -- This dynamically loads data from actual heist resources
    Heists = {
        ['atmrobbery'] = {
            resourceName = 'zcrh_atm',
            enabled = true,
            name = 'ATM Robbery',
            icon = '🏧',
            location = 'City-wide ATMs',
            description = 'Quick cash grab from ATM machines',
            riskLevel = 'Medium',
            experience = 'High',
            
            -- Data fetched from zcrh_atm/config.lua
            requiredCops = 1, -- Config.RequiredCopsCount
            cooldownMinutes = 0, -- No cooldown for ATM robbery
            requiredItems = {'hack_usb'}, -- Config.RequiredItem
            rewardType = 'cash', -- This heist gives direct cash
            rewardMin = 40000, -- Config.MinWithdrawl
            rewardMax = 100000, -- Config.MaxWithdrawal
            
            coords = nil, -- No specific location - city-wide
            
            instructions = {
                'Find any ATM machine around the city',
                'Ensure you have a Trojan USB item',
                'Use qtarget to interact with the ATM',
                'Select "Rob ATM" option',
                'Complete the hacking minigame within 120 seconds',
                'Collect the money and escape police'
            }
        },
        
        ['shopheist'] = {
            resourceName = 'zcrh_shop',
            enabled = true,
            name = 'Shop Robbery',
            icon = '🏪',
            location = 'Various 24/7 Stores',
            description = 'Rob convenience stores for cash and valuables',
            riskLevel = 'Medium',
            experience = 'Medium',
            
            -- Data fetched from zcrh_shop/config.lua
            requiredCops = 3, -- Config.RequiredCopsCount
            cooldownMinutes = 10, -- Config.Cooldown
            requiredItems = {'drill'}, -- Config.DrillItem.item
            rewardType = 'mixed', -- This heist gives both cash and items
            rewardItems = {
                {name = 'Cash Register', count = '$10,000-$20,000', value = 'Direct cash'},
                {name = 'Safe Money', count = '$280,000-$320,000', value = 'Direct cash'},
                {name = 'Gold', count = '75-100x', value = '$1,000 each'},
                {name = 'Diamond', count = '50-75x', value = '$2,000 each'}
            },
            rewardMin = 290000, -- Minimum from cash register + safe
            rewardMax = 490000, -- Maximum from all sources
            
            coords = {x = 24.0687, y = -1346.2, z = 28.4970}, -- First shop location
            
            instructions = {
                'Go to any 24/7 store around the city',
                'Ensure you have a Drill item',
                'Point your weapon at the shopkeeper to start robbery',
                'Rob the cash registers by shooting them',
                'Wait 120 seconds then drill the safe',
                'Collect items and money, then escape'
            }
        },
        
        ['carthief'] = {
            resourceName = 'zcrh_carthief',
            enabled = true,
            name = 'Car Theft',
            icon = '🚗',
            location = 'Sandy Shores Gang Boss',
            description = 'Steal high-value vehicles for the gang',
            riskLevel = 'Low',
            experience = 'Medium',
            
            -- Data fetched from zcrh_carthief/shared.lua
            requiredCops = 3, -- Config.MinPolice
            cooldownMinutes = 0.17, -- 10 seconds = 0.17 minutes (very short cooldown)
            requiredItems = {'hacker_device'}, -- Config.RequireItem
            rewardType = 'cash', -- This heist gives black money
            rewardMin = 18500, -- Config.money.min
            rewardMax = 28750, -- Config.money.max
            
            coords = {x = 765.9634, y = -3191.4658, z = 6.0383}, -- Config.NPC.locations[1]
            
            instructions = {
                'Go to Sandy Shores gang boss location',
                'Ensure you have a Hacker Device item',
                'Talk to the boss and accept car theft mission',
                'Follow GPS to find the target vehicle',
                'Steal the specified car model',
                'Remove the GPS tracker after 120 seconds',
                'Deliver the car to the drop-off location'
            }
        },
        
        ['truckrobbery'] = {
            resourceName = 'zcrh_truck',
            enabled = true,
            name = 'Truck Robbery',
            icon = '🚛',
            location = 'Highway Security Trucks',
            description = 'Intercept armored security trucks',
            riskLevel = 'Medium',
            experience = 'High',

            -- Data fetched from zcrh_truck/config.lua
            requiredCops = 4, -- Config.RequiredCopsCount
            cooldownMinutes = 30, -- Config.Cooldown
            requiredItems = {}, -- No specific items required
            rewardType = 'items', -- This heist gives items, not cash
            rewardItems = {
                {name = 'Gold', count = '35-75x', value = '$1,000 each'},
                {name = 'Diamond', count = '35-75x', value = '$2,000 each'}
            },
            rewardMin = 0, -- No direct cash reward
            rewardMax = 0, -- No direct cash reward

            coords = {x = 450.5392, y = -1492.0687, z = 28.2920}, -- Config.BossCoords

            instructions = {
                'Go to the truck robbery boss location',
                'Talk to the boss to get truck information',
                'Follow GPS to the enemy location',
                'Eliminate 10 armed enemies',
                'Find and intercept the security truck',
                'Use explosives to blow open the truck doors',
                'Collect gold and diamonds from the truck',
                'Escape before police backup arrives'
            }
        },
        
        ['drugheist'] = {
            resourceName = 'zcrh_drugheists',
            enabled = true,
            name = 'Drug Heist',
            icon = '💊',
            location = 'Raven Slaughterhouse',
            description = 'Raid drug manufacturing facilities',
            riskLevel = 'High',
            experience = 'Low',

            -- Data fetched from zcrh_drugheists/config.lua
            requiredCops = 5, -- Config.PoliceNumberRequired
            cooldownMinutes = 60, -- Config.TimerBeforeNewRob / 60
            requiredItems = {}, -- No specific items required
            rewardType = 'items', -- This heist gives items, not cash
            rewardItems = {
                {name = 'Inside Cocaine', count = '160-200x', value = 'Sellable item'}
            },
            rewardMin = 0, -- No direct cash reward
            rewardMax = 0, -- No direct cash reward

            coords = {x = 974.4865, y = -2167.2034, z = 29.4614}, -- Stores['raven_slaughterhouse'].position

            instructions = {
                'Go to the Raven Slaughterhouse facility',
                'Enter the drug manufacturing area',
                'Eliminate any security guards present',
                'Stay within 30 meters of the robbery area',
                'Wait for the 180-second robbery timer',
                'Collect cocaine and other drugs',
                'Escape before police arrive'
            }
        },
        
        ['vangelico'] = {
            resourceName = 'zcrh_vangelico',
            enabled = true,
            name = 'Vangelico Jewelry',
            icon = '💎',
            location = 'Vangelico Jewelry Store',
            description = 'High-stakes jewelry store heist',
            riskLevel = 'High',
            experience = 'Low',

            -- Data fetched from zcrh_vangelico/config.lua
            requiredCops = 6, -- Config.RequiredCopsRob
            cooldownMinutes = 35, -- Config.SecBetwNextRob / 60
            requiredItems = {}, -- No specific items required
            rewardType = 'items', -- This heist gives items, not cash
            rewardItems = {
                {name = 'Jewels', count = '10-14x per window', value = 'Sellable item'}
            },
            rewardMin = 0, -- No direct cash reward
            rewardMax = 0, -- No direct cash reward

            coords = {x = -629.99, y = -236.542, z = 38.05}, -- Stores["jewelry"].position

            instructions = {
                'Go to Vangelico jewelry store in Rockford Hills',
                'Break the front windows to enter the store',
                'Smash 10-14 jewelry display cases',
                'Collect diamonds and precious stones',
                'Avoid or eliminate security response',
                'Escape before heavy police backup arrives'
            }
        },
        
        ['oilrig'] = {
            resourceName = 'zcrh_oilrig',
            enabled = true,
            name = 'Oil Rig Heist',
            icon = '🛢️',
            location = 'Offshore Oil Rig',
            description = 'Ultimate high-risk offshore heist',
            riskLevel = 'High',
            experience = 'High',

            -- Data fetched from zcrh_oilrig/settings.lua
            requiredCops = 5, -- Settings.MinimalPolice
            cooldownMinutes = 30, -- Estimated based on complexity
            requiredItems = {}, -- No specific items required
            rewardType = 'items', -- This heist gives items, not cash
            rewardItems = {
                {name = 'Diamond', count = '1-3x', value = '$50,000 each'},
                {name = 'Gold', count = '270-370x', value = '$1,000 each'}
            },
            rewardMin = 0, -- No direct cash reward
            rewardMax = 0, -- No direct cash reward

            coords = {x = -1602.0609, y = 5203.8730, z = 3.3101}, -- Settings.BossSettings.bossCoords

            instructions = {
                'Go to Sandy Shores gang boss location',
                'Talk to the boss and accept the oil rig mission',
                'Travel to the offshore oil rig platform',
                'Eliminate 10 heavily armed security guards',
                'Complete the 300-second robbery timer',
                'Rob the main safe in the rig office',
                'Collect diamonds and gold bars',
                'Escape before military backup arrives'
            }
        },

        ['yachtheist'] = {
            resourceName = 'rm_yachtheist',
            enabled = true,
            name = 'Yacht Heist',
            icon = '🛥️',
            location = 'Luxury Yacht',
            description = 'Elite yacht robbery with high-value targets',
            riskLevel = 'Medium',
            experience = 'Medium',

            -- Data fetched from rm_yachtheist/config.lua
            requiredCops = 3, -- Config['YachtHeist']['requiredPoliceCount']
            cooldownMinutes = 120, -- Config['YachtHeist']['nextRob'] / 60 (7200 seconds = 120 minutes)
            requiredItems = {'bag'}, -- Config['YachtHeist']['requiredItems']
            rewardType = 'mixed', -- This heist gives both money and items
            rewardItems = {
                {name = 'Money Stacks', count = '1-5 stacks', value = '$250,000-$350,000 per stack'},
                {name = 'Gold', count = '75x', value = '$100 each'},
                {name = 'Cocaine Pouches', count = '75x', value = '$100 each'},
                {name = 'Weed Pouches', count = '75x', value = '$100 each'},
                {name = 'Artifacts', count = '1-10 items', value = '$100 each'}
            },
            rewardMin = 250000, -- Minimum from 1 money stack
            rewardMax = 2000000, -- Maximum from 5 stacks + all items

            coords = {x = -1612.8820, y = -1028.5046, z = 13.1531}, -- Config['YachtHeist']['startHeist']['pos']

            instructions = {
                'Go to the heist contact in Del Perro Pier',
                'Ensure you have a Bag item',
                'Talk to the contact and start the yacht heist',
                'Travel to the luxury yacht location',
                'Eliminate 30+ heavily armed yacht security',
                'Collect money stacks from tables (5 locations)',
                'Steal valuable artifacts (9 different items)',
                'Grab gold, cocaine, and weed pouches',
                'Deliver all loot to the buyer for final payment'
            }
        },

        ['submarineheist'] = {
            resourceName = 'sb_sub_heist',
            enabled = true,
            name = 'Submarine Heist',
            icon = '🚢',
            location = 'Cargo Ship & Submarine',
            description = 'Multi-stage underwater heist operation',
            riskLevel = 'High',
            experience = 'High',

            -- Data fetched from sb_sub_heist/config.lua
            requiredCops = 0, -- Config.police.Count
            cooldownMinutes = 60, -- Config.submarineheist.Cooldown
            requiredItems = {'subcard', 'detonator', 'drone_sh', 'c4', 'oxy_mask_sh'}, -- Config.submarineheistitems
            rewardType = 'items', -- This heist gives items that can be sold
            rewardItems = {
                {name = 'Subcard', count = '1x', value = '$5,000 each'}
            },
            rewardMin = 5000, -- Minimum from subcard sale
            rewardMax = 5000, -- Maximum from subcard sale

            coords = {x = -1657.4, y = -982.56, z = 8.16}, -- Config.submarineheist.startheist.pos

            instructions = {
                'Go to the heist contact at the docks',
                'Ensure you have: Subcard, Detonator, Drone, C4 (6x), Oxygen Mask',
                'Select your role: Droner, Hacker, Bomber, Shooter, or Diver',
                'Use telescope to scout the cargo ship',
                'Droner: Disable signal jammers with the drone',
                'Hacker: Control the submarine navigation system',
                'Bomber: Plant C4 explosives on the ship (6 locations)',
                'Shooter: Eliminate armed security guards',
                'Diver: Use oxygen mask and plant bomb in submarine container',
                'Deliver the submarine to the buyer for final payment'
            }
        }
    }
}

-- Export configuration for other resources
function GetHeistConfig(heistType)
    return Config.HMS.Heists[heistType]
end

function GetAllHeistConfigs()
    return Config.HMS.Heists
end
