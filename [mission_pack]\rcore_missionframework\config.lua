Config = {}

Config.Database = 1 --[ 1 = OxMySQL / 2 = MySQL-Async / 3 = GhMattiMySQL ] Choose your database
Config.Framework = 1 --[ 1 = ESX / 2 = QBCore / 3 = Other ] Choose your framework
Config.SocietySystem = 1 --[ 1 = esx_addonaccount / 2 = qb-bossmenu / 3 = qb-management ]
-- Leave empty if you don't have renamed or custom events
Config.FrameworkTriggers = {
    load = '',
    notify = '',
    object = '',
    resourceName = ''
}

Config.Debug = false

-- All core framework labels
Config.Labels = {
    ['MISSION'] = 'Mission',
    ['MISSION_START'] = 'Start mission',
    ['MISSION_LEAVE'] = 'Leave mission',
    ['MISSION_CANCEL'] = 'Cancel mission',
    ['MISSION_INACTIVE'] = 'You are not on any active mission.',
    ['MISSION_JOB_ERROR'] = 'Unable to start the mission, one of the selected doesn\'t have a job.',
    ['MISSION_PLAYER_ERROR'] = 'Unable to start the mission, one of the selected is already on another mission.',
    ['REFRESH_PLAYERS'] = 'Refresh nearby players',

    ['ENTER_TELEPORT'] = '~INPUT_PICKUP~ Enter',

    ['CARRYABLE_PICKUP'] = '~INPUT_PICKUP~ Pick up',
    ['CARRYABLE_DROP'] = '~INPUT_PICKUP~ Drop',
    ['CARRYABLE_PICKUP_TRUNK'] = '~INPUT_PICKUP~ Take out from trunk',
    ['CARRYABLE_DROP_TRUNK'] = '~INPUT_PICKUP~ Put in the trunk',
    ['CARRYABLE_PICKUP_PLAYER'] = '~INPUT_PICKUP~ Take',
    ['CARRYABLE_DROP_PLAYER'] = '~INPUT_PICKUP~ Deliver',
    ['CARRYABLE_PICKUP_GROUND'] = '~INPUT_PICKUP~ Take from ground',
    ['CARRYABLE_DROP_GROUND'] = '~INPUT_PICKUP~ Put on the ground',

    ['TOW_LOAD'] = '~INPUT_PICKUP~ Load vehicle~n~~INPUT_SPRINT~ + ~INPUT_PICKUP~ Stop loading vehicle',
    ['TOW_START'] = '~INPUT_PICKUP~ Start loading vehicle',
    ['TOW_UNLOAD'] = '~INPUT_PICKUP~ Unload vehicle'
}

-- Colors of each drawable including alpha
Config.ArrowColor = { 239, 199, 80, 100 }
Config.MarkerColor = { 239, 199, 80, 200 }
Config.CheckpointColor = { 239, 199, 80, 200 }

-- All vehicle colors with labels assigned
Config.VehicleColors = {
    [0] = 'Black',
    [1] = 'Black',
    [2] = 'Steel',
    [3] = 'Silver',
    [4] = 'Silver',
    [5] = 'Silver',
    [6] = 'Gray',
    [7] = 'Silver',
    [8] = 'Silver',
    [9] = 'Silver',
    [10] = 'Metal',
    [11] = 'Grey',
    [12] = 'Black',
    [13] = 'Gray',
    [14] = 'Grey',
    [15] = 'Black',
    [16] = 'Poly',
    [17] = 'Silver',
    [18] = 'Silver',
    [19] = 'Metal',
    [20] = 'Silver',
    [21] = 'Black',
    [22] = 'Graphite',
    [23] = 'Grey',
    [24] = 'Silver',
    [25] = 'Silver',
    [26] = 'Silver',
    [27] = 'Red',
    [28] = 'Red',
    [29] = 'Red',
    [30] = 'Red',
    [31] = 'Red',
    [32] = 'Red',
    [33] = 'Red',
    [34] = 'Red',
    [35] = 'Red',
    [36] = 'Orange',
    [37] = 'Gold',
    [38] = 'Orange',
    [39] = 'Red',
    [40] = 'Red',
    [41] = 'Orange',
    [42] = 'Yellow',
    [43] = 'Red',
    [44] = 'Red',
    [45] = 'Red',
    [46] = 'Red',
    [47] = 'Red',
    [48] = 'Red',
    [49] = 'Green',
    [50] = 'Green',
    [51] = 'Green',
    [52] = 'Green',
    [53] = 'Green',
    [54] = 'Green',
    [55] = 'Green',
    [56] = 'Green',
    [57] = 'Green',
    [58] = 'Green',
    [59] = 'Green',
    [60] = 'Green',
    [61] = 'Blue',
    [62] = 'Blue',
    [63] = 'Blue',
    [64] = 'Blue',
    [65] = 'Blue',
    [66] = 'Blue',
    [67] = 'Blue',
    [68] = 'Blue',
    [69] = 'Blue',
    [70] = 'Blue',
    [71] = 'Blue',
    [72] = 'Blue',
    [73] = 'Blue',
    [74] = 'Blue',
    [75] = 'Blue',
    [76] = 'Blue',
    [77] = 'Blue',
    [78] = 'Blue',
    [79] = 'Blue',
    [80] = 'Blue',
    [81] = 'Blue',
    [82] = 'Blue',
    [83] = 'Blue',
    [84] = 'Blue',
    [85] = 'Blue',
    [86] = 'Blue',
    [87] = 'Blue',
    [88] = 'Yellow',
    [89] = 'Yellow',
    [90] = 'Bronze',
    [91] = 'Yellow',
    [92] = 'Lime',
    [93] = 'Brown',
    [94] = 'Brown',
    [95] = 'Brown',
    [96] = 'Brown',
    [97] = 'Brown',
    [98] = 'Brown',
    [99] = 'Brown',
    [100] = 'Brown',
    [101] = 'Brown',
    [102] = 'Beechwood',
    [103] = 'Beechwood',
    [104] = 'Orange',
    [105] = 'Sand',
    [106] = 'Sand',
    [107] = 'Cream',
    [108] = 'Brown',
    [109] = 'Brown',
    [110] = 'Brown',
    [111] = 'White',
    [112] = 'White',
    [113] = 'White',
    [114] = 'Brown',
    [115] = 'Brown',
    [116] = 'Brown',
    [117] = 'Steel',
    [118] = 'Steel',
    [119] = 'Aluminium',
    [120] = 'Chrome',
    [121] = 'White',
    [122] = 'White',
    [123] = 'Orange',
    [124] = 'Orange',
    [125] = 'Green',
    [126] = 'Yellow',
    [127] = 'Blue',
    [128] = 'Green',
    [129] = 'Brown',
    [130] = 'Orange',
    [131] = 'White',
    [132] = 'White',
    [133] = 'Green',
    [134] = 'White',
    [135] = 'Pink',
    [136] = 'Pink',
    [137] = 'Pink',
    [138] = 'Orange',
    [139] = 'Green',
    [140] = 'Blue',
    [141] = 'Blue',
    [142] = 'Purple',
    [143] = 'Red',
    [144] = 'Green',
    [145] = 'Purple',
    [146] = 'Blue',
    [147] = 'Black',
    [148] = 'Purple',
    [149] = 'Purple',
    [150] = 'Red',
    [151] = 'Green',
    [152] = 'Green',
    [153] = 'Brown',
    [154] = 'Tan',
    [155] = 'Green',
    [156] = 'Alloy',
    [157] = 'Blue',
    [158] = 'Gold',
    [159] = 'Gold'
}