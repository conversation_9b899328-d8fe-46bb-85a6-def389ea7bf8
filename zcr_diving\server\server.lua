Core = nil

Citizen.CreateThread(function()
    Core = GetCore()
end)


AddEventHandler('onResourceStop', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then
        return
      end
    for i = 1, 256 do
        local identifier = GetIdentifier(tonumber(i))
        if identifier then
            local lobby = coopData[identifier]
            if lobby then
                for ii = 1, #lobby.roomSetting.ObjectData do
                    local objectData = lobby.roomSetting.ObjectData[ii]
                    if objectData then
                        local objectId = tonumber(objectData)
                        local object = NetworkGetEntityFromNetworkId(objectId)
                        if DoesEntityExist(object) then 
                            DeleteEntity(object)
                        end
                        lobby.roomSetting.ObjectData[ii] = nil -- Clearing the object reference
                    end
                end
                for ii = 1, #lobby.roomSetting.Vehicle do
                    local vehicle = lobby.roomSetting.Vehicle[ii]
                    if DoesEntityExist(vehicle) then 
                        DeleteEntity(vehicle)
                    end
                    lobby.roomSetting.Vehicle[ii] = nil -- Clearing the vehicle reference
                end


                for _, player in ipairs(lobby.players) do
                    TriggerClientEvent('zcr_diving:client:TakeLooby',  tonumber(player["src"]))
                end
                coopData[identifier] = nil
                JoobTask[identifier] = nil
            end
        end
    end

    -- for k,v in pairs(JoobTask) do
    --     JoobTask[k] = nil
    -- end

    -- for k,v in pairs(playerJobData) do
    --     playerJobData[k] = nil
    -- end

    -- for k,v in pairs(JoobTask) do
    --     JoobTask[k] = nil
    -- end
    -- syncedCounts = {}
end)




Citizen.CreateThread(function()
    local result = ExecuteSql("SELECT * FROM `diving_db`")
    for i = 1, #result do
        local playerData = result[i]
        local dataInfo = {
            profiledata = json.decode(playerData.profiledata),
        }
        if not playerJobData[playerData.identifier] then
            playerJobData[playerData.identifier] = dataInfo
        end
        playerJobData[playerData.identifier] = dataInfo
    end
end)

cooldowns = {}
coopData = {}
playerJobData = {}
JoobTask = {}
DATAA = {}

RegisterServerEvent('zcr_diving:loadData', function()
    local src = source
    loadDivingData(src)
end)
function loadDivingData(src)
    local identifier = GetIdentifier(src)
    local data = playerJobData[identifier]
    if not data then
        firsData(src, function()
            data = playerJobData[identifier]
        end)
    end
    playerJobData[identifier] = data
    playerJobData[identifier].src = src or source
    playerJobData[identifier].profiledata.avatar = GetDiscordAvatar(src) or Config.ExampleProfilePicture
    playerJobData[identifier].profiledata.name = GetName(src)
    playerJobData[identifier].profiledata.identifier = identifier
    Citizen.Wait(100)
    savePlayerData(src)
end

function firsData(src, callback)
    local identifier = GetIdentifier(src)

    if playerJobData[identifier] then
        return
    end

    local dataInfo = {
        identifier = identifier,
        profiledata = {
            ["xp"] = 0,
            ["level"] = 1,
            ['avatar'] = GetDiscordAvatar(src) or Config.ExampleProfilePicture,
            ['name'] = GetName(src),
            ['identifier'] = identifier,
        },
    }
    Citizen.Wait(100)
    playerJobData[identifier] = dataInfo
    ExecuteSql(
        'INSERT INTO diving_db (identifier, profiledata) VALUES (:identifier, :profiledata) ',
        {
            identifier = identifier,
            profiledata = json.encode(dataInfo.profiledata),
        }
    )
    callback()
end

function savePlayerData(src)
    local src = src
    local identifier = GetIdentifier(src)
    local data = playerJobData[identifier]
    if not data then
        return
    end
    ExecuteSql(
        'UPDATE diving_db SET profiledata = :profiledata WHERE identifier = :identifier',
        {
            identifier = identifier,
            profiledata = json.encode(data.profiledata)
        }
    )
end

Citizen.CreateThread(function()
    while Core == nil do
        Citizen.Wait(0)
    end
    RegisterCallback('zcr_diving:server:getPlayerData', function(source, cb)
        local src = source
        local identifier = GetIdentifier(src)
        local data = playerJobData[identifier]
        local dataInfo = {
            bank = GetPlayerMoney(src, 'bank'),
            name = data.profiledata.name,
            identifier = data.profiledata.identifier,
            image = data.profiledata.avatar or Config.ExampleProfilePicture,
            level = data.profiledata.level,
            xp = data.profiledata.xp,
            nextxp = Config.RequiredXP[data.profiledata.level +1] or 0,
            src = src
        }
        cb(dataInfo)
    end)

    RegisterCallback('zcr_diving:server:getVehicleData', function(source, cb, data)
        local src = source
        local hostidentifier = data
        local lobby = coopData[hostidentifier]
        if not lobby then return end
        local dataInfo = {
            Vehicle = lobby.roomSetting.Vehicle or {}
        }
        callbackNETID = NetworkGetNetworkIdFromEntity(dataInfo.Vehicle[1])
        cb(callbackNETID)
    end)

    RegisterCallback('zcr_diving:server:getVehicleAnchor', function(source, cb, data)
        local src = source
        local hostidentifier = data
        local lobby = coopData[hostidentifier]
        local currentanchor = lobby.roomSetting.anchor
        cb(tostring(currentanchor))
    end)

    RegisterCallback('zcr_diving:server:CreatePlayerLobby', function(source, cb)
        local src = source
        local identifier = GetIdentifier(src)
        local data = playerJobData[identifier]
        if not data then
            return
        end
        if not coopData[identifier] then
            coopData[identifier] = {
                roomSetting = {
                    name = "Diving",
                    owneridentifier = identifier,
                    ownersrc = src,
                    startJob = false,
                    finishJob = false,
                    changed = false,
                    carDelivered = false,
                    Area  = false,
                    Mission = "NOSELECT",
                    ObjectData = {},
                    awards = {
                        money = 0,
                        xp = 0
                    },
                    Vehicle = {},
                    seatindex = -1,

                },
                players = {
                    {
                        name = GetName(src),
                        identifier = identifier,
                        image = GetDiscordAvatar(src) or Config.ExampleProfilePicture,
                        level = data.profiledata.level,
                        owner = true,
                        src = src
                    }
                }
            }
        end
        cb(coopData[identifier].players)
    end)
    RegisterCallback('zcr_diving:server:getSellMarketData', function(source, cb)
        local src = source
        local sellMarketData = Config.Diving['Market']["SellItems"]
        local PlayerInventory = GetPlayerInventory(src)
        
        local data = {}
    
        local inventoryItems = {}
        for _, item in ipairs(PlayerInventory) do
            inventoryItems[item.name] = item.amount 
        end

        for _, marketItem in ipairs(sellMarketData) do
            local itemName = marketItem.itemName
            if inventoryItems[itemName] then
                marketItem.itemCount = inventoryItems[itemName]
                marketItem.itemMaxValue =inventoryItems[itemName] or 0
            else
                marketItem.itemCount = 0
                marketItem.itemMaxValue = 0
            end
            table.insert(data, marketItem)
        end
        cb(data)
    end)
    
end)


RegisterServerEvent('zcr_diving:server:acceptInvite', function(data)
    local src = source
    local identifier = GetIdentifier(src)
    local playerData = playerJobData[identifier]
    local hostidentifier = data.hostIdentifier
    local lobby = coopData[hostidentifier]
    if not lobby then return end
    if lobby.roomSetting.startJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobalreadystarted'].text, Config.NotificationText['jobalreadystarted'].type)
        return
    end
    for _, player in ipairs(lobby.players) do
        if player.identifier == identifier then
            TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['playeralreadyinlobby'].text, Config.NotificationText['playeralreadyinlobby'].type)
            return
        end
    end
    if #lobby.players >= 4 then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['lobbyfull'].text, Config.NotificationText['lobbyfull'].type)
        return
    end
    if coopData[identifier] then
        coopData[identifier] = nil
    end
    table.insert(lobby.players, {
        name = playerData.profiledata.name,
        identifier = playerData.profiledata.identifier,
        image = playerData.profiledata.avatar or Config.ExampleProfilePicture,
        level = playerData.profiledata.level,
        owner = false,
        src = src
    })
    
    if tonumber(#lobby.players) ~= 1 then
        if lobby.roomSetting.Mission ~= "NOSELECT" then
            if lobby.roomSetting.Mission.awards.money then
                lobby.roomSetting.Mission.awards.money = math.ceil(lobby.roomSetting.Mission.awards.money * lobby.roomSetting.Mission.awards.onlinejobextra * #lobby.players)
            end
        end
    else
        if lobby.roomSetting.Mission ~= "NOSELECT" then
            lobby.roomSetting.Mission.awards.money = math.ceil(lobby.roomSetting.Mission.awards.money)
        end
    end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:RefreshLobby', player.src, coopData[hostidentifier])
    end

end)

RegisterServerEvent('zcr_diving:server:firePlayer', function(data)
    local src = source
    local identifier = GetIdentifier(src)
    local playerData = playerJobData[identifier]
    local targetID = tonumber(data.targetID)
    local oldPlayerID
    if not targetID or not playerData then return end
    local targetIdentifier = GetIdentifier(targetID)
    if targetIdentifier ~= data.identifier then return end
    local lobby = coopData[identifier]
    if not lobby then return end
    for i, player in ipairs(lobby.players) do
        if player.identifier == targetIdentifier then
            oldPlayerID = tonumber(player.src)
            table.remove(lobby.players, i)
            break
        end
    end
    if tonumber(#lobby.players) ~= 1 then
        if lobby.roomSetting.Mission ~= "NOSELECT" then
            lobby.roomSetting.Mission.awards.money = math.ceil(lobby.roomSetting.Mission.awards.money * lobby.roomSetting.Mission.awards.onlinejobextra * #lobby.players)
        end
    else
        if lobby.roomSetting.Mission ~= "NOSELECT" then
            lobby.roomSetting.Mission.awards.money = lobby.roomSetting.Mission.awards.oldmoney
        end
    end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:RefreshLobby', player.src, coopData[identifier])
    end
    TriggerClientEvent('zcr_diving:client:TakeLooby', oldPlayerID)
end)

RegisterServerEvent('zcr_diving:server:invetePlayer', function(data)
    local src = source
    local identifier = GetIdentifier(src)
    local playerData = playerJobData[identifier]
    local targetID = tonumber(data.targetID)
    if not targetID or not playerData then return end
    local targetIdentifier = GetIdentifier(targetID)
    local targetPlayer = playerJobData[targetIdentifier]
    if not targetPlayer then return end
    local lobby = coopData[identifier]
    if not lobby then return end
    for _, player in ipairs(lobby.players) do
        if player.identifier == targetPlayer.profiledata.identifier then
            TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['playeralreadyinlobby'].text, Config.NotificationText['playeralreadyinlobby'].type)
            return
        end
    end
    if #lobby.players >= 4 then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['lobbyfull'].text, Config.NotificationText['lobbyfull'].type)
        return
    end
    TriggerClientEvent('zcr_diving:client:invetePlayer', targetID, playerData.profiledata.name, identifier, playerData.profiledata.src or src)
end)

RegisterServerEvent('zcr_diving:server:selectMission', function(data)
    local src = source
    if cooldowns[src] and (os.time() - cooldowns[src] < 5) then
        return
    end
    local identifier = GetIdentifier(src)
    local playerData = playerJobData[identifier]
    local lobby = coopData[identifier]
    if not lobby then return end
    if lobby.roomSetting.startJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobalreadystarted'].text, Config.NotificationText['jobalreadystarted'].type)
        return
    end
    if data == "NOSELECT" or data == false then
        lobby.roomSetting.Mission = "NOSELECT"
        for _, player in ipairs(lobby.players) do
            TriggerClientEvent('zcr_diving:client:RefreshLobby', player.src, coopData[identifier])
        end
        return
    else
        if lobby.roomSetting.Mission ~= "NOSELECT" then
            Wait(200)
            lobby.roomSetting.Mission = "NOSELECT"
            Wait(500)
        end
        for k,v in pairs(Config.Diving['Mission']) do
            if v.name == data.name then
                lobby.roomSetting.Mission  = data
                break
            end
        end
    end

    if not lobby.roomSetting.Mission then
        lobby.roomSetting.Mission = "NOSELECT"
        return
    end

    if tonumber(#lobby.players) ~= 1 then
        lobby.roomSetting.Mission.awards.oldmoney = lobby.roomSetting.Mission.awards.money
        lobby.roomSetting.Mission.awards.money = math.ceil(lobby.roomSetting.Mission.awards.money * lobby.roomSetting.Mission.awards.onlinejobextra * #lobby.players)
    end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:RefreshLobby', player.src, coopData[identifier])
    end
    cooldowns[src] = os.time()
end)

RegisterServerEvent('zcr_diving:server:startJob', function(data)
    local src = source
    local identifier = GetIdentifier(src)
    local playerData = playerJobData[identifier]
    local lobby = coopData[identifier]
    if not lobby then return end

    if lobby.roomSetting.startJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobalreadystarted'].text, Config.NotificationText['jobalreadystarted'].type)

        return
    end

    if lobby.roomSetting.Mission == "NOSELECT" then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['missionnotselected'].text, Config.NotificationText['missionnotselected'].type)
        return
    end

    if #lobby.players > 4 then
        print('not enough players')
        return
    end

    if lobby.roomSetting.Mission.jobTask then
        local newlobby = coopData[identifier]
        if tonumber(#newlobby.players) == 1 then
            for k,v in pairs(lobby.roomSetting.Mission.jobTask) do

                lobby.roomSetting.Mission.jobTask[k].amount = data.jobTask[k].singleAmount
                data.jobTask[k].amount = data.jobTask[k].singleAmount
            end
        end
    end

    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:RefreshLobby', player.src, coopData[identifier])
    end

    lobby.roomSetting.owneridentifier = identifier
    lobby.roomSetting.anchor = false
    lobby.roomSetting.ownersrc = src
    lobby.roomSetting.startJob = true
    lobby.roomSetting.awards = data.awards
    JoobTask[identifier] = {
        Players = {},
        JobTask = {}
    }

    for jobdata, __ in ipairs(data.jobTask) do
        local jobtasktable = {
            name = data.jobTask[jobdata].name,
            amount = data.jobTask[jobdata].amount,
            label = data.jobTask[jobdata].label,
            madeAmount = 0,
            img = data.jobTask[jobdata].img,
            finish = false
        }
        table.insert(JoobTask[identifier].JobTask, jobtasktable)
    end

    for _, player in ipairs(lobby.players) do
        local playerData = playerJobData[player.identifier]
        local jobtaskPlayer = {
            identifier = playerData.profiledata.identifier,
            level = playerData.profiledata.level,
            name = playerData.profiledata.name,
            image = playerData.profiledata.avatar or Config.ExampleProfilePicture,
            scoreAmount = 0,
            source = player.src
        }
        table.insert(JoobTask[identifier].Players, jobtaskPlayer)
    end
    

    jobVehicle = CreateVehicle(GetHashKey(Config.Diving['jobVehicle']),
    lobby.roomSetting.Mission.vehicleSpawnCoords[1].x,
    lobby.roomSetting.Mission.vehicleSpawnCoords[1].y,
    lobby.roomSetting.Mission.vehicleSpawnCoords[1].z,
    lobby.roomSetting.Mission.vehicleSpawnCoords[1].w, true, true)

    while not DoesEntityExist(jobVehicle) do
        Wait(0)
    end
    table.insert(lobby.roomSetting.Vehicle, jobVehicle)
    Wait(200)
    divingNetID = NetworkGetNetworkIdFromEntity(jobVehicle)
    for _, player in ipairs(lobby.players) do
        Wait(200)
        if player.src == src then
            TriggerClientEvent('zcr_diving:client:StartJobOwner', player.src, lobby, JoobTask[identifier], divingNetID, true, lobby.roomSetting.Mission.vehicleSpawnCoords[1])
        else
            TriggerClientEvent('zcr_diving:client:StartJobOwner', player.src, lobby, JoobTask[identifier], divingNetID, false, lobby.roomSetting.Mission.vehicleSpawnCoords[1])
        end
        TriggerClientEvent('zcr_diving:client:teleportPlayer', player.src, 'vehicle', GetEntityCoords(jobVehicle))
    end

    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:RefreshJob', player.src, JoobTask[identifier])
    end

end)

RegisterNetEvent('zcr_diving:server:deleteModel', function(jobTaskName, owneridentifier)
    local src = source  
    if not owneridentifier then return end
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    if not lobby.roomSetting.startJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobnotstarted'].text, Config.NotificationText['jobnotstarted'].type)
        return
    end

    for _, player in ipairs(lobby.players) do
        if player.identifier == identifier then
            for k,v in pairs(JoobTask[owneridentifier].JobTask) do
                if v.name == jobTaskName then

                    v.madeAmount = v.madeAmount + 1
                    if v.madeAmount <= v.amount then

                        if v.madeAmount == v.amount then
                            v.finish = true
                            TriggerEvent('zcr_diving:server:FinishJob', v.name, identifier, owneridentifier)
                        end
    
                        for _, player in ipairs(JoobTask[owneridentifier].Players) do
                            if player.identifier == identifier then
                                player.scoreAmount = player.scoreAmount + 1
                            end
                        end
                    else
                        v.finish = true
                        TriggerEvent('zcr_diving:server:FinishJob', v.name, identifier, owneridentifier)
                    end

                end
            end
        end
    end

    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:RefreshJob', player.src, JoobTask[owneridentifier])
    end
end)

RegisterServerEvent('zcr_diving:server:syncDeleteObject', function(areaName, owneridentifier, objecount)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    if areaName ~= "seaSuitCase" then
        if not lobby.roomSetting.startJob then
            TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobnotstarted'].text, Config.NotificationText['jobnotstarted'].type)
            return
        end
    end

    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:syncDeleteObject', player.src, areaName, objecount)
    end
end)

RegisterServerEvent('zcr_diving:server:syncAddBox', function(modelName, coords, objecount, boxHeight, bigbox, selectedBoxModel, owneridentifier)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    if not lobby.roomSetting.startJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobnotstarted'].text, Config.NotificationText['jobnotstarted'].type)
        return
    end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:syncAddBox', player.src, modelName, coords, objecount, boxHeight, bigbox, selectedBoxModel)
    end
end)

RegisterServerEvent('zcr_diving:server:syncAddLiftBag', function(modelName, coords, objecount, liftHeight, boxHeight, owneridentifier)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:syncAddLiftBag', player.src, modelName, coords, objecount, liftHeight, boxHeight)
    end
end)

RegisterServerEvent('zcr_diving:server:syncInteracted', function(objectName, objectCount, owneridentifier)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:syncInteracted', player.src, objectName, objectCount)
    end
end)

RegisterServerEvent('zcr_diving:server:syncAddObject', function(netID, modelName, coords, areaName, owneridentifier, objecount)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    if not lobby.roomSetting.startJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobnotstarted'].text, Config.NotificationText['jobnotstarted'].type)
        return
    end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:syncAddObject', player.src, netID, modelName, coords, areaName, objecount)
    end
end)

RegisterServerEvent('zcr_diving:server:syncAddObjectShip', function(modelName, boneIndex, crateOffsetX, crateOffsetY, crateOffsetZ, objecount, owneridentifier )
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:syncAddObjectShip', player.src, modelName, boneIndex, crateOffsetX, crateOffsetY, crateOffsetZ, objecount)
    end
end)


local syncedCounts = {}
RegisterServerEvent('zcr_diving:server:syncChangeObject', function(netId, modelName, coords, areaName, owneridentifier, objectCount, onylDelete)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:syncChangeObject', player.src, netId, modelName, coords, areaName, objectCount, onylDelete)
    end
end)

RegisterServerEvent('zcr_diving:server:FinishJob', function(jobTaskName, identifier, owneridentifier)
    local src = source
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    if not lobby.roomSetting.startJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobnotstarted'].text, Config.NotificationText['jobnotstarted'].type)
        return
    end
    if not JoobTask[owneridentifier] then return end

    local allFinished = true

    for k,v in pairs(JoobTask[owneridentifier].JobTask) do
        if v.name == "seaChanged" then
            lobby.roomSetting.changed = true
            for _, player in ipairs(lobby.players) do
                TriggerClientEvent('zcr_diving:client:FinishJob', player.src,  coopData[owneridentifier])
            end

        end
        if v.name == "seaChanged" or v.name == "seaClean" then
            if not v.finish then
                allFinished = false
                break 
            end
        end

    end

    if allFinished then
        lobby.roomSetting.finishJob = true
        lobby.roomSetting.startJob = false
        lobby.roomSetting.changed = true
        lobby.roomSetting.carDelivered = true
        for _, player in ipairs(lobby.players) do
            TriggerClientEvent('zcr_diving:client:FinishJob', player.src,  coopData[owneridentifier])
        end
    end
end)

function discordloghistoryData(source, data)
    local src = source
    local identifier = GetIdentifier(src)
    local dataInfo = {
        identifier = identifier,
        avatar = GetDiscordAvatar(src) or Config.ExampleProfilePicture,
        name = GetName(src),
        id = src,
        money = data.money,
    }
    return dataInfo
end

RegisterServerEvent('zcr_diving:server:addItem', function(dataName)
    local src = source
    if dataName == 'coral' then
        local coral, coralAmount = getRandomItem('RandomCoral')
        if not coral then
            return
        end
        addItem(src, coral, tonumber(coralAmount), false, false)
        TriggerClientEvent('zcr_diving:client:sendNotification', src, coralAmount.." pieces of "..coral.." collected", "infoNotify")
    elseif dataName == 'suitCase' then
        local suitCase, suitCaseAmount = getRandomItem('RandomSuitCase')
        if not suitCase then
            return
        end
        addItem(src, suitCase, tonumber(suitCaseAmount), false, false)
        TriggerClientEvent('zcr_diving:client:sendNotification', src, suitCaseAmount.." pieces of "..suitCase.." collected", "infoNotify")
    elseif dataName == 'trash' then
    local trash, trashAmount = getRandomItem('RandomTrash')
    if not trash then
        return
    end
    addItem(src, trash, tonumber(trashAmount), false, false)
    TriggerClientEvent('zcr_diving:client:sendNotification', src, trashAmount.." pieces of "..trash.." collected", "infoNotify")

    end
end)

RegisterServerEvent('zcr_diving:server:LeaveVehicle', function(owneridentifier,totalBox)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    local ItemownerSrc = lobby.roomSetting.ownersrc
    if not lobby then return end
    if not lobby.roomSetting.finishJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobnotstarted'].text, Config.NotificationText['jobnotstarted'].type)
        return
    end
    local vehiclesToDelete = {}
    lobby.roomSetting.awards.money = math.ceil(lobby.roomSetting.awards.money / #lobby.players)
    for _, player in ipairs(lobby.players) do
        TriggerClientEvent('zcr_diving:client:teleportPlayer', player.src, 'npc', Config.Diving.coords.intreactionCoords)
        Wait(1000)
        for k,v in pairs(lobby.roomSetting.Vehicle) do
            table.insert(vehiclesToDelete, v) 
        end

        for _, vehicle in ipairs(vehiclesToDelete) do
            if DoesEntityExist(vehicle) then
                DeleteEntity(vehicle)
            end
        end

        for _, vehicle in ipairs(vehiclesToDelete) do
            for i, v in ipairs(lobby.roomSetting.Vehicle) do
                if v == vehicle then
                    table.remove(lobby.roomSetting.Vehicle, i)
                    break
                end
            end
        end

        if #lobby.roomSetting.Vehicle == 0 then
            local historyData = {
                ['money'] = tonumber(lobby.roomSetting.awards.money),
                ['time'] = os.date("%m-%d-%Y %H:%M"),
            }
            local discordlog = discordloghistoryData(tonumber(player.src), historyData)
            sendDiscordLogHistory(discordlog)
            AddMoney(tonumber(player.src), 'bank', tonumber(lobby.roomSetting.awards.money))
            AddXPDivingJob(tonumber(player.src),  tonumber(lobby.roomSetting.awards.xp))
            Wait(300)
            savePlayerData(player.src)

            for __, jobtask in ipairs(JoobTask[owneridentifier].Players) do
                if jobtask.identifier == player.identifier then
                    TriggerClientEvent('zcr_diving:client:LeaveVehicle', player.src,  jobtask.scoreAmount, lobby.roomSetting.awards)
                end
            end
        end
    end
    for i = 1, tonumber(totalBox) do
        local shipItem, shipAmount = getRandomItem('RandomShipBox')
        if not shipItem then
            return
        end
        addItem(tonumber(src), shipItem, tonumber(shipAmount), false, false)
        TriggerClientEvent('zcr_diving:client:sendNotification', tonumber(src), shipAmount.." pieces of "..shipItem.." collected", "infoNotify")
    end
    TriggerClientEvent('zcr_diving:client:sendNotification', src, "You delivered quantity ".. totalBox, "succesNotify")
    
    coopData[owneridentifier] = nil
    JoobTask[owneridentifier] = nil
    syncedCounts = {}
end)

RegisterServerEvent('zcr_diving:server:syncSettings', function(name, coords, lower, owneridentifier)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    if name == "anchor" then
        lobby.roomSetting.anchor = not lobby.roomSetting.anchor 
        for _, player in ipairs(lobby.players) do
            TriggerClientEvent('zcr_diving:client:syncSettings', player.src, name, lobby.roomSetting.anchor,lower)
        end
        return
    else
        for _, player in ipairs(lobby.players) do
            if player.src ~= src then
                TriggerClientEvent('zcr_diving:client:syncSettings', player.src, name, coords,lower)
            end
        end
    end


end) 

RegisterServerEvent('zcr_diving:server:syncCoords', function(coordsTable, areaName)
    local src = source
    local identifier = GetIdentifier(src)
    local lobby = coopData[identifier]
    if not lobby then return end
    if not lobby.roomSetting.startJob then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['jobnotstarted'].text, Config.NotificationText['jobnotstarted'].type)
        return
    end
    for _, player in ipairs(lobby.players) do
        if player.src ~= src then
            TriggerClientEvent('zcr_diving:client:syncCoords', player.src, coordsTable, areaName)
        end
    end
end)

RegisterServerEvent('zcr_diving:server:buyItem', function(data)
    if not data then return end
    local src = source
    local playerMoney = GetPlayerMoney(src, 'bank')
    local totalMoney = data.itemCount * data.itemPrice
    if playerMoney < totalMoney then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['notenoughmoney'].text, Config.NotificationText['notenoughmoney'].type)
        return
    end
    
    RemoveMoney(src, 'bank', totalMoney)
    addItem(src, data.itemName, tonumber(data.itemCount), false,false)
    TriggerClientEvent('zcr_diving:client:sendNotification', src, data.itemCount.." pieces of "..data.itemName.." purchased", "infoNotify")
end)

RegisterServerEvent('zcr_diving:server:sellItem', function(data)
    if not data then return end
    local src = source
    local PlayerInventory = GetPlayerInventory(src)
    local itemData = nil
    for _, item in ipairs(PlayerInventory) do
        if item.name == data.itemName then
            itemData = item
            break
        end
    end

    if not itemData then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['itemnotfound'].text, Config.NotificationText['itemnotfound'].type)
        return
    end

    if itemData.amount < data.itemCount then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['notenoughitem'].text, Config.NotificationText['notenoughitem'].type)
        return
    end

    local totalMoney = data.itemCount * data.itemPrice
    AddMoney(src, 'bank', totalMoney)
    TriggerClientEvent('zcr_diving:client:sendNotification', src, totalMoney.."$ Added", "succesNotify")
    removeItem(src, data.itemName, tonumber(data.itemCount), false)
end)

RegisterServerEvent('zcr_diving:server:removeItem', function(itemName, itemCount)
    local src = source
    itemCount = tonumber(itemCount) or 1
    removeItem(src, itemName, tonumber(itemCount or 1))
    TriggerClientEvent('zcr_diving:client:sendNotification', src, itemCount .. " pieces of " .. itemName .. " removed", "infoNotify")

end)


function getRandomItem(configName)
    local totalProbability = 0
    if Config[configName] then
        for _, item in ipairs(Config[configName]) do
            totalProbability = totalProbability + item.probability
        end
    end

    local randomNumber = math.random(0, totalProbability)

    local cumulativeProbability = 0
    for _, itemData in ipairs(Config[configName]) do
        cumulativeProbability = cumulativeProbability + itemData.probability
        if randomNumber <= cumulativeProbability then
            return itemData.item, itemData.amount
        end
    end
end

function AddXPDivingJob(source, xp)
    if not xp and xp <= 0 then
        return
    end
    local src = source
    local identifier = GetIdentifier(src)
    local data = playerJobData[identifier]
    if not data then
        return
    end
    local profiledata = data.profiledata
    profiledata.xp = tonumber(profiledata.xp) + tonumber(xp)
    if profiledata.level > #Config.RequiredXP then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['maxlevel'].text, Config.NotificationText['maxlevel'].type)
        return
    end
    if tonumber(profiledata.xp) >= tonumber(Config.RequiredXP[profiledata.level]) then
        profiledata.level = tonumber(profiledata.level) + 1
        profiledata.xp = 0
    end
end

RegisterNetEvent('grapple:server:start')
AddEventHandler('grapple:server:start', function(destination, target, options, boxId, liftbagId, owneridentifier)
    local src = source
    local lobby = coopData[owneridentifier]
    for _, player in ipairs(lobby.players) do
        if player.src ~= src then
            TriggerClientEvent('grapple:client:start', player.src, destination, target, options, boxId, liftbagId)
        end
    end
end)

RegisterNetEvent('grapple:server:update')
AddEventHandler('grapple:server:update', function(targetNetId, position)
    local src = source
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    for _, player in ipairs(lobby.players) do
        if player.src ~= src then
            TriggerClientEvent('grapple:client:update', player.src, targetNetId, position)
        end
    end
end)

RegisterNetEvent('grapple:server:end')
AddEventHandler('grapple:server:end', function(targetNetId)
    local src = source
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    for _, player in ipairs(lobby.players) do
        if player.src ~= src then
            TriggerClientEvent('grapple:client:end', player.src, targetNetId)
        end
    end
end)

RegisterServerEvent('zcr_diving:server:resetJobButton', function(owneridentifier)
    local src = source
    local identifier = GetIdentifier(src)
    local owneridentifier = owneridentifier
    if not owneridentifier then return end
    if tostring(owneridentifier) ~= tostring(identifier) then
        TriggerClientEvent('zcr_diving:client:sendNotification', src, Config.NotificationText['notowner'].text, Config.NotificationText['notowner'].type)
        return
    end
    local lobby = coopData[owneridentifier]
    if not lobby then return end
    if lobby.roomSetting.startJob then
        for _, player in ipairs(lobby.players) do
            if player.src == src then
                local vehiclesToDelete = {}
                for k,v in pairs(lobby.roomSetting.Vehicle) do
                    table.insert(vehiclesToDelete, v) 
                end
                for _, vehicle in ipairs(vehiclesToDelete) do
                    if DoesEntityExist(vehicle) then
                        DeleteEntity(vehicle)
                    end
                end
                for _, vehicle in ipairs(vehiclesToDelete) do
                    for i, v in ipairs(lobby.roomSetting.Vehicle) do
                        if v == vehicle then
                            table.remove(lobby.roomSetting.Vehicle, i)
                            break
                        end
                    end
                end

                coopData[owneridentifier] = nil
                JoobTask[owneridentifier] = nil
                syncedCounts = {}

                for _, remainingPlayer in ipairs(lobby.players) do
                    SetPlayerRoutingBucket(tonumber(remainingPlayer.src), 0)
                    TriggerClientEvent('zcr_diving:client:resetjob', remainingPlayer.src)
                    TriggerClientEvent('zcr_diving:client:sendNotification', remainingPlayer.src, Config.NotificationText['resetJob'].text, Config.NotificationText['resetJob'].type)
                end
                break
            end
        end
    end

end)