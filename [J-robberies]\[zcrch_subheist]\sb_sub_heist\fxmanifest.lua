fx_version 'adamant'
game 'gta5'
lua54 'yes'

version '1.1'
author 'StArBoys'
description 'SB SUBMARINE HEIST'

shared_scripts {
  'config.lua'
}

server_scripts {
  'server/server.lua',
  'editables/editable_server.lua'
}

client_scripts {
  'client/client.lua',
  'editables/editable_client.lua',
}

ui_page "html/detonate.html"

files {
    'html/detonate.html',
    'html/index.js',
    'html/css/*.css',
    'html/img/*.png',
    'html/img/*.svg',
}

escrow_ignore {
  'config.lua',  
  'editables/editable_client.lua',
  'editables/editable_server.lua'
}

dependencies {
   'interact-sound',
   'howdy-hackminigame',
   'mx_fixwiring',
   'controlroom',
   'subheistmap',
   --es_extended(or)qb-core(or)custom
}

dependency '/assetpacks'