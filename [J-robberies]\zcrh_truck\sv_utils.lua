local ESX = exports['es_extended']:getSharedObject()
local YourWebhook = 'https://discord.com/api/webhooks/1355501984156876910/UZB3ow78jLbraf4B_C3UpPVS5SH5i6xOa25friMeRf-q-TvstDR0oxsMkMWmWaU93Kcp'  -- help: https://docs.brutalscripts.com/site/others/discord-webhook

function GetWebhook()
    return YourWebhook
end

-- Buy here: (4€+VAT) https://store.brutalscripts.com
function notification(source, title, text, time, type)
    if Config.BrutalNotify then
        TriggerClientEvent('brutal_notify:SendAlert', source, title, text, time, type)
    else
        TriggerClientEvent('zcrh_truck:client:DefaultNotify', text)
    end
end

function PlayerNameFunction(source)
    return GetPlayerName(source)
end

RegisterServerEvent('zcrh_truck:server:PoliceAlert')
AddEventHandler('zcrh_truck:server:PoliceAlert', function(coords)
    local players = ESX.GetExtendedPlayers('job', Config.CopsJobs[1])
    for i = 1, #players do
        TriggerClientEvent('zcrh_truck:client:PoliceAlert', players[i].source, coords)
    end
end)