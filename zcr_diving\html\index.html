<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="main.css" />
    <script src="./js/vue.global.js"></script>
    <script src="./js/vuex.global.js"></script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css"
    />
  </head>
  <body>
    <div id="app">
      <div class="notifyList" v-show="notifyShow">
        <div v-for="(notification, index) in notifications" :key="index" :class="['notifyBox', notification.type]">
          <div class="notifyLine"></div>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 34 34" fill="none" v-if="notification.type === 'infoNotify'">
            <path d="M17 3.1875C14.2682 3.1875 11.5976 3.99759 9.32619 5.51533C7.05474 7.03306 5.28436 9.19028 4.23892 11.7142C3.19348 14.2381 2.91995 17.0153 3.45291 19.6947C3.98587 22.374 5.30138 24.8352 7.23309 26.7669C9.16481 28.6986 11.626 30.0141 14.3053 30.5471C16.9847 31.0801 19.7619 30.8065 22.2858 29.7611C24.8097 28.7156 26.9669 26.9453 28.4847 24.6738C30.0024 22.4024 30.8125 19.7319 30.8125 17C30.8086 13.3379 29.3522 9.82687 26.7626 7.23736C24.1731 4.64785 20.6621 3.19137 17 3.1875ZM16.4688 9.5625C16.784 9.5625 17.0921 9.65597 17.3542 9.83109C17.6163 10.0062 17.8206 10.2551 17.9412 10.5463C18.0618 10.8376 18.0934 11.158 18.0319 11.4672C17.9704 11.7763 17.8186 12.0603 17.5957 12.2832C17.3728 12.5061 17.0888 12.6579 16.7797 12.7194C16.4705 12.7809 16.1501 12.7493 15.8589 12.6287C15.5676 12.5081 15.3187 12.3038 15.1436 12.0417C14.9685 11.7796 14.875 11.4715 14.875 11.1562C14.875 10.7336 15.0429 10.3282 15.3418 10.0293C15.6407 9.73041 16.0461 9.5625 16.4688 9.5625ZM18.0625 24.4375C17.4989 24.4375 16.9584 24.2136 16.5599 23.8151C16.1614 23.4166 15.9375 22.8761 15.9375 22.3125V17C15.6557 17 15.3855 16.8881 15.1862 16.6888C14.9869 16.4895 14.875 16.2193 14.875 15.9375C14.875 15.6557 14.9869 15.3855 15.1862 15.1862C15.3855 14.9869 15.6557 14.875 15.9375 14.875C16.5011 14.875 17.0416 15.0989 17.4401 15.4974C17.8386 15.8959 18.0625 16.4364 18.0625 17V22.3125C18.3443 22.3125 18.6146 22.4244 18.8138 22.6237C19.0131 22.823 19.125 23.0932 19.125 23.375C19.125 23.6568 19.0131 23.927 18.8138 24.1263C18.6146 24.3256 18.3443 24.4375 18.0625 24.4375Z">
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 34 34" fill="none" v-if="notification.type === 'errorNotify'">
            <path
              d="M18.2522 3.58703C17.7621 2.66053 16.2377 2.66053 15.7476 3.58703L2.99757 27.6704C2.88279 27.8862 2.82589 28.1281 2.83241 28.3725C2.83893 28.6169 2.90864 28.8554 3.03476 29.0649C3.16088 29.2743 3.33909 29.4475 3.55205 29.5676C3.765 29.6876 4.00543 29.7505 4.2499 29.75H29.7499C29.9942 29.7505 30.2345 29.6877 30.4473 29.5677C30.6601 29.4477 30.8382 29.2746 30.9642 29.0653C31.0901 28.856 31.1597 28.6176 31.1662 28.3734C31.1726 28.1291 31.1156 27.8874 31.0008 27.6718L18.2522 3.58703ZM18.4166 25.5H15.5832V22.6667H18.4166V25.5ZM15.5832 19.8334V12.75H18.4166L18.418 19.8334H15.5832Z"
            />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="none" v-if="notification.type === 'succesNotify'">
            <path
              d="M16 2C19.713 2 23.274 3.475 25.8995 6.1005C28.525 8.72601 30 12.287 30 16C30 19.713 28.525 23.274 25.8995 25.8995C23.274 28.525 19.713 30 16 30C12.287 30 8.72601 28.525 6.1005 25.8995C3.475 23.274 2 19.713 2 16C2 12.287 3.475 8.72601 6.1005 6.1005C8.72601 3.475 12.287 2 16 2ZM14.256 18.762L11.146 15.65C11.0345 15.5385 10.9021 15.4501 10.7565 15.3897C10.6108 15.3294 10.4547 15.2983 10.297 15.2983C10.1393 15.2983 9.9832 15.3294 9.83752 15.3897C9.69185 15.4501 9.55949 15.5385 9.448 15.65C9.22283 15.8752 9.09633 16.1806 9.09633 16.499C9.09633 16.8174 9.22283 17.1228 9.448 17.348L13.408 21.308C13.5192 21.4201 13.6514 21.509 13.7971 21.5697C13.9429 21.6304 14.0991 21.6616 14.257 21.6616C14.4149 21.6616 14.5711 21.6304 14.7169 21.5697C14.8626 21.509 14.9948 21.4201 15.106 21.308L23.306 13.106C23.419 12.995 23.5089 12.8627 23.5705 12.7167C23.6321 12.5708 23.6641 12.4141 23.6649 12.2557C23.6656 12.0973 23.635 11.9403 23.5748 11.7938C23.5145 11.6473 23.4259 11.5142 23.3139 11.4021C23.202 11.29 23.069 11.2012 22.9225 11.1408C22.7761 11.0804 22.6191 11.0496 22.4607 11.0501C22.3023 11.0507 22.1456 11.0826 21.9996 11.144C21.8536 11.2054 21.7212 11.2951 21.61 11.408L14.256 18.762Z"
            />
          </svg>
          <div class="notifyTextBox">
            <h2 class="notifyText" v-if="notification.type === 'infoNotify'">{{Locales["info"]}}</h2>
            <h2 class="notifyText" v-if="notification.type === 'errorNotify'">{{Locales["error"]}}</h2>
            <h2 class="notifyText" v-if="notification.type === 'succesNotify'">{{Locales["successs"]}}</h2>
            <p class="notifySubText">{{ notification.message }}</p>
          </div>
        </div>
      </div>
      
      <div class="finishBox" v-show="finishShow">
        <div class="earnBox">
          <h2 class="earnTitle">{{Locales["your_earnings"]}}</h2>
          <div class="earnList">
            <div class="earn expEarn">
              <div class="earnTopSide">{{Locales["LEVELEXP"]}}</div>
              <div class="earnTextBox">
                <h2 class="earnText">{{formatNumber(finishjobData.xp)}} {{Locales["EXP"]}}</h2>
                <p class="earnSubText">{{Locales["Received"]}}</p>
              </div>
            </div>
            <div class="earn moneyEarn">
              <div class="earnTopSide">{{Locales["REWARDMONEY"]}}</div>
              <div class="earnTextBox">
                <h2 class="earnText">{{serverMoneyType}} {{formatNumber(finishjobData.money)}}</h2>
                <p class="earnSubText">{{Locales["Received"]}}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="finishTitleBox">
          <p class="finishSubTitle">{{Locales["Mission"]}}</p>
          <h2 class="finishTitle">{{Locales["Completed"]}}</h2>
          <h3 class="finishName">{{finishjobData.name}}</h3>
        </div>
        <div
          class="earnBox"
          style="
            background: linear-gradient(
              270deg,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 0.06) 100%
            );
          "
        >
          <h2 class="earnTitle">{{Locales["YOUR_SCORE"]}}</h2>
          <div class="earnList">
            <div class="earn moneyEarn scoreEarn">
              <div class="earnTopSide">{{Locales["YOUR_SCOREE"]}}</div>
              <div class="earnTextBox">
                <h2 class="earnText">{{finishjobData.scoreAmount}} {{Locales["Mission"]}}</h2>
                <p class="earnSubText">{{Locales["Received"]}}</p>
              </div>
            </div>
          </div>
        </div>
        <img src="./img/finishEffect.png" alt="" class="finishEffect" />
      </div>
      
      <div class="scoreList" v-show="playerShow">
        <div class="scoreBox" v-if="progressData && progressData.Players" v-for="(data, index) in progressData.Players" :key="index">
          <div class="scorePlayerBox">
            <div class="scorePlayerImg" 
            :style="`background-image: url('${data.image}')`"></div>
            <div class="scorePlayerTextBox">
              <h2 class="scorePlayerName">{{data.name}}</h2>
              <div class="scorePlayerLevel">
                <div class="scorePlayerLevelLine"></div>
                <h2 class="scorePlayerLevelText">{{data.level}} {{Locales["LEVEL"]}}</h2>
              </div>
            </div>
          </div>
          <div class="scoreSay">
            <img src="./img/tb.png" alt="" class="kn" />
            <h2 class="scoreSayText"> {{data.scoreAmount}} </h2>
          </div>
        </div>
      </div>

      <div class="allScoreBox" v-show="scoreBoxShow">
        <div class="allScore" v-for="task in filteredTasks" :key="task.name">
          <img :src="'./img/' + task.img" alt="" class="allScoreImg" />
          <h2 class="allScoreText">{{ task.madeAmount }}<span>/{{ task.amount }}</span></h2>
        </div>
      </div>

      <div v-if="progressbar > 0" class="loadingBox">
        <div class="loadingText">
          <div class="loadingLine"></div>
          {{progressbarLabel}}
        </div>
        <div class="loadingBar">
          <div class="bars">
            <div class="bar":style="{'width' : progressbar + '%'}"></div>
          </div>
        </div>
      </div>

      <div class="tubeBox" v-if="tubeShow" :style="tubeStyle">
        <div class="tubeBar">
          <div class="tubeSay say1"></div>
          <div class="tubeSay say2"></div>
          <div class="tubeSay say3"></div>
          <div class="bar" :style="{ height: tubeHeight }"></div>
        </div>
        <div class="tubeIcon">
          <img src="./img/tubeImg.png" alt="" class="tubeImg" />
        </div>
      </div>
      
      <div
        v-if="mainShow"
        :class="currentScreen == 'jobScreen' ? 'General' : 'General sellMenuActive'"
      >
        <div class="exitBox" @click="closeNUI">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width=".7813vw"
            height=".7813vw"
            viewBox="0 0 15 15"
            fill="none"
          >
            <g clip-path="url(#clip0_8_1375)">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M7.50016 8.72008L10.5494 11.7693C10.7112 11.9311 10.9306 12.022 11.1595 12.022C11.3883 12.022 11.6077 11.9311 11.7695 11.7693C11.9313 11.6075 12.0222 11.3881 12.0222 11.1592C12.0222 10.9304 11.9313 10.711 11.7695 10.5492L8.71916 7.49993L11.769 4.4507C11.849 4.37059 11.9126 4.27548 11.9559 4.17082C11.9992 4.06616 12.0215 3.95399 12.0215 3.84071C12.0214 3.72744 11.9991 3.61528 11.9557 3.51064C11.9124 3.40599 11.8488 3.31092 11.7687 3.23084C11.6886 3.15076 11.5935 3.08725 11.4888 3.04393C11.3841 3.0006 11.272 2.97832 11.1587 2.97834C11.0454 2.97837 10.9332 3.00071 10.8286 3.04408C10.724 3.08745 10.6289 3.15101 10.5488 3.23113L7.50016 6.28035L4.45094 3.23113C4.37141 3.14871 4.27627 3.08296 4.17107 3.03771C4.06586 2.99246 3.95269 2.96861 3.83817 2.96757C3.72365 2.96652 3.61007 2.98829 3.50405 3.0316C3.39803 3.07492 3.3017 3.13892 3.22068 3.21986C3.13966 3.30081 3.07557 3.39708 3.03216 3.50305C2.98874 3.60903 2.96686 3.72259 2.9678 3.83711C2.96874 3.95164 2.99248 4.06482 3.03763 4.17007C3.08279 4.27532 3.14845 4.37053 3.23079 4.45013L6.28116 7.49993L3.23136 10.5497C3.14902 10.6293 3.08336 10.7245 3.03821 10.8298C2.99306 10.935 2.96932 11.0482 2.96838 11.1627C2.96744 11.2773 2.98931 11.3908 3.03273 11.4968C3.07615 11.6028 3.14024 11.6991 3.22126 11.78C3.30228 11.8609 3.39861 11.9249 3.50462 11.9683C3.61064 12.0116 3.72423 12.0333 3.83875 12.0323C3.95327 12.0312 4.06643 12.0074 4.17164 11.9621C4.27685 11.9169 4.37199 11.8511 4.45151 11.7687L7.50016 8.72008Z"
                fill="white"
              />
            </g>
            <defs>
              <clipPath id="clip0_8_1375">
                <rect
                  width="13.8"
                  height="13.8"
                  fill="white"
                  transform="translate(0.600098 0.599976)"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
        <div class="topSide">
          <div class="titleBox">
            <img src="./img/Logo.png" alt="" class="logo" />
            <div class="titleTextBox">
              <h2 class="titleText" v-if="serverName.length < 7">
                {{serverName}}
              </h2>
              <p class="titleSubText">{{Locales["DIVING"]}}</p>
            </div>
          </div>
          <div class="profileBox">
            <img
              v-if="playerData.image"
              :src="playerData.image"
              class="profileImg"
            />

            <div class="profileTextBox">
              <h2 class="profileText" v-if="playerData.name">
                {{playerData.name}}
              </h2>
              <p class="profileSubText">
                {{serverMoneyType}}{{formatNumber(playerData.bank)}}
              </p>
            </div>
          </div>
          <div class="levelBox">
            <div class="single-chart">
              <svg viewBox="0 0 36 36" class="circular-chart orange">
                <path
                  class="circle-bg"
                  d="M18 2.0845
                              a 15.9155 15.9155 0 0 1 0 31.831
                              a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  class="circle"
                  :stroke-dasharray="computedStrokeDasharray"
                  d="M18 2.0845
                              a 15.9155 15.9155 0 0 1 0 31.831
                              a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <text x="18" y="23.2" class="percentage">
                  {{playerData.level}}
                </text>
              </svg>
            </div>
            <div class="levelTextBox">
              <div class="levelTexts">
                <div class="levelLine"></div>
                <h2 class="levelText">LEVEL {{playerData.level}}</h2>
              </div>
              <h2 class="levelSubText">
                required <span>{{xpData.requiredXp}} {{Locales["EXP"]}}</span>
              </h2>
            </div>
            <div class="expBarBox">
              {{Locales["EXP"]}}
              <div class="expBar"></div>
            </div>
          </div>
        </div>
        <!-- "marketSide sellMenu" burası 2.npc olan sell menü -->
        <div class="marketSide sellMenu" v-if="currentScreen === 'sellScreen'">
          <div class="marketTopSide">
            <div class="regionTitle">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1.6146vw"
                height="1.6146vw"
                viewBox="0 0 31 31"
                fill="none"
              >
                <g clip-path="url(#clip0_256_795)">
                  <path
                    d="M27.28 28.52C28.6497 28.52 29.76 27.4097 29.76 26.04C29.76 24.6703 28.6497 23.56 27.28 23.56C25.9104 23.56 24.8 24.6703 24.8 26.04C24.8 27.4097 25.9104 28.52 27.28 28.52Z"
                    fill="white"
                  />
                  <path
                    d="M9.29995 28.52C10.6696 28.52 11.7799 27.4097 11.7799 26.04C11.7799 24.6703 10.6696 23.56 9.29995 23.56C7.93028 23.56 6.81995 24.6703 6.81995 26.04C6.81995 27.4097 7.93028 28.52 9.29995 28.52Z"
                    fill="white"
                  />
                  <path
                    d="M29.14 20.46H9.77802L10.1916 19.7892C10.3689 19.5015 10.421 19.1537 10.336 18.8263L9.9324 17.2726L27.9019 16.3389C28.5832 16.3041 29.14 15.717 29.14 15.035V6.82C29.14 6.138 28.582 5.58 27.9 5.58H6.89378L6.65136 4.64814C6.5823 4.38236 6.42696 4.14703 6.20973 3.97905C5.99249 3.81108 5.72564 3.71996 5.45104 3.72H1.24C0.911132 3.72 0.595733 3.85064 0.363188 4.08319C0.130643 4.31573 0 4.63113 0 4.96C0 5.28887 0.130643 5.60427 0.363188 5.83681C0.595733 6.06936 0.911132 6.2 1.24 6.2H4.49252L7.80332 18.9379L6.50132 21.049C6.38551 21.2368 6.32198 21.4522 6.31729 21.6728C6.3126 21.8934 6.36693 22.1113 6.47466 22.3039C6.58195 22.4967 6.73882 22.6573 6.92904 22.7692C7.11925 22.881 7.3359 22.94 7.55656 22.94H29.14C29.4689 22.94 29.7843 22.8094 30.0168 22.5768C30.2494 22.3443 30.38 22.0289 30.38 21.7C30.38 21.3711 30.2494 21.0557 30.0168 20.8232C29.7843 20.5906 29.4689 20.46 29.14 20.46Z"
                    fill="white"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_256_795">
                    <rect width="31" height="31" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              <div class="regionTitleText">
                {{Locales["DIVINGSELLMARKET"]}}
                <p>{{Locales["CHOOSE"]}}</p>
              </div>
            </div>
            <div class="searchInputBox">
              <input
                type="text"
                class="searchInput"
                :placeholder="Locales['SEARCHITEM']"
                v-model="sellMarketSearch"
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width=".7292vw"
                height=".7813vw"
                viewBox="0 0 14 15"
                fill="none"
              >
                <path
                  d="M11.025 12.5625L7.48125 9.01875C7.2 9.24375 6.87656 9.42187 6.51094 9.55312C6.14531 9.68437 5.75625 9.75 5.34375 9.75C4.32188 9.75 3.45713 9.396 2.7495 8.688C2.04188 7.98 1.68788 7.11525 1.6875 6.09375C1.68713 5.07225 2.04113 4.2075 2.7495 3.4995C3.45788 2.7915 4.32263 2.4375 5.34375 2.4375C6.36488 2.4375 7.22981 2.7915 7.93856 3.4995C8.64731 4.2075 9.00113 5.07225 9 6.09375C9 6.50625 8.93438 6.89531 8.80313 7.26094C8.67188 7.62656 8.49375 7.95 8.26875 8.23125L11.8125 11.775L11.025 12.5625ZM5.34375 8.625C6.04688 8.625 6.64463 8.379 7.137 7.887C7.62938 7.395 7.87538 6.79725 7.875 6.09375C7.87463 5.39025 7.62863 4.79269 7.137 4.30106C6.64538 3.80944 6.04763 3.56325 5.34375 3.5625C4.63988 3.56175 4.04231 3.80794 3.55106 4.30106C3.05981 4.79419 2.81363 5.39175 2.8125 6.09375C2.81138 6.79575 3.05756 7.3935 3.55106 7.887C4.04456 8.3805 4.64213 8.6265 5.34375 8.625Z"
                  fill="white"
                  fill-opacity="0.62"
                />
              </svg>
            </div>
          </div>
          <div class="marketList">
            <div class="itemSellBox" v-for="(value, key) in sellMarketData">
              <div class="itemTopSide">
                <div class="itemNameBox">
                  <h2 class="itemName">{{value.itemLabel}}</h2>
                  <p class="itemSubName">Buy Item</p>
                </div>
                <h2 class="itemKg">x{{value.itemMaxValue}}</h2>
              </div>
              <img :src="value.itemImage" alt="" class="itemImg" />
              <div class="itemBuyButtonBox">
                <div class="itemAmountBox">
                  <div
                    class="itemAmountButton"
                    @click="changeSellCount('remove', value, key)"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 16 16"
                      fill="none"
                    >
                      <path
                        d="M12.537 8.64685H3.46289V7.35056H12.537V8.64685Z"
                      />
                    </svg>
                  </div>
                  <h2 class="itemAmountText">x{{value.itemCount}}</h2>
                  <div
                    class="itemAmountButton"
                    @click="changeSellCount('add', value, key)"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 14 14"
                      fill="none"
                    >
                      <path
                        d="M10.8889 7.55444H7.55553V10.8878H6.44442V7.55444H3.11108V6.44333H6.44442V3.11H7.55553V6.44333H10.8889V7.55444Z"
                      />
                    </svg>
                  </div>
                </div>
                <div class="itemSellButtonList">
                  <div class="itemSellButton" @click="sellItem(value, key)">
                    Sell ({{serverMoneyType}}{{formatNumber(value.itemCount *
                    value.itemPrice)}})
                  </div>
                  <div
                    class="itemSellAllButton"
                    @click="sellAllItem(value, key)"
                  >
                  {{Locales["SELLALL"]}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- "bottomSide" burası anamenü divi -->
        <div
          class="bottomSide"
          v-if="currentScreen === 'jobScreen' || currentScreen === 'marketScreen'"
        >
          <div class="categoryList">
            <div
              :class="currentScreen == 'jobScreen' ? 'categoryBox selectCategory' : 'categoryBox'"
              @click="ChangeScreen('jobScreen')"
            >
              <img
                src="./img/category1.png"
                alt=""
                class="categoryImg category1"
              />
              <div class="categoryTextBox">
                <h2 class="categoryText">{{Locales["DIVINGJOBMENU"]}}</h2>
                <p class="categorySubText">{{Locales["CLICK_MENU"]}}</p>
              </div>
            </div>
            <!-- <div
              :class="currentScreen == 'marketScreen' ? 'categoryBox selectCategory' : 'categoryBox'"
              @click="ChangeScreen('marketScreen')"
            >
              <img
                src="./img/category2.png"
                alt=""
                class="categoryImg category2"
              />
              <div class="categoryTextBox">
                  <h2 class="categoryText">{{Locales["DIVINGMARKETMENU"]}}</h2>
                <p class="categorySubText">{{Locales["CLICK_MENU"]}}</p>
              </div>
            </div> -->
          </div>
          <!-- "marketSide" burası 1.npc market kısmı -->
          <div class="marketSide" v-if="currentScreen === 'marketScreen'">
            <div class="marketTopSide">
              <div class="regionTitle">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.6146vw"
                  height="1.6146vw"
                  viewBox="0 0 31 31"
                  fill="none"
                >
                  <g clip-path="url(#clip0_256_795)">
                    <path
                      d="M27.28 28.52C28.6497 28.52 29.76 27.4097 29.76 26.04C29.76 24.6703 28.6497 23.56 27.28 23.56C25.9104 23.56 24.8 24.6703 24.8 26.04C24.8 27.4097 25.9104 28.52 27.28 28.52Z"
                      fill="white"
                    />
                    <path
                      d="M9.29995 28.52C10.6696 28.52 11.7799 27.4097 11.7799 26.04C11.7799 24.6703 10.6696 23.56 9.29995 23.56C7.93028 23.56 6.81995 24.6703 6.81995 26.04C6.81995 27.4097 7.93028 28.52 9.29995 28.52Z"
                      fill="white"
                    />
                    <path
                      d="M29.14 20.46H9.77802L10.1916 19.7892C10.3689 19.5015 10.421 19.1537 10.336 18.8263L9.9324 17.2726L27.9019 16.3389C28.5832 16.3041 29.14 15.717 29.14 15.035V6.82C29.14 6.138 28.582 5.58 27.9 5.58H6.89378L6.65136 4.64814C6.5823 4.38236 6.42696 4.14703 6.20973 3.97905C5.99249 3.81108 5.72564 3.71996 5.45104 3.72H1.24C0.911132 3.72 0.595733 3.85064 0.363188 4.08319C0.130643 4.31573 0 4.63113 0 4.96C0 5.28887 0.130643 5.60427 0.363188 5.83681C0.595733 6.06936 0.911132 6.2 1.24 6.2H4.49252L7.80332 18.9379L6.50132 21.049C6.38551 21.2368 6.32198 21.4522 6.31729 21.6728C6.3126 21.8934 6.36693 22.1113 6.47466 22.3039C6.58195 22.4967 6.73882 22.6573 6.92904 22.7692C7.11925 22.881 7.3359 22.94 7.55656 22.94H29.14C29.4689 22.94 29.7843 22.8094 30.0168 22.5768C30.2494 22.3443 30.38 22.0289 30.38 21.7C30.38 21.3711 30.2494 21.0557 30.0168 20.8232C29.7843 20.5906 29.4689 20.46 29.14 20.46Z"
                      fill="white"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_256_795">
                      <rect width="31" height="31" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <div class="regionTitleText">
                  {{Locales["DIVINGMARKET"]}}
                  <p>Choose</p>
                </div>
              </div>
              <div class="searchInputBox">
                <input
                  type="text"
                  class="searchInput"
                  :placeholder="Locales['SEARCHITEM']"
                  v-model="marketSearch"
                />
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width=".7292vw"
                  height=".7813vw"
                  viewBox="0 0 14 15"
                  fill="none"
                >
                  <path
                    d="M11.025 12.5625L7.48125 9.01875C7.2 9.24375 6.87656 9.42187 6.51094 9.55312C6.14531 9.68437 5.75625 9.75 5.34375 9.75C4.32188 9.75 3.45713 9.396 2.7495 8.688C2.04188 7.98 1.68788 7.11525 1.6875 6.09375C1.68713 5.07225 2.04113 4.2075 2.7495 3.4995C3.45788 2.7915 4.32263 2.4375 5.34375 2.4375C6.36488 2.4375 7.22981 2.7915 7.93856 3.4995C8.64731 4.2075 9.00113 5.07225 9 6.09375C9 6.50625 8.93438 6.89531 8.80313 7.26094C8.67188 7.62656 8.49375 7.95 8.26875 8.23125L11.8125 11.775L11.025 12.5625ZM5.34375 8.625C6.04688 8.625 6.64463 8.379 7.137 7.887C7.62938 7.395 7.87538 6.79725 7.875 6.09375C7.87463 5.39025 7.62863 4.79269 7.137 4.30106C6.64538 3.80944 6.04763 3.56325 5.34375 3.5625C4.63988 3.56175 4.04231 3.80794 3.55106 4.30106C3.05981 4.79419 2.81363 5.39175 2.8125 6.09375C2.81138 6.79575 3.05756 7.3935 3.55106 7.887C4.04456 8.3805 4.64213 8.6265 5.34375 8.625Z"
                    fill="white"
                    fill-opacity="0.62"
                  />
                </svg>
              </div>
            </div>
            <div class="marketList">
              <div class="itemBox" v-for="(value, key) in marketData">
                <div class="itemTopSide">
                  <div class="itemNameBox">
                    <h2 class="itemName">{{value.itemLabel}}</h2>
                    <p class="itemSubName">{{Locales["BUYITEM"]}}</p>
                  </div>
                  <h2 class="itemKg">{{value.itemKG}} Kg</h2>
                </div>
                <img :src="value.itemImage" class="itemImg" />
                <div class="itemBuyButtonBox">
                  <div class="itemAmountBox">
                    <div
                      class="itemAmountButton"
                      @click="changeMarketCount('remove', value, key)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 16 16"
                        fill="none"
                      >
                        <path
                          d="M12.537 8.64685H3.46289V7.35056H12.537V8.64685Z"
                        />
                      </svg>
                    </div>
                    <h2 class="itemAmountText">x{{value.itemCount}}</h2>
                    <div
                      class="itemAmountButton"
                      @click="changeMarketCount('add', value, key)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 14 14"
                        fill="none"
                      >
                        <path
                          d="M10.8889 7.55444H7.55553V10.8878H6.44442V7.55444H3.11108V6.44333H6.44442V3.11H7.55553V6.44333H10.8889V7.55444Z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="itemBuyButton" @click="BuyItems(value, key)">
                    {{serverMoneyType}} {{formatNumber(value.itemPrice *
                    value.itemCount)}}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- "jobSide" burası anamenü divi -->
          <div class="jobSide" v-if="currentScreen === 'jobScreen'">
            <div class="regionBoxs">
              <div class="regionTopSide">
                <div class="regionTitle">
                  <svg
                    width="1.7188vw"
                    height="1.7188vw"
                    viewBox="0 0 33 33"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g id="mdi:location">
                      <path
                        id="Vector"
                        d="M16.5 15.8125C15.5883 15.8125 14.714 15.4503 14.0693 14.8057C13.4247 14.161 13.0625 13.2867 13.0625 12.375C13.0625 11.4633 13.4247 10.589 14.0693 9.94432C14.714 9.29966 15.5883 8.9375 16.5 8.9375C17.4117 8.9375 18.286 9.29966 18.9307 9.94432C19.5753 10.589 19.9375 11.4633 19.9375 12.375C19.9375 12.8264 19.8486 13.2734 19.6758 13.6905C19.5031 14.1075 19.2499 14.4865 18.9307 14.8057C18.6115 15.1249 18.2325 15.3781 17.8155 15.5508C17.3984 15.7236 16.9514 15.8125 16.5 15.8125ZM16.5 2.75C13.9473 2.75 11.4991 3.76406 9.6941 5.5691C7.88906 7.37413 6.875 9.82229 6.875 12.375C6.875 19.5938 16.5 30.25 16.5 30.25C16.5 30.25 26.125 19.5938 26.125 12.375C26.125 9.82229 25.1109 7.37413 23.3059 5.5691C21.5009 3.76406 19.0527 2.75 16.5 2.75Z"
                        fill="white"
                      />
                    </g>
                  </svg>
                  <div class="regionTitleText">
                    Select job region
                    {{Locales["SELECT_JOB_REGION"]}}
                    <p>{{Locales["CHOOSE"]}}</p>
                  </div>
                </div>
                <div class="regionTitleText" style="align-items: flex-end">
                  <p>{{Locales["TOTAL_REGION"]}}</p>
                  {{Object.values(missionData).length}} {{Locales["REGIONS"]}}
                </div>
              </div>

              <swiper-container
                class="regionList"
                space-between="15.5"
                slides-per-view="4"
              >
                <swiper-slide
                  class="regionBox"
                  v-for="(value, key) in missionData"
                  :key="key"
                  @click="selectMission(value, key)"
                  :style="{
                    'opacity': selectJobData && value.regionName === selectJobData.regionName ? 1 : (selectJobData ? 0.5 : 1)
                  }"
                  >
                  <div class="regionNameBox">
                    <div class="regionNameIcon">
                      <svg
                        width="0.8vw"
                        height="0.8vw"
                        viewBox="0 0 33 33"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g id="mdi:location">
                          <path
                            id="Vector"
                            d="M16.5 15.8125C15.5883 15.8125 14.714 15.4503 14.0693 14.8057C13.4247 14.161 13.0625 13.2867 13.0625 12.375C13.0625 11.4633 13.4247 10.589 14.0693 9.94432C14.714 9.29966 15.5883 8.9375 16.5 8.9375C17.4117 8.9375 18.286 9.29966 18.9307 9.94432C19.5753 10.589 19.9375 11.4633 19.9375 12.375C19.9375 12.8264 19.8486 13.2734 19.6758 13.6905C19.5031 14.1075 19.2499 14.4865 18.9307 14.8057C18.6115 15.1249 18.2325 15.3781 17.8155 15.5508C17.3984 15.7236 16.9514 15.8125 16.5 15.8125ZM16.5 2.75C13.9473 2.75 11.4991 3.76406 9.6941 5.5691C7.88906 7.37413 6.875 9.82229 6.875 12.375C6.875 19.5938 16.5 30.25 16.5 30.25C16.5 30.25 26.125 19.5938 26.125 12.375C26.125 9.82229 25.1109 7.37413 23.3059 5.5691C21.5009 3.76406 19.0527 2.75 16.5 2.75Z"
                            fill="#FFFFFF99"
                          />
                        </g>
                      </svg>
                    </div>
                    <h2 class="regionName">{{value.regionLabel}}</h2>
                  </div>
                  <div class="regionInfo">
                    <div class="regionInfoTextBox">
                      <h2 class="regionInfoText">{{Locales["REGIONS"]}} {{key + 1}}</h2>
                      <p class="regionInfoSubText">{{value.regionInfo}}</p>
                    </div>
                    <div class="regionInfos">
                      <div class="regionInfosBox" v-if="value.minLevel == 0 ">

                        {{Locales["NO_LEVEL_REQUIRED"]}}
                      </div>
                      <div class="regionInfosBox" v-else>
                        {{Locales["LEVEL"]}} {{value.minLevel}} {{Locales["Required"]}}
                      </div>
                      <div class="regionInfosBox regionInfosPlayer">
                        {{Locales["1-4PLAYER"]}}
                      </div>
                    </div>
                  </div>
                  <div class="regionAwardBox">
                    {{Locales["AWARDS"]}}
                    <div class="regionAwardList">
                      <div class="regionAward" v-if="value.awards">
                        {{serverMoneyType}}{{formatNumber(value.awards.money)}}
                      </div>
                      <div class="regionAward expAward" v-if="value.awards">
                        {{formatNumber(value.awards.xp)}}EXP
                      </div>
                    </div>
                  </div>
                  <img
                    src="./img/region1.png"
                    alt=""
                    class="regionBG region1"
                  />
                </swiper-slide>
              </swiper-container>
            </div>
            <div class="playerListBox">
              <div class="playerListTitle">
                <svg
                  width="1.5104vw"
                  height="1.5104vw"
                  viewBox="0 0 29 29"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="mdi:users">
                    <path
                      id="Vector"
                      d="M19.3333 20.5416V22.9583H2.41663V20.5416C2.41663 20.5416 2.41663 15.7083 10.875 15.7083C19.3333 15.7083 19.3333 20.5416 19.3333 20.5416ZM15.1041 9.06248C15.1041 8.22603 14.8561 7.40837 14.3914 6.71288C13.9267 6.0174 13.2662 5.47534 12.4934 5.15524C11.7206 4.83515 10.8703 4.75139 10.0499 4.91458C9.22951 5.07776 8.47595 5.48055 7.88449 6.07201C7.29303 6.66347 6.89024 7.41703 6.72705 8.23741C6.56387 9.05779 6.64762 9.90813 6.96772 10.6809C7.28781 11.4537 7.82988 12.1142 8.52536 12.5789C9.22084 13.0436 10.0385 13.2916 10.875 13.2916C11.9966 13.2916 13.0723 12.8461 13.8654 12.053C14.6586 11.2598 15.1041 10.1841 15.1041 9.06248ZM19.2608 15.7083C20.0036 16.2832 20.6114 17.0138 21.0415 17.8489C21.4715 18.6839 21.7134 19.6031 21.75 20.5416V22.9583H26.5833V20.5416C26.5833 20.5416 26.5833 16.1554 19.2608 15.7083ZM18.125 4.83332C17.2932 4.82868 16.4798 5.07735 15.7929 5.54623C16.5269 6.57179 16.9215 7.80133 16.9215 9.06248C16.9215 10.3236 16.5269 11.5532 15.7929 12.5787C16.4798 13.0476 17.2932 13.2963 18.125 13.2916C19.2466 13.2916 20.3223 12.8461 21.1154 12.053C21.9086 11.2598 22.3541 10.1841 22.3541 9.06248C22.3541 7.94084 21.9086 6.86513 21.1154 6.07201C20.3223 5.27889 19.2466 4.83332 18.125 4.83332Z"
                      fill="white"
                    />
                  </g>
                </svg>
                {{Locales["PLAYER_LIST"]}}
              </div>
              <div class="playerList">
                <div v-for="(value, key) in 4" :key="key">
                  <div
                    v-if="playerList && playerList[key]"
                    :class="playerList[key].owner == true ? 'playerBox ownerPlayer' : 'playerBox'"
                    @click="firePlayer(playerList[key].identifier, key)"
                  >
                    <div
                      class="playerImg"
                      :style="`background-image: url('${ playerList[key].image}')`"
                    ></div>
                    <div class="playerTextBox">
                      <h2 class="playerName">
                        {{playerList[key].name}}
                        <span v-if="playerList[key].owner"> ({{Locales["OWNER"]}})</span>
                      </h2>
                      <div class="playerLevel">
                        <div class="playerLevelLine"></div>
                        <h2 class="playerLevelText">
                          {{Locales["LEVEL"]}} {{playerList[key].level}}
                        </h2>
                      </div>
                    </div>
                  </div>
                  <div v-else>
                    <div v-if="this.invitePlayer[key]">
                      <div class="playerBox invitePlayer">
                        <div class="input-wrapper">
                          <input
                            type="text"
                            placeholder="player ID..."
                            v-model="inviteModal"
                            class="modern-input"
                          />
                        </div>
                        <div class="inviteButton" @click="InvitePlayer(key)">
                          <svg
                            viewBox="0 0 25 25"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <g id="ph:plus-fill">
                              <path
                                id="Vector"
                                d="M20.3125 3.125H4.6875C4.2731 3.125 3.87567 3.28962 3.58265 3.58265C3.28962 3.87567 3.125 4.2731 3.125 4.6875V20.3125C3.125 20.7269 3.28962 21.1243 3.58265 21.4174C3.87567 21.7104 4.2731 21.875 4.6875 21.875H20.3125C20.7269 21.875 21.1243 21.7104 21.4174 21.4174C21.7104 21.1243 21.875 20.7269 21.875 20.3125V4.6875C21.875 4.2731 21.7104 3.87567 21.4174 3.58265C21.1243 3.28962 20.7269 3.125 20.3125 3.125ZM17.9688 13.2812H13.2812V17.9688C13.2812 18.176 13.1989 18.3747 13.0524 18.5212C12.9059 18.6677 12.7072 18.75 12.5 18.75C12.2928 18.75 12.0941 18.6677 11.9476 18.5212C11.8011 18.3747 11.7188 18.176 11.7188 17.9688V13.2812H7.03125C6.82405 13.2812 6.62534 13.1989 6.47882 13.0524C6.33231 12.9059 6.25 12.7072 6.25 12.5C6.25 12.2928 6.33231 12.0941 6.47882 11.9476C6.62534 11.8011 6.82405 11.7188 7.03125 11.7188H11.7188V7.03125C11.7188 6.82405 11.8011 6.62534 11.9476 6.47882C12.0941 6.33231 12.2928 6.25 12.5 6.25C12.7072 6.25 12.9059 6.33231 13.0524 6.47882C13.1989 6.62534 13.2812 6.82405 13.2812 7.03125V11.7188H17.9688C18.176 11.7188 18.3747 11.8011 18.5212 11.9476C18.6677 12.0941 18.75 12.2928 18.75 12.5C18.75 12.7072 18.6677 12.9059 18.5212 13.0524C18.3747 13.1989 18.176 13.2812 17.9688 13.2812Z"
                              />
                            </g>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <div
                        class="playerBox playerInvite"
                        @click="openInveteModel(this.playerData.identifier, key)"
                      >
                        <svg
                          width="1.3021vw"
                          height="1.3021vw"
                          viewBox="0 0 25 25"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g id="ph:plus-fill">
                            <path
                              id="Vector"
                              d="M20.3125 3.125H4.6875C4.2731 3.125 3.87567 3.28962 3.58265 3.58265C3.28962 3.87567 3.125 4.2731 3.125 4.6875V20.3125C3.125 20.7269 3.28962 21.1243 3.58265 21.4174C3.87567 21.7104 4.2731 21.875 4.6875 21.875H20.3125C20.7269 21.875 21.1243 21.7104 21.4174 21.4174C21.7104 21.1243 21.875 20.7269 21.875 20.3125V4.6875C21.875 4.2731 21.7104 3.87567 21.4174 3.58265C21.1243 3.28962 20.7269 3.125 20.3125 3.125ZM17.9688 13.2812H13.2812V17.9688C13.2812 18.176 13.1989 18.3747 13.0524 18.5212C12.9059 18.6677 12.7072 18.75 12.5 18.75C12.2928 18.75 12.0941 18.6677 11.9476 18.5212C11.8011 18.3747 11.7188 18.176 11.7188 17.9688V13.2812H7.03125C6.82405 13.2812 6.62534 13.1989 6.47882 13.0524C6.33231 12.9059 6.25 12.7072 6.25 12.5C6.25 12.2928 6.33231 12.0941 6.47882 11.9476C6.62534 11.8011 6.82405 11.7188 7.03125 11.7188H11.7188V7.03125C11.7188 6.82405 11.8011 6.62534 11.9476 6.47882C12.0941 6.33231 12.2928 6.25 12.5 6.25C12.7072 6.25 12.9059 6.33231 13.0524 6.47882C13.1989 6.62534 13.2812 6.82405 13.2812 7.03125V11.7188H17.9688C18.176 11.7188 18.3747 11.8011 18.5212 11.9476C18.6677 12.0941 18.75 12.2928 18.75 12.5C18.75 12.7072 18.6677 12.9059 18.5212 13.0524C18.3747 13.1989 18.176 13.2812 17.9688 13.2812Z"
                              fill="white"
                              fill-opacity="0.51"
                            />
                          </g>
                        </svg>
                        {{Locales["PLAYER_INVITE"]}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="detailBox" v-if="selectJobData">
              <div class="detailTitle">
                <svg
                  width="1.1458vw"
                  height="1.1458vw"
                  viewBox="0 0 22 22"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="bxs:detail">
                    <path
                      id="Vector"
                      d="M17.8462 2.677H3.56928C3.09597 2.677 2.64205 2.86502 2.30737 3.1997C1.97269 3.53438 1.78467 3.98831 1.78467 4.46162V16.9539C1.78467 17.4272 1.97269 17.8812 2.30737 18.2158C2.64205 18.5505 3.09597 18.7385 3.56928 18.7385H17.8462C18.3195 18.7385 18.7734 18.5505 19.1081 18.2158C19.4428 17.8812 19.6308 17.4272 19.6308 16.9539V4.46162C19.6308 3.98831 19.4428 3.53438 19.1081 3.1997C18.7734 2.86502 18.3195 2.677 17.8462 2.677ZM9.81544 15.1693H4.46159V13.3847H9.81544V15.1693ZM16.9539 11.6001H4.46159V9.81546H16.9539V11.6001ZM16.9539 8.03085H4.46159V6.24623H16.9539V8.03085Z"
                      fill="white"
                    />
                  </g>
                </svg>
                {{Locales["JOB_DETAILS"]}}
              </div>
              <div class="detailTextBox">
                <div class="detailTexts">
                  <h2 class="detailText">{{Locales["JOB_STEP_ONE"]}}</h2>
                  <p class="detailSubText">{{selectJobData.regionStep}}</p>
                </div>
                <div class="detailTexts">
                  <h2 class="detailText">{{Locales["JOB_STEP_TWO"]}}</h2>
                  <p class="detailSubText" style="width: 15.2083vw">
                    {{selectJobData.regionStep2}}
                  </p>
                </div>
              </div>
              <div class="rewardBox">
                <div class="rewardTopBox">{{Locales["REWARDS_RECEIVE"]}}</div>
                <div class="reward">
                  {{Locales["MONEY"]}}
                  <h2 class="rewardText">
                    {{serverMoneyType}}
                    {{formatNumber(selectJobData.awards.money)}}
                  </h2>
                </div>
                <div class="reward expReward">
                  {{Locales["EXP"]}}
                  <h2 class="rewardText">
                    {{formatNumber(selectJobData.awards.xp)}} {{Locales["EXP"]}}
                  </h2>
                </div>
              </div>
            </div>
            <div v-else>
              <div class="detailBox">
                <div class="detailTitle">
                  <svg
                    width="1.1458vw"
                    height="1.1458vw"
                    viewBox="0 0 22 22"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g id="bxs:detail">
                      <path
                        id="Vector"
                        d="M17.8462 2.677H3.56928C3.09597 2.677 2.64205 2.86502 2.30737 3.1997C1.97269 3.53438 1.78467 3.98831 1.78467 4.46162V16.9539C1.78467 17.4272 1.97269 17.8812 2.30737 18.2158C2.64205 18.5505 3.09597 18.7385 3.56928 18.7385H17.8462C18.3195 18.7385 18.7734 18.5505 19.1081 18.2158C19.4428 17.8812 19.6308 17.4272 19.6308 16.9539V4.46162C19.6308 3.98831 19.4428 3.53438 19.1081 3.1997C18.7734 2.86502 18.3195 2.677 17.8462 2.677ZM9.81544 15.1693H4.46159V13.3847H9.81544V15.1693ZM16.9539 11.6001H4.46159V9.81546H16.9539V11.6001ZM16.9539 8.03085H4.46159V6.24623H16.9539V8.03085Z"
                        fill="white"
                      />
                    </g>
                  </svg>
                  {{Locales["JOB_DETAILS"]}}
                </div>
                <div class="noDetail">
                  <svg
                    width="1.5104vw"
                    height="1.5625vw"
                    viewBox="0 0 29 30"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g id="bxs:detail">
                      <path
                        id="Vector"
                        d="M24.167 4.125H4.83366C4.19272 4.125 3.57803 4.37961 3.12482 4.83283C2.6716 5.28604 2.41699 5.90073 2.41699 6.54167V23.4583C2.41699 24.0993 2.6716 24.714 3.12482 25.1672C3.57803 25.6204 4.19272 25.875 4.83366 25.875H24.167C24.8079 25.875 25.4226 25.6204 25.8758 25.1672C26.329 24.714 26.5837 24.0993 26.5837 23.4583V6.54167C26.5837 5.90073 26.329 5.28604 25.8758 4.83283C25.4226 4.37961 24.8079 4.125 24.167 4.125ZM13.292 21.0417H6.04199V18.625H13.292V21.0417ZM22.9587 16.2083H6.04199V13.7917H22.9587V16.2083ZM22.9587 11.375H6.04199V8.95833H22.9587V11.375Z"
                        fill="white"
                        fill-opacity="0.42"
                      />
                    </g>
                  </svg>
                  <h2 class="noDetailText">{{Locales["SELECT_REGION"]}}</h2>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- "startButton" burası anamenü divi -->
        <div
          class="startButton"
          v-if="currentScreen === 'jobScreen'"
          @click="startJob"
        >
          <img src="./img/buttonImg.png" alt="" class="buttonImg" />
          <h2 class="startText">{{ jobStart ? Locales['JOB_RESET'] : Locales['JOB_START'] }}</h2>
        </div>
        <img src="./img/bgEffect.png" alt="" class="bgEffect" />
        <img src="./img/leftEffect.png" alt="" class="leftEffect" />
      </div>
      <div class="requestMenu" v-show="requestMenuShow">
        <h2 class="requestTitle">{{Locales["INVITE_REQUIEST"]}}</h2>
        <p class="requestText" v-if="this.reqestHostName">
          {{Locales["INVITE_TEXT"]}} {{reqestHostName}}
        </p>
        <div class="requestButtonList">
          <div class="denyButton requestButton" @click="declineInvite">
            {{Locales["DENY"]}}
          </div>
          <div class="acceptButton requestButton" @click="acceptInvite">
            {{Locales["ACCEPT"]}}
          </div>
        </div>
      </div>
    </div>
    <script src="https://code.jquery.com/jquery-1.12.4.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script src="https://unpkg.com/swiper@5.3.8/js/swiper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-element-bundle.min.js"></script>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/howler/2.1.1/howler.min.js"
      type="text/javascript"
    ></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="./js/app.js" type="module"></script>
  </body>
</html>
