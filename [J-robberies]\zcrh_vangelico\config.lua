Config = {}
Config.Locale = 'en'

Config.RequiredCopsRob = 6
Config.MinJewels = 10
Config.MaxJewels = 14
Config.MaxWindows = 12
Config.SecBetwNextRob = 2100 --20 mins
Config.EnableMarker = true

-- Bag system removed - no longer required

Stores = {
	["jewelry"] = {
		position = { ['x'] = -629.99, ['y'] = -236.542, ['z'] = 38.05 },
		nameofstore = "Crastenburg Jewelry",
		lastrobbed = 0
	}
}

-- Heist Notification Configuration
Config.HeistNotifications = {
	Success = {
		Use = true,
		missionTextLabel = "~y~JEWELRY HEIST~s~",
		passFailTextLabel = "COMPLETED.",
		messageLabel = "Jewel cases smashed! Handle the cops!"
	},
	Failed = {
		Use = true,
		missionTextLabel = "~y~JEWELRY HEIST~s~",
		passFailTextLabel = "FAILED.",
		messageLabel = "The heist was unsuccessful."
	}
}