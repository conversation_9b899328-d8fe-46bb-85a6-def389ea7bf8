RegisterServerEvent('yachtheist:server:policeAlert')
AddEventHandler('yachtheist:server:policeAlert', function(coords)
    if Config['YachtHeist']['framework']['name'] == 'ESX' then
        for k, v in pairs(Config['YachtHeist']['dispatchJobs']) do
            local players = ESX.GetExtendedPlayers('job', v)
            for i = 1, #players do
                TriggerClientEvent('yachtheist:client:policeAlert', players[i].source, coords)
            end
        end
    elseif Config['YachtHeist']['framework']['name'] == 'QB' then
        local players = QBCore.Functions.GetPlayers()
        for i = 1, #players do
            local player = QBCore.Functions.GetPlayer(players[i])
            for k, v in pairs(Config['YachtHeist']['dispatchJobs']) do
                if player.PlayerData.job.name == v then
                    TriggerClientEvent('yachtheist:client:policeAlert', players[i], coords)
                end
            end
        end
    end
end)

discord = {
    ['webhook'] = 'https://ptb.discord.com/api/webhooks/1006426207035854920/g9LAtMVSa3a4q5sYvH0Yn4xKQI89AgzcdHTZfLJGIL_mcE-ebRXiT5mxJO9F8cPQ_P2M',
    ['name'] = 'rm_yachtheist',
    ['image'] = 'https://cdn.discordapp.com/avatars/869260464775921675/dff6a13a5361bc520ef126991405caae.png?size=1024'
}

function discordLog(name, message)
    local data = {
        {
            ["color"] = '3553600',
            ["title"] = "**".. name .."**",
            ["description"] = message,
        }
    }
    PerformHttpRequest(discord['webhook'], function(err, text, headers) end, 'POST', json.encode({username = discord['name'], embeds = data, avatar_url = discord['image']}), { ['Content-Type'] = 'application/json' })
end

-- Track player rewards for accurate notifications
local playerRewards = {}

-- Function to track rewards (call this when giving rewards to players)
function trackYachtReward(source, rewardType, rewardData)
    if not playerRewards[source] then
        playerRewards[source] = {
            totalMoney = 0,
            items = {},
            artifacts = {}
        }
    end

    if rewardType == 'money' then
        playerRewards[source].totalMoney = playerRewards[source].totalMoney + (rewardData.amount or 0)
    elseif rewardType == 'gold' or rewardType == 'weed' or rewardType == 'coke' then
        local itemName = rewardData.itemName or rewardType
        if not playerRewards[source].items[itemName] then
            playerRewards[source].items[itemName] = 0
        end
        playerRewards[source].items[itemName] = playerRewards[source].items[itemName] + (rewardData.count or 0)
    elseif rewardType == 'artifact' then
        local artifactName = rewardData.itemName or 'artifact'
        if not playerRewards[source].artifacts[artifactName] then
            playerRewards[source].artifacts[artifactName] = 0
        end
        playerRewards[source].artifacts[artifactName] = playerRewards[source].artifacts[artifactName] + 1
    end
end

-- Function to show heist completion with actual rewards
function showYachtHeistSuccess(source)
    if not playerRewards[source] then return end

    if Config['YachtHeist']['HeistNotifications'].Success.Use then
        local rewards = {}

        -- Add money rewards
        if playerRewards[source].totalMoney > 0 then
            table.insert(rewards, {
                stat = "Money Collected",
                value = "~g~$" .. playerRewards[source].totalMoney
            })
        end

        -- Add item rewards
        for itemName, count in pairs(playerRewards[source].items) do
            if count > 0 then
                table.insert(rewards, {
                    stat = string.upper(itemName:sub(1,1)) .. itemName:sub(2) .. " Collected",
                    value = "~g~" .. count .. "x"
                })
            end
        end

        -- Add artifact rewards
        for artifactName, count in pairs(playerRewards[source].artifacts) do
            if count > 0 then
                table.insert(rewards, {
                    stat = string.upper(artifactName:sub(1,1)) .. artifactName:sub(2) .. " Collected",
                    value = "~g~" .. count .. "x"
                })
            end
        end

        -- Show notification if there are rewards
        if #rewards > 0 then
            TriggerClientEvent('heist-notify:show', source, Config['YachtHeist']['HeistNotifications'].Success, rewards, 7, true)
        end
    end

    -- Clear player rewards
    playerRewards[source] = nil
end

-- HMS Integration Events
-- NOTE: The main yacht heist script should call these events:
-- TriggerEvent('yachtheist:hms:heistStarted') - when heist starts
-- TriggerEvent('yachtheist:hms:heistEnded', playerSource) - when heist completes successfully
-- TriggerEvent('yachtheist:hms:heistFailed', reason) - when heist fails
-- exports['rm_yachtheist']:trackYachtReward(source, rewardType, rewardData) - when giving rewards

RegisterServerEvent('yachtheist:hms:heistStarted')
AddEventHandler('yachtheist:hms:heistStarted', function()
    -- Notify HMS that yacht heist started
    TriggerEvent('hms:heistStarted', 'yachtheist')
end)

RegisterServerEvent('yachtheist:hms:heistEnded')
AddEventHandler('yachtheist:hms:heistEnded', function(playerSource)
    -- Show success notification with actual rewards
    if playerSource then
        showYachtHeistSuccess(playerSource)
    end

    -- Notify HMS that yacht heist ended
    TriggerEvent('hms:heistEnded', 'yachtheist')
    -- Update HMS with cooldown data (7200 seconds = 120 minutes)
    TriggerEvent('hms:updateCooldown', 'yachtheist', os.time(), 7200)
end)

RegisterServerEvent('yachtheist:hms:heistFailed')
AddEventHandler('yachtheist:hms:heistFailed', function(reason)
    -- Show failure notification
    if Config['YachtHeist']['HeistNotifications'].Failed.Use then
        TriggerClientEvent('heist-notify:show', source, Config['YachtHeist']['HeistNotifications'].Failed, nil, 5, true)
    end

    -- Notify HMS that yacht heist failed
    TriggerEvent('hms:heistFailed', 'yachtheist')
end)

-- Export functions for the main script to call
exports('trackYachtReward', trackYachtReward)
exports('showYachtHeistSuccess', showYachtHeistSuccess)