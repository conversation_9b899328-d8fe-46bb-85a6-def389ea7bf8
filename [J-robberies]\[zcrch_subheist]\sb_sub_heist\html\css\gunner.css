@keyframes slide-in-bottom {
  0% {
    transform: translateY(200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slide-in-top {
  0% {
    transform: translateY(-200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes rotate-center {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
@keyframes custom-animation {
  0% {
    opacity: 0.8;
    transform: scale(0.2);
  }
  80% {
    opacity: 0;
    transform: scale(1.2);
  }
  to {
    opacity: 0;
    transform: scale(2.2);
  }
}
@keyframes slide-in-left {
  0% {
    transform: translateX(-200px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.image-11-icon {
  top: 0;
  left: -981px;
  width: 3016px;
  height: 1692px;
  object-fit: cover;
  display: none;
}
.div,
.ellgunner,
.image-11-icon {
  position: absolute;
}
.ellgunner {
  top: 428px;
  left: 1194px;
  border-radius: 50%;
  background-color: #aeaeae;
  filter: blur(250px);
  width: 841px;
  height: 841px;
}
.div {
  top: 0;
  left: 0;
  width: 1925px;
  height: 1083px;
  mix-blend-mode: screen;
}
.image-12-icon {
  position: absolute;
  top: 14px;
  left: 1px;
  width: 1920px;
  height: 1080px;
  object-fit: cover;
  display: none;
}
.busniess-web-banner-02sds-1-icon {
  position: absolute;
  top: -476.9px;
  left: -961px;
  width: 2899.5px;
  height: 1631.8px;
  object-fit: cover;
}
.rguner3sdsdsd-icon,
.rgunnershp56dffsfg-icon,
.torgunneshp2dsd-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  opacity: 0;
}
.rguner3sdsdsd-icon {
  height: 77.13%;
  width: 100.41%;
  top: 24.17%;
  right: -0.3%;
  bottom: -1.3%;
  left: -0.1%;
  object-fit: contain;
}
.rguner3sdsdsd-icon.animate {
  animation: 1s ease 0s 1 normal forwards slide-in-bottom;
}
.rgunnershp56dffsfg-icon,
.torgunneshp2dsd-icon {
  height: 71.11%;
  width: 99.9%;
  top: 28.98%;
  right: 0.1%;
  bottom: -0.09%;
  left: 0;
}
.torgunneshp2dsd-icon.animate {
  animation: 1s ease 1s 1 normal forwards slide-in-bottom;
}
.rgunnershp56dffsfg-icon {
  height: 57.96%;
  width: 100.26%;
  top: 42.13%;
  right: -0.26%;
}
.rgunnershp56dffsfg-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-bottom;
}
.frame-3201gunner-icon {
  position: absolute;
  top: -70px;
  left: 1392px;
  width: 561.1px;
  height: 1424.3px;
  opacity: 0;
}
.frame-3201gunner-icon.animate {
  animation: 9s ease 3s 1 normal forwards slide-in-top;
}
.gunner-icon,
.gunner2-icon,
.gunner3-icon,
.gunner8-icon,
.gunnerr4-icon {
  position: absolute;
  height: 5.11%;
  width: 4.34%;
  top: 71.04%;
  right: 41.9%;
  bottom: 23.85%;
  left: 53.76%;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.gunner-icon,
.gunner3-icon,
.gunner8-icon,
.gunnerr4-icon {
  height: 22.57%;
  width: 2.68%;
  top: 33.53%;
  right: 4.85%;
  bottom: 43.9%;
  left: 92.47%;
  opacity: 0;
}
.gunner8-icon.animate {
  animation: 1s ease 2s 1 normal forwards slide-in-top;
}
.gunner-icon,
.gunner3-icon,
.gunnerr4-icon {
  height: 2.11%;
  width: 1.79%;
  top: 76.15%;
  right: 21.8%;
  bottom: 21.73%;
  left: 76.4%;
  opacity: 1;
}
.gunner-icon.animate {
  animation: 1s ease 0s infinite normal forwards rotate-center;
}
.gunner3-icon,
.gunnerr4-icon {
  top: 45.88%;
  right: 89.73%;
  bottom: 52.01%;
  left: 8.48%;
}
.gunner2-icon1.animate,
.gunner3-icon.animate,
.gunnerr4-icon.animate {
  animation: 2s ease 0s infinite normal forwards rotate-center;
}
.gunner3-icon {
  height: 4.04%;
  width: 3.43%;
  top: 24.19%;
  right: 78.86%;
  bottom: 71.77%;
  left: 17.7%;
}
.gunner2-icon1 {
  height: 4.38%;
  width: 3.72%;
  top: 43.68%;
  right: 12.29%;
  bottom: 51.94%;
  left: 83.99%;
  opacity: 1;
}
.gunner1-icon,
.gunner2-icon1,
.gunnerselected-icon,
.gunnervector1-icon {
  position: absolute;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.gunner1-icon {
  height: 4.96%;
  width: 4.21%;
  top: 77.21%;
  right: 27.49%;
  bottom: 17.83%;
  left: 68.3%;
  opacity: 0;
}
.gunner1-icon.animate {
  animation: 1s ease 0s infinite normal forwards custom-animation;
}
.gunnerselected-icon,
.gunnervector1-icon {
  height: 2.8%;
  width: 2.38%;
  top: 80.45%;
  right: 64.27%;
  bottom: 16.74%;
  left: 33.35%;
}
.gunnerselected-icon {
  height: 2.95%;
  width: 8.92%;
  top: 76.84%;
  right: 86.53%;
  bottom: 20.21%;
  left: 4.55%;
}
.hegunnerd-icon,
.hegunnerd-icon1 {
  height: 16.84%;
  width: 0.91%;
  max-width: 100%;
  max-height: 100%;
  opacity: 1;
}
.hegunnerd-icon {
  position: absolute;
  top: 9.4%;
  right: 93.1%;
  bottom: 73.76%;
  left: 5.99%;
  overflow: hidden;
}
.hegunnerd-icon.animate,
.hegunnerd-icon1.animate {
  animation: 1s ease-in-out 3s infinite alternate-reverse none slide-in-top;
}
.hegunnerd-icon1 {
  top: -1.5%;
  right: 4.39%;
  bottom: 84.66%;
  left: 94.71%;
}
.bomberfr,
.hegunnerd-icon1,
.vegunnerd-icon {
  position: absolute;
  overflow: hidden;
}
.vegunnerd-icon {
  height: 1.12%;
  width: 14.9%;
  top: 81.34%;
  right: 80.57%;
  bottom: 17.54%;
  left: 4.53%;
  max-width: 100%;
  max-height: 100%;
  opacity: 1;
}
.vegunnerd-icon.animate {
  animation: 1s ease-in-out 3s infinite alternate-reverse both slide-in-left;
}
.bomberfr {
  top: -35px;
  left: -37px;
  width: 1568px;
  height: 1330.2px;
}
.gunner {
  width: 100%;
  position: relative;
  height: 1080px;
  overflow: hidden;
}
