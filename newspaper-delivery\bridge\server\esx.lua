if GetResourceState('es_extended') ~= 'started' then return end

if GetResourceState('ox_inventory') ~= 'started' then
    return lib.print.error('ox inventory is required for ESX bridge unless you make the changes yourself.')
end

local ESX = exports['es_extended']:getSharedObject()

function GetPlayer(id)
    return ESX.GetPlayerFromId(id)
end

function DoNotification(src, text, nType)
    TriggerClientEvent('esx:showNotification', src, text, nType)
end

function GetPlyIdentifier(xPlayer)
    return xPlayer.identifier
end

function GetByIdentifier(cid)
    return ESX.GetPlayerFromIdentifier(cid)
end

function GetSourceFromIdentifier(cid)
    local xPlayer = ESX.GetPlayerFromIdentifier(cid)
    return xPlayer and xPlayer.source or false
end

function GetCharacterName(xPlayer)
    return xPlayer.getName()
end

function AddMoney(xPlayer, moneyType, amount)
    local account = moneyType == 'cash' and 'money' or moneyType
    xPlayer.addAccountMoney(account, amount, "newspaper-delivery")
    local data = {
        ['Player'] = xPlayer,
        ['Log'] = 'Newspaper Job', 
        ['Title'] = 'Newspaper Job Payments',
        ['Message'] = GetPlayerName(xPlayer)..' Earned '..amount, 
        ['Color'] = 'green', 
    }
    TriggerEvent('Melx_Core:ZLog', data,"https://discord.com/api/webhooks/1236978078388064256/3REOZjRZ7PiP0lfuR4CC0u3ujSvhIVgNDL_3aAkuzU5EojZosTkVVeaZsYVvcWncaCmd")
end

AddEventHandler('esx:playerLogout', function(source)
    OnServerPlayerUnload(source)
end)