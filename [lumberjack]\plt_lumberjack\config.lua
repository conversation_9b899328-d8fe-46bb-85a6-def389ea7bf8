__ = locals["EN"]                                  -- SET = ("EN" or "TR" or "Custom"), You can edit or add more languages files; in locals folder,          
PLT,locals= {},nil                                              
PLT.ChainSawSoundVolume = 7                        -- SET = (number), "1 to 100", what will be the default voice level of ChainSaw.
PLT.CommandSetVolume  = "chainsawvolume"           -- SET = ("CommandName"), the command for players to set the chainsaw voice level.
PLT.CommandFixVehicle = "fixjobvehicle"            -- SET = ("CommandName"), the command for players to fix position of the vehicle.
PLT.CommandCancelJob  = "canceljob"                -- SET = ("CommandName"), the command for players to cancel the job.
PLT.ActionCoords = vector4(-544.3577, 5262.3452, 74.2041, 158.8519) -- SET = (vector4(x,y,z,heading)), Ped,Action and Blip coordinates.
PLT.TruckFuelPercentage = 99.9                     -- SET = (float number)), Spawned truck fuel percentage when lumber delivery work.
PLT.ActionPed = "s_m_y_construct_01"               -- SET = ("ModelName"), https://docs.fivem.net/docs/game-references/ped-models/
PLT.DeveloperModeON = false                        -- SET = (true or false), WARNING! Set 'false' before activating the server for players. Set 'true' to be able to add new trees and construction points.
PLT.LessCpuUsage = false                           -- SET = (true or false), The 3 affected animations are; Falling tree, logs rolling on stand and pallet of lumber moving on stand. If set to 'false' animations are more realistic but take more cpu power, like 0.1ms. If set to 'true' Animations are less realistic but take less cpu power, like 0.04ms.
PLT.JobSettings = {
    cutting ={
        spawnAllTreesAfterRestart = true,          -- SET = (true or false), If you set 'true' all the added trees will spawn as cuttable after the restart.
        howManyMinutesAfterTreeCuttedReSpawn = 30, -- SET = (minute), The tree respawn timer.
        WhatMinimumCountOfCuttableTrees = 5,       -- SET = (number), The minimum number of the cuttable trees that will be allways on the server map. If the tree numbers are below this number, the system will automaticly spawn this many new trees without checking the respawn timer.
    },
    stacking ={
        forkliftsAutomaticallyOnTrailer = false,        -- SET = (true or false), If set to 'true' the forklifts on the trailers will not be deleted after the delivery of the trucks. If set to 'false' the forklifts on the trailers will be deleted after the delivery of the trucks. Deleting the forklifts will cause the players to extra forklift parking job.
        minimumWoodPileWhenRestart = 15,                -- SET = (number), The minimum number of the pallet of lumbers that will be after the restart of the server. If there are more pallets of lumbers before the restart, this will not effect it. If there are less than this number of pallets, server will start with this number of pallets of lumbers.
        automaticReinforcementWhenWoodPileDecrease = 0, -- SET = (minutes), If there are less number of pallet of lumbers on the Lumber Mill, the system will automaticly fill the numbers to written number by adding more pallets.
    },
    delivery ={
        MinutesAwoodpileDeleteFromEveryConstruction = 10,-- SET = (number), How many minutes will it take for one pallet of lumber to decrease from every construction zone.
    },
}

PLT.Salaries = {
    cutting = math.random(18000, 20000),                  -- SET = (money), How much cash will be given for cutting one tree and delivering the logs. Estimated time this action will take: 5 mins
    stacking ={
        eachWoodPile = math.random(2300, 2840),        -- SET = (money), How much cash will be given for loading one pallet of lumbers on to the trailer.Estimated time this action will take: 1 min
        eachForklift = math.random(2550, 3200),          -- SET = (money), How much cash will be given for parking one forklift on to the trailer. Estimated time this action will take: 15 sec or 45 sec.
    },  
    delivery ={
        truckEndOfTheJob = math.random(24000, 28000),    -- SET = (money), How much cash will be given for delivering the pallet of lumbers to constructions. Estimated time this action will take: 2-3 mins
        distancePaymentEach1Km = math.random(8000, 9000),-- SET = (money), How much cash will be given for 'each km' driven as traveling expenses. The construction distances are between 1.1 km to 8.4 km(multiply by 2 for the return path.) Estimated time this action will take: 1 min for each km.
    },
}

PLT.Blips = {  -- https://docs.fivem.net/docs/game-references/blips/
    workplace ={                        
        name = "Lumberjack Jobs",    -- SET = (Blip Name),  blip name   of work place on gps.
        sprite =77,                  -- SET = (Blip Sprite),blip sprite of work place on gps.
        scale = 0.99,                -- SET = (Blip Scale), blip scale  of work place on gps.
        colour =46,                  -- SET = (Blip Colour),blip colour of work place on gps.
    },
    cutting = {
        nameCuttable = "[Lumberjack] Cuttable Tree",                -- SET = (Blip Name),  blip name   of cuttable tree on gps.
        nameUnCuttable = "[Lumberjack] Already Cutted Tree",        -- SET = (Blip Name),  blip name   of cutted tree on gps.
        sprite =1,                                                  -- SET = (Blip Sprite),blip sprite of tree on gps.
        scale = 0.50,                                               -- SET = (Blip Scale), blip scale  of tree on gps.
        colourCuttable =2,                                          -- SET = (Blip Colour),blip colour of cutted tree on gps.
        colourUnCuttable =1,                                        -- SET = (Blip Colour),blip colour of cuttable tree on gps.
        deleteTelehandlerName = "[Lumberjack] Telehandler delivery",-- SET = (Blip Name),  blip name   of where to deliver the telehandler and complete the job on gps.
        deleteTelehandlerSprite = 651,                              -- SET = (Blip Sprite),blip sprite of where to deliver the telehandler and complete the job on gps.
        deleteTelehandlerScale = 0.75,                              -- SET = (Blip Scale), blip scale  of where to deliver the telehandler and complete the job on gps.
        deleteTelehandlerColour = 1,                                -- SET = (Blip Colour),blip colour of where to deliver the telehandler and complete the job on gps.
        deliveryWoodsName = "[Lumberjack] Logs Delivery Point",     -- SET = (Blip Name),  blip name   of where the cutted wood (logs) will be delivered on gps.
        deliveryWoodsSprite = 237,                                  -- SET = (Blip Sprite),blip sprite of where the cutted wood (logs) will be delivered on gps.
        deliveryWoodsScale = 0.75,                                  -- SET = (Blip Scale), blip scale  of where the cutted wood (logs) will be delivered on gps.
        deliveryWoodsColour = 56,                                   -- SET = (Blip Colour),blip colour of where the cutted wood (logs) will be delivered on gps.
    },
    stacking = {
        avaibleForForklift = "[Lumberjack] Forklift Can Be Parked",    -- SET = (Blip Name),  blip name   of the available truck which has space on its trailer for forklift on GPS.
        unAvaibleForForklift = "[Lumberjack] Forklift Can't Be Parked",-- SET = (Blip Name),  blip name   of the truck which has no space on its trailer for forklift on GPS..
        avaibleForWoodpile = "[Lumberjack] Lumber Can Be Load",        -- SET = (Blip Name),  blip name   of the available truck which has space on its trailer for pallet of lumbers on GPS.
        unAvaibleForWoodpile = "[Lumberjack] Lumber Can't Be Load",    -- SET = (Blip Name),  blip name   of the truck which has no space on its trailer for pallet of lumbers on GPS..
        sprite =1,                                                     -- SET = (Blip Sprite),blip sprite of truck and trailer on gps.
        scale = 0.50,                                                  -- SET = (Blip Scale), blip scale  of truck and trailer on gps.
        avaibleColour =2,                                              -- SET = (Blip Colour),blip colour of truck and trailer on gps if it has space.
        unAvaibleColour =1,                                            -- SET = (Blip Colour),blip colour of truck and trailer on gps if it doesn't has space.
        woodpileName = "[Lumberjack] Pickable Pallet of Lumber",       -- SET = (Blip Name),  blip name   of pickable pallet of lumber on gps.
        woodpileSprite = 237,                                          -- SET = (Blip Sprite),blip sprite of pickable pallet of lumber on gps.
        woodpileScale = 0.75,                                          -- SET = (Blip Scale), blip scale  of pickable pallet of lumber on gps.
        woodpileColour = 2,                                            -- SET = (Blip Colour),blip colour of pickable pallet of lumber on gps.
        deleteForkliftName = "[Lumberjack] Forklift Delivery Point",   -- SET = (Blip Name),  blip name   of where to deliver the forklift and complete the job on gps.
        deleteForkliftSprite = 651,                                    -- SET = (Blip Sprite),blip sprite of where to deliver the forklift and complete the job on gps.
        deleteForkliftScale = 0.75,                                    -- SET = (Blip Scale), blip scale  of where to deliver the forklift and complete the job on gps.
        deleteForkliftColour = 1,                                      -- SET = (Blip Colour),blip colour of where to deliver the forklift and complete the job on gps.
    },
    delivery = {
        nameDeliverable = "[Lumberjack] Construction Needs Lumbers",        -- SET = (Blip Name),  blip name   of constructions which is available to deliver pallet of lumbers on gps.
        nameUnDeliverable = "[Lumberjack] Construction Doesn't Need Lumber",-- SET = (Blip Name),  blip name   of constructions which is unavailable to deliver pallet of lumbers on gps.
        sprite =237,                                                        -- SET = (Blip Sprite),blip sprite of construction on gps.
        scale = 0.75,                                                       -- SET = (Blip Scale), blip scale  of construction on gps.
        colourDeliverable =2,                                               -- SET = (Blip Colour),blip colour of available construction on gps.
        colourUnDeliverable =1,                                             -- SET = (Blip Colour),blip colour of unavailable construction on gps.
    },
}
--[[ **** example requiredJob values *****
    requiredJob = false, --> All players regardless of job can do it.
    requiredJob = "lumberjack", --> Only the players who has 'lumberjack' can do this work.
    requiredJob = {name = "lumberjack", grade = 0}, Only the players who has 'Lumberjack grade 0' can do this work.
    requiredJob = {name = "lumberjack", grade = {0,1,2}}, Only the players who has 'Lumberjack grade 0/1/2' can do this work.
    requiredJob = {"lumberjack",{name = "police", grade = 0},{name = "driver", grade = {0,1,2}}}, 	Only the players who has 'Lumberjack, Police 0, Driver 0/1/2' can do this work.
]]
PLT.Info = {
    ["cutting"] = {
        requiredJob = false,
        telehandlerDeleteCoord =  vector3(-568.7362, 5271.447, 69.23539),   -- SET = (vector3(x,y,z)), Coordinate of where to deliver the telehandler and complete the job.
        deliveryWoodPoint = vector3(-496.2000, 5346.958, 81.40),            -- SET = (vector3(x,y,z)), DO NOT CHANGE THIS! Coordinate of logs delivery point. 
        deliveryTelehandlerPoint = vector4(-492.121, 5345.432, 81.58, 70.0),-- SET = (vector4(x,y,z,heading)), DO NOT CHANGE THIS! Coordinate of telehandler at the logs delivery. 
        telehandlerSpawnCoord ={ vector4(-588.2868, 5313.793, 69.18664, 160.7172),vector4(-586.3259, 5317.744, 69.18974, 160.6038),vector4(-585.0861, 5321.263, 69.18664, 160.5841),vector4(-583.5, 5324.924, 69.18771, 159.2242),vector4(-582.0623, 5328.699, 69.18748, 159.0216),vector4(-584.8527, 5312.465, 69.18861, 160.4053),vector4(-583.3779, 5316.087, 69.19795, 160.1181),vector4(-581.9298, 5320.368, 69.20696, 161.5383),vector4(-580.5125, 5324.054, 69.2132, 162.1688),vector4(-579.1234, 5328.287, 69.20783, 161.8894),vector4(-581.424, 5311.158, 69.18909, 160.2473),vector4(-580.4468, 5313.901, 69.18591, 160.5486),vector4(-579.4099, 5316.822, 69.1945, 160.5737),vector4(-578.4983, 5319.264, 69.18954, 163.3436),vector4(-577.5975, 5322.061, 69.19084, 161.5971),vector4(-576.6854, 5324.791, 69.18767, 161.5109),vector4(-578.579, 5310.091, 69.20319, 159.9745),vector4(-577.4261, 5313.28, 69.18895, 160.3403),vector4(-576.3934, 5316.168, 69.18915, 160.3368),vector4(-575.4583, 5318.775, 69.18978, 160.3605),vector4(-574.4438, 5321.605, 69.18946, 160.3037),vector4(-571.3571, 5332.46, 69.18926, 161.2482),vector4(-585.8737, 5304.879, 69.19073, 161.5411),vector4(-586.9597, 5301.648, 69.19585, 161.406),vector4(-587.9234, 5298.799, 69.19183, 161.2918),vector4(-588.8846, 5295.974, 69.19474, 161.2376),vector4(-590.5237, 5306.187, 69.18702, 164.9597),vector4(-592.1606, 5301.384, 69.19356, 162.4393),vector4(-593.7802, 5296.268, 69.19294, 162.5081),vector4(-594.7476, 5306.748, 69.19643, 161.8421),vector4(-596.566, 5301.418, 69.19389, 163.0143),vector4(-598.499, 5295.535, 69.19373, 164.9408),vector4(-602.0703, 5295.938, 69.20741, 176.3252),vector4(-601.5087, 5301.455, 69.18858, 171.4753),vector4(-600.4835, 5307.762, 69.19832, 175.9703),vector4(-599.362, 5313.393, 69.23081, 170.3931),vector4(-597.9982, 5319.051, 69.27753, 167.1678),vector4(-596.5364, 5324.332, 69.29095, 158.6687),vector4(-603.341, 5322.685, 69.41562, 174.2962),vector4(-602.7268, 5327.793, 69.43404, 174.4315),vector4(-602.0022, 5333.793, 69.44668, 168.1988),vector4(-600.8656, 5339.215, 69.43231, 168.2512),vector4(-599.8888, 5344.515, 69.43716, 171.4124),vector4(-576.7087, 5335.448, 69.19864, 160.4372),vector4(-577.9768, 5331.939, 69.20933, 160.0818),vector4(-575.5717, 5338.556, 69.18451, 160.0688),vector4(-574.3433, 5341.933, 69.19243, 159.973),vector4(-573.2056, 5344.885, 69.20839, 162.0803),vector4(-572.1898, 5348.041, 69.20197, 162.0719),vector4(-571.1853, 5351.159, 69.20305, 162.0705),vector4(-570.1638, 5354.322, 69.21341, 162.1238),vector4(-569.2201, 5357.258, 69.19904, 162.0323),vector4(-568.1574, 5360.525, 69.18624, 162.0431),vector4(-567.1718, 5363.554, 69.18902, 162.025),vector4(-566.2128, 5366.502, 69.18847, 162.0013),vector4(-565.2816, 5369.362, 69.18887, 161.9782),vector4(-564.3964, 5372.076, 69.18781, 161.9511),vector4(-563.475, 5374.89, 69.1833, 161.8958),vector4(-562.5884, 5377.571, 69.17714, 161.8411),vector4(-561.5306, 5380.835, 68.89951, 161.9507),vector4(-560.5527, 5383.803, 68.43446, 161.8116),vector4(-559.6185, 5386.653, 67.92539, 161.9991),vector4(-575.7661, 5327.548, 69.19786, 161.5885),vector4(-574.8743, 5330.276, 69.19872, 160.6508),vector4(-573.9803, 5332.83, 69.18829, 160.6728),vector4(-573.1159, 5335.3, 69.18821, 160.6721),vector4(-572.2971, 5337.645, 69.18864, 160.6741),vector4(-571.3715, 5340.293, 69.18931, 160.6752),vector4(-570.4525, 5342.919, 69.20657, 160.6748),vector4(-569.6253, 5345.292, 69.21341, 160.734),vector4(-568.7574, 5347.78, 69.21609, 160.8192),vector4(-567.9802, 5350.066, 69.21782, 162.8692),vector4(-567.1119, 5352.891, 69.21474, 162.9003),vector4(-566.3252, 5355.453, 69.20014, 162.8679),vector4(-565.3963, 5358.494, 69.18535, 162.8825),vector4(-564.4958, 5361.433, 69.18674, 162.9448),vector4(-563.5458, 5364.544, 69.18893, 162.9902),vector4(-562.6649, 5367.474, 69.18813, 161.7528),vector4(-561.7219, 5370.256, 69.18822, 162.8498),vector4(-560.8513, 5373.085, 69.17728, 162.8578),vector4(-560.0238, 5375.799, 69.1915, 163.1647),vector4(-559.3049, 5378.173, 69.13126, 162.8776),vector4(-558.5023, 5380.789, 68.89764, 162.7493),vector4(-557.6735, 5383.489, 68.54863, 163.1235),vector4(-561.0952, 5359.936, 69.21305, 166.0172),vector4(-560.1249, 5362.891, 69.19965, 162.2113),vector4(-559.2316, 5365.786, 69.1926, 163.4241),vector4(-558.4268, 5368.526, 69.1875, 163.6075),vector4(-557.6647, 5371.127, 69.1953, 163.7109),vector4(-556.8994, 5373.79, 69.29078, 163.7225),vector4(-556.0815, 5376.812, 69.20152, 166.2231),vector4(-555.0738, 5380.953, 68.93114, 166.1907),},
        woodStand ={[1] = {coord = vec3(-498.133667, 5345.688477, 81.632874), rot = vec3(90.0, 100.0, 160.0)},[2] = {coord = vec3(-498.133667, 5345.688477, 80.746934), rot = vec3(90.0, 150.0, 160.0)},[3] = {coord = vec3(-500.325806, 5346.401367, 80.540024), rot = vec3(90.0, 300.0, 160.0)},[4] = {coord = vec3(-505.050415, 5348.471680, 77.231514), rot = vec3(90.0, 750.0, 160.0)},[5] = {coord = vec3(-547.221558, 5363.637695, 73.182549), rot = vec3(90.0, 6100.0, 160.0)},[6] = {coord = vec3(-548.312622, 5364.116211, 72.741699), rot = vec3(90.0, 180.0, 159.399536)},[7] = {coord = vec3(-549.193481, 5361.606445, 72.741699), rot = vec3(90.0, 270.0, 159.399536)},[8] = {coord = vec3(-549.744019, 5360.112305, 72.741699), rot = vec3(90.0, 300.0, 159.399536)},[9] = {coord = vec3(-554.728882, 5346.323242, 72.770233), rot = vec3(90.0, 367.0, 159.399536)},[10] = {coord = vec3(-556.070190, 5342.739258, 73.106934), rot = vec3(94.495789, 367.0, 159.399536)},[11] = {coord = vec3(-559.403442, 5333.608398, 73.942978), rot = vec3(94.495789, 367.0, 159.399536)},[12] = {coord = vec3(-560.324341, 5330.991211, 73.811722), rot = vec3(89.294518, 367.0, 159.399536)},[13] = {coord = vec3(-563.587524, 5322.094727, 73.811722), rot = vec3(89.294518, 367.0, 159.399536)},[14] = {coord = vec3(-563.587524, 5322.094727, 73.811722), rot = vec3(89.294518, 367.0, 159.399536)},},
    },
    ["stacking"] = {
        requiredJob = false,
        forkliftDeleteCoord = vector3(-568.7362, 5271.447, 69.75),          -- SET = (vector3(x,y,z)), Coordinate of where to deliver the forklift and complete the job.
        forkliftSpawnCoords ={vector4(-512.4199, 5263.232, 80.04218, 339.6524),vector4(-513.3233, 5260.804, 80.04294, 339.4336),vector4(-514.2581, 5258.264, 80.05180, 339.1408),vector4(-515.1686, 5255.878, 80.04965, 338.9757),vector4(-516.3257, 5252.744, 79.80816, 338.7620),vector4(-517.5734, 5249.369, 79.39219, 346.8996),vector4(-510.3323, 5262.616, 80.0614, 340.2651),vector4(-511.1326, 5260.358, 80.06413, 340.6244),vector4(-512.132, 5257.524, 80.06631, 340.5287),vector4(-513.0472, 5254.953, 80.06549, 340.5627),vector4(-514.1389, 5251.925, 79.87162, 339.6548),vector4(-515.3828, 5248.711, 79.5319, 338.3526),vector4(-508.744, 5262.274, 80.06174, 340.2083),vector4(-509.7169, 5259.716, 80.06513, 340.964),vector4(-510.663, 5256.947, 80.06545, 341.1732),vector4(-511.5545, 5254.364, 80.06499, 341.1045),vector4(-512.6642, 5251.177, 79.86835, 340.0557),vector4(-513.7904, 5248.163, 79.65378, 340.5769),vector4(-514.3364, 5264.046, 79.9702, 339.5006),vector4(-515.3884, 5261.542, 79.99316, 340.3225),vector4(-516.3056, 5258.999, 80.01713, 340.3739),vector4(-517.2686, 5256.32, 80.00751, 340.561),vector4(-518.3229, 5253.545, 79.67558, 341.2562),vector4(-519.4805, 5250.553, 79.24733, 340.5996),},
    },
    ["delivery"] = {--pltpacker pltphantom pltphantom3
        requiredJob = false,
        truckTrailer ={-- You can add more truck and trailers here. Be sure to add the same format as before.  https://wiki.rage.mp/index.php?title=Vehicle_Colors
            [1] =  {name= "pltphantom", coord = vector4(-631.0, 5311.050, 71.078, 90.0),color1 = 89, color2 =  0},
            [2] =  {name= "pltphantom3",coord = vector4(-631.0, 5320.075, 71.078, 90.0),color1 = 55, color2 = 88},
            [3] =  {name= "pltpacker",  coord = vector4(-631.0, 5329.100, 71.078, 90.0),color1 = 150, color2 = 131},
            [4] =  {name= "pltphantom3",coord = vector4(-631.0, 5338.125, 71.078, 90.0),color1 = 137, color2 = 44},
            [5] =  {name= "pltphantom", coord = vector4(-631.0, 5347.150, 71.078, 90.0),color1 = 148, color2 = 139},
            [6] =  {name= "pltpacker",  coord = vector4(-631.0, 5356.175, 71.078, 90.0),color1 = 82, color2 = 24},
            [7] =  {name= "pltphantom3",coord = vector4(-631.0, 5365.200, 71.078, 90.0),color1 = 131, color2 = 139},
            [8] =  {name= "pltphantom3",coord = vector4(-631.0, 5374.225, 71.078, 90.0),color1 = 19, color2 = 24},
            [9] =  {name= "pltphantom", coord = vector4(-631.0, 5383.250, 71.078, 90.0),color1 = 108, color2 = 24},
            [10]=  {name= "pltpacker",  coord = vector4(-631.0, 5392.275, 71.078, 90.0),color1 = 0, color2 = 0},  

        },
    },
}
PLT.ForkliftColour= {color1 = 42,color2 = 79} --https://wiki.rage.mp/index.php?title=Vehicle_Colors
PLT.Vehicles ={forklift = "pltforklift", trailer = "plttrflat", telehandler = "jcb"}
PLT.Props ={
    chainsaw = "polat_lumberjack_chainsaw003",
    tree1 = "polat_lumberjack_a1",
    tree2 = "polat_lumberjack_a2",
    tree3 = "polat_lumberjack_a3",
    tree4 = "polat_lumberjack_a4",
    tree5 = "polat_lumberjack_a5",
    leaves3 = "polat_lumberjack_leaves_a3",
    leaves4 = "polat_lumberjack_leaves_a4",
    leaves5 = "polat_lumberjack_leaves_a5",
    pltwoodpile1 = "polat_lumberjack_woodpile_1",
    pltwoodpile2 = "polat_lumberjack_woodpile_2",
    trailerramp = "polat_lumberjack_ramp001",
}
PLT.Info.stacking.standCoord = {
    [0] =  {coord = vec3(-509.910248, 5269.984863, 79.577888), rot = vec3(-2.550000, 0.000000, 70.000000)},
    [1] =  {coord = vec3(-515.788574, 5272.070313, 79.556946), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [2] =  {coord = vec3(-519.712402, 5273.466797, 78.936813), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [3] =  {coord = vec3(-523.636230, 5274.892578, 78.575157), rot = vec3(-4.935977, 0.000000, 69.668045)},
    [4] =  {coord = vec3(-527.560059, 5276.318359, 78.213501), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [5] =  {coord = vec3(-531.483887, 5277.744141, 77.851845), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [6] =  {coord = vec3(-535.407715, 5279.169922, 77.490189), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [7] =  {coord = vec3(-539.331543, 5280.595703, 77.128532), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [8] =  {coord = vec3(-543.255371, 5282.021484, 76.766876), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [9] =  {coord = vec3(-547.179199, 5283.447266, 76.405220), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [10] = {coord = vec3(-551.103027, 5284.873047, 76.043564), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [11] = {coord = vec3(-555.026855, 5286.298828, 75.681908), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [12] = {coord = vec3(-558.950684, 5287.724609, 75.320251), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [13] = {coord = vec3(-562.874512, 5289.150391, 74.958595), rot = vec3(-4.935977, 0.000000, 70.068108)},
    [14] = {coord = vec3(-567.258789, 5291.220703, 74.436378), rot = vec3(-4.935977, 0.000000, 58.567333)},
    [15] = {coord = vec3(-570.672119, 5295.146484, 73.973648), rot = vec3(-5.935977, 0.000000, 22.665943)},
    [16] = {coord = vec3(-571.132568, 5300.283203, 73.440376), rot = vec3(-4.935977, 0.000000, -10.83437)},
    [17] = {coord = vec3(-569.811279, 5304.736328, 72.896576), rot = vec3(-5.535950, 0.000000, -18.53461)},
    [18] = {coord = vec3(-568.069580, 5309.384766, 72.582298), rot = vec3(0.0000000, 0.000000, -19.83392)},
    [19] = {coord = vec3(-566.458008, 5314.013672, 72.587563), rot = vec3(0.0000000, 0.000000, -19.83392)},
    [20] = {coord = vec3(-564.846436, 5318.642578, 72.592300), rot = vec3(0.0000000, 0.000000, -19.83392)},       
}
PLT.Info.delivery.constructions = {}
PLT.Info.delivery.constructions[1] = {coord = vector3(-214.7899, 6310.706, 31.41837),
    props ={
        [1] = {coord = vec3(-205.654602, 6295.138184, 30.492229), rot = vec3(-0.028752, -0.022327, 44.797421)},
        [2] = {coord = vec3(-204.507736, 6296.293457, 30.497509), rot = vec3(-0.153996, -0.027518, 44.338005)},
        [3] = {coord = vec3(-205.579941, 6301.635742, 30.492060), rot = vec3(0.000000, 0.000000, -48.797657)},
        [4] = {coord = vec3(-203.441345, 6301.034180, 30.490322), rot = vec3(0.000000, 0.000000, -49.898083)},
        [5] = {coord = vec3(-202.226608, 6299.733398, 30.494192), rot = vec3(0.000000, 0.000000, -49.698101)},
        [6] = {coord = vec3(-200.820190, 6298.575684, 30.495608), rot = vec3(0.000000, 0.000000, -49.497417)},
        [7] = {coord = vec3(-199.499374, 6297.359863, 30.490100), rot = vec3(0.000000, 0.000000, -49.697975)},
        [8] = {coord = vec3(-198.270279, 6296.020508, 30.485888), rot = vec3(0.000000, 0.000000, -49.297829)},
        [9] = {coord = vec3(-196.881042, 6294.848633, 30.480888), rot = vec3(0.000000, 0.000000, -49.297829)},
        [10] = {coord = vec3(-195.391861, 6293.315430, 30.481314), rot = vec3(0.000000, 0.000000, -49.297829)},
        [11] = {coord = vec3(-159.250610, 6262.371094, 30.489895), rot = vec3(0.000000, 0.000000, -46.397781)},
        [12] = {coord = vec3(-156.432159, 6265.134766, 30.490320), rot = vec3(0.000000, 0.000000, -46.397781)},
        [13] = {coord = vec3(-160.469940, 6263.630859, 30.490746), rot = vec3(0.000000, 0.000000, -46.397781)},
        [14] = {coord = vec3(-157.611511, 6266.335938, 30.491171), rot = vec3(0.000000, 0.000000, -46.397781)},
        [15] = {coord = vec3(-161.619308, 6264.792969, 30.491596), rot = vec3(0.000000, 0.000000, -46.397781)},
        [16] = {coord = vec3(-158.790863, 6267.576172, 30.491596), rot = vec3(0.000000, 0.000000, -46.397781)},
        [17] = {coord = vec3(-162.798660, 6266.062500, 30.492022), rot = vec3(0.000000, 0.000000, -46.397781)},
        [18] = {coord = vec3(-160.000198, 6268.816406, 30.492022), rot = vec3(0.000000, 0.000000, -46.397781)},
    }
}
PLT.Info.delivery.constructions[2] = {coord = vector3(149.3539, 6432.362, 31.39369),
    props ={
        [1] = {coord = vec3(147.266586, 6461.440918, 30.764000), rot = vec3(0.000000, 0.000000, 44.696587)},
        [2] = {coord = vec3(144.448135, 6464.302246, 30.764425), rot = vec3(0.000000, 0.000000, 44.696587)},
        [3] = {coord = vec3(141.599701, 6467.173340, 30.764851), rot = vec3(0.000000, 0.000000, 44.696587)},
        [4] = {coord = vec3(140.410355, 6465.981934, 30.764851), rot = vec3(0.000000, 0.000000, 44.696587)},
        [5] = {coord = vec3(143.287918, 6463.070801, 30.763721), rot = vec3(0.054369, -0.040086, 44.697464)},
        [6] = {coord = vec3(146.096375, 6460.258301, 30.764147), rot = vec3(0.054369, -0.040086, 44.697464)},
        [7] = {coord = vec3(144.897034, 6459.037598, 30.764572), rot = vec3(0.054369, -0.040086, 44.697464)},
        [8] = {coord = vec3(142.078583, 6461.869629, 30.764572), rot = vec3(0.054369, -0.040086, 44.697464)},
        [9] = {coord = vec3(139.260132, 6464.711426, 30.764997), rot = vec3(0.054369, -0.040086, 44.697464)},
        [10] = {coord = vec3(138.070786, 6463.520020, 30.765423), rot = vec3(0.054369, -0.040086, 44.697464)},
        [11] = {coord = vec3(140.889236, 6460.619629, 30.765848), rot = vec3(0.054369, -0.040086, 44.697464)},
        [12] = {coord = vec3(143.717682, 6457.777832, 30.766273), rot = vec3(0.054369, -0.040086, 44.697464)},
        [13] = {coord = vec3(142.508347, 6456.566895, 30.766699), rot = vec3(0.054369, -0.040086, 44.697464)},
        [14] = {coord = vec3(139.709885, 6459.408691, 30.767124), rot = vec3(0.054369, -0.040086, 44.697464)},
        [15] = {coord = vec3(136.841461, 6462.289551, 30.767550), rot = vec3(0.054369, -0.040086, 44.697464)},
        [16] = {coord = vec3(135.592148, 6461.049316, 30.767550), rot = vec3(0.054369, -0.040086, 44.697464)},
        [17] = {coord = vec3(138.460571, 6458.119629, 30.767975), rot = vec3(0.054369, -0.040086, 44.697464)},
        [18] = {coord = vec3(141.249039, 6455.258301, 30.768400), rot = vec3(0.054369, -0.040086, 44.697464)},
        [19] = {coord = vec3(139.979736, 6454.008301, 30.768826), rot = vec3(0.054369, -0.040086, 44.697464)},
        [20] = {coord = vec3(137.191269, 6456.898926, 30.769251), rot = vec3(0.054369, -0.040086, 44.697464)},
        [21] = {coord = vec3(134.312851, 6459.760254, 30.769676), rot = vec3(0.054369, -0.040086, 44.697464)},
    }
}
-- PLT.Info.delivery.constructions[3] = {coord = vector3(-500.2505, -957.2551, 23.927),
--     props ={
--         [1] = {coord = vec3(-481.75, -970.0, 22.552256), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [2] = {coord = vec3(-481.75, -966.0, 22.552256), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [3] = {coord = vec3(-483.50, -970.0, 22.552681), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [4] = {coord = vec3(-483.50, -966.0, 22.552681), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [5] = {coord = vec3(-485.25, -970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [6] = {coord = vec3(-485.25, -966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [7] = {coord = vec3(-487.00, -970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [8] = {coord = vec3(-487.00, -966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [9] = {coord = vec3(-488.75, -970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [10] = {coord = vec3(-488.75,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [11] = {coord = vec3(-490.50,-970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [12] = {coord = vec3(-490.50,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [13] = {coord = vec3(-492.25,-970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [14] = {coord = vec3(-492.25,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [15] = {coord = vec3(-494.00,-970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [16] = {coord = vec3(-494.00,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [17] = {coord = vec3(-495.75,-970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [18] = {coord = vec3(-495.75,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [19] = {coord = vec3(-497.50,-970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [20] = {coord = vec3(-497.50,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [21] = {coord = vec3(-499.25,-970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [22] = {coord = vec3(-499.25,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [23] = {coord = vec3(-501.00,-970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [24] = {coord = vec3(-501.00,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [25] = {coord = vec3(-502.75,-970.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--         [26] = {coord = vec3(-502.75,-966.0, 22.553106), rot = vec3(0.000000, 0.000000, 0.000000)},
--     }
-- }
PLT.Info.delivery.constructions[3] = {coord = vector3(-885.2681, 406.4718, 86.18835),
    props ={
        [1] = {coord = vec3(-930.337891, 402.406403, 78.119652), rot = vec3(0.009013, -0.009271, -159.085663)},
        [2] = {coord = vec3(-928.816406, 398.502594, 78.122505), rot = vec3(0.009013, -0.009271, 21.005777)},
        [3] = {coord = vec3(-928.646240, 403.006989, 78.126785), rot = vec3(0.009013, -0.009271, 20.305904)},
        [4] = {coord = vec3(-927.116406, 399.102594, 78.122505), rot = vec3(0.009013, -0.009271, 21.005777)},
        [5] = {coord = vec3(-925.092773, 406.039948, 78.127213), rot = vec3(0.009013, -0.009271, -69.194550)},
        [6] = {coord = vec3(-924.472168, 404.498444, 78.127213), rot = vec3(0.009013, -0.009271, -69.194550)},
        [7] = {coord = vec3(-923.901611, 402.946930, 78.127213), rot = vec3(0.009013, -0.009271, -69.194550)},
        [8] = {coord = vec3(-923.270996, 401.395416, 78.127213), rot = vec3(0.009013, -0.009271, -69.194550)},
        [9] = {coord = vec3(-922.640381, 399.833893, 78.127213), rot = vec3(0.009013, -0.009271, -69.194550)},
        [10] = {coord = vec3(-921.979736, 398.262360, 78.127213), rot = vec3(0.009013, -0.009271, -69.194550)},
    }
}
PLT.Info.delivery.constructions[4] = {coord = vector3(893.8294, 2367.316, 51.52855),
    props ={
        [1] = {coord = vec3(872.408325, 2349.312988, 50.691677), rot = vec3(0.000000,  0.000000, 0.000000)},
        [2] = {coord = vec3(872.408325, 2345.349121, 50.692101), rot = vec3(0.000000,  0.000000, 0.000000)},
        [3] = {coord = vec3(872.408325, 2341.345215, 50.692524), rot = vec3(0.000000,  0.000000, 0.000000)},
        [4] = {coord = vec3(874.160034, 2341.345215, 50.692947), rot = vec3(0.000000,  0.000000, 0.000000)},
        [5] = {coord = vec3(874.160034, 2345.349121, 50.692524), rot = vec3(0.000000,  0.000000, 0.000000)},
        [6] = {coord = vec3(874.160034, 2349.343018, 50.692947), rot = vec3(0.000000,  0.000000, 0.000000)},
        [7] = {coord = vec3(874.160034, 2353.356934, 50.693371), rot = vec3(0.000000,  0.000000, 0.000000)},
        [8] = {coord = vec3(876.001831, 2353.356934, 50.693371), rot = vec3(0.000000,  0.000000, 0.000000)},
        [9] = {coord = vec3(876.001831, 2349.322998, 50.693794), rot = vec3(0.000000,  0.000000, 0.000000)},
        [10] = {coord = vec3(876.001831, 2345.319092, 50.694218), rot = vec3(0.000000, 0.000000, 0.000000)},
        [11] = {coord = vec3(877.733521, 2345.319092, 50.694641), rot = vec3(0.000000, 0.000000, 0.000000)},
        [12] = {coord = vec3(877.753540, 2349.343018, 50.694641), rot = vec3(0.000000, 0.000000, 0.000000)},
        [13] = {coord = vec3(877.753540, 2353.366943, 50.695065), rot = vec3(0.000000, 0.000000, 0.000000)},
    }
}
PLT.Info.delivery.constructions[5] = {coord = vector3(2924.593, 4389.744, 50.12745),
    props ={
        [1] = {coord = vec3(2901.003418, 4375.369629, 49.369480), rot = vec3(0.000000, 0.000000, 22.599146)},
        [2] = {coord = vec3(2902.614990, 4371.531738, 49.369904), rot = vec3(0.000000, 0.000000, 22.599146)},
        [3] = {coord = vec3(2902.574951, 4376.062988, 49.369904), rot = vec3(0.000000, 0.000000, 22.599146)},
        [4] = {coord = vec3(2904.246582, 4372.264160, 49.370327), rot = vec3(0.000000, 0.000000, 22.599146)},
        [5] = {coord = vec3(2904.176514, 4376.697754, 49.370327), rot = vec3(0.000000, 0.000000, 22.599146)},
        [6] = {coord = vec3(2905.828125, 4372.889160, 49.370327), rot = vec3(0.000000, 0.000000, 22.599146)},
        [7] = {coord = vec3(2905.828125, 4377.342285, 49.370750), rot = vec3(0.000000, 0.000000, 22.599146)},
        [8] = {coord = vec3(2907.499756, 4373.572754, 49.370750), rot = vec3(0.000000, 0.000000, 22.599146)},
        [9] = {coord = vec3(2907.429688, 4378.016113, 49.370750), rot = vec3(0.000000, 0.000000, 22.599146)},
        [10] = {coord = vec3(2909.051270, 4374.275879, 49.371174), rot = vec3(0.000000, 0.000000, 22.599146)},
        [11] = {coord = vec3(2909.051270, 4378.738770, 49.371174), rot = vec3(0.000000, 0.000000, 22.599146)},
        [12] = {coord = vec3(2910.742920, 4375.027832, 49.371174), rot = vec3(0.000000, 0.000000, 22.599146)},
    }
}
PLT.Info.delivery.constructions[6] = {coord = vector3(1243.7, -1294.627, 35.27112),
    props ={
        [1] = {coord = vec3(1184.561890, -1312.057495, 33.859966), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [2] = {coord = vec3(1184.932251, -1307.663208, 33.860390), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [3] = {coord = vec3(1186.283569, -1312.217651, 33.886337), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [4] = {coord = vec3(1186.673950, -1307.743286, 33.886917), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [5] = {coord = vec3(1187.955200, -1312.357788, 33.920734), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [6] = {coord = vec3(1188.375610, -1307.873413, 33.925861), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [7] = {coord = vec3(1189.666870, -1312.507935, 33.948265), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [8] = {coord = vec3(1190.047241, -1307.993530, 33.962955), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [9] = {coord = vec3(1191.388550, -1312.668091, 34.009613), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [10] = {coord = vec3(1191.708862, -1308.113647, 33.996193), rot = vec3(-0.000003, -1.200001, -4.699985)},
        [11] = {coord = vec3(1220.074341, -1320.494385, 34.227390), rot = vec3(0.000000, 0.000000, -5.199974)},
        [12] = {coord = vec3(1219.658936, -1324.858032, 34.219875), rot = vec3(0.003206, 0.583918, -5.188698)},
        [13] = {coord = vec3(1217.947266, -1324.707886, 34.220299), rot = vec3(0.003206, 0.583912, -5.188698)},
        [14] = {coord = vec3(1218.327637, -1320.353638, 34.220722), rot = vec3(0.003206, 0.583908, -5.188698)},
        [15] = {coord = vec3(1216.625977, -1320.193481, 34.221146), rot = vec3(0.003206, 0.583908, -5.188698)},
        [16] = {coord = vec3(1216.215576, -1324.547729, 34.221146), rot = vec3(0.003206, 0.583908, -5.188698)},
        [17] = {coord = vec3(1214.493896, -1324.387573, 34.221569), rot = vec3(0.003206, 0.583908, -5.188698)},
        [18] = {coord = vec3(1214.874268, -1320.063354, 34.221569), rot = vec3(0.003206, 0.583908, -5.188698)},
        [19] = {coord = vec3(1213.162598, -1319.923218, 34.221992), rot = vec3(0.003206, 0.583908, -5.188698)},
        [20] = {coord = vec3(1212.812256, -1324.197388, 34.221992), rot = vec3(0.003206, 0.583908, -5.188698)},
    }
}
PLT.Info.delivery.constructions[7] = {coord = vector3(1212.586, -2992.105, 5.951317),
    props ={
        [1] = {coord = vec3(1203.664673, -2971.00, 4.891133), rot = vec3(0.000000, 0.000000, 90.0)},
        [2] = {coord = vec3(1203.664673, -2972.75, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [3] = {coord = vec3(1203.664673, -2974.50, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [4] = {coord = vec3(1203.664673, -2976.25, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [5] = {coord = vec3(1203.664673, -2978.00, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [6] = {coord = vec3(1203.664673, -2979.75, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [7] = {coord = vec3(1203.664673, -2981.50, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [8] = {coord = vec3(1203.664673, -2983.25, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [9] = {coord = vec3(1203.664673, -2985.00, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [10] = {coord = vec3(1203.664673, -2986.75, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [11] = {coord = vec3(1203.664673, -2988.50, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [12] = {coord = vec3(1203.664673, -2990.25, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [13] = {coord = vec3(1203.664673, -2992.00, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [14] = {coord = vec3(1203.664673, -2993.75, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [15] = {coord = vec3(1203.664673, -2995.50, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [16] = {coord = vec3(1203.664673, -2997.25, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [17] = {coord = vec3(1203.664673, -2999.00, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [18] = {coord = vec3(1203.664673, -3000.75, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [19] = {coord = vec3(1203.664673, -3002.50, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
        [20] = {coord = vec3(1203.664673, -3004.25, 4.891558), rot = vec3(0.000000, 0.000000, 90.0)},
    }
}
PLT.Info.delivery.constructions[8] = {coord = vector3(-133.7866, -1047.01, 27.36717),
    props ={
        [1] = {coord = vec3(-124.146896, -987.654663, 26.280857), rot = vec3(0.000000, 0.000000, 70.794609)},
        [2] = {coord = vec3(-124.707016, -989.306274, 26.281282), rot = vec3(0.000000, 0.000000, 70.794609)},
        [3] = {coord = vec3(-125.257133, -990.867798, 26.281708), rot = vec3(0.000000, 0.000000, 70.794609)},
        [4] = {coord = vec3(-125.787247, -992.439331, 26.281708), rot = vec3(0.000000, 0.000000, 70.794609)},
        [5] = {coord = vec3(-126.327362, -994.010864, 26.282133), rot = vec3(0.000000, 0.000000, 70.794609)},
        [6] = {coord = vec3(-126.857475, -995.592407, 26.282558), rot = vec3(0.000000, 0.000000, 70.794609)},
        [7] = {coord = vec3(-127.387589, -997.173950, 26.282984), rot = vec3(0.000000, 0.000000, 70.794609)},
        [8] = {coord = vec3(-127.947708, -998.785522, 26.282984), rot = vec3(0.000000, 0.000000, 70.794609)},
        [9] = {coord = vec3(-128.507446, -1000.367065, 26.283409), rot = vec3(0.000000, 0.000000, 70.794609)},
        [10] = {coord = vec3(-129.057144, -1001.958618, 26.283834), rot = vec3(0.000000, 0.000000, 70.794609)},
        [11] = {coord = vec3(-129.646820, -1003.630249, 26.284260), rot = vec3(0.000000, 0.000000, 70.794609)},
        [12] = {coord = vec3(-130.176529, -1005.191772, 26.284685), rot = vec3(0.000000, 0.000000, 70.794609)},
        [13] = {coord = vec3(-130.726227, -1006.833374, 26.285110), rot = vec3(0.000000, 0.000000, 70.794609)},
        [14] = {coord = vec3(-131.275925, -1008.434937, 26.285536), rot = vec3(0.000000, 0.000000, 70.794609)},
        [15] = {coord = vec3(-131.835617, -1010.036499, 26.285961), rot = vec3(0.000000, 0.000000, 70.794609)},
        [16] = {coord = vec3(-132.355331, -1011.648071, 26.285961), rot = vec3(0.000000, 0.000000, 70.794609)},
        [17] = {coord = vec3(-132.915024, -1013.239624, 26.286386), rot = vec3(0.000000, 0.000000, 70.794609)},
    }
}
-- PLT.Info.delivery.constructions[10] = {coord = vec3(130.322479, -416.002686, 41.060417),
--     props ={
--         [1]= {coord=vec3(119.170715, -452.599304, 40.129848),rot=vec3(0.000000, 0.000000, 68.066299)},
--         [2]= {coord=vec3(115.460251, -451.105164, 40.130272),rot=vec3(0.000000, 0.000000, 68.066292)},
--         [3]= {coord=vec3(111.749786, -449.611023, 40.130695),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [4]= {coord=vec3(108.039322, -448.116882, 40.131119),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [5]= {coord=vec3(108.711685, -446.447174, 40.131542),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [6]= {coord=vec3(112.422150, -447.941315, 40.131966),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [7]= {coord=vec3(116.132614, -449.435455, 40.132389),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [8]= {coord=vec3(119.843079, -450.929596, 40.132813),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [9]= {coord=vec3(120.515442, -449.259888, 40.133236),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [10]= {coord=vec3(116.804977, -447.765747, 40.133659),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [11]= {coord=vec3(113.094513, -446.271606, 40.134083),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [12]= {coord=vec3(109.384048, -444.777466, 40.134506),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [13]= {coord=vec3(110.056412, -443.107758, 40.134930),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [14]= {coord=vec3(113.766876, -444.601898, 40.135353),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [15]= {coord=vec3(117.477341, -446.096039, 40.135777),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [16]= {coord=vec3(121.187805, -447.590179, 40.136200),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [17]= {coord=vec3(121.860168, -445.920471, 40.136623),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [18]= {coord=vec3(118.149704, -444.426331, 40.137047),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [19]= {coord=vec3(114.439240, -442.932190, 40.137470),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [20]= {coord=vec3(110.728775, -441.438049, 40.137894),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [21]= {coord=vec3(111.401138, -439.768341, 40.138317),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [22]= {coord=vec3(115.111603, -441.262482, 40.138741),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [23]= {coord=vec3(118.822067, -442.756622, 40.139164),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [24]= {coord=vec3(122.532532, -444.250763, 40.139587),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [25]= {coord=vec3(123.204895, -442.581055, 40.140011),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [26]= {coord=vec3(119.494431, -441.086914, 40.140434),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [27]= {coord=vec3(115.783966, -439.592773, 40.140858),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [28]= {coord=vec3(112.073502, -438.098633, 40.141281),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [29]= {coord=vec3(112.745865, -436.428925, 40.141705),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [30]= {coord=vec3(116.456329, -437.923065, 40.142128),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [31]= {coord=vec3(120.166794, -439.417206, 40.142551),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [32]= {coord=vec3(123.877258, -440.911346, 40.142975),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [33]= {coord=vec3(124.549622, -439.241638, 40.143398),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [34]= {coord=vec3(120.839157, -437.747498, 40.143822),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [35]= {coord=vec3(117.128693, -436.253357, 40.144245),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [36]= {coord=vec3(113.418228, -434.759216, 40.144669),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [37]= {coord=vec3(114.090591, -433.089508, 40.145092),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [38]= {coord=vec3(117.801056, -434.583649, 40.145515),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [39]= {coord=vec3(121.511520, -436.077789, 40.145939),rot=vec3(0.000000, 0.000000, 68.066284)},
--         [40]= {coord=vec3(125.221985, -437.571930, 40.146362),rot=vec3(0.000000, 0.000000, 68.066284)},
--     }
-- }
for k,v in pairs(PLT.Info.delivery.constructions) do 
    if not PLT.Info.delivery.constructions[k].distance then 
	    PLT.Info.delivery.constructions[k].distance = #(v.coord - vector3(PLT.ActionCoords.x,PLT.ActionCoords.y,PLT.ActionCoords.z)) 
    end
	if not PLT.Info.delivery.MaxGasolinePayment then PLT.Info.delivery.MaxGasolinePayment = math.floor(PLT.Info.delivery.constructions[k].distance) end 
	if not PLT.Info.delivery.MinGasolinePayment then PLT.Info.delivery.MinGasolinePayment = math.floor(PLT.Info.delivery.constructions[k].distance) end
	PLT.Info.delivery.MaxGasolinePayment = math.floor(math.max(PLT.Info.delivery.MaxGasolinePayment,PLT.Info.delivery.constructions[k].distance))
	PLT.Info.delivery.MinGasolinePayment = math.floor(math.min(PLT.Info.delivery.MinGasolinePayment,PLT.Info.delivery.constructions[k].distance)) 
end
if PLT.Salaries and PLT.Salaries.delivery and PLT.Salaries.delivery.distancePaymentEach1Km then
    PLT.Info.delivery.MinGasolinePayment = math.floor((PLT.Info.delivery.MinGasolinePayment * PLT.Salaries.delivery.distancePaymentEach1Km)/1000)
    PLT.Info.delivery.MaxGasolinePayment = math.floor((PLT.Info.delivery.MaxGasolinePayment * PLT.Salaries.delivery.distancePaymentEach1Km)/1000)
end
PLT.Info.cutting.trees={}
PLT.Info.cutting.trees[1]={coord=vec3(-665.438354,5265.196289,74.998535),[5]={tree2x234={coord=vec3(-665.377319,5264.499023,75.689865),rot=vec3(88.027107,-0.000002,4.999990)},tree4x4={coord=vec3(-664.719727,5256.983398,75.839752),rot=vec3(97.236641,-0.000002,4.999990)},tree2x23={coord=vec3(-665.377319,5264.499023,75.689865),rot=vec3(88.027107,-0.000002,4.999990)},tree3x3={coord=vec3(-665.046387,5260.716797,75.820648),rot=vec3(88.027107,-0.000002,4.999990)},tree2x2={coord=vec3(-665.385193,5264.588379,75.686768),rot=vec3(88.027107,-0.000002,4.999990)},},[35]={tree2x234={coord=vec3(-665.036865,5264.623047,75.810730),rot=vec3(90.694473,-0.000001,34.999954)},tree4x4={coord=vec3(-660.707275,5258.439453,75.609238),rot=vec3(95.320755,-0.000000,34.999954)},tree2x23={coord=vec3(-665.036865,5264.623047,75.810730),rot=vec3(90.694473,-0.000001,34.999954)},tree3x3={coord=vec3(-662.858032,5261.511230,75.764687),rot=vec3(90.694473,-0.000001,34.999954)},tree2x2={coord=vec3(-665.088501,5264.696777,75.724319),rot=vec3(88.000732,-0.000001,34.999954)},},[65]={tree2x234={coord=vec3(-664.803955,5264.900391,75.658386),rot=vec3(90.694412,0.000000,65.000008)},tree4x4={coord=vec3(-657.962769,5261.709961,75.511894),rot=vec3(94.376930,0.000000,65.000008)},tree2x23={coord=vec3(-664.803955,5264.900391,75.658386),rot=vec3(90.694412,0.000000,65.000008)},tree3x3={coord=vec3(-661.361145,5263.294922,75.612343),rot=vec3(90.694412,0.000000,65.000008)},tree2x2={coord=vec3(-664.885498,5264.938477,75.659477),rot=vec3(90.694412,0.000000,65.000008)},},[95]={tree2x234={coord=vec3(-664.741028,5265.257324,75.599968),rot=vec3(92.517448,-0.000001,95.000000)},tree4x4={coord=vec3(-657.228027,5265.914551,75.158379),rot=vec3(96.274010,-0.000001,95.000000)},tree2x23={coord=vec3(-664.741028,5265.257324,75.599968),rot=vec3(92.517441,-0.000001,95.000000)},tree3x3={coord=vec3(-660.960144,5265.587891,75.433105),rot=vec3(92.517433,-0.000001,95.000000)},tree2x2={coord=vec3(-664.830566,5265.249512,75.603920),rot=vec3(92.517433,-0.000001,95.000000)},},[130]={tree2x234={coord=vec3(-664.902100,5265.646484,75.560684),rot=vec3(95.320694,0.000001,130.000000)},tree4x4={coord=vec3(-659.144165,5270.478516,74.750664),rot=vec3(103.218605,-0.000000,130.000000)},tree2x23={coord=vec3(-664.902100,5265.646484,75.560684),rot=vec3(95.320686,0.000001,130.000000)},tree3x3={coord=vec3(-662.004456,5268.078125,75.208405),rot=vec3(96.273880,0.000001,130.000000)},tree2x2={coord=vec3(-664.970764,5265.588867,75.569031),rot=vec3(95.320679,0.000001,130.000000)},},[160]={tree2x234={coord=vec3(-665.198914,5265.854004,75.531471),rot=vec3(97.236641,0.000001,159.999939)},tree4x4={coord=vec3(-662.637512,5272.891113,74.410530),rot=vec3(103.218605,-0.000000,159.999939)},tree2x23={coord=vec3(-665.198914,5265.854004,75.531471),rot=vec3(97.236641,0.000000,159.999939)},tree3x3={coord=vec3(-663.909912,5269.395508,75.052917),rot=vec3(97.236641,0.000000,159.999939)},tree2x2={coord=vec3(-665.229492,5265.770020,75.542809),rot=vec3(97.236641,0.000000,159.999939)},},[190]={tree2x234={coord=vec3(-665.559937,5265.885742,75.530792),rot=vec3(96.273895,-0.000000,-169.999908)},tree4x4={coord=vec3(-666.862976,5273.274902,74.595825),rot=vec3(101.184807,-0.000000,-169.999908)},tree2x23={coord=vec3(-665.559937,5265.885742,75.530792),rot=vec3(96.273888,-0.000000,-169.999908)},tree3x3={coord=vec3(-666.215698,5269.604492,75.115631),rot=vec3(96.273880,-0.000000,-169.999908)},tree2x2={coord=vec3(-665.544373,5265.797852,75.540627),rot=vec3(96.273880,-0.000000,-169.999908)},},[220]={tree2x234={coord=vec3(-665.888306,5265.732422,75.563286),rot=vec3(93.442513,-0.000000,-139.999985)},tree4x4={coord=vec3(-670.731995,5271.504883,74.999985),rot=vec3(97.236641,-0.000000,-139.999985)},tree2x23={coord=vec3(-665.888306,5265.732422,75.563286),rot=vec3(93.442505,-0.000000,-139.999985)},tree3x3={coord=vec3(-668.325867,5268.637207,75.335167),rot=vec3(93.442505,0.000000,-139.999985)},tree2x2={coord=vec3(-665.830566,5265.663574,75.568687),rot=vec3(94.376923,0.000000,-139.999985)},},[250]={tree2x234={coord=vec3(-666.096130,5265.435547,75.606781),rot=vec3(90.694412,0.000001,-109.999962)},tree4x4={coord=vec3(-673.189331,5268.017090,75.405289),rot=vec3(91.601341,0.000001,-109.999962)},tree2x23={coord=vec3(-666.096130,5265.435547,75.606781),rot=vec3(90.694412,0.000001,-109.999962)},tree3x3={coord=vec3(-669.665771,5266.734863,75.560738),rot=vec3(90.694412,0.000001,-109.999962)},tree2x2={coord=vec3(-666.011536,5265.404785,75.607872),rot=vec3(91.601341,0.000001,-109.999962)},},[300]={tree2x234={coord=vec3(-666.044556,5264.846191,75.680557),rot=vec3(88.027107,0.000000,-60.000000)},tree4x4={coord=vec3(-672.578308,5261.074219,75.830444),rot=vec3(88.907364,0.000000,-60.000000)},tree2x23={coord=vec3(-666.044556,5264.846191,75.680557),rot=vec3(88.027107,0.000000,-60.000000)},tree3x3={coord=vec3(-669.332642,5262.947754,75.811340),rot=vec3(88.027107,0.000001,-60.000000)},tree2x2={coord=vec3(-665.966675,5264.891113,75.677460),rot=vec3(88.907364,0.000000,-60.000000)},},[330]={tree2x234={coord=vec3(-665.788330,5264.589844,75.710892),rot=vec3(88.027107,0.000001,-29.999979)},tree4x4={coord=vec3(-669.560608,5258.056152,75.860779),rot=vec3(94.376930,0.000001,-29.999979)},tree2x23={coord=vec3(-665.788330,5264.589844,75.710892),rot=vec3(88.027107,0.000001,-29.999979)},tree3x3={coord=vec3(-667.686707,5261.301758,75.841675),rot=vec3(88.027107,0.000001,-29.999979)},tree2x2={coord=vec3(-665.743347,5264.667480,75.707794),rot=vec3(88.027107,0.000001,-29.999979)},},}
PLT.Info.cutting.trees[2]={coord=vec3(-682.996521,5362.876953,62.859646),[0]={tree2x234={coord=vec3(-682.996521,5362.176758,63.697704),rot=vec3(75.822174,0.000000,0.000000)},tree4x4={coord=vec3(-682.996521,5354.857910,65.436691),rot=vec3(79.689850,0.000000,0.000000)},tree2x23={coord=vec3(-682.996521,5362.176758,63.697704),rot=vec3(75.822174,0.000000,0.000000)},tree3x3={coord=vec3(-682.996521,5358.493652,64.628204),rot=vec3(75.822174,0.000000,0.000000)},tree2x2={coord=vec3(-682.996521,5362.264160,63.675663),rot=vec3(76.580383,0.000000,0.000000)},},[35]={tree2x234={coord=vec3(-682.595032,5362.303711,63.591591),rot=vec3(84.592377,-0.000001,34.999954)},tree4x4={coord=vec3(-678.284363,5356.146973,64.193016),rot=vec3(91.601402,-0.000001,34.999954)},tree2x23={coord=vec3(-682.595032,5362.303711,63.591591),rot=vec3(84.592377,-0.000001,34.999954)},tree3x3={coord=vec3(-680.425720,5359.205566,63.949612),rot=vec3(84.592377,-0.000001,34.999954)},tree2x2={coord=vec3(-682.646423,5362.376953,63.583111),rot=vec3(84.592377,-0.000001,34.999954)},},[65]={tree2x234={coord=vec3(-682.362122,5362.581055,63.506638),rot=vec3(96.273895,0.000000,65.000008)},tree4x4={coord=vec3(-675.561401,5359.410156,62.681671),rot=vec3(100.182983,0.000000,65.000008)},tree2x23={coord=vec3(-682.362122,5362.581055,63.506638),rot=vec3(96.273888,0.000000,65.000008)},tree3x3={coord=vec3(-678.939697,5360.985352,63.091476),rot=vec3(96.273880,0.000000,65.000008)},tree2x2={coord=vec3(-682.443237,5362.619141,63.516472),rot=vec3(96.273880,0.000000,65.000008)},},[95]={tree2x234={coord=vec3(-682.299194,5362.937988,63.340565),rot=vec3(104.250893,-0.000000,95.000000)},tree4x4={coord=vec3(-675.010315,5363.575684,61.482235),rot=vec3(107.409775,0.000000,95.000000)},tree2x23={coord=vec3(-682.299194,5362.937988,63.340565),rot=vec3(104.250885,-0.000000,95.000000)},tree3x3={coord=vec3(-678.631104,5363.258789,62.405373),rot=vec3(104.250877,-0.000000,95.000000)},tree2x2={coord=vec3(-682.386108,5362.930664,63.362720),rot=vec3(104.250877,-0.000000,95.000000)},},[125]={tree2x234={coord=vec3(-682.423096,5363.278320,63.177616),rot=vec3(108.483757,-0.000003,124.999985)},tree4x4={coord=vec3(-676.558350,5367.384766,60.674313),rot=vec3(110.664268,-0.000003,124.999985)},tree2x23={coord=vec3(-682.423096,5363.278320,63.177616),rot=vec3(108.483749,-0.000003,124.999985)},tree3x3={coord=vec3(-679.471680,5365.344727,61.973198),rot=vec3(108.483742,-0.000003,124.999985)},tree2x2={coord=vec3(-682.493042,5363.229492,63.206150),rot=vec3(108.483742,-0.000003,124.999985)},},[155]={tree2x234={coord=vec3(-682.700684,5363.511230,63.164394),rot=vec3(110.664268,0.000001,154.999954)},tree4x4={coord=vec3(-679.715576,5369.913086,60.445419),rot=vec3(111.770905,0.000001,154.999954)},tree2x23={coord=vec3(-682.700684,5363.511230,63.164394),rot=vec3(110.664261,0.000001,154.999954)},tree3x3={coord=vec3(-681.198425,5366.732910,61.823761),rot=vec3(110.664253,-0.000000,154.999954)},tree2x2={coord=vec3(-682.736267,5363.435059,63.271156),rot=vec3(111.770889,0.000001,154.999954)},},[185]={tree2x234={coord=vec3(-683.057556,5363.574219,63.201553),rot=vec3(108.483757,0.000001,-174.999893)},tree4x4={coord=vec3(-683.681580,5370.706543,60.808250),rot=vec3(110.664268,0.000001,-174.999893)},tree2x23={coord=vec3(-683.057556,5363.574219,63.201553),rot=vec3(108.483749,0.000001,-174.999893)},tree3x3={coord=vec3(-683.371582,5367.163574,61.997135),rot=vec3(108.483742,0.000001,-174.999893)},tree2x2={coord=vec3(-683.050110,5363.489258,63.230087),rot=vec3(108.483742,0.000001,-174.999893)},},[215]={tree2x234={coord=vec3(-683.398010,5363.450195,63.354221),rot=vec3(101.184914,0.000001,-144.999969)},tree4x4={coord=vec3(-687.645691,5369.516602,61.779896),rot=vec3(106.346321,-0.000001,-144.999969)},tree2x23={coord=vec3(-683.398010,5363.450195,63.354221),rot=vec3(101.184914,0.000001,-144.999969)},tree3x3={coord=vec3(-685.535645,5366.502930,62.617306),rot=vec3(101.184914,0.000001,-144.999969)},tree2x2={coord=vec3(-683.347351,5363.377930,63.371677),rot=vec3(101.184914,0.000001,-144.999969)},},[245]={tree2x234={coord=vec3(-683.630920,5363.172852,63.499172),rot=vec3(91.601448,0.000004,-114.999985)},tree4x4={coord=vec3(-690.469910,5366.362305,63.178200),rot=vec3(99.191185,0.000002,-114.999985)},tree2x23={coord=vec3(-683.630920,5363.172852,63.499172),rot=vec3(91.601448,0.000004,-114.999985)},tree3x3={coord=vec3(-687.072632,5364.777832,63.393002),rot=vec3(91.601448,0.000004,-114.999985)},tree2x2={coord=vec3(-683.549377,5363.134766,63.501690),rot=vec3(91.601448,0.000004,-114.999985)},},[275]={tree2x234={coord=vec3(-683.693848,5362.815918,63.632748),rot=vec3(80.486732,-0.000002,-84.999954)},tree4x4={coord=vec3(-691.110718,5362.166504,64.650406),rot=vec3(86.292625,-0.000002,-84.999954)},tree2x23={coord=vec3(-683.693848,5362.815918,63.632748),rot=vec3(80.486725,-0.000002,-84.999954)},tree3x3={coord=vec3(-687.426331,5362.489258,64.260628),rot=vec3(80.486717,-0.000002,-84.999954)},tree2x2={coord=vec3(-683.605408,5362.823730,63.617870),rot=vec3(80.486717,-0.000002,-84.999954)},},[305]={tree2x234={coord=vec3(-683.569946,5362.475586,63.768192),rot=vec3(75.822174,0.000001,-54.999851)},tree4x4={coord=vec3(-689.565369,5358.277344,65.617180),rot=vec3(81.291595,0.000001,-54.999847)},tree2x23={coord=vec3(-683.569946,5362.475586,63.768192),rot=vec3(75.822174,0.000001,-54.999851)},tree3x3={coord=vec3(-686.587097,5360.362793,64.698692),rot=vec3(75.822174,0.000001,-54.999851)},tree2x2={coord=vec3(-683.498474,5362.525391,63.746151),rot=vec3(75.822174,0.000001,-54.999851)},},[335]={tree2x234={coord=vec3(-683.292358,5362.242676,63.748917),rot=vec3(73.592262,-0.000000,-24.999947)},tree4x4={coord=vec3(-686.352783,5355.679688,65.826279),rot=vec3(77.346191,-0.000000,-24.999941)},tree2x23={coord=vec3(-683.292358,5362.242676,63.748917),rot=vec3(73.592262,0.000001,-24.999945)},tree3x3={coord=vec3(-684.832520,5358.939941,64.822021),rot=vec3(73.592262,0.000001,-24.999945)},tree2x2={coord=vec3(-683.255859,5362.320801,63.723495),rot=vec3(73.592262,0.000001,-24.999945)},},}
PLT.Info.cutting.trees[3]={coord=vec3(-670.344971,5347.275879,62.466404),[0]={tree2x234={coord=vec3(-670.344971,5346.575684,63.208275),rot=vec3(84.592331,0.000000,0.000000)},tree4x4={coord=vec3(-670.344971,5339.060059,63.919701),rot=vec3(88.027107,0.000000,0.000000)},tree2x23={coord=vec3(-670.344971,5346.575684,63.208275),rot=vec3(84.592323,0.000000,0.000000)},tree3x3={coord=vec3(-670.344971,5342.793457,63.566299),rot=vec3(84.592316,0.000000,0.000000)},tree2x2={coord=vec3(-670.344971,5346.665039,63.199795),rot=vec3(84.592316,0.000000,0.000000)},},[50]={tree2x234={coord=vec3(-669.808716,5346.825684,62.998196),rot=vec3(98.209000,-0.000001,49.999825)},tree4x4={coord=vec3(-664.085144,5342.022949,61.870316),rot=vec3(97.226898,-0.000001,49.999817)},tree2x23={coord=vec3(-669.808716,5346.825684,62.998196),rot=vec3(98.209000,-0.000002,49.999825)},tree3x3={coord=vec3(-666.928345,5344.408691,62.455757),rot=vec3(98.209000,-0.000003,49.999825)},tree2x2={coord=vec3(-669.876953,5346.882813,63.011047),rot=vec3(99.191086,-0.000003,49.999825)},},[80]={tree2x234={coord=vec3(-669.655579,5347.154297,62.970291),rot=vec3(104.250893,-0.000000,79.999969)},tree4x4={coord=vec3(-662.450012,5345.884277,60.758209),rot=vec3(97.168610,-0.000000,79.999969)},tree2x23={coord=vec3(-669.655579,5347.154297,62.970291),rot=vec3(107.409767,0.000000,79.999969)},tree3x3={coord=vec3(-666.085693,5346.524902,61.883617),rot=vec3(107.409760,0.000000,79.999969)},tree2x2={coord=vec3(-669.740173,5347.168945,62.997219),rot=vec3(107.409760,0.000000,79.999969)},},[110]={tree2x234={coord=vec3(-669.687195,5347.515137,62.858566),rot=vec3(109.568588,-0.000000,109.999962)},tree4x4={coord=vec3(-663.003174,5349.947754,60.220139),rot=vec3(107.377396,0.000000,109.999962)},tree2x23={coord=vec3(-669.687195,5347.515137,62.858566),rot=vec3(109.568581,0.000000,109.999962)},tree3x3={coord=vec3(-666.323486,5348.739258,61.586147),rot=vec3(109.568573,-0.000000,109.999962)},tree2x2={coord=vec3(-669.766846,5347.485840,62.888710),rot=vec3(109.568573,-0.000000,109.999962)},},[140]={tree2x234={coord=vec3(-669.895020,5347.812012,62.894619),rot=vec3(107.409668,-0.000002,139.999985)},tree4x4={coord=vec3(-665.264954,5353.330078,60.525944),rot=vec3(110.664268,-0.000001,139.999985)},tree2x23={coord=vec3(-669.895020,5347.812012,62.894619),rot=vec3(107.409660,-0.000002,139.999985)},tree3x3={coord=vec3(-667.564941,5350.588867,61.757950),rot=vec3(107.409653,-0.000002,139.999985)},tree2x2={coord=vec3(-669.950195,5347.746094,62.921547),rot=vec3(107.409653,-0.000002,139.999985)},},[170]={tree2x234={coord=vec3(-670.223389,5347.965332,62.977592),rot=vec3(101.184807,-0.000000,169.999908)},tree4x4={coord=vec3(-668.937378,5355.258789,61.513283),rot=vec3(104.250793,-0.000000,169.999908)},tree2x23={coord=vec3(-670.223389,5347.965332,62.977592),rot=vec3(101.184799,-0.000000,169.999908)},tree3x3={coord=vec3(-669.576233,5351.635742,62.240685),rot=vec3(101.184792,-0.000000,169.999908)},tree2x2={coord=vec3(-670.238708,5347.878418,62.995049),rot=vec3(101.184792,-0.000000,169.999908)},},[200]={tree2x234={coord=vec3(-670.584412,5347.933594,63.086506),rot=vec3(91.601341,-0.000001,-159.999939)},tree4x4={coord=vec3(-673.165283,5355.023926,62.875549),rot=vec3(92.517349,-0.000001,-159.999939)},tree2x23={coord=vec3(-670.584412,5347.933594,63.086506),rot=vec3(91.601334,-0.000001,-159.999939)},tree3x3={coord=vec3(-671.883240,5351.501953,62.980343),rot=vec3(91.601326,-0.000001,-159.999939)},tree2x2={coord=vec3(-670.553650,5347.849121,63.089024),rot=vec3(91.601326,-0.000001,-159.999939)},},[230]={tree2x234={coord=vec3(-670.881226,5347.726074,63.209927),rot=vec3(79.689926,-0.000001,-129.999985)},tree4x4={coord=vec3(-676.570740,5352.500488,64.331009),rot=vec3(83.754883,-0.000001,-129.999985)},tree2x23={coord=vec3(-670.881226,5347.726074,63.209927),rot=vec3(79.689926,-0.000001,-129.999985)},tree3x3={coord=vec3(-673.744446,5350.128418,63.889854),rot=vec3(79.689926,-0.000001,-129.999985)},tree2x2={coord=vec3(-670.813416,5347.668945,63.193817),rot=vec3(79.689926,-0.000001,-129.999985)},},[260]={tree2x234={coord=vec3(-671.034363,5347.397461,63.280739),rot=vec3(72.863625,0.000002,-99.999985)},tree4x4={coord=vec3(-678.138672,5348.649902,65.395027),rot=vec3(74.328186,0.000002,-99.999985)},tree2x23={coord=vec3(-671.034363,5347.397461,63.280739),rot=vec3(72.863625,0.000002,-99.999985)},tree3x3={coord=vec3(-674.609558,5348.027832,64.400101),rot=vec3(72.863625,0.000002,-99.999985)},tree2x2={coord=vec3(-670.949646,5347.382813,63.254223),rot=vec3(72.863625,0.000002,-99.999985)},},[290]={tree2x234={coord=vec3(-671.002747,5347.036621,63.323978),rot=vec3(70.720734,-0.000000,-69.999992)},tree4x4={coord=vec3(-677.698669,5344.599609,65.706459),rot=vec3(74.328186,0.000000,-69.999992)},tree2x23={coord=vec3(-671.002747,5347.036621,63.323978),rot=vec3(70.720734,-0.000000,-69.999992)},tree3x3={coord=vec3(-674.372437,5345.810059,64.578308),rot=vec3(70.720734,-0.000000,-69.999992)},tree2x2={coord=vec3(-670.922913,5347.065918,63.294262),rot=vec3(70.720734,-0.000000,-69.999992)},},[320]={tree2x234={coord=vec3(-670.794922,5346.739746,63.268024),rot=vec3(73.592323,-0.000000,-39.999950)},tree4x4={coord=vec3(-675.449707,5341.192871,65.290382),rot=vec3(76.580452,-0.000000,-39.999950)},tree2x23={coord=vec3(-670.794922,5346.739746,63.268024),rot=vec3(73.592323,-0.000000,-39.999950)},tree3x3={coord=vec3(-673.137451,5343.948242,64.341125),rot=vec3(73.592323,-0.000000,-39.999950)},tree2x2={coord=vec3(-670.739441,5346.805664,63.242603),rot=vec3(73.592323,-0.000000,-39.999950)},},[350]={tree2x234={coord=vec3(-670.466553,5346.586426,63.256966),rot=vec3(82.104507,-0.000001,-10.000004)},tree4x4={coord=vec3(-671.765015,5339.222168,64.293938),rot=vec3(84.592331,-0.000000,-10.000004)},tree2x23={coord=vec3(-670.466553,5346.586426,63.256966),rot=vec3(82.104507,-0.000001,-10.000004)},tree3x3={coord=vec3(-671.119995,5342.880371,63.778820),rot=vec3(82.104507,-0.000001,-10.000004)},tree2x2={coord=vec3(-670.451050,5346.674316,63.244602),rot=vec3(82.104507,-0.000001,-10.000004)},},}
PLT.Info.cutting.trees[4]={coord=vec3(-677.051941,5332.747559,65.489082),[0]={tree2x234={coord=vec3(-677.051941,5332.047363,66.191330),rot=vec3(87.155548,0.000000,0.000000)},tree4x4={coord=vec3(-677.051941,5324.507324,66.515938),rot=vec3(86.283989,0.000000,0.000000)},tree2x23={coord=vec3(-677.051941,5332.047363,66.191330),rot=vec3(88.027107,0.000000,0.000000)},tree3x3={coord=vec3(-677.051941,5328.250488,66.322113),rot=vec3(88.027107,0.000000,0.000000)},tree2x2={coord=vec3(-677.051941,5332.137207,66.188232),rot=vec3(89.796448,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-676.701965,5332.141113,66.068283),rot=vec3(92.517349,-0.000001,29.999979)},tree4x4={coord=vec3(-672.931152,5325.609863,65.736710),rot=vec3(92.517349,-0.000001,29.999979)},tree2x23={coord=vec3(-676.701965,5332.141113,66.068283),rot=vec3(93.442505,-0.000001,29.999979)},tree3x3={coord=vec3(-674.805908,5328.856934,65.685158),rot=vec3(90.667160,-0.000001,29.999979)},tree2x2={coord=vec3(-676.746887,5332.218750,66.073685),rot=vec3(96.273880,-0.000002,29.999979)},},[60]={tree2x234={coord=vec3(-676.445740,5332.397461,65.967545),rot=vec3(99.191086,-0.000000,60.000000)},tree4x4={coord=vec3(-669.992065,5328.671387,64.674255),rot=vec3(97.207428,0.000000,60.000000)},tree2x23={coord=vec3(-676.445740,5332.397461,65.967545),rot=vec3(100.182983,-0.000000,60.000000)},tree3x3={coord=vec3(-673.207520,5330.527832,65.220901),rot=vec3(99.171204,0.000000,60.000000)},tree2x2={coord=vec3(-676.522461,5332.441406,65.983452),rot=vec3(102.196655,-0.000000,60.000000)},},[90]={tree2x234={coord=vec3(-676.351929,5332.747559,65.945496),rot=vec3(105.293289,-0.000000,90.000000)},tree4x4={coord=vec3(-669.070251,5332.747559,63.954376),rot=vec3(105.293289,-0.000000,90.000000)},tree2x23={coord=vec3(-676.351929,5332.747559,65.945496),rot=vec3(105.293282,-0.000000,90.000000)},tree3x3={coord=vec3(-672.687439,5332.747559,64.943474),rot=vec3(105.293274,-0.000000,90.000000)},tree2x2={coord=vec3(-676.438721,5332.747559,65.969231),rot=vec3(105.293274,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-676.445740,5333.097656,65.940765),rot=vec3(105.293396,0.000000,120.000000)},tree4x4={coord=vec3(-670.139648,5336.738281,63.949631),rot=vec3(105.293396,0.000000,120.000000)},tree2x23={coord=vec3(-676.445740,5333.097656,65.940765),rot=vec3(105.293388,-0.000000,120.000000)},tree3x3={coord=vec3(-673.272217,5334.929688,64.938736),rot=vec3(105.293381,-0.000000,120.000000)},tree2x2={coord=vec3(-676.520935,5333.054199,65.964500),rot=vec3(105.293381,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-676.701965,5333.354004,66.024506),rot=vec3(100.182983,-0.000000,149.999954)},tree4x4={coord=vec3(-672.986938,5339.788574,64.689896),rot=vec3(104.250793,-0.000001,149.999954)},tree2x23={coord=vec3(-676.701965,5333.354004,66.024506),rot=vec3(100.182976,-0.000000,149.999954)},tree3x3={coord=vec3(-674.832397,5336.592285,65.352875),rot=vec3(100.182968,-0.000000,149.999954)},tree2x2={coord=vec3(-676.746277,5333.277344,66.040413),rot=vec3(100.182968,-0.000000,149.999954)},},[180]={tree2x234={coord=vec3(-677.051941,5333.447754,66.091812),rot=vec3(91.601341,-0.000000,-179.999893)},tree4x4={coord=vec3(-677.051941,5340.993652,65.770859),rot=vec3(93.442513,-0.000000,-179.999893)},tree2x23={coord=vec3(-677.051941,5333.447754,66.091812),rot=vec3(91.601334,-0.000000,-179.999893)},tree3x3={coord=vec3(-677.051941,5337.245117,65.985649),rot=vec3(91.601326,-0.000000,-179.999893)},tree2x2={coord=vec3(-677.051941,5333.357910,66.094330),rot=vec3(91.601326,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-677.401917,5333.354004,66.219894),rot=vec3(84.592331,-0.000000,-149.999954)},tree4x4={coord=vec3(-681.159668,5339.862793,66.876328),rot=vec3(88.027107,-0.000000,-149.999954)},tree2x23={coord=vec3(-677.401917,5333.354004,66.219894),rot=vec3(84.592323,-0.000001,-149.999954)},tree3x3={coord=vec3(-679.292969,5336.629395,66.577919),rot=vec3(84.592316,-0.000001,-149.999954)},tree2x2={coord=vec3(-677.357117,5333.276367,66.211411),rot=vec3(85.438248,-0.000001,-149.999954)},},[240]={tree2x234={coord=vec3(-677.658142,5333.097656,66.259453),rot=vec3(79.689850,-0.000002,-119.999985)},tree4x4={coord=vec3(-684.090149,5336.811035,67.500549),rot=vec3(82.104507,-0.000002,-119.999985)},tree2x23={coord=vec3(-677.658142,5333.097656,66.259453),rot=vec3(79.689850,-0.000002,-119.999985)},tree3x3={coord=vec3(-680.895020,5334.966309,66.939384),rot=vec3(79.689850,-0.000002,-119.999985)},tree2x2={coord=vec3(-677.581421,5333.053711,66.243340),rot=vec3(79.689850,-0.000002,-119.999985)},},[270]={tree2x234={coord=vec3(-677.751953,5332.747559,66.303528),rot=vec3(77.346176,0.000000,-90.000000)},tree4x4={coord=vec3(-685.117615,5332.747559,67.787201),rot=vec3(81.291595,0.000000,-90.000000)},tree2x23={coord=vec3(-677.751953,5332.747559,66.303528),rot=vec3(77.346169,0.000000,-90.000000)},tree3x3={coord=vec3(-681.458679,5332.747559,67.135735),rot=vec3(78.119614,0.000000,-90.000000)},tree2x2={coord=vec3(-677.664124,5332.747559,66.283813),rot=vec3(77.346161,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-677.658142,5332.397461,66.280449),rot=vec3(78.119644,0.000001,-60.000000)},tree4x4={coord=vec3(-684.055725,5328.704102,67.724548),rot=vec3(81.291595,0.000000,-60.000000)},tree2x23={coord=vec3(-677.658142,5332.397461,66.280449),rot=vec3(78.119644,0.000001,-60.000000)},tree3x3={coord=vec3(-680.877686,5330.538574,67.062546),rot=vec3(78.119644,0.000001,-60.000000)},tree2x2={coord=vec3(-677.581848,5332.441406,66.261917),rot=vec3(78.119644,0.000001,-60.000000)},},[330]={tree2x234={coord=vec3(-677.401917,5332.141113,66.256508),rot=vec3(82.104507,0.000001,-29.999979)},tree4x4={coord=vec3(-681.140625,5325.665039,67.238487),rot=vec3(82.104507,0.000001,-29.999979)},tree2x23={coord=vec3(-677.401917,5332.141113,66.256508),rot=vec3(82.104507,0.000002,-29.999979)},tree3x3={coord=vec3(-679.283386,5328.882324,66.778366),rot=vec3(82.104507,0.000002,-29.999979)},tree2x2={coord=vec3(-677.357361,5332.218262,66.244148),rot=vec3(82.925537,0.000002,-29.999979)},},}
PLT.Info.cutting.trees[5]={coord=vec3(-685.429626,5348.882813,66.259949),[0]={tree2x234={coord=vec3(-685.429626,5348.182617,66.969696),rot=vec3(86.292625,0.000000,0.000000)},tree4x4={coord=vec3(-685.429626,5340.694336,67.454910),rot=vec3(86.292625,0.000000,0.000000)},tree2x23={coord=vec3(-685.429626,5348.182617,66.969696),rot=vec3(86.292618,0.000000,0.000000)},tree3x3={coord=vec3(-685.429626,5344.391602,67.215340),rot=vec3(86.292610,0.000000,0.000000)},tree2x2={coord=vec3(-685.429626,5348.212402,66.967751),rot=vec3(86.292610,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-685.079651,5348.276367,66.912857),rot=vec3(90.694412,-0.000001,29.999979)},tree4x4={coord=vec3(-681.327942,5341.778320,66.821907),rot=vec3(94.376930,-0.000001,29.999979)},tree2x23={coord=vec3(-685.079651,5348.276367,66.912857),rot=vec3(90.694412,-0.000001,29.999979)},tree3x3={coord=vec3(-683.180298,5344.986816,66.866814),rot=vec3(90.694412,-0.000001,29.999979)},tree2x2={coord=vec3(-685.094666,5348.302246,66.913223),rot=vec3(90.694412,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-684.823425,5348.532715,66.844795),rot=vec3(96.273895,0.000000,60.000000)},tree4x4={coord=vec3(-678.363708,5344.803223,65.854736),rot=vec3(101.184807,-0.000000,60.000000)},tree2x23={coord=vec3(-684.823425,5348.532715,66.844795),rot=vec3(96.273888,0.000000,60.000000)},tree3x3={coord=vec3(-681.553101,5346.644531,66.429634),rot=vec3(96.273880,0.000000,60.000000)},tree2x2={coord=vec3(-684.849243,5348.547363,66.848068),rot=vec3(96.273880,0.000000,60.000000)},},[90]={tree2x234={coord=vec3(-684.729614,5348.882813,66.843987),rot=vec3(101.184807,-0.000000,90.000000)},tree4x4={coord=vec3(-677.368103,5348.882813,65.158394),rot=vec3(106.346214,-0.000000,90.000000)},tree2x23={coord=vec3(-684.729614,5348.882813,66.843987),rot=vec3(101.184799,-0.000000,90.000000)},tree3x3={coord=vec3(-681.002747,5348.882813,66.107079),rot=vec3(101.184792,-0.000000,90.000000)},tree2x2={coord=vec3(-684.759033,5348.882813,66.849800),rot=vec3(101.184792,-0.000000,90.000000)},},[125]={tree2x234={coord=vec3(-684.856201,5349.284180,66.822395),rot=vec3(104.250793,-0.000000,124.999985)},tree4x4={coord=vec3(-678.898438,5353.456055,64.865158),rot=vec3(109.568588,-0.000003,124.999985)},tree2x23={coord=vec3(-684.856201,5349.284180,66.822395),rot=vec3(104.250793,-0.000000,124.999985)},tree3x3={coord=vec3(-681.840027,5351.395996,65.887207),rot=vec3(104.250793,-0.000000,124.999985)},tree2x2={coord=vec3(-684.880005,5349.267578,66.829781),rot=vec3(104.250793,-0.000000,124.999985)},},[155]={tree2x234={coord=vec3(-685.133789,5349.517090,66.823418),rot=vec3(103.218605,0.000001,154.999954)},tree4x4={coord=vec3(-682.046448,5356.137695,64.997498),rot=vec3(106.346214,0.000001,154.999954)},tree2x23={coord=vec3(-685.133789,5349.517090,66.823418),rot=vec3(103.218597,-0.000000,154.999954)},tree3x3={coord=vec3(-683.570801,5352.869141,65.954712),rot=vec3(103.218590,-0.000000,154.999954)},tree2x2={coord=vec3(-685.146118,5349.490723,66.830276),rot=vec3(103.218590,-0.000000,154.999954)},},[185]={tree2x234={coord=vec3(-685.490662,5349.580078,66.856789),rot=vec3(100.182983,0.000001,-174.999893)},tree4x4={coord=vec3(-686.134338,5356.937988,65.530136),rot=vec3(104.250793,0.000002,-174.999893)},tree2x23={coord=vec3(-685.490662,5349.580078,66.856789),rot=vec3(100.182976,0.000001,-174.999893)},tree3x3={coord=vec3(-685.816528,5353.305176,66.185158),rot=vec3(100.182968,0.000001,-174.999893)},tree2x2={coord=vec3(-685.488098,5349.550781,66.862091),rot=vec3(100.182968,0.000001,-174.999893)},},[215]={tree2x234={coord=vec3(-685.831116,5349.456055,66.873802),rot=vec3(94.377052,0.000002,-144.999969)},tree4x4={coord=vec3(-690.122681,5355.585449,66.246101),rot=vec3(99.191185,0.000001,-144.999969)},tree2x23={coord=vec3(-685.831116,5349.456055,66.873802),rot=vec3(94.377052,0.000002,-144.999969)},tree3x3={coord=vec3(-688.003784,5352.559082,66.583862),rot=vec3(94.377052,0.000002,-144.999969)},tree2x2={coord=vec3(-685.813965,5349.431641,66.876091),rot=vec3(94.377052,0.000002,-144.999969)},},[245]={tree2x234={coord=vec3(-686.064026,5349.178711,66.993469),rot=vec3(90.694496,0.000004,-114.999985)},tree4x4={coord=vec3(-692.864441,5352.350098,66.792511),rot=vec3(94.377052,0.000003,-114.999985)},tree2x23={coord=vec3(-686.064026,5349.178711,66.993469),rot=vec3(90.694489,0.000004,-114.999985)},tree3x3={coord=vec3(-689.506836,5350.784180,66.947418),rot=vec3(90.694481,0.000004,-114.999985)},tree2x2={coord=vec3(-686.036865,5349.166016,66.993835),rot=vec3(90.694481,0.000004,-114.999985)},},[275]={tree2x234={coord=vec3(-686.126953,5348.821777,66.987846),rot=vec3(87.155548,-0.000002,-84.999954)},tree4x4={coord=vec3(-693.593262,5348.168945,67.250229),rot=vec3(90.694412,-0.000002,-84.999954)},tree2x23={coord=vec3(-686.126953,5348.821777,66.987846),rot=vec3(87.155548,-0.000002,-84.999954)},tree3x3={coord=vec3(-689.906860,5348.491211,67.176369),rot=vec3(87.155548,-0.000002,-84.999954)},tree2x2={coord=vec3(-686.097107,5348.824707,66.986359),rot=vec3(87.155548,-0.000002,-84.999954)},},[320]={tree2x234={coord=vec3(-685.879578,5348.346680,67.010040),rot=vec3(84.592438,-0.000000,-39.999950)},tree4x4={coord=vec3(-690.681580,5342.623535,67.717216),rot=vec3(84.592438,-0.000000,-39.999950)},tree2x23={coord=vec3(-685.879578,5348.346680,67.010040),rot=vec3(84.592438,-0.000000,-39.999950)},tree3x3={coord=vec3(-688.310669,5345.449219,67.368057),rot=vec3(84.592438,-0.000001,-39.999950)},tree2x2={coord=vec3(-685.860352,5348.369629,67.007210),rot=vec3(84.592438,-0.000001,-39.999950)},},}
PLT.Info.cutting.trees[6]={coord=vec3(-690.436523,5326.994141,67.996536),[0]={tree2x234={coord=vec3(-690.436523,5326.293945,68.660286),rot=vec3(88.907364,0.000000,0.000000)},tree4x4={coord=vec3(-690.436523,5318.746582,68.804237),rot=vec3(90.694412,-0.000000,0.000000)},tree2x23={coord=vec3(-690.436523,5326.293945,68.660286),rot=vec3(88.907356,0.000000,0.000000)},tree3x3={coord=vec3(-690.436523,5322.495605,68.540222),rot=vec3(86.258057,0.000000,0.000000)},tree2x2={coord=vec3(-690.436523,5326.383789,68.658569),rot=vec3(91.601318,-0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-690.086548,5326.387695,68.604401),rot=vec3(92.517349,-0.000001,29.999979)},tree4x4={coord=vec3(-686.315735,5319.856445,68.272827),rot=vec3(93.442513,-0.000001,29.999979)},tree2x23={coord=vec3(-690.086548,5326.387695,68.604401),rot=vec3(93.442505,-0.000001,29.999979)},tree3x3={coord=vec3(-688.190491,5323.103516,68.326279),rot=vec3(92.508080,-0.000001,29.999979)},tree2x2={coord=vec3(-690.131470,5326.465332,68.609802),rot=vec3(95.320679,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-689.830322,5326.644043,68.568069),rot=vec3(96.273895,0.000000,60.000000)},tree4x4={coord=vec3(-683.331909,5322.892090,67.743103),rot=vec3(97.236641,0.000000,60.000000)},tree2x23={coord=vec3(-689.830322,5326.644043,68.568069),rot=vec3(96.273888,0.000000,60.000000)},tree3x3={coord=vec3(-686.559998,5324.755859,68.152908),rot=vec3(96.273880,0.000000,60.000000)},tree2x2={coord=vec3(-689.907776,5326.688965,68.577904),rot=vec3(97.236610,0.000001,60.000000)},},[120]={tree2x234={coord=vec3(-689.830322,5327.344238,68.589043),rot=vec3(97.236740,-0.000000,120.000000)},tree4x4={coord=vec3(-683.344788,5331.088867,67.528091),rot=vec3(99.191185,-0.000000,120.000000)},tree2x23={coord=vec3(-689.830322,5327.344238,68.589043),rot=vec3(97.236732,0.000000,120.000000)},tree3x3={coord=vec3(-686.566528,5329.228516,68.110489),rot=vec3(97.236725,-0.000000,120.000000)},tree2x2={coord=vec3(-689.907654,5327.299805,68.600380),rot=vec3(97.236725,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-690.086548,5327.600586,68.585739),rot=vec3(94.376930,-0.000000,149.999954)},tree4x4={coord=vec3(-686.323059,5334.118652,68.009621),rot=vec3(97.236641,-0.000000,149.999954)},tree2x23={coord=vec3(-690.086548,5327.600586,68.585739),rot=vec3(94.376923,-0.000000,149.999954)},tree3x3={coord=vec3(-688.192566,5330.880859,68.295807),rot=vec3(94.376923,-0.000000,149.999954)},tree2x2={coord=vec3(-690.131409,5327.522949,68.592606),rot=vec3(94.376923,-0.000000,149.999954)},},[180]={tree2x234={coord=vec3(-690.436523,5327.694336,68.646545),rot=vec3(92.517349,-0.000000,-179.999893)},tree4x4={coord=vec3(-690.436523,5335.236328,68.314972),rot=vec3(92.517349,-0.000000,-179.999893)},tree2x23={coord=vec3(-690.436523,5327.694336,68.646545),rot=vec3(92.517349,-0.000000,-179.999893)},tree3x3={coord=vec3(-690.436523,5331.489746,68.479683),rot=vec3(92.517349,-0.000000,-179.999893)},tree2x2={coord=vec3(-690.436523,5327.604492,68.650497),rot=vec3(92.517349,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-690.786499,5327.600586,68.701691),rot=vec3(89.796448,-0.000000,-149.999954)},tree4x4={coord=vec3(-694.560974,5334.138184,68.728508),rot=vec3(93.442513,-0.000000,-149.999954)},tree2x23={coord=vec3(-690.786499,5327.600586,68.701691),rot=vec3(89.796448,-0.000000,-149.999954)},tree3x3={coord=vec3(-692.685974,5330.890625,68.715187),rot=vec3(89.796448,-0.000000,-149.999954)},tree2x2={coord=vec3(-690.741455,5327.522949,68.701370),rot=vec3(89.796448,-0.000000,-149.999954)},},[240]={tree2x234={coord=vec3(-691.042725,5327.344238,68.736526),rot=vec3(87.155548,-0.000002,-119.999985)},tree4x4={coord=vec3(-697.572266,5331.113770,69.111137),rot=vec3(88.027107,-0.000002,-119.999985)},tree2x23={coord=vec3(-691.042725,5327.344238,68.736526),rot=vec3(87.155548,-0.000002,-119.999985)},tree3x3={coord=vec3(-694.328674,5329.241211,68.925049),rot=vec3(87.155548,-0.000002,-119.999985)},tree2x2={coord=vec3(-690.964905,5327.299316,68.732063),rot=vec3(88.027107,-0.000002,-119.999985)},},[270]={tree2x234={coord=vec3(-691.136536,5326.994141,68.741730),rot=vec3(84.592331,0.000000,-90.000000)},tree4x4={coord=vec3(-698.651978,5326.994141,69.453163),rot=vec3(84.592331,0.000000,-90.000000)},tree2x23={coord=vec3(-691.136536,5326.994141,68.741730),rot=vec3(84.592323,0.000000,-90.000000)},tree3x3={coord=vec3(-694.918640,5326.994141,69.099754),rot=vec3(84.592316,0.000000,-90.000000)},tree2x2={coord=vec3(-691.046936,5326.994141,68.733246),rot=vec3(85.438248,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-691.042725,5326.644043,68.743401),rot=vec3(84.583855,-0.000000,-60.000000)},tree4x4={coord=vec3(-697.551147,5322.886230,69.455940),rot=vec3(84.583855,-0.000000,-60.000000)},tree2x23={coord=vec3(-691.042725,5326.644043,68.743401),rot=vec3(84.583847,-0.000000,-60.000000)},tree3x3={coord=vec3(-694.318054,5324.752930,69.101982),rot=vec3(84.583839,-0.000000,-60.000000)},tree2x2={coord=vec3(-690.965088,5326.688965,68.734909),rot=vec3(85.429672,-0.000000,-60.000000)},},[330]={tree2x234={coord=vec3(-690.786499,5326.387695,68.693451),rot=vec3(86.283989,0.000001,-29.999979)},tree4x4={coord=vec3(-694.553040,5319.863770,69.182709),rot=vec3(86.283989,0.000001,-29.999979)},tree2x23={coord=vec3(-690.786499,5326.387695,68.693451),rot=vec3(87.146812,0.000001,-29.999979)},tree3x3={coord=vec3(-692.683655,5323.101563,68.882553),rot=vec3(87.146812,0.000001,-29.999979)},tree2x2={coord=vec3(-690.741577,5326.465332,68.688972),rot=vec3(88.018265,0.000001,-29.999979)},},}
PLT.Info.cutting.trees[7]={coord=vec3(-747.431091,5364.630371,57.455418),[0]={tree2x234={coord=vec3(-747.431091,5363.930176,58.213676),rot=vec3(79.689850,0.000000,0.000000)},tree4x4={coord=vec3(-747.431091,5356.502930,59.334770),rot=vec3(82.104507,0.000000,0.000000)},tree2x23={coord=vec3(-747.431091,5363.930176,58.213676),rot=vec3(79.689850,0.000000,0.000000)},tree3x3={coord=vec3(-747.431091,5360.192383,58.893608),rot=vec3(79.689850,0.000000,0.000000)},tree2x2={coord=vec3(-747.431091,5364.018555,58.197567),rot=vec3(80.486732,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-747.081116,5364.023926,58.225365),rot=vec3(75.822174,-0.000000,29.999979)},tree4x4={coord=vec3(-743.421631,5357.685059,59.964355),rot=vec3(79.689850,-0.000001,29.999979)},tree2x23={coord=vec3(-747.081116,5364.023926,58.225365),rot=vec3(75.822174,-0.000000,29.999979)},tree3x3={coord=vec3(-745.239502,5360.833984,59.155861),rot=vec3(75.822174,-0.000000,29.999979)},tree2x2={coord=vec3(-747.124756,5364.099609,58.203323),rot=vec3(75.822174,-0.000000,29.999979)},},[60]={tree2x234={coord=vec3(-746.824890,5364.280273,58.225800),rot=vec3(77.346176,0.000000,60.000000)},tree4x4={coord=vec3(-740.446045,5360.597168,59.879482),rot=vec3(80.486732,-0.000000,60.000000)},tree2x23={coord=vec3(-746.824890,5364.280273,58.225800),rot=vec3(77.346169,0.000001,60.000000)},tree3x3={coord=vec3(-743.614746,5362.426758,59.058010),rot=vec3(77.346161,0.000001,60.000000)},tree2x2={coord=vec3(-746.900940,5364.324219,58.206081),rot=vec3(77.346161,0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-746.731079,5364.630371,58.244953),rot=vec3(83.754791,-0.000000,90.000000)},tree4x4={coord=vec3(-739.226929,5364.630371,59.066162),rot=vec3(85.438248,-0.000000,90.000000)},tree2x23={coord=vec3(-746.731079,5364.630371,58.244953),rot=vec3(83.754784,-0.000000,90.000000)},tree3x3={coord=vec3(-742.954651,5364.630371,58.658222),rot=vec3(83.754784,-0.000000,90.000000)},tree2x2={coord=vec3(-746.820557,5364.630371,58.235161),rot=vec3(83.754784,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-746.824890,5364.980469,58.121490),rot=vec3(93.442627,-0.000000,120.000000)},tree4x4={coord=vec3(-740.299072,5368.748047,57.668182),rot=vec3(93.442627,-0.000000,120.000000)},tree2x23={coord=vec3(-746.824890,5364.980469,58.121490),rot=vec3(93.442627,-0.000000,120.000000)},tree3x3={coord=vec3(-743.540771,5366.876465,57.893364),rot=vec3(93.442627,-0.000000,120.000000)},tree2x2={coord=vec3(-746.902710,5364.935547,58.126892),rot=vec3(93.442627,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-747.081116,5365.236816,57.984943),rot=vec3(102.196655,0.000000,149.999954)},tree4x4={coord=vec3(-743.391785,5371.626953,56.390083),rot=vec3(102.196655,0.000000,149.999954)},tree2x23={coord=vec3(-747.081116,5365.236816,57.984943),rot=vec3(102.196655,0.000000,149.999954)},tree3x3={coord=vec3(-745.224487,5368.452637,57.182339),rot=vec3(102.196655,0.000000,149.999954)},tree2x2={coord=vec3(-747.125061,5365.160645,58.003956),rot=vec3(102.196655,0.000000,149.999954)},},[180]={tree2x234={coord=vec3(-747.431091,5365.330566,57.847946),rot=vec3(107.409668,-0.000000,-179.999893)},tree4x4={coord=vec3(-747.431091,5372.533691,55.479271),rot=vec3(108.483757,-0.000000,-179.999893)},tree2x23={coord=vec3(-747.431091,5365.330566,57.847946),rot=vec3(107.409660,-0.000000,-179.999893)},tree3x3={coord=vec3(-747.431091,5368.955566,56.711277),rot=vec3(107.409653,-0.000000,-179.999893)},tree2x2={coord=vec3(-747.431091,5365.244629,57.874874),rot=vec3(108.483742,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-747.781067,5365.236816,57.843224),rot=vec3(109.568588,-0.000000,-149.999954)},tree4x4={coord=vec3(-751.337585,5371.396973,55.314796),rot=vec3(112.888603,-0.000000,-149.999954)},tree2x23={coord=vec3(-747.781067,5365.236816,57.843224),rot=vec3(109.568581,-0.000000,-149.999954)},tree3x3={coord=vec3(-749.570862,5368.336914,56.570805),rot=vec3(109.568573,-0.000000,-149.999954)},tree2x2={coord=vec3(-747.738647,5365.163574,57.873367),rot=vec3(110.664253,-0.000000,-149.999954)},},[240]={tree2x234={coord=vec3(-748.037292,5364.980469,57.859581),rot=vec3(107.409668,-0.000003,-119.999985)},tree4x4={coord=vec3(-754.275452,5368.582520,55.600906),rot=vec3(107.409668,-0.000003,-119.999985)},tree2x23={coord=vec3(-748.037292,5364.980469,57.859581),rot=vec3(107.409660,-0.000002,-119.999985)},tree3x3={coord=vec3(-751.176636,5366.792969,56.722912),rot=vec3(107.409653,-0.000001,-119.999985)},tree2x2={coord=vec3(-747.962952,5364.937500,57.886509),rot=vec3(107.409653,-0.000001,-119.999985)},},[275]={tree2x234={coord=vec3(-748.128418,5364.569336,57.965714),rot=vec3(101.184807,-0.000002,-84.999954)},tree4x4={coord=vec3(-755.505859,5363.923828,56.501404),rot=vec3(104.250793,-0.000002,-84.999954)},tree2x23={coord=vec3(-748.128418,5364.569336,57.965714),rot=vec3(101.184799,-0.000002,-84.999954)},tree3x3={coord=vec3(-751.841064,5364.244629,57.228806),rot=vec3(101.184792,-0.000002,-84.999954)},tree2x2={coord=vec3(-748.040466,5364.577148,57.983170),rot=vec3(101.184792,-0.000002,-84.999954)},},[305]={tree2x234={coord=vec3(-748.004517,5364.229004,58.030994),rot=vec3(94.376930,0.000001,-54.999832)},tree4x4={coord=vec3(-754.170288,5359.911133,57.454868),rot=vec3(102.196655,0.000002,-54.999809)},tree2x23={coord=vec3(-748.004517,5364.229004,58.030994),rot=vec3(94.376923,0.000001,-54.999828)},tree3x3={coord=vec3(-751.107422,5362.056152,57.741062),rot=vec3(94.376923,0.000000,-54.999825)},tree2x2={coord=vec3(-747.931030,5364.280273,58.037861),rot=vec3(94.376923,0.000000,-54.999825)},},[340]={tree2x234={coord=vec3(-747.670532,5363.972656,58.165131),rot=vec3(84.592438,0.000008,-19.999992)},tree4x4={coord=vec3(-750.240967,5356.910156,58.706547),rot=vec3(90.694496,0.000007,-19.999992)},tree2x23={coord=vec3(-747.670532,5363.972656,58.165131),rot=vec3(84.592438,0.000008,-19.999992)},tree3x3={coord=vec3(-748.964111,5360.418457,58.523148),rot=vec3(84.592438,0.000008,-19.999992)},tree2x2={coord=vec3(-747.639893,5364.056641,58.156651),rot=vec3(84.592438,0.000008,-19.999992)},},}
PLT.Info.cutting.trees[8]={coord=vec3(-736.037415,5360.251465,59.739574),[0]={tree2x234={coord=vec3(-736.037415,5359.551270,60.559448),rot=vec3(82.104507,0.000000,0.000000)},tree4x4={coord=vec3(-736.037415,5352.073730,61.596424),rot=vec3(83.754791,0.000000,0.000000)},tree2x23={coord=vec3(-736.037415,5359.551270,60.559448),rot=vec3(82.104507,0.000000,0.000000)},tree3x3={coord=vec3(-736.037415,5355.788086,61.081303),rot=vec3(82.104507,0.000000,0.000000)},tree2x2={coord=vec3(-736.037415,5359.640625,60.547085),rot=vec3(82.925537,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-735.687439,5359.645020,60.636730),rot=vec3(81.291595,-0.000001,29.999979)},tree4x4={coord=vec3(-731.956421,5353.183105,61.779694),rot=vec3(81.291595,-0.000001,29.999979)},tree2x23={coord=vec3(-735.687439,5359.645020,60.586731),rot=vec3(81.283447,-0.000000,29.999979)},tree3x3={coord=vec3(-733.809875,5356.393066,61.162457),rot=vec3(81.283440,-0.000000,29.999979)},tree2x2={coord=vec3(-735.731934,5359.722168,60.573090),rot=vec3(82.096283,0.000000,29.999979)},},[60]={tree2x234={coord=vec3(-735.431213,5359.901367,60.547253),rot=vec3(82.925545,-0.000000,60.000000)},tree4x4={coord=vec3(-728.943420,5356.155273,61.476978),rot=vec3(82.925545,-0.000000,60.000000)},tree2x23={coord=vec3(-735.431213,5359.901367,60.547253),rot=vec3(82.925537,-0.000000,60.000000)},tree3x3={coord=vec3(-732.166260,5358.016113,61.015133),rot=vec3(82.925537,-0.000000,60.000000)},tree2x2={coord=vec3(-735.508545,5359.945801,60.536167),rot=vec3(83.754784,-0.000000,60.000000)},},[90]={tree2x234={coord=vec3(-735.337402,5360.251465,60.444679),rot=vec3(87.155548,-0.000000,90.000000)},tree4x4={coord=vec3(-727.797729,5360.251465,60.709293),rot=vec3(87.155548,-0.000000,90.000000)},tree2x23={coord=vec3(-735.337402,5360.251465,60.444679),rot=vec3(87.155548,-0.000000,90.000000)},tree3x3={coord=vec3(-731.543091,5360.251465,60.633202),rot=vec3(87.155548,-0.000000,90.000000)},tree2x2={coord=vec3(-735.427307,5360.251465,60.440212),rot=vec3(88.027107,-0.000000,90.000000)},},[135]={tree2x234={coord=vec3(-735.542419,5360.746582,60.329174),rot=vec3(96.273895,-0.000000,135.000000)},tree4x4={coord=vec3(-730.236389,5366.052734,59.504208),rot=vec3(96.273895,-0.000000,135.000000)},tree2x23={coord=vec3(-735.542419,5360.746582,60.329174),rot=vec3(96.273888,-0.000000,135.000000)},tree3x3={coord=vec3(-732.872192,5363.416992,59.914013),rot=vec3(96.273880,-0.000000,135.000000)},tree2x2={coord=vec3(-735.605652,5360.683594,60.339008),rot=vec3(97.236610,-0.000000,135.000000)},},[165]={tree2x234={coord=vec3(-735.856262,5360.927734,60.258129),rot=vec3(102.196793,-0.000000,164.999924)},tree4x4={coord=vec3(-733.946594,5368.055176,58.608250),rot=vec3(103.218750,-0.000000,164.999924)},tree2x23={coord=vec3(-735.856262,5360.927734,60.258129),rot=vec3(102.196785,0.000000,164.999924)},tree3x3={coord=vec3(-734.895203,5364.514648,59.455513),rot=vec3(102.196785,0.000001,164.999924)},tree2x2={coord=vec3(-735.879028,5360.842773,60.277142),rot=vec3(102.196785,0.000001,164.999924)},},[210]={tree2x234={coord=vec3(-736.387390,5360.857910,60.182190),rot=vec3(104.250793,0.000001,-149.999954)},tree4x4={coord=vec3(-740.045776,5367.194824,58.213875),rot=vec3(108.483757,-0.000001,-149.999954)},tree2x23={coord=vec3(-736.387390,5360.857910,60.182190),rot=vec3(104.250793,0.000001,-149.999954)},tree3x3={coord=vec3(-738.228455,5364.046875,59.247002),rot=vec3(104.250793,0.000002,-149.999954)},tree2x2={coord=vec3(-736.343811,5360.782227,60.204346),rot=vec3(104.250793,0.000002,-149.999954)},},[240]={tree2x234={coord=vec3(-736.643616,5360.601563,60.230713),rot=vec3(101.184807,-0.000001,-119.999985)},tree4x4={coord=vec3(-743.057068,5364.303711,58.766403),rot=vec3(103.218605,-0.000001,-119.999985)},tree2x23={coord=vec3(-736.643616,5360.601563,60.230713),rot=vec3(101.184799,-0.000001,-119.999985)},tree3x3={coord=vec3(-739.871155,5362.464844,59.493805),rot=vec3(101.184792,-0.000002,-119.999985)},tree2x2={coord=vec3(-736.567139,5360.557617,60.248169),rot=vec3(101.184792,-0.000002,-119.999985)},},[270]={tree2x234={coord=vec3(-736.737427,5360.251465,60.330860),rot=vec3(96.273895,0.000000,-90.000000)},tree4x4={coord=vec3(-744.241211,5360.251465,59.395893),rot=vec3(98.209000,0.000000,-90.000000)},tree2x23={coord=vec3(-736.737427,5360.251465,60.330860),rot=vec3(96.273888,0.000000,-90.000000)},tree3x3={coord=vec3(-740.513672,5360.251465,59.915699),rot=vec3(96.273880,0.000000,-90.000000)},tree2x2={coord=vec3(-736.647949,5360.251465,60.340694),rot=vec3(96.273880,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-736.643616,5359.901367,60.399815),rot=vec3(90.694412,0.000000,-60.000000)},tree4x4={coord=vec3(-743.180786,5356.127441,60.198322),rot=vec3(92.517349,0.000000,-60.000000)},tree2x23={coord=vec3(-736.643616,5359.901367,60.399815),rot=vec3(90.694412,0.000000,-60.000000)},tree3x3={coord=vec3(-739.933411,5358.001953,60.353771),rot=vec3(90.694412,0.000000,-60.000000)},tree2x2={coord=vec3(-736.565674,5359.946289,60.400906),rot=vec3(90.694412,0.000000,-60.000000)},},}
PLT.Info.cutting.trees[9]={coord=vec3(-721.466736,5373.521973,58.231228),[0]={tree2x234={coord=vec3(-721.466736,5372.821777,59.020813),rot=vec3(78.119644,0.000000,0.000000)},tree4x4={coord=vec3(-721.466736,5365.434570,60.464916),rot=vec3(78.119644,0.000000,0.000000)},tree2x23={coord=vec3(-721.466736,5372.821777,59.020813),rot=vec3(78.119644,0.000000,0.000000)},tree3x3={coord=vec3(-721.466736,5369.104004,59.802910),rot=vec3(78.119644,0.000000,0.000000)},tree2x2={coord=vec3(-721.466736,5372.909668,59.002285),rot=vec3(78.900841,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-721.116760,5372.915527,59.041676),rot=vec3(76.580383,-0.000000,29.999979)},tree4x4={coord=vec3(-717.445313,5366.556152,60.793655),rot=vec3(76.580383,-0.000000,29.999979)},tree2x23={coord=vec3(-721.116760,5372.915527,59.041676),rot=vec3(76.580376,-0.000000,29.999979)},tree3x3={coord=vec3(-719.269104,5369.715332,59.923351),rot=vec3(76.580368,-0.000000,29.999979)},tree2x2={coord=vec3(-721.160522,5372.991699,59.020790),rot=vec3(77.346161,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-720.860535,5373.171875,58.996578),rot=vec3(78.119644,-0.000001,60.000000)},tree4x4={coord=vec3(-714.462952,5369.478516,60.550682),rot=vec3(78.119644,-0.000001,60.000000)},tree2x23={coord=vec3(-720.860535,5373.171875,58.996578),rot=vec3(78.119644,-0.000001,60.000000)},tree3x3={coord=vec3(-717.640991,5371.312988,59.778675),rot=vec3(78.119644,-0.000001,60.000000)},tree2x2={coord=vec3(-720.936829,5373.215820,58.978050),rot=vec3(78.900841,-0.000000,60.000000)},},[90]={tree2x234={coord=vec3(-720.766724,5373.521973,58.957722),rot=vec3(84.592331,-0.000000,90.000000)},tree4x4={coord=vec3(-713.251282,5373.521973,59.669147),rot=vec3(84.592331,-0.000000,90.000000)},tree2x23={coord=vec3(-720.766724,5373.521973,58.957722),rot=vec3(84.592323,-0.000000,90.000000)},tree3x3={coord=vec3(-716.984619,5373.521973,59.315746),rot=vec3(84.592316,-0.000000,90.000000)},tree2x2={coord=vec3(-720.856323,5373.521973,58.949242),rot=vec3(85.438248,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-720.860535,5373.872070,58.831551),rot=vec3(92.517448,-0.000000,120.000000)},tree4x4={coord=vec3(-714.329163,5377.642578,58.499969),rot=vec3(94.377052,-0.000000,120.000000)},tree2x23={coord=vec3(-720.860535,5373.872070,58.831551),rot=vec3(92.517441,-0.000000,120.000000)},tree3x3={coord=vec3(-717.573669,5375.769531,58.664684),rot=vec3(92.517433,-0.000000,120.000000)},tree2x2={coord=vec3(-720.938416,5373.827148,58.835503),rot=vec3(92.517433,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-721.116760,5374.128418,58.765778),rot=vec3(100.182983,-0.000000,149.999954)},tree4x4={coord=vec3(-717.401733,5380.562988,57.431171),rot=vec3(101.184807,0.000001,149.999954)},tree2x23={coord=vec3(-721.116760,5374.128418,58.765778),rot=vec3(100.182976,-0.000000,149.999954)},tree3x3={coord=vec3(-719.247192,5377.366699,58.094143),rot=vec3(100.182968,-0.000000,149.999954)},tree2x2={coord=vec3(-721.161072,5374.051758,58.781689),rot=vec3(100.182968,-0.000000,149.999954)},},[180]={tree2x234={coord=vec3(-721.466736,5374.222168,58.684006),rot=vec3(104.250793,-0.000000,-179.999893)},tree4x4={coord=vec3(-721.466736,5381.538574,56.825691),rot=vec3(105.293289,-0.000000,-179.999893)},tree2x23={coord=vec3(-721.466736,5374.222168,58.684006),rot=vec3(104.250793,-0.000000,-179.999893)},tree3x3={coord=vec3(-721.466736,5377.904297,57.748817),rot=vec3(104.250793,-0.000000,-179.999893)},tree2x2={coord=vec3(-721.466736,5374.134766,58.706161),rot=vec3(104.250793,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-721.816711,5374.128418,58.663742),rot=vec3(104.250793,0.000001,-149.999954)},tree4x4={coord=vec3(-725.475098,5380.465332,56.695427),rot=vec3(107.409668,0.000000,-149.999954)},tree2x23={coord=vec3(-721.816711,5374.128418,58.663742),rot=vec3(104.250793,0.000001,-149.999954)},tree3x3={coord=vec3(-723.657776,5377.317383,57.728554),rot=vec3(104.250793,0.000002,-149.999954)},tree2x2={coord=vec3(-721.773132,5374.052734,58.685898),rot=vec3(104.250793,0.000002,-149.999954)},},[240]={tree2x234={coord=vec3(-722.072937,5373.872070,58.709991),rot=vec3(102.196655,-0.000002,-119.999985)},tree4x4={coord=vec3(-728.462952,5377.561035,57.115131),rot=vec3(104.250793,-0.000002,-119.999985)},tree2x23={coord=vec3(-722.072937,5373.872070,58.709991),rot=vec3(102.196655,-0.000002,-119.999985)},tree3x3={coord=vec3(-725.288696,5375.728516,57.907387),rot=vec3(102.196655,-0.000002,-119.999985)},tree2x2={coord=vec3(-721.996765,5373.828125,58.729004),rot=vec3(102.196655,-0.000002,-119.999985)},},[270]={tree2x234={coord=vec3(-722.166748,5373.521973,58.808846),rot=vec3(97.236641,0.000000,-90.000000)},tree4x4={coord=vec3(-729.655640,5373.521973,57.857918),rot=vec3(99.191086,0.000000,-90.000000)},tree2x23={coord=vec3(-722.166748,5373.521973,58.808846),rot=vec3(97.236641,0.000000,-90.000000)},tree3x3={coord=vec3(-725.935486,5373.521973,58.330296),rot=vec3(97.236641,0.000000,-90.000000)},tree2x2={coord=vec3(-722.077454,5373.521973,58.820183),rot=vec3(97.236641,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-722.072937,5373.171875,58.880016),rot=vec3(89.796448,0.000000,-60.000000)},tree4x4={coord=vec3(-728.610535,5369.397949,58.906837),rot=vec3(89.796448,0.000000,-60.000000)},tree2x23={coord=vec3(-722.072937,5373.171875,58.880016),rot=vec3(89.796448,0.000000,-60.000000)},tree3x3={coord=vec3(-725.362976,5371.272461,58.893513),rot=vec3(89.796448,0.000000,-60.000000)},tree2x2={coord=vec3(-721.994995,5373.216797,58.879696),rot=vec3(90.694412,0.000000,-60.000000)},},}
PLT.Info.cutting.trees[10]={coord=vec3(-723.673218,5363.122559,60.036758),[0]={tree2x234={coord=vec3(-723.673218,5362.422363,60.818363),rot=vec3(78.900841,0.000000,0.000000)},tree4x4={coord=vec3(-723.673218,5355.014648,62.271599),rot=vec3(78.900841,0.000000,0.000000)},tree2x23={coord=vec3(-723.673218,5362.422363,60.818363),rot=vec3(78.900841,0.000000,0.000000)},tree3x3={coord=vec3(-723.673218,5358.694336,61.549698),rot=vec3(78.900841,0.000000,0.000000)},tree2x2={coord=vec3(-723.673218,5362.510742,60.801037),rot=vec3(79.689850,0.000000,0.000000)},},[45]={tree2x234={coord=vec3(-723.178223,5362.627441,60.890282),rot=vec3(74.328186,-0.000000,45.000000)},tree4x4={coord=vec3(-718.038757,5357.487793,62.929470),rot=vec3(74.328186,-0.000000,45.000000)},tree2x23={coord=vec3(-723.178223,5362.627441,60.890282),rot=vec3(74.328186,-0.000000,45.000000)},tree3x3={coord=vec3(-720.591797,5360.041016,61.728992),rot=vec3(71.399330,-0.000000,45.000000)},tree2x2={coord=vec3(-723.239502,5362.688965,60.865971),rot=vec3(78.119644,-0.000000,45.000000)},},[75]={tree2x234={coord=vec3(-722.997070,5362.941406,60.850021),rot=vec3(78.119720,0.000001,74.999977)},tree4x4={coord=vec3(-715.861511,5361.029297,62.382862),rot=vec3(76.557472,0.000000,74.999977)},tree2x23={coord=vec3(-722.997070,5362.941406,60.850021),rot=vec3(78.900917,0.000001,74.999977)},tree3x3={coord=vec3(-719.396179,5361.976563,61.581352),rot=vec3(78.900917,0.000001,74.999977)},tree2x2={coord=vec3(-723.082397,5362.964355,60.832695),rot=vec3(80.486824,0.000001,74.999977)},},[105]={tree2x234={coord=vec3(-722.997070,5363.303711,60.768307),rot=vec3(86.292625,0.000000,104.999908)},tree4x4={coord=vec3(-715.720581,5365.252930,61.306427),rot=vec3(86.292625,0.000000,104.999908)},tree2x23={coord=vec3(-722.997070,5363.303711,60.768307),rot=vec3(86.292618,0.000000,104.999908)},tree3x3={coord=vec3(-719.335205,5364.284668,61.013954),rot=vec3(86.292610,0.000000,104.999908)},tree2x2={coord=vec3(-723.083862,5363.280273,60.762486),rot=vec3(87.155540,0.000000,104.999908)},},[135]={tree2x234={coord=vec3(-723.178223,5363.617676,60.637020),rot=vec3(92.517349,-0.000000,135.000000)},tree4x4={coord=vec3(-717.845398,5368.950195,60.195454),rot=vec3(93.442513,-0.000000,135.000000)},tree2x23={coord=vec3(-723.178223,5363.617676,60.637020),rot=vec3(92.517349,-0.000000,135.000000)},tree3x3={coord=vec3(-720.494507,5366.301270,60.470161),rot=vec3(92.517349,-0.000000,135.000000)},tree2x2={coord=vec3(-723.241760,5363.554199,60.640972),rot=vec3(93.442505,-0.000000,135.000000)},},[180]={tree2x234={coord=vec3(-723.673218,5363.822754,60.534447),rot=vec3(101.184807,-0.000000,-179.999893)},tree4x4={coord=vec3(-723.673218,5371.228516,59.070137),rot=vec3(102.196655,-0.000000,-179.999893)},tree2x23={coord=vec3(-723.673218,5363.822754,60.534447),rot=vec3(101.184799,-0.000000,-179.999893)},tree3x3={coord=vec3(-723.673218,5367.549805,59.797539),rot=vec3(101.184792,-0.000000,-179.999893)},tree2x2={coord=vec3(-723.673218,5363.734375,60.551903),rot=vec3(101.184792,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-724.023193,5363.729004,60.485649),rot=vec3(102.196655,-0.000000,-149.999954)},tree4x4={coord=vec3(-727.712524,5370.119141,58.890789),rot=vec3(106.346214,-0.000000,-149.999954)},tree2x23={coord=vec3(-724.023193,5363.729004,60.485649),rot=vec3(102.196655,-0.000000,-149.999954)},tree3x3={coord=vec3(-725.879822,5366.944824,59.683044),rot=vec3(102.196655,-0.000000,-149.999954)},tree2x2={coord=vec3(-723.979248,5363.652832,60.504662),rot=vec3(102.196655,-0.000000,-149.999954)},},[240]={tree2x234={coord=vec3(-724.279419,5363.472656,60.474419),rot=vec3(99.191086,-0.000001,-119.999985)},tree4x4={coord=vec3(-730.733093,5367.198730,59.158634),rot=vec3(101.184807,-0.000001,-119.999985)},tree2x23={coord=vec3(-724.279419,5363.472656,60.474419),rot=vec3(99.191086,-0.000001,-119.999985)},tree3x3={coord=vec3(-727.527222,5365.347656,59.867615),rot=vec3(99.191086,-0.000001,-119.999985)},tree2x2={coord=vec3(-724.202515,5363.428223,60.488792),rot=vec3(99.191086,-0.000001,-119.999985)},},[270]={tree2x234={coord=vec3(-724.373230,5363.122559,60.527893),rot=vec3(94.376930,0.000000,-90.000000)},tree4x4={coord=vec3(-731.900208,5363.122559,59.951767),rot=vec3(94.376930,0.000000,-90.000000)},tree2x23={coord=vec3(-724.373230,5363.122559,60.527893),rot=vec3(94.376923,0.000000,-90.000000)},tree3x3={coord=vec3(-728.161133,5363.122559,60.237961),rot=vec3(94.376923,0.000000,-90.000000)},tree2x2={coord=vec3(-724.283508,5363.122559,60.534760),rot=vec3(95.320679,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-724.279419,5362.772461,60.576775),rot=vec3(88.027107,0.000000,-60.000000)},tree4x4={coord=vec3(-730.813171,5359.000488,60.836658),rot=vec3(88.027107,0.000000,-60.000000)},tree2x23={coord=vec3(-724.279419,5362.772461,60.576775),rot=vec3(88.027107,0.000000,-60.000000)},tree3x3={coord=vec3(-727.567505,5360.874023,60.707561),rot=vec3(88.027107,0.000001,-60.000000)},tree2x2={coord=vec3(-724.201538,5362.817383,60.573677),rot=vec3(89.796448,0.000000,-60.000000)},},[330]={tree2x234={coord=vec3(-724.023193,5362.516113,60.730755),rot=vec3(83.746407,0.000001,-29.999979)},tree4x4={coord=vec3(-727.775208,5356.017578,61.553062),rot=vec3(83.746407,0.000001,-29.999979)},tree2x23={coord=vec3(-724.023193,5362.516113,60.730755),rot=vec3(83.746399,0.000001,-29.999979)},tree3x3={coord=vec3(-725.911377,5359.245605,61.144577),rot=vec3(83.746391,0.000001,-29.999979)},tree2x2={coord=vec3(-723.978455,5362.593750,60.720951),rot=vec3(86.283989,0.000001,-29.999979)},},}
PLT.Info.cutting.trees[11]={coord=vec3(-722.339417,5391.321777,53.461197),[0]={tree2x234={coord=vec3(-722.339417,5390.621582,54.243519),rot=vec3(74.328186,0.000000,0.000000)},tree4x4={coord=vec3(-722.339417,5383.353027,56.282707),rot=vec3(75.071449,0.000000,0.000000)},tree2x23={coord=vec3(-722.339417,5390.621582,54.243519),rot=vec3(74.328186,0.000000,0.000000)},tree3x3={coord=vec3(-722.339417,5386.963867,55.269730),rot=vec3(74.328186,0.000000,0.000000)},tree2x2={coord=vec3(-722.339417,5390.708008,54.219208),rot=vec3(74.328186,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-721.989441,5390.715332,54.256744),rot=vec3(73.592262,-0.000000,29.999979)},tree4x4={coord=vec3(-718.368652,5384.443848,56.279118),rot=vec3(73.592262,-0.000000,29.999979)},tree2x23={coord=vec3(-721.989441,5390.715332,54.256744),rot=vec3(73.592262,-0.000000,29.999979)},tree3x3={coord=vec3(-720.167297,5387.559082,55.329853),rot=vec3(73.592262,-0.000000,29.999979)},tree2x2={coord=vec3(-722.032654,5390.790039,54.231323),rot=vec3(74.328186,-0.000000,29.999979)},},[60]={tree2x234={coord=vec3(-721.733215,5390.971680,54.213913),rot=vec3(78.119644,-0.000001,60.000000)},tree4x4={coord=vec3(-715.335632,5387.278320,55.713017),rot=vec3(78.900841,-0.000000,60.000000)},tree2x23={coord=vec3(-721.733215,5390.971680,54.213913),rot=vec3(78.119644,-0.000001,60.000000)},tree3x3={coord=vec3(-718.513672,5389.112793,54.996010),rot=vec3(78.119644,-0.000001,60.000000)},tree2x2={coord=vec3(-721.809509,5391.015625,54.195385),rot=vec3(78.119644,-0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-721.639404,5391.321777,54.181789),rot=vec3(86.292625,-0.000000,90.000000)},tree4x4={coord=vec3(-714.106201,5391.321777,54.559910),rot=vec3(87.155548,-0.000000,90.000000)},tree2x23={coord=vec3(-721.639404,5391.321777,54.181789),rot=vec3(86.292618,-0.000000,90.000000)},tree3x3={coord=vec3(-717.848328,5391.321777,54.427437),rot=vec3(86.292610,-0.000000,90.000000)},tree2x2={coord=vec3(-721.729187,5391.321777,54.175968),rot=vec3(86.292610,-0.000000,90.000000)},},[130]={tree2x234={coord=vec3(-721.803162,5391.771973,54.081059),rot=vec3(99.191086,0.000000,130.000000)},tree4x4={coord=vec3(-716.094543,5396.562012,52.875275),rot=vec3(99.191086,0.000000,130.000000)},tree2x23={coord=vec3(-721.803162,5391.771973,54.081059),rot=vec3(99.191086,0.000000,130.000000)},tree3x3={coord=vec3(-718.930298,5394.182617,53.474255),rot=vec3(99.191086,0.000000,130.000000)},tree2x2={coord=vec3(-721.871216,5391.714844,54.095432),rot=vec3(99.191086,0.000000,130.000000)},},[160]={tree2x234={coord=vec3(-722.099976,5391.979492,53.980053),rot=vec3(105.293289,-0.000000,159.999939)},tree4x4={coord=vec3(-719.609497,5398.822266,51.988930),rot=vec3(106.346214,-0.000000,159.999939)},tree2x23={coord=vec3(-722.099976,5391.979492,53.980053),rot=vec3(105.293282,-0.000000,159.999939)},tree3x3={coord=vec3(-720.846680,5395.422852,52.978027),rot=vec3(105.293274,-0.000000,159.999939)},tree2x2={coord=vec3(-722.129639,5391.897949,54.003792),rot=vec3(105.293274,-0.000000,159.999939)},},[190]={tree2x234={coord=vec3(-722.460999,5392.011230,53.964394),rot=vec3(108.483757,-0.000000,-169.999908)},tree4x4={coord=vec3(-723.704285,5399.062012,51.521091),rot=vec3(108.472893,0.000000,-169.999908)},tree2x23={coord=vec3(-722.460999,5392.011230,53.964394),rot=vec3(108.483749,-0.000000,-169.999908)},tree3x3={coord=vec3(-723.086670,5395.559570,52.759975),rot=vec3(108.483742,-0.000000,-169.999908)},tree2x2={coord=vec3(-722.446167,5391.927246,53.992928),rot=vec3(108.483742,-0.000000,-169.999908)},},[220]={tree2x234={coord=vec3(-722.789368,5391.857910,54.056931),rot=vec3(110.664268,0.000001,-139.999985)},tree4x4={coord=vec3(-727.329590,5397.269043,51.442951),rot=vec3(110.664268,0.000001,-139.999985)},tree2x23={coord=vec3(-722.789368,5391.857910,54.056931),rot=vec3(110.664261,0.000001,-139.999985)},tree3x3={coord=vec3(-725.074219,5394.581055,52.716297),rot=vec3(110.664253,0.000001,-139.999985)},tree2x2={coord=vec3(-722.735229,5391.793457,54.088692),rot=vec3(110.664253,0.000001,-139.999985)},},[255]={tree2x234={coord=vec3(-723.015564,5391.502930,54.040688),rot=vec3(104.250793,-0.000000,-104.999908)},tree4x4={coord=vec3(-730.082947,5393.396484,52.072372),rot=vec3(107.409668,-0.000000,-104.999908)},tree2x23={coord=vec3(-723.015564,5391.502930,54.040688),rot=vec3(104.250793,-0.000000,-104.999908)},tree3x3={coord=vec3(-726.572205,5392.456055,53.105499),rot=vec3(105.293289,-0.000000,-104.999908)},tree2x2={coord=vec3(-722.931335,5391.480469,54.062843),rot=vec3(105.293289,-0.000000,-104.999908)},},[285]={tree2x234={coord=vec3(-723.015564,5391.140625,54.055054),rot=vec3(95.320808,-0.000000,-74.999977)},tree4x4={coord=vec3(-730.275940,5389.195313,53.355019),rot=vec3(97.236740,-0.000000,-74.999977)},tree2x23={coord=vec3(-723.015564,5391.140625,54.055054),rot=vec3(95.320801,-0.000000,-74.999977)},tree3x3={coord=vec3(-726.669312,5390.161621,53.702763),rot=vec3(95.320793,-0.000000,-74.999977)},tree2x2={coord=vec3(-722.929016,5391.164063,54.063400),rot=vec3(96.273994,-0.000000,-74.999977)},},[325]={tree2x234={coord=vec3(-722.740906,5390.748535,54.208225),rot=vec3(81.291634,0.000001,-34.999962)},tree4x4={coord=vec3(-727.020874,5384.635742,55.241180),rot=vec3(81.291634,0.000001,-34.999962)},tree2x23={coord=vec3(-722.740906,5390.748535,54.208225),rot=vec3(81.291634,0.000001,-34.999962)},tree3x3={coord=vec3(-724.894775,5387.672363,54.783413),rot=vec3(81.291634,0.000001,-34.999962)},tree2x2={coord=vec3(-722.689880,5390.821289,54.194599),rot=vec3(82.104538,0.000002,-34.999966)},},}
PLT.Info.cutting.trees[12]={coord=vec3(-734.357605,5375.646973,56.017548),[0]={tree2x234={coord=vec3(-734.357605,5374.946777,56.846775),rot=vec3(75.822174,0.000000,0.000000)},tree4x4={coord=vec3(-734.357605,5367.627930,58.695766),rot=vec3(75.822174,0.000000,0.000000)},tree2x23={coord=vec3(-734.357605,5374.946777,56.846775),rot=vec3(75.822174,0.000000,0.000000)},tree3x3={coord=vec3(-734.357605,5371.263672,57.702271),rot=vec3(74.313286,0.000000,0.000000)},tree2x2={coord=vec3(-734.357605,5375.034180,56.824734),rot=vec3(77.346176,0.000000,0.000000)},},[40]={tree2x234={coord=vec3(-733.907654,5375.110840,56.891918),rot=vec3(75.071518,-0.000000,39.999947)},tree4x4={coord=vec3(-729.218994,5369.522949,58.836639),rot=vec3(77.346252,0.000000,39.999954)},tree2x23={coord=vec3(-733.907654,5375.110840,56.891918),rot=vec3(75.071510,-0.000000,39.999943)},tree3x3={coord=vec3(-731.548157,5372.298828,57.870590),rot=vec3(75.071510,0.000000,39.999939)},tree2x2={coord=vec3(-733.963562,5375.177246,56.868732),rot=vec3(75.071510,0.000000,39.999939)},},[70]={tree2x234={coord=vec3(-733.699829,5375.407715,56.840504),rot=vec3(78.119644,-0.000000,69.999992)},tree4x4={coord=vec3(-726.758057,5372.880859,58.394608),rot=vec3(80.486732,-0.000000,69.999992)},tree2x23={coord=vec3(-733.699829,5375.407715,56.840504),rot=vec3(78.119644,-0.000000,69.999992)},tree3x3={coord=vec3(-730.206421,5374.136230,57.622601),rot=vec3(78.119644,-0.000000,69.999992)},tree2x2={coord=vec3(-733.782593,5375.437988,56.821976),rot=vec3(78.119644,-0.000000,69.999992)},},[100]={tree2x234={coord=vec3(-733.668213,5375.768555,56.777374),rot=vec3(84.592331,-0.000003,99.999985)},tree4x4={coord=vec3(-726.267029,5377.073730,57.488800),rot=vec3(86.292625,-0.000003,99.999985)},tree2x23={coord=vec3(-733.668213,5375.768555,56.777374),rot=vec3(84.592323,-0.000003,99.999985)},tree3x3={coord=vec3(-729.943604,5376.425293,57.135399),rot=vec3(84.592316,-0.000003,99.999985)},tree2x2={coord=vec3(-733.756470,5375.752930,56.768894),rot=vec3(84.592316,-0.000003,99.999985)},},[130]={tree2x234={coord=vec3(-733.821350,5376.097168,56.608505),rot=vec3(92.517349,0.000001,130.000000)},tree4x4={coord=vec3(-728.044006,5380.945313,56.166939),rot=vec3(93.442513,0.000001,130.000000)},tree2x23={coord=vec3(-733.821350,5376.097168,56.608505),rot=vec3(92.517349,0.000001,130.000000)},tree3x3={coord=vec3(-730.913940,5378.536621,56.441647),rot=vec3(92.517349,0.000001,130.000000)},tree2x2={coord=vec3(-733.890198,5376.039551,56.612457),rot=vec3(92.517349,0.000001,130.000000)},},[175]={tree2x234={coord=vec3(-734.296570,5376.344238,56.439171),rot=vec3(105.293289,-0.000002,174.999893)},tree4x4={coord=vec3(-733.661865,5383.598145,54.448048),rot=vec3(107.409668,-0.000001,174.999893)},tree2x23={coord=vec3(-734.296570,5376.344238,56.439171),rot=vec3(105.293282,-0.000002,174.999893)},tree3x3={coord=vec3(-733.977173,5379.994629,55.437145),rot=vec3(105.293274,-0.000002,174.999893)},tree2x2={coord=vec3(-734.304138,5376.257813,56.462910),rot=vec3(105.293274,-0.000002,174.999893)},},[205]={tree2x234={coord=vec3(-734.653442,5376.281250,56.394928),rot=vec3(108.483757,-0.000000,-154.999954)},tree4x4={coord=vec3(-737.679199,5382.770020,53.891624),rot=vec3(111.770905,-0.000001,-154.999954)},tree2x23={coord=vec3(-734.653442,5376.281250,56.394928),rot=vec3(108.483749,-0.000000,-154.999954)},tree3x3={coord=vec3(-736.176147,5379.546875,55.190510),rot=vec3(109.568573,-0.000000,-154.999954)},tree2x2={coord=vec3(-734.617371,5376.203613,56.423462),rot=vec3(108.483742,0.000000,-154.999954)},},[235]={tree2x234={coord=vec3(-734.931030,5376.048340,56.415848),rot=vec3(107.409668,0.000002,-124.999985)},tree4x4={coord=vec3(-740.831543,5380.179688,54.047173),rot=vec3(108.483757,0.000003,-124.999985)},tree2x23={coord=vec3(-734.931030,5376.048340,56.415848),rot=vec3(107.409660,0.000001,-124.999985)},tree3x3={coord=vec3(-737.900452,5378.127441,55.279179),rot=vec3(107.409653,0.000001,-124.999985)},tree2x2={coord=vec3(-734.860718,5375.999023,56.442776),rot=vec3(107.409653,0.000001,-124.999985)},},[265]={tree2x234={coord=vec3(-735.054932,5375.708008,56.491936),rot=vec3(101.184914,0.000000,-95.000000)},tree4x4={coord=vec3(-742.432373,5376.353516,55.027611),rot=vec3(101.184914,0.000000,-95.000000)},tree2x23={coord=vec3(-735.054932,5375.708008,56.491936),rot=vec3(101.184914,0.000000,-95.000000)},tree3x3={coord=vec3(-738.767578,5376.032715,55.755020),rot=vec3(101.184914,0.000000,-95.000000)},tree2x2={coord=vec3(-734.966980,5375.700195,56.509392),rot=vec3(101.184914,0.000000,-95.000000)},},[295]={tree2x234={coord=vec3(-734.992004,5375.351074,56.589252),rot=vec3(90.694412,-0.000000,-65.000008)},tree4x4={coord=vec3(-741.833191,5372.160645,56.497761),rot=vec3(90.694412,-0.000000,-65.000008)},tree2x23={coord=vec3(-734.992004,5375.351074,56.589252),rot=vec3(90.694412,-0.000000,-65.000008)},tree3x3={coord=vec3(-738.434814,5373.745605,56.493210),rot=vec3(89.787453,-0.000000,-65.000008)},tree2x2={coord=vec3(-734.910461,5375.389160,56.590343),rot=vec3(91.601341,-0.000000,-65.000008)},},[325]={tree2x234={coord=vec3(-734.759094,5375.073730,56.781132),rot=vec3(82.096313,0.000002,-34.999966)},tree4x4={coord=vec3(-739.047913,5368.948730,57.819180),rot=vec3(82.096313,0.000002,-34.999966)},tree2x23={coord=vec3(-734.759094,5375.073730,56.781132),rot=vec3(82.917259,0.000002,-34.999966)},tree3x3={coord=vec3(-736.921509,5371.985352,57.299557),rot=vec3(82.917259,0.000001,-34.999966)},tree2x2={coord=vec3(-734.707825,5375.146973,56.770035),rot=vec3(83.746429,0.000001,-34.999966)},},}
PLT.Info.cutting.trees[13]={coord=vec3(-724.674561,5350.648438,62.374687),[0]={tree2x234={coord=vec3(-724.674561,5349.948242,63.152714),rot=vec3(75.822174,0.000000,0.000000)},tree4x4={coord=vec3(-724.674561,5342.629395,64.991707),rot=vec3(74.313286,0.000000,0.000000)},tree2x23={coord=vec3(-724.674561,5349.948242,63.152714),rot=vec3(75.822174,0.000000,0.000000)},tree3x3={coord=vec3(-724.674561,5346.265137,64.083214),rot=vec3(75.822174,0.000000,0.000000)},tree2x2={coord=vec3(-724.674561,5350.035645,63.130672),rot=vec3(75.822174,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-724.324585,5350.041992,63.187584),rot=vec3(70.720734,0.000001,29.999979)},tree4x4={coord=vec3(-720.761780,5343.871094,65.625061),rot=vec3(70.720734,0.000001,29.999979)},tree2x23={coord=vec3(-724.324585,5350.041992,63.187584),rot=vec3(70.720734,0.000001,29.999979)},tree3x3={coord=vec3(-722.531616,5346.936523,64.441910),rot=vec3(70.720734,0.000001,29.999979)},tree2x2={coord=vec3(-724.367065,5350.115234,63.157867),rot=vec3(71.427925,-0.000000,29.999979)},},[60]={tree2x234={coord=vec3(-724.068359,5350.298340,63.200432),rot=vec3(71.427925,-0.000001,60.000000)},tree4x4={coord=vec3(-717.871155,5346.720215,65.604767),rot=vec3(72.142212,-0.000001,60.000000)},tree2x23={coord=vec3(-724.068359,5350.298340,63.200432),rot=vec3(71.427925,-0.000001,60.000000)},tree3x3={coord=vec3(-720.949646,5348.497559,64.410400),rot=vec3(71.427925,-0.000001,60.000000)},tree2x2={coord=vec3(-724.142273,5350.340820,63.171764),rot=vec3(71.427925,-0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-723.974548,5350.648438,63.185917),rot=vec3(75.822174,-0.000000,90.000000)},tree4x4={coord=vec3(-716.655518,5350.648438,64.924904),rot=vec3(78.119644,-0.000000,90.000000)},tree2x23={coord=vec3(-723.974548,5350.648438,63.185917),rot=vec3(75.822174,-0.000000,90.000000)},tree3x3={coord=vec3(-720.291260,5350.648438,64.116417),rot=vec3(75.822174,-0.000000,90.000000)},tree2x2={coord=vec3(-724.061829,5350.648438,63.163876),rot=vec3(76.580383,-0.000000,90.000000)},},[140]={tree2x234={coord=vec3(-724.224609,5351.184570,62.990166),rot=vec3(88.907364,0.000001,139.999985)},tree4x4={coord=vec3(-719.373108,5356.966309,62.839111),rot=vec3(97.236641,0.000000,139.999985)},tree2x23={coord=vec3(-724.224609,5351.184570,62.990166),rot=vec3(88.907356,0.000001,139.999985)},tree3x3={coord=vec3(-721.783081,5354.094238,63.062607),rot=vec3(88.907349,0.000001,139.999985)},tree2x2={coord=vec3(-724.282471,5351.115723,62.988449),rot=vec3(89.796417,0.000001,139.999985)},},[170]={tree2x234={coord=vec3(-724.552979,5351.337891,62.879749),rot=vec3(99.191086,-0.000000,169.999908)},tree4x4={coord=vec3(-723.258911,5358.677246,61.563965),rot=vec3(100.182983,0.000000,169.999908)},tree2x23={coord=vec3(-724.552979,5351.337891,62.879749),rot=vec3(99.191086,-0.000000,169.999908)},tree3x3={coord=vec3(-723.901733,5355.031250,62.272945),rot=vec3(99.191086,-0.000000,169.999908)},tree2x2={coord=vec3(-724.568420,5351.250488,62.894123),rot=vec3(100.182983,0.000000,169.999908)},},[200]={tree2x234={coord=vec3(-724.914001,5351.306152,62.759792),rot=vec3(102.196655,-0.000000,-159.999939)},tree4x4={coord=vec3(-727.437683,5358.239746,61.164932),rot=vec3(102.196655,-0.000000,-159.999939)},tree2x23={coord=vec3(-724.914001,5351.306152,62.759792),rot=vec3(102.196655,-0.000000,-159.999939)},tree3x3={coord=vec3(-726.184021,5354.795410,61.957188),rot=vec3(102.196655,-0.000000,-159.999939)},tree2x2={coord=vec3(-724.883911,5351.223633,62.828804),rot=vec3(104.250793,-0.000000,-159.999939)},},[230]={tree2x234={coord=vec3(-725.210815,5351.098633,62.741768),rot=vec3(100.183090,-0.000002,-129.999985)},tree4x4={coord=vec3(-730.902588,5355.874512,61.407146),rot=vec3(100.183090,-0.000002,-129.999985)},tree2x23={coord=vec3(-725.210815,5351.098633,62.741768),rot=vec3(100.183083,-0.000002,-129.999985)},tree3x3={coord=vec3(-728.075195,5353.501953,62.070126),rot=vec3(100.183075,-0.000003,-129.999985)},tree2x2={coord=vec3(-725.142944,5351.041504,62.757679),rot=vec3(101.184914,-0.000002,-129.999985)},},[260]={tree2x234={coord=vec3(-725.363953,5350.770020,62.825310),rot=vec3(97.236641,0.000003,-99.999985)},tree4x4={coord=vec3(-732.739075,5352.070313,61.874382),rot=vec3(97.236641,0.000003,-99.999985)},tree2x23={coord=vec3(-725.363953,5350.770020,62.825310),rot=vec3(97.236641,0.000003,-99.999985)},tree3x3={coord=vec3(-729.075439,5351.424316,62.296761),rot=vec3(96.264275,0.000002,-99.999985)},tree2x2={coord=vec3(-725.276062,5350.754395,62.836647),rot=vec3(98.209000,0.000003,-99.999985)},},[290]={tree2x234={coord=vec3(-725.332336,5350.409180,62.879677),rot=vec3(92.517349,-0.000000,-69.999992)},tree4x4={coord=vec3(-732.419250,5347.830078,62.438110),rot=vec3(92.517349,-0.000000,-69.999992)},tree2x23={coord=vec3(-725.332336,5350.409180,62.879677),rot=vec3(92.517349,-0.000000,-69.999992)},tree3x3={coord=vec3(-728.898804,5349.111328,62.712818),rot=vec3(92.517349,-0.000000,-69.999992)},tree2x2={coord=vec3(-725.247864,5350.439941,62.883629),rot=vec3(92.517349,-0.000000,-69.999992)},},[320]={tree2x234={coord=vec3(-725.124512,5350.112305,62.988308),rot=vec3(86.292725,-0.000000,-39.999947)},tree4x4={coord=vec3(-729.966736,5344.341309,63.366417),rot=vec3(90.694496,0.000000,-39.999950)},tree2x23={coord=vec3(-725.124512,5350.112305,62.988308),rot=vec3(86.292725,-0.000000,-39.999943)},tree3x3={coord=vec3(-727.561340,5347.208008,63.233948),rot=vec3(86.292725,-0.000000,-39.999935)},tree2x2={coord=vec3(-725.066772,5350.181152,62.982491),rot=vec3(86.292725,-0.000000,-39.999935)},},[350]={tree2x234={coord=vec3(-724.796143,5349.958984,63.165241),rot=vec3(78.900841,-0.000001,-10.000004)},tree4x4={coord=vec3(-726.082520,5342.663574,64.618477),rot=vec3(78.900841,-0.000001,-10.000004)},tree2x23={coord=vec3(-724.796143,5349.958984,63.165241),rot=vec3(78.900841,-0.000001,-10.000004)},tree3x3={coord=vec3(-725.443481,5346.287598,63.896576),rot=vec3(78.900841,-0.000001,-10.000004)},tree2x2={coord=vec3(-724.780762,5350.045898,63.147915),rot=vec3(78.900841,-0.000001,-10.000004)},},}
PLT.Info.cutting.trees[14]={coord=vec3(-486.651428,5459.638672,81.948334),[0]={tree2x234={coord=vec3(-486.651428,5458.938477,82.511711),rot=vec3(98.209000,-0.000000,0.000000)},tree4x4={coord=vec3(-486.651428,5451.466797,81.433823),rot=vec3(104.250793,-0.000000,0.000000)},tree2x23={coord=vec3(-486.651428,5458.938477,82.511711),rot=vec3(98.209000,-0.000000,0.000000)},tree3x3={coord=vec3(-486.651428,5455.178223,81.969276),rot=vec3(98.209000,-0.000000,0.000000)},tree2x2={coord=vec3(-486.651428,5459.027832,82.524559),rot=vec3(98.209000,-0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-486.301422,5459.032227,82.546707),rot=vec3(92.517349,-0.000001,29.999979)},tree4x4={coord=vec3(-482.530579,5452.500977,82.215134),rot=vec3(95.320694,-0.000001,29.999979)},tree2x23={coord=vec3(-486.301422,5459.032227,82.546707),rot=vec3(92.517349,-0.000001,29.999979)},tree3x3={coord=vec3(-484.403748,5455.745605,82.379845),rot=vec3(92.517349,-0.000001,29.999979)},tree2x2={coord=vec3(-486.346375,5459.109863,82.550659),rot=vec3(92.517349,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-486.045197,5459.288574,82.644554),rot=vec3(88.027107,-0.000000,60.000000)},tree4x4={coord=vec3(-479.511444,5455.516602,82.904442),rot=vec3(91.601341,-0.000000,60.000000)},tree2x23={coord=vec3(-486.045197,5459.288574,82.644554),rot=vec3(88.027107,-0.000000,60.000000)},tree3x3={coord=vec3(-482.757111,5457.390137,82.775337),rot=vec3(88.027107,-0.000001,60.000000)},tree2x2={coord=vec3(-486.123108,5459.333496,82.641457),rot=vec3(88.027107,-0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-485.951416,5459.638672,82.723953),rot=vec3(82.925545,-0.000000,90.000000)},tree4x4={coord=vec3(-478.459869,5459.638672,83.543678),rot=vec3(86.292625,-0.000000,90.000000)},tree2x23={coord=vec3(-485.951416,5459.638672,82.723953),rot=vec3(82.925537,-0.000000,90.000000)},tree3x3={coord=vec3(-482.181335,5459.638672,83.191833),rot=vec3(82.925537,-0.000000,90.000000)},tree2x2={coord=vec3(-486.040741,5459.638672,82.712868),rot=vec3(83.754784,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-486.045197,5459.988770,82.703667),rot=vec3(80.486824,-0.000000,120.000000)},tree4x4={coord=vec3(-479.597473,5463.711426,83.951317),rot=vec3(82.925636,-0.000000,120.000000)},tree2x23={coord=vec3(-486.045197,5459.988770,82.703667),rot=vec3(80.486824,-0.000000,120.000000)},tree3x3={coord=vec3(-482.800415,5461.862305,83.331543),rot=vec3(80.486824,-0.000000,120.000000)},tree2x2={coord=vec3(-486.122070,5459.944336,82.688789),rot=vec3(80.486824,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-486.301422,5460.245117,82.753075),rot=vec3(82.104507,-0.000000,149.999954)},tree4x4={coord=vec3(-482.562714,5466.721191,83.790054),rot=vec3(84.592331,0.000000,149.999954)},tree2x23={coord=vec3(-486.301422,5460.245117,82.753075),rot=vec3(82.104507,-0.000000,149.999954)},tree3x3={coord=vec3(-484.419922,5463.503906,83.274933),rot=vec3(82.104507,-0.000000,149.999954)},tree2x2={coord=vec3(-486.346008,5460.167969,82.740715),rot=vec3(82.925537,-0.000000,149.999954)},},[180]={tree2x234={coord=vec3(-486.651428,5460.338867,82.694954),rot=vec3(86.292625,-0.000000,-179.999893)},tree4x4={coord=vec3(-486.651428,5467.871582,83.128082),rot=vec3(88.907364,-0.000000,-179.999893)},tree2x23={coord=vec3(-486.651428,5460.338867,82.694954),rot=vec3(86.292618,-0.000000,-179.999893)},tree3x3={coord=vec3(-486.651428,5464.129883,82.940598),rot=vec3(86.292610,-0.000000,-179.999893)},tree2x2={coord=vec3(-486.651428,5460.249023,82.689133),rot=vec3(86.292610,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-487.001434,5460.245117,82.604950),rot=vec3(91.601341,-0.000000,-149.999954)},tree4x4={coord=vec3(-490.774475,5466.780273,82.283997),rot=vec3(95.320694,-0.000000,-149.999954)},tree2x23={coord=vec3(-487.001434,5460.245117,82.604950),rot=vec3(91.601334,-0.000000,-149.999954)},tree3x3={coord=vec3(-488.900208,5463.533691,82.498787),rot=vec3(91.601326,-0.000000,-149.999954)},tree2x2={coord=vec3(-486.956451,5460.167480,82.607468),rot=vec3(91.601326,-0.000000,-149.999954)},},[240]={tree2x234={coord=vec3(-487.257660,5459.988770,82.562111),rot=vec3(97.236641,-0.000002,-119.999985)},tree4x4={coord=vec3(-493.743225,5463.733398,81.381172),rot=vec3(102.196655,-0.000002,-119.999985)},tree2x23={coord=vec3(-487.257660,5459.988770,82.562111),rot=vec3(97.236641,-0.000002,-119.999985)},tree3x3={coord=vec3(-490.521484,5461.873047,82.083557),rot=vec3(97.236641,-0.000002,-119.999985)},tree2x2={coord=vec3(-487.180328,5459.944336,82.573448),rot=vec3(97.236641,-0.000002,-119.999985)},},[270]={tree2x234={coord=vec3(-487.351440,5459.638672,82.482704),rot=vec3(101.184807,0.000000,-90.000000)},tree4x4={coord=vec3(-494.757050,5459.638672,80.848389),rot=vec3(104.250793,0.000000,-90.000000)},tree2x23={coord=vec3(-487.351440,5459.638672,82.482704),rot=vec3(101.184799,0.000000,-90.000000)},tree3x3={coord=vec3(-491.078278,5459.638672,81.745796),rot=vec3(101.184792,0.000000,-90.000000)},tree2x2={coord=vec3(-487.263153,5459.638672,82.500160),rot=vec3(101.184792,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-487.257660,5459.288574,82.442986),rot=vec3(102.196655,-0.000000,-60.000000)},tree4x4={coord=vec3(-493.647736,5455.599609,80.738121),rot=vec3(106.346214,-0.000000,-60.000000)},tree2x23={coord=vec3(-487.257660,5459.288574,82.442986),rot=vec3(102.196655,-0.000000,-60.000000)},tree3x3={coord=vec3(-490.473419,5457.432129,81.640381),rot=vec3(102.196655,-0.000000,-60.000000)},tree2x2={coord=vec3(-487.181488,5459.332520,82.461998),rot=vec3(103.218605,-0.000000,-60.000000)},},[330]={tree2x234={coord=vec3(-487.001434,5459.032227,82.453583),rot=vec3(102.196655,-0.000000,-29.999979)},tree4x4={coord=vec3(-490.690735,5452.642090,80.858719),rot=vec3(103.218605,-0.000000,-29.999979)},tree2x23={coord=vec3(-487.001434,5459.032227,82.453583),rot=vec3(102.196655,-0.000001,-29.999979)},tree3x3={coord=vec3(-488.858063,5455.816406,81.650978),rot=vec3(102.196655,-0.000001,-29.999979)},tree2x2={coord=vec3(-486.957458,5459.108398,82.472595),rot=vec3(102.196655,-0.000001,-29.999979)},},}
PLT.Info.cutting.trees[15]={coord=vec3(-493.792450,5468.375977,80.936920),[0]={tree2x234={coord=vec3(-493.792450,5467.675781,81.561691),rot=vec3(92.517349,-0.000000,0.000000)},tree4x4={coord=vec3(-493.792450,5460.133789,81.120117),rot=vec3(95.320694,-0.000000,0.000000)},tree2x23={coord=vec3(-493.792450,5467.675781,81.561691),rot=vec3(92.517349,-0.000000,0.000000)},tree3x3={coord=vec3(-493.792450,5463.880371,81.394829),rot=vec3(92.517349,-0.000000,0.000000)},tree2x2={coord=vec3(-493.792450,5467.765625,81.565643),rot=vec3(92.517349,-0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-493.442444,5467.769531,81.707970),rot=vec3(85.438248,-0.000001,29.999979)},tree4x4={coord=vec3(-489.679871,5461.252441,82.198357),rot=vec3(89.796448,-0.000001,29.999979)},tree2x23={coord=vec3(-493.442444,5467.769531,81.707970),rot=vec3(85.438248,-0.000001,29.999979)},tree3x3={coord=vec3(-491.548950,5464.489746,82.010117),rot=vec3(85.438248,-0.000001,29.999979)},tree2x2={coord=vec3(-493.487305,5467.847168,81.700813),rot=vec3(85.438248,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-493.186218,5468.025879,81.790253),rot=vec3(80.486732,-0.000000,60.000000)},tree4x4={coord=vec3(-486.738495,5464.303223,82.927917),rot=vec3(83.754791,-0.000000,60.000000)},tree2x23={coord=vec3(-493.186218,5468.025879,81.790253),rot=vec3(80.486725,-0.000000,60.000000)},tree3x3={coord=vec3(-489.941437,5466.152344,82.418137),rot=vec3(80.486717,-0.000000,60.000000)},tree2x2={coord=vec3(-493.263092,5468.070313,81.775375),rot=vec3(80.486717,-0.000000,60.000000)},},[90]={tree2x234={coord=vec3(-493.092438,5468.375977,81.804977),rot=vec3(77.346176,-0.000000,90.000000)},tree4x4={coord=vec3(-485.726776,5468.375977,83.288651),rot=vec3(79.689850,-0.000000,90.000000)},tree2x23={coord=vec3(-493.092438,5468.375977,81.804977),rot=vec3(77.346169,-0.000000,90.000000)},tree3x3={coord=vec3(-489.385712,5468.375977,82.637184),rot=vec3(78.119614,-0.000000,90.000000)},tree2x2={coord=vec3(-493.180237,5468.375977,81.785263),rot=vec3(77.346161,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-493.186218,5468.726074,81.767769),rot=vec3(78.119720,-0.000000,120.000000)},tree4x4={coord=vec3(-486.788635,5472.419434,83.211853),rot=vec3(82.925636,-0.000000,120.000000)},tree2x23={coord=vec3(-493.186218,5468.726074,81.767769),rot=vec3(78.119720,-0.000000,120.000000)},tree3x3={coord=vec3(-489.966675,5470.584961,82.549858),rot=vec3(78.119720,-0.000000,120.000000)},tree2x2={coord=vec3(-493.262482,5468.682129,81.749237),rot=vec3(78.119720,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-493.442444,5468.982422,81.718605),rot=vec3(84.592331,0.000000,149.999954)},tree4x4={coord=vec3(-489.684723,5475.491211,82.430038),rot=vec3(86.292625,0.000000,149.999954)},tree2x23={coord=vec3(-493.442444,5468.982422,81.718605),rot=vec3(84.592323,0.000001,149.999954)},tree3x3={coord=vec3(-491.551392,5472.257813,82.076630),rot=vec3(84.592316,0.000001,149.999954)},tree2x2={coord=vec3(-493.487244,5468.904785,81.710121),rot=vec3(84.592316,0.000001,149.999954)},},[180]={tree2x234={coord=vec3(-493.792450,5469.076172,81.566727),rot=vec3(91.601341,-0.000000,-179.999893)},tree4x4={coord=vec3(-493.792450,5476.622070,81.245773),rot=vec3(94.376930,-0.000000,-179.999893)},tree2x23={coord=vec3(-493.792450,5469.076172,81.566727),rot=vec3(91.601334,-0.000000,-179.999893)},tree3x3={coord=vec3(-493.792450,5472.873535,81.460564),rot=vec3(91.601326,-0.000000,-179.999893)},tree2x2={coord=vec3(-493.792450,5468.986328,81.569244),rot=vec3(91.601326,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-494.142456,5468.982422,81.467842),rot=vec3(100.182983,-0.000000,-149.999954)},tree4x4={coord=vec3(-497.857483,5475.416992,80.023232),rot=vec3(102.196655,-0.000000,-149.999954)},tree2x23={coord=vec3(-494.142456,5468.982422,81.467842),rot=vec3(100.182976,-0.000000,-149.999954)},tree3x3={coord=vec3(-496.012024,5472.220703,80.796211),rot=vec3(100.182968,0.000000,-149.999954)},tree2x2={coord=vec3(-494.098145,5468.905762,81.483749),rot=vec3(100.182968,0.000000,-149.999954)},},[240]={tree2x234={coord=vec3(-494.398682,5468.726074,81.356949),rot=vec3(105.293289,-0.000002,-119.999985)},tree4x4={coord=vec3(-500.704803,5472.366699,79.255821),rot=vec3(108.483757,-0.000003,-119.999985)},tree2x23={coord=vec3(-494.398682,5468.726074,81.356949),rot=vec3(105.293282,-0.000001,-119.999985)},tree3x3={coord=vec3(-497.572205,5470.558105,80.354927),rot=vec3(105.293274,-0.000001,-119.999985)},tree2x2={coord=vec3(-494.323517,5468.682617,81.380684),rot=vec3(105.293274,-0.000001,-119.999985)},},[270]={tree2x234={coord=vec3(-494.492462,5468.375977,81.346962),rot=vec3(107.409668,0.000000,-90.000000)},tree4x4={coord=vec3(-501.695648,5468.375977,78.978287),rot=vec3(108.483757,0.000000,-90.000000)},tree2x23={coord=vec3(-494.492462,5468.375977,81.346962),rot=vec3(107.409660,0.000000,-90.000000)},tree3x3={coord=vec3(-498.117432,5468.375977,80.210297),rot=vec3(107.409653,0.000000,-90.000000)},tree2x2={coord=vec3(-494.406586,5468.375977,81.373894),rot=vec3(107.409653,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-494.398682,5468.025879,81.360634),rot=vec3(105.293289,-0.000000,-60.000000)},tree4x4={coord=vec3(-500.704803,5464.385254,79.314507),rot=vec3(106.346214,-0.000000,-60.000000)},tree2x23={coord=vec3(-494.398682,5468.025879,81.360634),rot=vec3(105.293282,-0.000001,-60.000000)},tree3x3={coord=vec3(-497.572205,5466.193848,80.358612),rot=vec3(105.293274,-0.000001,-60.000000)},tree2x2={coord=vec3(-494.323517,5468.069336,81.384369),rot=vec3(105.293274,-0.000001,-60.000000)},},[330]={tree2x234={coord=vec3(-494.142456,5467.769531,81.469429),rot=vec3(100.182983,-0.000000,-29.999979)},tree4x4={coord=vec3(-497.857483,5461.334961,80.134819),rot=vec3(103.218605,-0.000000,-29.999979)},tree2x23={coord=vec3(-494.142456,5467.769531,81.469429),rot=vec3(100.182976,-0.000000,-29.999979)},tree3x3={coord=vec3(-496.012024,5464.531250,80.797798),rot=vec3(100.182968,-0.000000,-29.999979)},tree2x2={coord=vec3(-494.098145,5467.846191,81.485336),rot=vec3(100.182968,-0.000000,-29.999979)},},}
PLT.Info.cutting.trees[16]={coord=vec3(-501.248444,5459.338379,78.374001),[0]={tree2x234={coord=vec3(-501.248444,5458.638184,78.936829),rot=vec3(94.376930,-0.000000,0.000000)},tree4x4={coord=vec3(-501.248444,5451.111328,78.360710),rot=vec3(96.273895,-0.000000,0.000000)},tree2x23={coord=vec3(-501.248444,5458.638184,78.936829),rot=vec3(94.376923,-0.000000,0.000000)},tree3x3={coord=vec3(-501.248444,5454.850098,78.646896),rot=vec3(94.376923,-0.000000,0.000000)},tree2x2={coord=vec3(-501.248444,5458.728027,78.943695),rot=vec3(94.376923,-0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-500.898438,5458.731934,79.088150),rot=vec3(86.292625,-0.000001,29.999979)},tree4x4={coord=vec3(-497.131836,5452.208008,79.466278),rot=vec3(89.796448,-0.000001,29.999979)},tree2x23={coord=vec3(-500.898438,5458.731934,79.088150),rot=vec3(86.292618,-0.000001,29.999979)},tree3x3={coord=vec3(-499.002899,5455.448730,79.333794),rot=vec3(86.292610,-0.000001,29.999979)},tree2x2={coord=vec3(-500.943329,5458.809570,79.082329),rot=vec3(86.292610,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-500.642212,5458.988281,79.154999),rot=vec3(78.119644,-0.000001,60.000000)},tree4x4={coord=vec3(-494.244629,5455.294922,80.479095),rot=vec3(81.291595,-0.000000,60.000000)},tree2x23={coord=vec3(-500.642212,5458.988281,79.154999),rot=vec3(78.119644,-0.000001,60.000000)},tree3x3={coord=vec3(-497.422668,5457.129395,79.937096),rot=vec3(78.900841,-0.000000,60.000000)},tree2x2={coord=vec3(-500.718475,5459.032227,79.136467),rot=vec3(78.119644,-0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-500.548431,5459.338379,79.223373),rot=vec3(74.328186,-0.000000,90.000000)},tree4x4={coord=vec3(-493.280090,5459.338379,81.262566),rot=vec3(78.119644,-0.000000,90.000000)},tree2x23={coord=vec3(-500.548431,5459.338379,79.223373),rot=vec3(74.328186,-0.000000,90.000000)},tree3x3={coord=vec3(-496.890656,5459.338379,80.249588),rot=vec3(74.328186,-0.000000,90.000000)},tree2x2={coord=vec3(-500.635071,5459.338379,79.199066),rot=vec3(74.328186,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-500.642212,5459.688477,79.214958),rot=vec3(74.328247,-0.000000,120.000000)},tree4x4={coord=vec3(-494.347626,5463.322754,81.254135),rot=vec3(75.822243,-0.000000,120.000000)},tree2x23={coord=vec3(-500.642212,5459.688477,79.214958),rot=vec3(74.328247,-0.000000,120.000000)},tree3x3={coord=vec3(-497.474487,5461.517578,80.241165),rot=vec3(74.328247,-0.000000,120.000000)},tree2x2={coord=vec3(-500.717255,5459.645020,79.190651),rot=vec3(74.328247,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-500.898438,5459.944824,79.132011),rot=vec3(79.689850,0.000001,149.999954)},tree4x4={coord=vec3(-497.184875,5466.376465,80.483109),rot=vec3(80.486732,-0.000000,149.999954)},tree2x23={coord=vec3(-500.898438,5459.944824,79.132011),rot=vec3(79.689850,0.000001,149.999954)},tree3x3={coord=vec3(-499.029602,5463.181641,79.811943),rot=vec3(79.689850,0.000001,149.999954)},tree2x2={coord=vec3(-500.942719,5459.868164,79.115898),rot=vec3(79.689850,0.000001,149.999954)},},[180]={tree2x234={coord=vec3(-501.248444,5460.038574,79.056747),rot=vec3(88.907364,-0.000000,-179.999893)},tree4x4={coord=vec3(-501.248444,5467.585938,79.200699),rot=vec3(92.517349,-0.000000,-179.999893)},tree2x23={coord=vec3(-501.248444,5460.038574,79.056747),rot=vec3(88.907356,-0.000000,-179.999893)},tree3x3={coord=vec3(-501.248444,5463.836914,79.129189),rot=vec3(88.907349,-0.000000,-179.999893)},tree2x2={coord=vec3(-501.248444,5459.948730,79.055031),rot=vec3(88.907349,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-501.598450,5459.944824,78.959198),rot=vec3(98.209000,0.000001,-149.999954)},tree4x4={coord=vec3(-505.334290,5466.416016,77.771317),rot=vec3(100.182983,-0.000000,-149.999954)},tree2x23={coord=vec3(-501.598450,5459.944824,78.959198),rot=vec3(98.209000,0.000001,-149.999954)},tree3x3={coord=vec3(-503.478485,5463.201172,78.416763),rot=vec3(98.209000,0.000001,-149.999954)},tree2x2={coord=vec3(-501.553925,5459.867676,78.972046),rot=vec3(98.209000,0.000001,-149.999954)},},[240]={tree2x234={coord=vec3(-501.854675,5459.688477,78.826118),rot=vec3(104.250793,-0.000002,-119.999985)},tree4x4={coord=vec3(-508.191101,5463.346680,76.857803),rot=vec3(105.293289,-0.000002,-119.999985)},tree2x23={coord=vec3(-501.854675,5459.688477,78.826118),rot=vec3(104.250793,-0.000002,-119.999985)},tree3x3={coord=vec3(-505.043457,5461.529297,77.890930),rot=vec3(104.250793,-0.000002,-119.999985)},tree2x2={coord=vec3(-501.779144,5459.644531,78.848274),rot=vec3(104.250793,-0.000002,-119.999985)},},[270]={tree2x234={coord=vec3(-501.948456,5459.338379,78.782921),rot=vec3(107.409668,0.000000,-90.000000)},tree4x4={coord=vec3(-509.151642,5459.338379,76.524246),rot=vec3(107.409668,0.000000,-90.000000)},tree2x23={coord=vec3(-501.948456,5459.338379,78.782921),rot=vec3(107.409660,0.000000,-90.000000)},tree3x3={coord=vec3(-505.573425,5459.338379,77.646255),rot=vec3(107.409653,0.000000,-90.000000)},tree2x2={coord=vec3(-501.862579,5459.338379,78.809853),rot=vec3(107.409653,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-501.854675,5458.988281,78.837280),rot=vec3(106.346214,-0.000000,-60.000000)},tree4x4={coord=vec3(-508.128052,5455.366699,76.712685),rot=vec3(106.346214,-0.000000,-60.000000)},tree2x23={coord=vec3(-501.854675,5458.988281,78.837280),rot=vec3(106.346207,-0.000000,-60.000000)},tree3x3={coord=vec3(-505.011719,5457.165527,77.743088),rot=vec3(105.282730,-0.000000,-60.000000)},tree2x2={coord=vec3(-501.779877,5459.031250,78.862610),rot=vec3(107.409653,-0.000000,-60.000000)},},[330]={tree2x234={coord=vec3(-501.598450,5458.731934,78.855087),rot=vec3(101.184807,0.000001,-29.999979)},tree4x4={coord=vec3(-505.301270,5452.318359,77.390778),rot=vec3(101.184807,0.000001,-29.999979)},tree2x23={coord=vec3(-501.598450,5458.731934,78.855087),rot=vec3(101.184799,0.000001,-29.999979)},tree3x3={coord=vec3(-503.461884,5455.504395,78.068176),rot=vec3(100.172935,0.000000,-29.999979)},tree2x2={coord=vec3(-501.554321,5458.808105,78.872543),rot=vec3(102.196632,0.000001,-29.999979)},},}
PLT.Info.cutting.trees[17]={coord=vec3(-511.703979,5463.463379,75.147621),[0]={tree2x234={coord=vec3(-511.703979,5462.763184,75.784691),rot=vec3(90.694412,-0.000000,0.000000)},tree4x4={coord=vec3(-511.703979,5455.214844,75.693199),rot=vec3(91.601341,-0.000000,0.000000)},tree2x23={coord=vec3(-511.703979,5462.763184,75.784691),rot=vec3(90.694412,-0.000000,0.000000)},tree3x3={coord=vec3(-511.703979,5458.964355,75.738647),rot=vec3(90.694412,-0.000000,0.000000)},tree2x2={coord=vec3(-511.703979,5462.853027,75.785782),rot=vec3(90.694412,-0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-511.353973,5462.856934,75.949501),rot=vec3(82.925545,-0.000001,29.999979)},tree4x4={coord=vec3(-507.608215,5456.369141,76.879227),rot=vec3(83.754791,-0.000001,29.999979)},tree2x23={coord=vec3(-511.353973,5462.856934,75.949501),rot=vec3(82.925537,-0.000001,29.999979)},tree3x3={coord=vec3(-509.468933,5459.591797,76.417381),rot=vec3(82.925537,-0.000001,29.999979)},tree2x2={coord=vec3(-511.398651,5462.934570,75.938416),rot=vec3(82.925537,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-511.097748,5463.113281,76.005630),rot=vec3(75.071449,-0.000001,60.000000)},tree4x4={coord=vec3(-504.780792,5459.466309,77.895363),rot=vec3(76.580383,-0.000000,60.000000)},tree2x23={coord=vec3(-511.097748,5463.113281,76.005630),rot=vec3(75.071442,-0.000002,60.000000)},tree3x3={coord=vec3(-507.918762,5461.277832,76.984306),rot=vec3(75.071442,-0.000002,60.000000)},tree2x2={coord=vec3(-511.173065,5463.156738,75.982445),rot=vec3(75.822144,-0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-511.003967,5463.463379,75.982002),rot=vec3(72.142204,-0.000000,90.000000)},tree4x4={coord=vec3(-503.818695,5463.463379,78.296944),rot=vec3(72.142204,-0.000000,90.000000)},tree2x23={coord=vec3(-511.003967,5463.463379,75.982002),rot=vec3(72.142204,-0.000000,90.000000)},tree3x3={coord=vec3(-507.388000,5463.463379,77.146988),rot=vec3(72.142204,-0.000000,90.000000)},tree2x2={coord=vec3(-511.089630,5463.463379,75.954399),rot=vec3(72.142204,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-511.097748,5463.813477,75.988838),rot=vec3(74.328247,-0.000000,120.000000)},tree4x4={coord=vec3(-504.803162,5467.447754,78.028015),rot=vec3(75.822243,-0.000000,120.000000)},tree2x23={coord=vec3(-511.097748,5463.813477,75.988838),rot=vec3(74.328247,-0.000000,120.000000)},tree3x3={coord=vec3(-507.930023,5465.642578,76.965042),rot=vec3(73.584953,-0.000000,120.000000)},tree2x2={coord=vec3(-511.172791,5463.770020,75.964531),rot=vec3(75.071518,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-511.353973,5464.069824,75.920403),rot=vec3(82.104507,-0.000000,149.999954)},tree4x4={coord=vec3(-507.615265,5470.545898,76.957382),rot=vec3(83.754791,-0.000000,149.999954)},tree2x23={coord=vec3(-511.353973,5464.069824,75.920403),rot=vec3(82.104507,-0.000000,149.999954)},tree3x3={coord=vec3(-509.472473,5467.328613,76.442261),rot=vec3(82.104507,-0.000000,149.999954)},tree2x2={coord=vec3(-511.398560,5463.992676,75.908043),rot=vec3(82.925537,-0.000000,149.999954)},},[180]={tree2x234={coord=vec3(-511.703979,5464.163574,75.751106),rot=vec3(92.517349,-0.000000,-179.999893)},tree4x4={coord=vec3(-511.703979,5471.705566,75.419533),rot=vec3(94.376930,-0.000000,-179.999893)},tree2x23={coord=vec3(-511.703979,5464.163574,75.751106),rot=vec3(92.517349,-0.000000,-179.999893)},tree3x3={coord=vec3(-511.703979,5467.958984,75.584244),rot=vec3(92.517349,-0.000000,-179.999893)},tree2x2={coord=vec3(-511.703979,5464.073730,75.755058),rot=vec3(92.517349,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-512.053955,5464.069824,75.653290),rot=vec3(101.184807,-0.000001,-149.999954)},tree4x4={coord=vec3(-515.756775,5470.483398,74.188980),rot=vec3(105.293289,0.000000,-149.999954)},tree2x23={coord=vec3(-512.053955,5464.069824,75.653290),rot=vec3(101.184799,-0.000001,-149.999954)},tree3x3={coord=vec3(-513.917358,5467.297363,74.916382),rot=vec3(101.184792,-0.000001,-149.999954)},tree2x2={coord=vec3(-512.009827,5463.993652,75.670746),rot=vec3(101.184792,-0.000001,-149.999954)},},[240]={tree2x234={coord=vec3(-512.310181,5463.813477,75.551239),rot=vec3(103.218605,-0.000001,-119.999985)},tree4x4={coord=vec3(-518.674622,5467.488281,73.403778),rot=vec3(95.244377,-0.000002,-119.999985)},tree2x23={coord=vec3(-512.310181,5463.813477,75.551239),rot=vec3(106.346214,-0.000000,-119.999985)},tree3x3={coord=vec3(-515.467224,5465.636230,74.482048),rot=vec3(106.346207,-0.000000,-119.999985)},tree2x2={coord=vec3(-512.235413,5463.770508,75.576569),rot=vec3(106.346207,-0.000000,-119.999985)},},[270]={tree2x234={coord=vec3(-512.403992,5463.463379,75.546745),rot=vec3(107.409668,0.000000,-90.000000)},tree4x4={coord=vec3(-519.607117,5463.463379,73.288071),rot=vec3(108.483757,0.000000,-90.000000)},tree2x23={coord=vec3(-512.403992,5463.463379,75.546745),rot=vec3(107.409660,0.000000,-90.000000)},tree3x3={coord=vec3(-516.028931,5463.463379,74.410080),rot=vec3(107.409653,0.000000,-90.000000)},tree2x2={coord=vec3(-512.318115,5463.463379,75.573677),rot=vec3(107.409653,0.000000,-90.000000)},},[300]={tree2x234={coord=vec3(-512.310181,5463.113281,75.565903),rot=vec3(104.250793,-0.000000,-60.000000)},tree4x4={coord=vec3(-518.646606,5459.455078,73.707588),rot=vec3(104.250793,-0.000000,-60.000000)},tree2x23={coord=vec3(-512.310181,5463.113281,75.565903),rot=vec3(104.250793,-0.000000,-60.000000)},tree3x3={coord=vec3(-515.498962,5461.272461,74.630714),rot=vec3(104.250793,-0.000000,-60.000000)},tree2x2={coord=vec3(-512.234619,5463.157227,75.588058),rot=vec3(104.250793,-0.000000,-60.000000)},},[330]={tree2x234={coord=vec3(-512.053955,5462.856934,75.678696),rot=vec3(98.209000,0.000002,-29.999975)},tree4x4={coord=vec3(-515.789795,5456.385742,74.600815),rot=vec3(99.191086,0.000000,-29.999979)},tree2x23={coord=vec3(-512.053955,5462.856934,75.678696),rot=vec3(98.209000,0.000001,-29.999968)},tree3x3={coord=vec3(-513.934021,5459.600586,75.136261),rot=vec3(98.209000,0.000001,-29.999962)},tree2x2={coord=vec3(-512.009460,5462.934082,75.691544),rot=vec3(99.191086,0.000002,-29.999966)},},}
PLT.Info.cutting.trees[18]={coord=vec3(-523.812683,5458.627930,71.678261),[0]={tree2x234={coord=vec3(-523.812683,5457.927734,72.361671),rot=vec3(88.907364,0.000000,0.000000)},tree4x4={coord=vec3(-523.812683,5450.380371,72.395622),rot=vec3(93.442513,-0.000000,0.000000)},tree2x23={coord=vec3(-523.812683,5457.927734,72.361671),rot=vec3(88.907356,0.000000,0.000000)},tree3x3={coord=vec3(-523.812683,5454.129395,72.434113),rot=vec3(89.796417,0.000000,0.000000)},tree2x2={coord=vec3(-523.812683,5458.017578,72.359955),rot=vec3(88.907349,0.000000,0.000000)},},	[30]={tree2x234={coord=vec3(-523.462708,5458.021484,72.434059),rot=vec3(81.291595,-0.000001,29.999979)},tree4x4={coord=vec3(-519.731689,5451.559570,73.467018),rot=vec3(83.754791,-0.000001,29.999979)},tree2x23={coord=vec3(-523.462708,5458.021484,72.434059),rot=vec3(81.291588,-0.000001,29.999979)},tree3x3={coord=vec3(-521.585083,5454.769531,73.009247),rot=vec3(81.291580,-0.000001,29.999979)},tree2x2={coord=vec3(-523.507202,5458.098633,72.420433),rot=vec3(81.291580,-0.000001,29.999979)},},	[60]={tree2x234={coord=vec3(-523.206482,5458.277832,72.454788),rot=vec3(75.822174,-0.000001,60.000000)},tree4x4={coord=vec3(-516.867981,5454.618164,74.303772),rot=vec3(78.900841,-0.000000,60.000000)},tree2x23={coord=vec3(-523.206482,5458.277832,72.454788),rot=vec3(75.822174,-0.000001,60.000000)},tree3x3={coord=vec3(-520.016663,5456.436035,73.385284),rot=vec3(75.822174,-0.000001,60.000000)},tree2x2={coord=vec3(-523.282043,5458.321777,72.432747),rot=vec3(75.822174,-0.000001,60.000000)},},	[90]={tree2x234={coord=vec3(-523.112671,5458.627930,72.473053),rot=vec3(74.328186,-0.000000,90.000000)},tree4x4={coord=vec3(-515.844299,5458.627930,74.512245),rot=vec3(74.328186,-0.000000,90.000000)},tree2x23={coord=vec3(-523.112671,5458.627930,72.473053),rot=vec3(74.328186,-0.000000,90.000000)},tree3x3={coord=vec3(-519.454895,5458.627930,73.499268),rot=vec3(74.328186,-0.000000,90.000000)},tree2x2={coord=vec3(-523.199280,5458.627930,72.448746),rot=vec3(74.328186,-0.000000,90.000000)},},	[120]={tree2x234={coord=vec3(-523.206482,5458.978027,72.411453),rot=vec3(76.580452,-0.000000,120.000000)},tree4x4={coord=vec3(-516.847351,5462.649902,74.163422),rot=vec3(76.580452,-0.000000,120.000000)},tree2x23={coord=vec3(-523.206482,5458.978027,72.411453),rot=vec3(76.580444,-0.000000,120.000000)},tree3x3={coord=vec3(-520.006287,5460.825684,73.293121),rot=vec3(76.580437,-0.000000,120.000000)},tree2x2={coord=vec3(-523.282288,5458.934082,72.390564),rot=vec3(76.580437,-0.000000,120.000000)},},	[150]={tree2x234={coord=vec3(-523.462708,5459.234375,72.376358),rot=vec3(84.592331,0.000000,149.999954)},tree4x4={coord=vec3(-519.704956,5465.743164,73.087791),rot=vec3(84.592331,0.000000,149.999954)},tree2x23={coord=vec3(-523.462708,5459.234375,72.376358),rot=vec3(84.592323,0.000001,149.999954)},tree3x3={coord=vec3(-521.571655,5462.509766,72.734383),rot=vec3(84.592316,0.000001,149.999954)},tree2x2={coord=vec3(-523.507507,5459.156738,72.367874),rot=vec3(85.438248,0.000001,149.999954)},},	[200]={tree2x234={coord=vec3(-524.052124,5459.285645,72.151817),rot=vec3(99.191086,-0.000000,-159.999939)},tree4x4={coord=vec3(-526.600891,5466.288086,70.946030),rot=vec3(103.218605,0.000000,-159.999939)},tree2x23={coord=vec3(-524.052124,5459.285645,72.151817),rot=vec3(99.191086,-0.000000,-159.999939)},tree3x3={coord=vec3(-525.334778,5462.809570,71.545013),rot=vec3(99.191086,-0.000000,-159.999939)},tree2x2={coord=vec3(-524.021729,5459.202148,72.166191),rot=vec3(99.191086,-0.000000,-159.999939)},},	[230]={tree2x234={coord=vec3(-524.348938,5459.078125,72.102310),rot=vec3(105.293396,-0.000002,-129.999985)},tree4x4={coord=vec3(-529.927002,5463.758301,70.111168),rot=vec3(105.293396,-0.000002,-129.999985)},tree2x23={coord=vec3(-524.348938,5459.078125,72.102310),rot=vec3(105.293388,-0.000001,-129.999985)},tree3x3={coord=vec3(-527.156067,5461.433594,71.100281),rot=vec3(105.293381,-0.000002,-129.999985)},tree2x2={coord=vec3(-524.282471,5459.022461,72.126045),rot=vec3(105.293381,-0.000002,-129.999985)},},	[260]={tree2x234={coord=vec3(-524.502075,5458.749512,72.069145),rot=vec3(106.346214,0.000003,-99.999985)},tree4x4={coord=vec3(-531.635803,5460.007324,69.944550),rot=vec3(106.346214,0.000003,-99.999985)},tree2x23={coord=vec3(-524.502075,5458.749512,72.069145),rot=vec3(106.346207,0.000002,-99.999985)},tree3x3={coord=vec3(-528.092102,5459.382324,70.999954),rot=vec3(106.346199,0.000002,-99.999985)},tree2x2={coord=vec3(-524.417053,5458.734863,72.094475),rot=vec3(106.346199,0.000002,-99.999985)},},	[290]={tree2x234={coord=vec3(-524.470459,5458.388672,72.112938),rot=vec3(104.250793,-0.000000,-69.999992)},tree4x4={coord=vec3(-531.345886,5455.886230,70.254623),rot=vec3(104.250793,-0.000000,-69.999992)},tree2x23={coord=vec3(-524.470459,5458.388672,72.112938),rot=vec3(104.250793,-0.000000,-69.999992)},tree3x3={coord=vec3(-527.930481,5457.129395,71.177750),rot=vec3(104.250793,-0.000000,-69.999992)},tree2x2={coord=vec3(-524.388489,5458.418457,72.135094),rot=vec3(104.250793,-0.000000,-69.999992)},},	[320]={tree2x234={coord=vec3(-524.262634,5458.091797,72.166977),rot=vec3(99.191185,0.000000,-39.999950)},tree4x4={coord=vec3(-529.052734,5452.383301,70.961174),rot=vec3(99.191185,0.000000,-39.999950)},tree2x23={coord=vec3(-524.262634,5458.091797,72.166977),rot=vec3(99.191177,0.000000,-39.999947)},tree3x3={coord=vec3(-526.673218,5455.218750,71.560165),rot=vec3(99.191170,0.000000,-39.999943)},tree2x2={coord=vec3(-524.205505,5458.159668,72.181351),rot=vec3(99.191170,0.000000,-39.999943)},},	[350]={tree2x234={coord=vec3(-523.934265,5457.938477,72.311974),rot=vec3(91.601341,-0.000000,-10.000000)},tree4x4={coord=vec3(-525.244568,5450.506836,71.991020),rot=vec3(94.376930,-0.000000,-10.000003)},tree2x23={coord=vec3(-523.934265,5457.938477,72.311974),rot=vec3(91.601334,-0.000000,-9.999999)},tree3x3={coord=vec3(-524.593689,5454.198730,72.205811),rot=vec3(91.601326,-0.000000,-9.999998)},tree2x2={coord=vec3(-523.918640,5458.026855,72.314491),rot=vec3(91.601326,-0.000000,-9.999998)},},}
PLT.Info.cutting.trees[19]={coord=vec3(-534.104858,5452.340332,68.992737),[0]={tree2x234={coord=vec3(-534.104858,5451.640137,69.654480),rot=vec3(91.601341,-0.000000,0.000000)},tree4x4={coord=vec3(-534.104858,5444.094238,69.443527),rot=vec3(91.601341,-0.000000,0.000000)},tree2x23={coord=vec3(-534.104858,5451.640137,69.654480),rot=vec3(91.601334,-0.000000,0.000000)},tree3x3={coord=vec3(-534.104858,5447.842773,69.548317),rot=vec3(91.601326,-0.000000,0.000000)},tree2x2={coord=vec3(-534.104858,5451.729980,69.601997),rot=vec3(92.517349,-0.000000,0.000000)},},	[30]={tree2x234={coord=vec3(-533.754883,5451.733887,69.689964),rot=vec3(82.925545,-0.000001,29.999979)},tree4x4={coord=vec3(-530.009155,5445.246094,70.619690),rot=vec3(82.925545,-0.000001,29.999979)},tree2x23={coord=vec3(-533.754883,5451.733887,69.689964),rot=vec3(82.925537,-0.000001,29.999979)},tree3x3={coord=vec3(-531.869873,5448.468750,70.157845),rot=vec3(82.925537,-0.000001,29.999979)},tree2x2={coord=vec3(-533.799561,5451.811523,69.678879),rot=vec3(82.925537,-0.000001,29.999979)},},	[60]={tree2x234={coord=vec3(-533.498657,5451.990234,69.744247),rot=vec3(76.580383,-0.000000,60.000000)},tree4x4={coord=vec3(-527.139526,5448.318359,71.496223),rot=vec3(79.689850,-0.000000,60.000000)},tree2x23={coord=vec3(-533.498657,5451.990234,69.744247),rot=vec3(76.580376,-0.000000,60.000000)},tree3x3={coord=vec3(-530.298462,5450.142578,70.625923),rot=vec3(76.580368,-0.000000,60.000000)},tree2x2={coord=vec3(-533.574463,5452.034180,69.723358),rot=vec3(76.580368,-0.000000,60.000000)},},	[90]={tree2x234={coord=vec3(-533.404846,5452.340332,69.791695),rot=vec3(74.328186,-0.000000,90.000000)},tree4x4={coord=vec3(-526.136475,5452.340332,71.830887),rot=vec3(75.822174,-0.000000,90.000000)},tree2x23={coord=vec3(-533.404846,5452.340332,69.791695),rot=vec3(74.328186,-0.000000,90.000000)},tree3x3={coord=vec3(-529.747070,5452.340332,70.817909),rot=vec3(74.328186,-0.000000,90.000000)},tree2x2={coord=vec3(-533.491455,5452.340332,69.767387),rot=vec3(75.071449,-0.000000,90.000000)},},	[135]={tree2x234={coord=vec3(-533.609863,5452.835449,69.775146),rot=vec3(80.486732,-0.000000,135.000000)},tree4x4={coord=vec3(-528.345337,5458.100098,71.022812),rot=vec3(82.104507,-0.000000,135.000000)},tree2x23={coord=vec3(-533.609863,5452.835449,69.775146),rot=vec3(80.486725,-0.000000,135.000000)},tree3x3={coord=vec3(-530.960510,5455.484863,70.403030),rot=vec3(80.486717,-0.000000,135.000000)},tree2x2={coord=vec3(-533.672668,5452.772461,69.760269),rot=vec3(80.486717,-0.000000,135.000000)},},	[165]={tree2x234={coord=vec3(-533.923706,5453.016602,69.646469),rot=vec3(88.027245,-0.000000,164.999924)},tree4x4={coord=vec3(-531.971008,5460.304199,69.796333),rot=vec3(88.907501,-0.000000,164.999924)},tree2x23={coord=vec3(-533.923706,5453.016602,69.646469),rot=vec3(88.027245,-0.000000,164.999924)},tree3x3={coord=vec3(-532.941040,5456.684082,69.777245),rot=vec3(88.027245,-0.000000,164.999924)},tree2x2={coord=vec3(-533.946960,5452.929688,69.643372),rot=vec3(88.027245,-0.000000,164.999924)},},	[195]={tree2x234={coord=vec3(-534.286011,5453.016602,69.552818),rot=vec3(95.320831,0.000000,-164.999924)},tree4x4={coord=vec3(-536.231384,5460.276855,68.742775),rot=vec3(97.236771,-0.000000,-164.999924)},tree2x23={coord=vec3(-534.286011,5453.016602,69.552818),rot=vec3(95.320824,-0.000000,-164.999924)},tree3x3={coord=vec3(-535.265015,5456.670410,69.200531),rot=vec3(95.320816,0.000000,-164.999924)},tree2x2={coord=vec3(-534.262817,5452.930176,69.561165),rot=vec3(95.320816,0.000000,-164.999924)},},	[225]={tree2x234={coord=vec3(-534.599854,5452.835449,69.498253),rot=vec3(101.184807,-0.000000,-135.000000)},tree4x4={coord=vec3(-539.836426,5458.071777,68.033943),rot=vec3(103.218605,-0.000000,-135.000000)},tree2x23={coord=vec3(-534.599854,5452.835449,69.498253),rot=vec3(101.184799,-0.000000,-135.000000)},tree3x3={coord=vec3(-537.235107,5455.470703,68.761345),rot=vec3(101.184792,-0.000000,-135.000000)},tree2x2={coord=vec3(-534.537415,5452.772949,69.515709),rot=vec3(102.196632,-0.000000,-135.000000)},},	[255]={tree2x234={coord=vec3(-534.781006,5452.521484,69.414528),rot=vec3(105.293289,-0.000000,-104.999908)},tree4x4={coord=vec3(-541.814575,5454.406250,67.473404),rot=vec3(105.293289,-0.000000,-104.999908)},tree2x23={coord=vec3(-534.781006,5452.521484,69.414528),rot=vec3(105.293282,-0.000000,-104.999908)},tree3x3={coord=vec3(-538.320618,5453.469727,68.412506),rot=vec3(105.293274,-0.000001,-104.999908)},tree2x2={coord=vec3(-534.697144,5452.499023,69.438263),rot=vec3(105.293274,-0.000001,-104.999908)},},	[285]={tree2x234={coord=vec3(-534.781006,5452.159180,69.379089),rot=vec3(107.409775,-0.000001,-74.999977)},tree4x4={coord=vec3(-541.738770,5450.294922,67.120399),rot=vec3(109.568695,-0.000001,-74.999977)},tree2x23={coord=vec3(-534.781006,5452.159180,69.379089),rot=vec3(107.409767,-0.000001,-74.999977)},tree3x3={coord=vec3(-538.282471,5451.221191,68.242416),rot=vec3(107.409760,-0.000001,-74.999977)},tree2x2={coord=vec3(-534.698059,5452.181152,69.406021),rot=vec3(107.409760,-0.000001,-74.999977)},},	[335]={tree2x234={coord=vec3(-534.400696,5451.706055,69.475456),rot=vec3(99.191101,0.000001,-24.999912)},tree4x4={coord=vec3(-537.550110,5444.952148,68.269669),rot=vec3(99.191101,0.000001,-24.999912)},tree2x23={coord=vec3(-534.400696,5451.706055,69.475456),rot=vec3(99.191093,0.000000,-24.999910)},tree3x3={coord=vec3(-535.985596,5448.307129,68.868652),rot=vec3(99.191086,0.000001,-24.999908)},tree2x2={coord=vec3(-534.363159,5451.786621,69.489830),rot=vec3(99.191086,0.000001,-24.999908)},},}
PLT.Info.cutting.trees[20]={coord=vec3(-535.350830,5465.550293,67.926582),[0]={tree2x234={coord=vec3(-535.350830,5464.850098,68.592575),rot=vec3(85.438248,0.000000,0.000000)},tree4x4={coord=vec3(-535.350830,5457.325195,69.192963),rot=vec3(88.907364,0.000000,0.000000)},tree2x23={coord=vec3(-535.350830,5464.850098,68.592575),rot=vec3(85.438248,0.000000,0.000000)},tree3x3={coord=vec3(-535.350830,5461.062988,68.894722),rot=vec3(85.438248,0.000000,0.000000)},tree2x2={coord=vec3(-535.350830,5464.939453,68.585419),rot=vec3(85.438248,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-535.000854,5464.943848,68.711288),rot=vec3(78.900841,-0.000001,29.999979)},tree4x4={coord=vec3(-531.296936,5458.528320,70.164528),rot=vec3(79.689850,-0.000001,29.999979)},tree2x23={coord=vec3(-535.000854,5464.943848,68.711288),rot=vec3(78.900841,-0.000001,29.999979)},tree3x3={coord=vec3(-533.136902,5461.715332,69.442627),rot=vec3(78.900841,-0.000001,29.999979)},tree2x2={coord=vec3(-535.044983,5465.020020,68.693962),rot=vec3(78.900841,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-534.744629,5465.200195,68.710648),rot=vec3(74.328186,-0.000000,60.000000)},tree4x4={coord=vec3(-528.450073,5461.565918,70.749840),rot=vec3(74.328186,-0.000000,60.000000)},tree2x23={coord=vec3(-534.744629,5465.200195,68.710648),rot=vec3(74.328186,-0.000000,60.000000)},tree3x3={coord=vec3(-531.576904,5463.371094,69.736862),rot=vec3(74.328186,-0.000000,60.000000)},tree2x2={coord=vec3(-534.819702,5465.243652,68.686340),rot=vec3(75.071449,-0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-534.650818,5465.550293,68.694786),rot=vec3(74.328186,-0.000000,90.000000)},tree4x4={coord=vec3(-527.382446,5465.550293,70.733978),rot=vec3(74.328186,-0.000000,90.000000)},tree2x23={coord=vec3(-534.650818,5465.550293,68.694786),rot=vec3(74.328186,-0.000000,90.000000)},tree3x3={coord=vec3(-530.993042,5465.550293,69.670998),rot=vec3(73.584892,-0.000000,90.000000)},tree2x2={coord=vec3(-534.737427,5465.550293,68.670479),rot=vec3(75.822174,-0.000000,90.000000)},},[145]={tree2x234={coord=vec3(-534.949341,5466.123535,68.700165),rot=vec3(88.027199,-0.000002,144.999969)},tree4x4={coord=vec3(-530.621948,5472.304199,68.960045),rot=vec3(88.027199,-0.000002,144.999969)},tree2x23={coord=vec3(-534.949341,5466.123535,68.590164),rot=vec3(87.146912,-0.000002,144.999969)},tree3x3={coord=vec3(-532.773010,5469.231445,68.779259),rot=vec3(87.146904,-0.000002,144.999969)},tree2x2={coord=vec3(-535.000916,5466.049805,68.585686),rot=vec3(88.018372,-0.000002,144.999969)},},[175]={tree2x234={coord=vec3(-535.289795,5466.247559,68.470673),rot=vec3(96.273895,-0.000001,174.999893)},tree4x4={coord=vec3(-534.635803,5473.722656,67.645706),rot=vec3(97.236641,-0.000001,174.999893)},tree2x23={coord=vec3(-535.289795,5466.247559,68.470673),rot=vec3(96.273888,-0.000001,174.999893)},tree3x3={coord=vec3(-534.960693,5470.009277,68.055511),rot=vec3(96.273880,-0.000001,174.999893)},tree2x2={coord=vec3(-535.297607,5466.158203,68.480507),rot=vec3(96.273880,-0.000001,174.999893)},},[205]={tree2x234={coord=vec3(-535.646667,5466.184570,68.399857),rot=vec3(103.218605,-0.000001,-154.999954)},tree4x4={coord=vec3(-538.752502,5472.845215,66.673645),rot=vec3(105.293289,-0.000001,-154.999954)},tree2x23={coord=vec3(-535.646667,5466.184570,68.399857),rot=vec3(103.218597,-0.000000,-154.999954)},tree3x3={coord=vec3(-537.209656,5469.536621,67.531151),rot=vec3(103.218590,0.000000,-154.999954)},tree2x2={coord=vec3(-535.609680,5466.105469,68.420433),rot=vec3(103.218590,0.000000,-154.999954)},},[235]={tree2x234={coord=vec3(-535.924255,5465.951660,68.328964),rot=vec3(106.346214,0.000001,-124.999985)},tree4x4={coord=vec3(-541.858093,5470.106445,66.204369),rot=vec3(106.346214,0.000001,-124.999985)},tree2x23={coord=vec3(-535.924255,5465.951660,68.328964),rot=vec3(106.346207,0.000001,-124.999985)},tree3x3={coord=vec3(-538.910400,5468.042480,67.259773),rot=vec3(106.346199,0.000002,-124.999985)},tree2x2={coord=vec3(-535.853516,5465.901855,68.354294),rot=vec3(106.346199,0.000002,-124.999985)},},[265]={tree2x234={coord=vec3(-536.048157,5465.611328,68.334251),rot=vec3(104.250893,0.000000,-95.000000)},tree4x4={coord=vec3(-543.337036,5466.249023,66.475922),rot=vec3(106.346321,-0.000000,-95.000000)},tree2x23={coord=vec3(-536.048157,5465.611328,68.334251),rot=vec3(104.250885,-0.000000,-95.000000)},tree3x3={coord=vec3(-539.716248,5465.932129,67.399055),rot=vec3(104.250877,0.000000,-95.000000)},tree2x2={coord=vec3(-535.961243,5465.604004,68.356407),rot=vec3(105.293381,0.000000,-95.000000)},},[295]={tree2x234={coord=vec3(-535.985229,5465.254395,68.409676),rot=vec3(100.182983,-0.000000,-65.000008)},tree4x4={coord=vec3(-542.719238,5462.114258,67.075066),rot=vec3(100.182983,-0.000000,-65.000008)},tree2x23={coord=vec3(-535.985229,5465.254395,68.409676),rot=vec3(100.182976,-0.000000,-65.000008)},tree3x3={coord=vec3(-539.374084,5463.674316,67.738045),rot=vec3(100.182968,-0.000000,-65.000008)},tree2x2={coord=vec3(-535.904968,5465.291992,68.425583),rot=vec3(101.184792,0.000000,-65.000008)},},[325]={tree2x234={coord=vec3(-535.752319,5464.977051,68.482094),rot=vec3(94.376991,0.000001,-34.999954)},tree4x4={coord=vec3(-540.069641,5458.811035,67.905968),rot=vec3(95.320755,0.000000,-34.999954)},tree2x23={coord=vec3(-535.752319,5464.977051,68.482094),rot=vec3(94.376984,0.000001,-34.999954)},tree3x3={coord=vec3(-537.924988,5461.874023,68.192162),rot=vec3(94.376984,0.000001,-34.999950)},tree2x2={coord=vec3(-535.700867,5465.050293,68.488960),rot=vec3(94.376984,0.000001,-34.999950)},},}
PLT.Info.cutting.trees[21]={coord=vec3(-504.567047,5475.591309,77.094551),[0]={tree2x234={coord=vec3(-504.567047,5474.891113,77.790199),rot=vec3(88.027107,0.000000,0.000000)},tree4x4={coord=vec3(-504.567047,5467.346680,78.050087),rot=vec3(90.694412,-0.000000,0.000000)},tree2x23={coord=vec3(-504.567047,5474.891113,77.790199),rot=vec3(88.027107,0.000000,0.000000)},tree3x3={coord=vec3(-504.567047,5471.094238,77.920982),rot=vec3(88.027107,0.000000,0.000000)},tree2x2={coord=vec3(-504.567047,5474.980957,77.787102),rot=vec3(88.027107,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-504.217041,5474.984863,77.840866),rot=vec3(77.346176,-0.000000,29.999979)},tree4x4={coord=vec3(-500.534210,5468.605957,79.384544),rot=vec3(80.486732,-0.000000,29.999979)},tree2x23={coord=vec3(-504.217041,5474.984863,77.840866),rot=vec3(77.346169,-0.000001,29.999979)},tree3x3={coord=vec3(-502.363678,5471.774902,78.673073),rot=vec3(77.346161,-0.000001,29.999979)},tree2x2={coord=vec3(-504.260956,5475.061035,77.821152),rot=vec3(77.346161,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-503.960815,5475.241211,77.944946),rot=vec3(71.427925,-0.000001,60.000000)},tree4x4={coord=vec3(-497.763641,5471.663086,80.239281),rot=vec3(73.592262,-0.000000,60.000000)},tree2x23={coord=vec3(-503.960815,5475.241211,77.944946),rot=vec3(71.427925,-0.000001,60.000000)},tree3x3={coord=vec3(-500.842102,5473.440430,79.154915),rot=vec3(71.427925,-0.000001,60.000000)},tree2x2={coord=vec3(-504.034698,5475.283691,77.916283),rot=vec3(72.142204,-0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-503.867035,5475.591309,77.954582),rot=vec3(70.720718,-0.000000,90.000000)},tree4x4={coord=vec3(-496.741364,5475.591309,80.447060),rot=vec3(74.328186,-0.000000,90.000000)},tree2x23={coord=vec3(-503.867035,5475.591309,77.954582),rot=vec3(70.720718,-0.000000,90.000000)},tree3x3={coord=vec3(-500.281067,5475.591309,79.208908),rot=vec3(70.720718,-0.000000,90.000000)},tree2x2={coord=vec3(-503.951996,5475.591309,77.924866),rot=vec3(71.427925,-0.000000,90.000000)},},[145]={tree2x234={coord=vec3(-504.165558,5476.164551,77.796440),rot=vec3(83.754883,-0.000002,144.999969)},tree4x4={coord=vec3(-499.861359,5482.311523,78.617638),rot=vec3(83.754883,-0.000002,144.999969)},tree2x23={coord=vec3(-504.165558,5476.164551,77.796440),rot=vec3(83.754875,-0.000002,144.999969)},tree3x3={coord=vec3(-501.999481,5479.257813,78.209702),rot=vec3(83.754868,-0.000002,144.999969)},tree2x2={coord=vec3(-504.216888,5476.091309,77.786652),rot=vec3(83.754868,-0.000002,144.999969)},},[180]={tree2x234={coord=vec3(-504.567047,5476.291504,77.634682),rot=vec3(97.236641,-0.000000,-179.999893)},tree4x4={coord=vec3(-504.567047,5483.780273,76.683746),rot=vec3(99.191086,-0.000000,-179.999893)},tree2x23={coord=vec3(-504.567047,5476.291504,77.634682),rot=vec3(97.236641,-0.000000,-179.999893)},tree3x3={coord=vec3(-504.567047,5480.060059,77.156128),rot=vec3(97.236641,-0.000000,-179.999893)},tree2x2={coord=vec3(-504.567047,5476.202148,77.646019),rot=vec3(97.236641,-0.000000,-179.999893)},},[225]={tree2x234={coord=vec3(-505.062012,5476.086426,77.456390),rot=vec3(108.483757,-0.000000,-135.000000)},tree4x4={coord=vec3(-510.124634,5481.148926,74.953087),rot=vec3(111.770905,-0.000000,-135.000000)},tree2x23={coord=vec3(-505.062012,5476.086426,77.456390),rot=vec3(108.483749,-0.000000,-135.000000)},tree3x3={coord=vec3(-507.609741,5478.634277,76.251968),rot=vec3(108.483742,-0.000000,-135.000000)},tree2x2={coord=vec3(-505.001648,5476.026367,77.484924),rot=vec3(108.483742,-0.000000,-135.000000)},},[255]={tree2x234={coord=vec3(-505.243195,5475.772461,77.425018),rot=vec3(110.664268,-0.000000,-104.999908)},tree4x4={coord=vec3(-512.065857,5477.600586,74.761040),rot=vec3(112.888603,-0.000000,-104.999908)},tree2x23={coord=vec3(-505.243195,5475.772461,77.425018),rot=vec3(110.664261,-0.000000,-104.999908)},tree3x3={coord=vec3(-508.676666,5476.692383,76.084381),rot=vec3(110.664253,-0.000000,-104.999908)},tree2x2={coord=vec3(-505.161865,5475.750488,77.456779),rot=vec3(110.664253,-0.000000,-104.999908)},},[285]={tree2x234={coord=vec3(-505.243195,5475.410156,77.465263),rot=vec3(108.483864,-0.000001,-74.999977)},tree4x4={coord=vec3(-512.158813,5473.557129,75.071945),rot=vec3(108.483864,-0.000001,-74.999977)},tree2x23={coord=vec3(-505.243195,5475.410156,77.465263),rot=vec3(108.483856,-0.000001,-74.999977)},tree3x3={coord=vec3(-508.723450,5474.477539,76.260841),rot=vec3(108.483849,-0.000001,-74.999977)},tree2x2={coord=vec3(-505.160736,5475.432129,77.493797),rot=vec3(108.483849,-0.000001,-74.999977)},},[315]={tree2x234={coord=vec3(-505.062012,5475.096191,77.566353),rot=vec3(102.196655,-0.000000,-45.000000)},tree4x4={coord=vec3(-510.279480,5469.878418,75.971489),rot=vec3(102.196655,-0.000000,-45.000000)},tree2x23={coord=vec3(-505.062012,5475.096191,77.566353),rot=vec3(102.196655,-0.000000,-45.000000)},tree3x3={coord=vec3(-507.687683,5472.470703,76.763748),rot=vec3(102.196655,-0.000000,-45.000000)},tree2x2={coord=vec3(-504.999817,5475.158203,77.585365),rot=vec3(102.196655,-0.000000,-45.000000)},},[345]={tree2x234={coord=vec3(-504.748230,5474.915039,77.693817),rot=vec3(92.517349,-0.000000,-14.999998)},tree4x4={coord=vec3(-506.700165,5467.629883,77.307243),rot=vec3(94.376930,-0.000000,-14.999998)},tree2x23={coord=vec3(-504.748230,5474.915039,77.693817),rot=vec3(92.517349,-0.000000,-14.999998)},tree3x3={coord=vec3(-505.730530,5471.249023,77.526955),rot=vec3(92.517349,-0.000000,-14.999998)},tree2x2={coord=vec3(-504.724976,5475.001953,77.697769),rot=vec3(92.517349,-0.000000,-14.999998)},},}
PLT.Info.cutting.trees[22]={coord=vec3(-492.409119,5478.260742,80.971428),[5]={tree2x234={coord=vec3(-492.348114,5477.563477,81.651596),rot=vec3(85.438248,-0.000002,4.999990)},tree4x4={coord=vec3(-491.692261,5470.067383,82.141983),rot=vec3(90.694412,-0.000002,4.999990)},tree2x23={coord=vec3(-492.348114,5477.563477,81.651596),rot=vec3(85.438248,-0.000002,4.999990)},tree3x3={coord=vec3(-492.018066,5473.791016,81.953743),rot=vec3(85.438248,-0.000002,4.999990)},tree2x2={coord=vec3(-492.355927,5477.652832,81.644440),rot=vec3(85.438248,-0.000002,4.999990)},},[35]={tree2x234={coord=vec3(-492.007629,5477.687500,81.759781),rot=vec3(80.486771,-0.000001,34.999958)},tree4x4={coord=vec3(-487.737244,5471.588867,83.007446),rot=vec3(85.438309,-0.000001,34.999954)},tree2x23={coord=vec3(-492.007629,5477.687500,81.759781),rot=vec3(80.486771,-0.000001,34.999954)},tree3x3={coord=vec3(-489.858582,5474.618164,82.387665),rot=vec3(80.486771,-0.000001,34.999950)},tree2x2={coord=vec3(-492.058533,5477.760254,81.744904),rot=vec3(80.486771,-0.000001,34.999950)},},[65]={tree2x234={coord=vec3(-491.774689,5477.964844,81.741409),rot=vec3(76.580383,-0.000000,65.000008)},tree4x4={coord=vec3(-485.119781,5474.861816,83.383385),rot=vec3(79.689850,0.000001,65.000008)},tree2x23={coord=vec3(-491.774689,5477.964844,81.741409),rot=vec3(76.580376,-0.000001,65.000008)},tree3x3={coord=vec3(-488.425629,5476.403320,82.623085),rot=vec3(76.580368,-0.000001,65.000008)},tree2x2={coord=vec3(-491.854034,5478.001953,81.720520),rot=vec3(76.580368,-0.000001,65.000008)},},[95]={tree2x234={coord=vec3(-491.711792,5478.321777,81.776016),rot=vec3(78.119720,-0.000000,95.000000)},tree4x4={coord=vec3(-484.352600,5478.965820,83.220100),rot=vec3(81.291695,-0.000001,95.000000)},tree2x23={coord=vec3(-491.711792,5478.321777,81.776016),rot=vec3(78.119720,-0.000000,95.000000)},tree3x3={coord=vec3(-488.008301,5478.645996,82.558105),rot=vec3(78.119720,-0.000000,95.000000)},tree2x2={coord=vec3(-491.799530,5478.314453,81.757484),rot=vec3(78.119720,-0.000000,95.000000)},},[125]={tree2x234={coord=vec3(-491.835724,5478.662109,81.690384),rot=vec3(82.104507,0.000000,124.999985)},tree4x4={coord=vec3(-485.710571,5482.950684,82.497360),rot=vec3(87.155548,0.000000,124.999985)},tree2x23={coord=vec3(-491.835724,5478.662109,81.690384),rot=vec3(82.104507,0.000001,124.999985)},tree3x3={coord=vec3(-488.753265,5480.820313,82.212242),rot=vec3(82.925537,0.000001,124.999985)},tree2x2={coord=vec3(-491.908752,5478.610840,81.678024),rot=vec3(82.925537,0.000001,124.999985)},},[235]={tree2x234={coord=vec3(-492.982513,5478.662109,81.336449),rot=vec3(107.409668,0.000002,-124.999985)},tree4x4={coord=vec3(-498.882996,5482.793457,78.967773),rot=vec3(111.770905,0.000003,-124.999985)},tree2x23={coord=vec3(-492.982513,5478.662109,81.336449),rot=vec3(107.409660,0.000001,-124.999985)},tree3x3={coord=vec3(-495.951904,5480.741211,80.199783),rot=vec3(107.409653,0.000001,-124.999985)},tree2x2={coord=vec3(-492.912170,5478.612793,81.363380),rot=vec3(107.409653,0.000001,-124.999985)},},[265]={tree2x234={coord=vec3(-493.106445,5478.321777,81.325287),rot=vec3(108.483864,0.000000,-95.000000)},tree4x4={coord=vec3(-500.238800,5478.945313,78.931969),rot=vec3(111.771011,-0.000000,-95.000000)},tree2x23={coord=vec3(-493.106445,5478.321777,81.325287),rot=vec3(108.483856,0.000000,-95.000000)},tree3x3={coord=vec3(-496.695770,5478.635742,80.120865),rot=vec3(108.483849,-0.000000,-95.000000)},tree2x2={coord=vec3(-493.021393,5478.314453,81.353821),rot=vec3(108.483849,-0.000000,-95.000000)},},[295]={tree2x234={coord=vec3(-493.043549,5477.964844,81.400459),rot=vec3(104.250793,-0.000001,-65.000000)},tree4x4={coord=vec3(-499.674683,5474.872559,79.432144),rot=vec3(106.346214,-0.000001,-65.000000)},tree2x23={coord=vec3(-493.043549,5477.964844,81.400459),rot=vec3(104.250793,-0.000001,-65.000000)},tree3x3={coord=vec3(-496.380646,5476.408691,80.465271),rot=vec3(104.250793,-0.000001,-65.000000)},tree2x2={coord=vec3(-492.964478,5478.001465,81.422615),rot=vec3(104.250793,-0.000001,-65.000000)},},[325]={tree2x234={coord=vec3(-492.810608,5477.687500,81.487038),rot=vec3(97.236702,0.000000,-34.999954)},tree4x4={coord=vec3(-497.106049,5471.553223,80.536095),rot=vec3(102.196724,-0.000000,-34.999954)},tree2x23={coord=vec3(-492.810608,5477.687500,81.487038),rot=vec3(97.236702,0.000000,-34.999954)},tree3x3={coord=vec3(-494.972260,5474.600098,81.008484),rot=vec3(97.236702,0.000000,-34.999954)},tree2x2={coord=vec3(-492.759399,5477.760742,81.498375),rot=vec3(97.236702,0.000000,-34.999954)},},}
PLT.Info.cutting.trees[23]={coord=vec3(-516.944580,5479.193359,72.038094),[0]={tree2x234={coord=vec3(-516.944580,5478.493164,72.768784),rot=vec3(82.925545,0.000000,0.000000)},tree4x4={coord=vec3(-516.944580,5471.001465,73.698509),rot=vec3(86.292625,0.000000,0.000000)},tree2x23={coord=vec3(-516.944580,5478.493164,72.768784),rot=vec3(82.925537,0.000000,0.000000)},tree3x3={coord=vec3(-516.944580,5474.723145,73.236664),rot=vec3(82.925537,0.000000,0.000000)},tree2x2={coord=vec3(-516.944580,5478.582520,72.757698),rot=vec3(82.925537,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-516.594604,5478.586914,72.878639),rot=vec3(73.592262,-0.000000,29.999979)},tree4x4={coord=vec3(-512.973816,5472.315430,74.901009),rot=vec3(76.580383,-0.000000,29.999979)},tree2x23={coord=vec3(-516.594604,5478.586914,72.878639),rot=vec3(73.592262,-0.000000,29.999979)},tree3x3={coord=vec3(-514.772461,5475.430664,73.951744),rot=vec3(73.592262,-0.000000,29.999979)},tree2x2={coord=vec3(-516.637817,5478.661621,72.853218),rot=vec3(73.592262,-0.000000,29.999979)},},[60]={tree2x234={coord=vec3(-516.338379,5478.843262,73.185570),rot=vec3(70.720734,-0.000002,60.000000)},tree4x4={coord=vec3(-510.167389,5475.280273,75.678047),rot=vec3(70.720734,-0.000002,60.000000)},tree2x23={coord=vec3(-516.338379,5478.843262,73.030563),rot=vec3(69.313370,-0.000002,60.000000)},tree3x3={coord=vec3(-513.260498,5477.066406,74.372581),rot=vec3(69.313362,-0.000002,60.000000)},tree2x2={coord=vec3(-516.411255,5478.885254,72.948769),rot=vec3(68.620239,-0.000000,60.000000)},},[90]={tree2x234={coord=vec3(-516.244568,5479.193359,73.039047),rot=vec3(70.013496,-0.000000,90.000000)},tree4x4={coord=vec3(-509.150238,5479.193359,75.619286),rot=vec3(70.713631,-0.000000,90.000000)},tree2x23={coord=vec3(-516.244568,5479.193359,72.964043),rot=vec3(69.306427,-0.000000,90.000000)},tree3x3={coord=vec3(-512.690674,5479.193359,74.306496),rot=vec3(69.306427,-0.000000,90.000000)},tree2x2={coord=vec3(-516.328796,5479.193359,72.932236),rot=vec3(69.999474,-0.000000,90.000000)},},[135]={tree2x234={coord=vec3(-516.449585,5479.688477,72.740730),rot=vec3(81.291595,-0.000000,135.000000)},tree4x4={coord=vec3(-511.173187,5484.964844,73.883690),rot=vec3(81.291595,-0.000000,135.000000)},tree2x23={coord=vec3(-516.449585,5479.688477,72.740730),rot=vec3(81.291588,-0.000000,135.000000)},tree3x3={coord=vec3(-513.794250,5482.343750,73.315918),rot=vec3(81.291580,-0.000000,135.000000)},tree2x2={coord=vec3(-516.512512,5479.625488,72.727104),rot=vec3(81.291580,-0.000000,135.000000)},},[165]={tree2x234={coord=vec3(-516.763428,5479.869629,72.581505),rot=vec3(93.442650,-0.000000,164.999924)},tree4x4={coord=vec3(-514.813110,5487.148438,72.078186),rot=vec3(92.508217,-0.000000,164.999924)},tree2x23={coord=vec3(-516.763428,5479.869629,72.581505),rot=vec3(94.377060,-0.000000,164.999924)},tree3x3={coord=vec3(-515.783020,5483.528320,72.291565),rot=vec3(94.377052,-0.000000,164.999924)},tree2x2={coord=vec3(-516.786682,5479.783203,72.588371),rot=vec3(94.377052,-0.000000,164.999924)},},[195]={tree2x234={coord=vec3(-517.125732,5479.869629,72.463707),rot=vec3(105.293449,-0.000000,-164.999924)},tree4x4={coord=vec3(-519.010376,5486.902832,70.472557),rot=vec3(105.293449,-0.000000,-164.999924)},tree2x23={coord=vec3(-517.125732,5479.869629,72.513710),rot=vec3(106.346367,-0.000000,-164.999924)},tree3x3={coord=vec3(-518.069275,5483.390625,71.444504),rot=vec3(106.346359,-0.000000,-164.999924)},tree2x2={coord=vec3(-517.103394,5479.786133,72.484039),rot=vec3(106.346359,-0.000000,-164.999924)},},[225]={tree2x234={coord=vec3(-517.439575,5479.688477,72.335953),rot=vec3(110.664268,-0.000000,-135.000000)},tree4x4={coord=vec3(-522.434143,5484.683105,69.621971),rot=vec3(109.557617,-0.000000,-135.000000)},tree2x23={coord=vec3(-517.439575,5479.688477,72.385956),rot=vec3(111.770897,-0.000000,-135.000000)},tree3x3={coord=vec3(-519.934265,5482.183105,70.976921),rot=vec3(111.770889,-0.000000,-135.000000)},tree2x2={coord=vec3(-517.380432,5479.629395,72.419334),rot=vec3(112.888588,-0.000000,-135.000000)},},[255]={tree2x234={coord=vec3(-517.620728,5479.374512,72.328743),rot=vec3(111.770905,0.000000,-104.999908)},tree4x4={coord=vec3(-524.392456,5481.188965,69.528847),rot=vec3(111.770905,0.000000,-104.999908)},tree2x23={coord=vec3(-517.620728,5479.374512,72.328743),rot=vec3(111.770897,0.000000,-104.999908)},tree3x3={coord=vec3(-521.028564,5480.287598,70.919708),rot=vec3(111.770889,0.000001,-104.999908)},tree2x2={coord=vec3(-517.539978,5479.352539,72.362122),rot=vec3(112.888588,0.000000,-104.999908)},},[285]={tree2x234={coord=vec3(-517.620728,5479.012207,72.460228),rot=vec3(107.356003,-0.000000,-74.999977)},tree4x4={coord=vec3(-524.580566,5477.147461,70.258308),rot=vec3(107.356003,-0.000000,-74.999977)},tree2x23={coord=vec3(-517.620728,5479.012207,72.460228),rot=vec3(107.356003,-0.000000,-74.999977)},tree3x3={coord=vec3(-521.123230,5478.073730,71.276955),rot=vec3(106.282440,-0.000000,-74.999977)},tree2x2={coord=vec3(-517.537781,5479.034180,72.487076),rot=vec3(109.513840,0.000000,-74.999977)},},}
PLT.Info.cutting.trees[24]={coord=vec3(-512.686096,5493.285645,71.517143),[0]={tree2x234={coord=vec3(-512.686096,5492.585449,72.222160),rot=vec3(82.104507,0.000000,0.000000)},tree4x4={coord=vec3(-512.686096,5485.107910,73.259140),rot=vec3(82.104507,0.000000,0.000000)},tree2x23={coord=vec3(-512.686096,5492.585449,72.222160),rot=vec3(82.104507,0.000000,0.000000)},tree3x3={coord=vec3(-512.686096,5488.822266,72.694016),rot=vec3(81.283447,0.000000,0.000000)},tree2x2={coord=vec3(-512.686096,5492.674805,72.209801),rot=vec3(82.925537,0.000000,0.000000)},},[35]={tree2x234={coord=vec3(-512.284607,5492.712402,72.349808),rot=vec3(71.427979,-0.000000,34.999966)},tree4x4={coord=vec3(-508.180145,5486.850586,74.754135),rot=vec3(71.427979,-0.000000,34.999966)},tree2x23={coord=vec3(-512.284607,5492.712402,72.349808),rot=vec3(71.427979,-0.000000,34.999966)},tree3x3={coord=vec3(-510.219055,5489.762695,73.559776),rot=vec3(71.427979,-0.000000,34.999966)},tree2x2={coord=vec3(-512.333496,5492.782227,72.321144),rot=vec3(72.142242,-0.000000,34.999966)},},[65]={tree2x234={coord=vec3(-512.051697,5492.989746,72.443771),rot=vec3(67.927231,-0.000000,65.000008)},tree4x4={coord=vec3(-505.711426,5490.033203,75.280563),rot=vec3(67.927231,-0.000000,65.000008)},tree2x23={coord=vec3(-512.051697,5492.989746,72.443771),rot=vec3(67.927223,-0.000000,65.000008)},tree3x3={coord=vec3(-508.860992,5491.501953,73.871376),rot=vec3(67.927216,-0.000000,65.000008)},tree2x2={coord=vec3(-512.127319,5493.024902,72.409950),rot=vec3(68.606483,-0.000000,65.000008)},},[95]={tree2x234={coord=vec3(-511.988770,5493.346680,72.479218),rot=vec3(69.313431,-0.000000,95.000000)},tree4x4={coord=vec3(-504.953369,5493.961914,75.145943),rot=vec3(69.313431,-0.000000,95.000000)},tree2x23={coord=vec3(-511.988770,5493.346680,72.429214),rot=vec3(69.306496,-0.000000,95.000000)},tree3x3={coord=vec3(-508.448395,5493.656250,73.771660),rot=vec3(69.306496,-0.000000,95.000000)},tree2x2={coord=vec3(-512.072632,5493.339355,72.397408),rot=vec3(69.999542,0.000000,95.000000)},},[135]={tree2x234={coord=vec3(-512.191101,5493.780762,72.335091),rot=vec3(78.900841,-0.000000,135.000000)},tree4x4={coord=vec3(-506.953003,5499.019043,73.788330),rot=vec3(78.900841,-0.000000,135.000000)},tree2x23={coord=vec3(-512.191101,5493.780762,72.285088),rot=vec3(78.111832,-0.000000,135.000000)},tree3x3={coord=vec3(-509.562408,5496.409668,73.017685),rot=vec3(77.330711,-0.000000,135.000000)},tree2x2={coord=vec3(-512.253357,5493.718262,72.266548),rot=vec3(79.681870,-0.000000,135.000000)},},[165]={tree2x234={coord=vec3(-512.504944,5493.961914,72.117035),rot=vec3(88.907501,-0.000000,164.999924)},tree4x4={coord=vec3(-510.551453,5501.252441,72.260971),rot=vec3(88.907501,-0.000000,164.999924)},tree2x23={coord=vec3(-512.504944,5493.961914,72.117035),rot=vec3(88.907494,-0.000000,164.999924)},tree3x3={coord=vec3(-511.521851,5497.630859,72.189468),rot=vec3(88.907486,-0.000000,164.999924)},tree2x2={coord=vec3(-512.528198,5493.875000,72.115318),rot=vec3(89.796570,-0.000000,164.999924)},},[195]={tree2x234={coord=vec3(-512.867249,5493.961914,71.961807),rot=vec3(100.183136,0.000000,-164.999924)},tree4x4={coord=vec3(-514.790283,5501.138672,70.627182),rot=vec3(100.183136,0.000000,-164.999924)},tree2x23={coord=vec3(-512.867249,5493.961914,71.961807),rot=vec3(100.183136,0.000000,-164.999924)},tree3x3={coord=vec3(-513.835022,5497.573730,71.290161),rot=vec3(100.183136,0.000000,-164.999924)},tree2x2={coord=vec3(-512.844360,5493.876465,71.977715),rot=vec3(100.183136,0.000000,-164.999924)},},[225]={tree2x234={coord=vec3(-513.181091,5493.780762,71.844765),rot=vec3(107.409668,-0.000000,-135.000000)},tree4x4={coord=vec3(-518.274475,5498.874512,69.586090),rot=vec3(109.568588,-0.000000,-135.000000)},tree2x23={coord=vec3(-513.181091,5493.780762,71.844765),rot=vec3(107.409660,-0.000000,-135.000000)},tree3x3={coord=vec3(-515.744324,5496.344238,70.598091),rot=vec3(105.272194,-0.000000,-135.000000)},tree2x2={coord=vec3(-513.120361,5493.720215,71.871696),rot=vec3(109.568573,-0.000000,-135.000000)},},[255]={tree2x234={coord=vec3(-513.362244,5493.466797,71.847313),rot=vec3(111.770905,0.000000,-104.999908)},tree4x4={coord=vec3(-520.133972,5495.281250,69.047417),rot=vec3(111.770905,0.000000,-104.999908)},tree2x23={coord=vec3(-513.362244,5493.466797,71.847313),rot=vec3(112.888596,0.000000,-104.999908)},tree3x3={coord=vec3(-516.742859,5494.372559,70.359726),rot=vec3(111.748505,0.000001,-104.999908)},tree2x2={coord=vec3(-513.282166,5493.445313,71.882317),rot=vec3(114.017464,-0.000000,-104.999908)},},[285]={tree2x234={coord=vec3(-513.362244,5493.104492,71.853973),rot=vec3(108.483864,-0.000001,-74.999977)},tree4x4={coord=vec3(-520.277832,5491.251465,69.510658),rot=vec3(108.483864,-0.000001,-74.999977)},tree2x23={coord=vec3(-513.362244,5493.104492,71.903976),rot=vec3(109.568687,-0.000002,-74.999977)},tree3x3={coord=vec3(-516.819824,5492.178223,70.631554),rot=vec3(109.568680,-0.000003,-74.999977)},tree2x2={coord=vec3(-513.280334,5493.126465,71.934120),rot=vec3(110.664375,-0.000002,-74.999977)},},[315]={tree2x234={coord=vec3(-513.181091,5492.790527,72.069641),rot=vec3(101.184807,-0.000000,-45.000000)},tree4x4={coord=vec3(-518.417664,5487.554199,70.605331),rot=vec3(101.184807,-0.000000,-45.000000)},tree2x23={coord=vec3(-513.181091,5492.790527,72.069641),rot=vec3(101.184799,-0.000000,-45.000000)},tree3x3={coord=vec3(-515.816345,5490.155273,71.282730),rot=vec3(100.172935,-0.000000,-45.000000)},tree2x2={coord=vec3(-513.118652,5492.853027,72.087097),rot=vec3(103.218590,-0.000000,-45.000000)},},[345]={tree2x234={coord=vec3(-512.867249,5492.609375,72.180199),rot=vec3(88.027107,-0.000000,-15.000001)},tree4x4={coord=vec3(-514.819946,5485.321777,72.390083),rot=vec3(87.146820,-0.000000,-15.000001)},tree2x23={coord=vec3(-512.867249,5492.609375,72.180199),rot=vec3(88.907364,-0.000000,-15.000000)},tree3x3={coord=vec3(-513.850342,5488.940430,72.252640),rot=vec3(88.907356,-0.000000,-15.000000)},tree2x2={coord=vec3(-512.843994,5492.696289,72.178482),rot=vec3(89.796440,-0.000000,-14.999996)},},}
PLT.Info.cutting.trees[25]={coord=vec3(-514.999329,5532.565918,68.985741),[0]={tree2x234={coord=vec3(-514.999329,5531.865723,69.617302),rot=vec3(89.796448,0.000000,0.000000)},tree4x4={coord=vec3(-514.999329,5524.316895,69.644119),rot=vec3(90.694412,-0.000000,0.000000)},tree2x23={coord=vec3(-514.999329,5531.865723,69.617302),rot=vec3(89.796448,0.000000,0.000000)},tree3x3={coord=vec3(-514.999329,5528.066895,69.630798),rot=vec3(89.796448,0.000000,0.000000)},tree2x2={coord=vec3(-514.999329,5531.955566,69.616982),rot=vec3(90.694412,-0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-514.649353,5531.959473,69.733047),rot=vec3(82.104507,-0.000001,29.999979)},tree4x4={coord=vec3(-510.910675,5525.483398,70.770027),rot=vec3(82.104507,-0.000001,29.999979)},tree2x23={coord=vec3(-514.649353,5531.959473,69.733047),rot=vec3(82.104507,-0.000002,29.999979)},tree3x3={coord=vec3(-512.767883,5528.700684,70.254906),rot=vec3(82.104507,-0.000002,29.999979)},tree2x2={coord=vec3(-514.693909,5532.036621,69.720688),rot=vec3(82.925537,-0.000002,29.999979)},},[60]={tree2x234={coord=vec3(-514.393127,5532.215820,69.721985),rot=vec3(76.580383,-0.000000,60.000000)},tree4x4={coord=vec3(-508.034027,5528.543945,71.418961),rot=vec3(76.580383,-0.000000,60.000000)},tree2x23={coord=vec3(-514.393127,5532.215820,69.721985),rot=vec3(76.580376,-0.000000,60.000000)},tree3x3={coord=vec3(-511.192932,5530.368164,70.603661),rot=vec3(76.580368,-0.000000,60.000000)},tree2x2={coord=vec3(-514.468933,5532.259766,69.751099),rot=vec3(78.119614,-0.000000,60.000000)},},[105]={tree2x234={coord=vec3(-514.323181,5532.747070,69.743233),rot=vec3(76.580383,0.000001,104.999908)},tree4x4={coord=vec3(-507.230499,5534.647949,71.385208),rot=vec3(78.119644,0.000000,104.999908)},tree2x23={coord=vec3(-514.323181,5532.747070,69.743233),rot=vec3(76.580376,0.000001,104.999908)},tree3x3={coord=vec3(-510.753815,5533.703613,70.624908),rot=vec3(76.580368,0.000001,104.999908)},tree2x2={coord=vec3(-514.407776,5532.724609,69.722343),rot=vec3(76.580368,0.000001,104.999908)},},[135]={tree2x234={coord=vec3(-514.504333,5533.061035,69.762077),rot=vec3(82.104507,-0.000000,135.000000)},tree4x4={coord=vec3(-509.216980,5538.348145,70.799057),rot=vec3(82.925545,-0.000000,135.000000)},tree2x23={coord=vec3(-514.504333,5533.061035,69.762077),rot=vec3(82.104507,-0.000000,135.000000)},tree3x3={coord=vec3(-511.843506,5535.721680,70.283936),rot=vec3(82.104507,-0.000000,135.000000)},tree2x2={coord=vec3(-514.567383,5532.998047,69.749718),rot=vec3(82.104507,-0.000000,135.000000)},},[165]={tree2x234={coord=vec3(-514.818176,5533.242188,69.672318),rot=vec3(88.027245,-0.000000,164.999924)},tree4x4={coord=vec3(-512.865479,5540.529785,69.932182),rot=vec3(90.694542,-0.000000,164.999924)},tree2x23={coord=vec3(-514.818176,5533.242188,69.672318),rot=vec3(88.027245,-0.000000,164.999924)},tree3x3={coord=vec3(-513.835510,5536.909668,69.803093),rot=vec3(88.027245,-0.000000,164.999924)},tree2x2={coord=vec3(-514.841431,5533.155273,69.669220),rot=vec3(88.907501,-0.000000,164.999924)},},[195]={tree2x234={coord=vec3(-515.180481,5533.242188,69.536469),rot=vec3(95.320831,0.000000,-164.999924)},tree4x4={coord=vec3(-517.125854,5540.502441,68.836426),rot=vec3(96.274048,0.000000,-164.999924)},tree2x23={coord=vec3(-515.180481,5533.242188,69.536469),rot=vec3(95.320824,-0.000000,-164.999924)},tree3x3={coord=vec3(-516.159485,5536.895996,69.184181),rot=vec3(95.320816,0.000000,-164.999924)},tree2x2={coord=vec3(-515.157288,5533.155762,69.544815),rot=vec3(95.320816,0.000000,-164.999924)},},[225]={tree2x234={coord=vec3(-515.494324,5533.061035,69.475975),rot=vec3(101.184807,-0.000000,-135.000000)},tree4x4={coord=vec3(-520.730896,5538.297363,67.901665),rot=vec3(103.218605,-0.000000,-135.000000)},tree2x23={coord=vec3(-515.494324,5533.061035,69.475975),rot=vec3(101.184799,-0.000000,-135.000000)},tree3x3={coord=vec3(-518.129578,5535.696289,68.739067),rot=vec3(101.184792,-0.000000,-135.000000)},tree2x2={coord=vec3(-515.431885,5532.998535,69.493431),rot=vec3(102.196632,-0.000000,-135.000000)},},[255]={tree2x234={coord=vec3(-515.675476,5532.747070,69.411278),rot=vec3(105.293289,-0.000000,-104.999908)},tree4x4={coord=vec3(-522.709045,5534.631836,67.420151),rot=vec3(108.483757,-0.000000,-104.999908)},tree2x23={coord=vec3(-515.675476,5532.747070,69.411278),rot=vec3(105.293282,-0.000000,-104.999908)},tree3x3={coord=vec3(-519.215088,5533.695313,68.409256),rot=vec3(105.293274,-0.000001,-104.999908)},tree2x2={coord=vec3(-515.591614,5532.724609,69.435013),rot=vec3(105.293274,-0.000001,-104.999908)},},[285]={tree2x234={coord=vec3(-515.675476,5532.384766,69.414726),rot=vec3(105.293396,-0.000001,-74.999977)},tree4x4={coord=vec3(-522.709045,5530.500000,67.313583),rot=vec3(106.346321,-0.000001,-74.999977)},tree2x23={coord=vec3(-515.675476,5532.384766,69.414726),rot=vec3(105.293388,-0.000001,-74.999977)},tree3x3={coord=vec3(-519.215088,5531.436523,68.412697),rot=vec3(105.293381,-0.000001,-74.999977)},tree2x2={coord=vec3(-515.591614,5532.407227,69.438461),rot=vec3(105.293381,-0.000001,-74.999977)},},[315]={tree2x234={coord=vec3(-515.494324,5532.070801,69.512894),rot=vec3(102.196655,-0.000000,-45.000000)},tree4x4={coord=vec3(-520.711731,5526.853027,67.918030),rot=vec3(103.218605,-0.000000,-45.000000)},tree2x23={coord=vec3(-515.494324,5532.070801,69.512894),rot=vec3(102.196655,-0.000000,-45.000000)},tree3x3={coord=vec3(-518.119995,5529.445313,68.710289),rot=vec3(102.196655,-0.000000,-45.000000)},tree2x2={coord=vec3(-515.432129,5532.132813,69.531906),rot=vec3(102.196655,-0.000000,-45.000000)},},[345]={tree2x234={coord=vec3(-515.180481,5531.889648,69.549347),rot=vec3(94.376930,-0.000000,-14.999998)},tree4x4={coord=vec3(-517.128601,5524.619141,68.973228),rot=vec3(94.376930,-0.000000,-14.999998)},tree2x23={coord=vec3(-515.180481,5531.889648,69.549347),rot=vec3(94.376923,-0.000000,-14.999998)},tree3x3={coord=vec3(-516.160889,5528.230957,69.259415),rot=vec3(94.376923,-0.000000,-14.999998)},tree2x2={coord=vec3(-515.157227,5531.976074,69.556213),rot=vec3(94.376923,-0.000000,-14.999998)},},}
PLT.Info.cutting.trees[26]={coord=vec3(-510.797302,5519.427246,70.416412),[0]={tree2x234={coord=vec3(-510.797302,5518.727051,71.080811),rot=vec3(87.155548,0.000000,0.000000)},tree4x4={coord=vec3(-510.797302,5511.187012,71.405418),rot=vec3(86.283989,0.000000,0.000000)},tree2x23={coord=vec3(-510.797302,5518.727051,71.080811),rot=vec3(88.027107,0.000000,0.000000)},tree3x3={coord=vec3(-510.797302,5514.930176,71.211594),rot=vec3(88.027107,0.000000,0.000000)},tree2x2={coord=vec3(-510.797302,5518.816895,71.077713),rot=vec3(88.907364,0.000000,0.000000)},},[35]={tree2x234={coord=vec3(-510.395813,5518.854004,71.202400),rot=vec3(75.822205,0.000001,34.999966)},tree4x4={coord=vec3(-506.197784,5512.858887,72.976379),rot=vec3(75.056450,-0.000002,34.999966)},tree2x23={coord=vec3(-510.395813,5518.854004,71.202400),rot=vec3(76.580421,0.000001,34.999966)},tree3x3={coord=vec3(-508.276306,5515.827148,72.034073),rot=vec3(75.814621,0.000001,34.999966)},tree2x2={coord=vec3(-510.446014,5518.925781,71.181511),rot=vec3(78.119667,-0.000000,34.999966)},},[65]={tree2x234={coord=vec3(-510.162872,5519.131348,71.224510),rot=vec3(72.142212,0.000000,65.000008)},tree4x4={coord=vec3(-503.650787,5516.094727,73.539452),rot=vec3(72.142212,0.000000,65.000008)},tree2x23={coord=vec3(-510.162872,5519.131348,71.224510),rot=vec3(72.142212,0.000000,65.000008)},tree3x3={coord=vec3(-506.885681,5517.603027,72.389496),rot=vec3(72.142212,0.000000,65.000008)},tree2x2={coord=vec3(-510.240509,5519.167480,71.196907),rot=vec3(72.863625,-0.000000,65.000008)},},[95]={tree2x234={coord=vec3(-510.099976,5519.488281,71.226585),rot=vec3(73.592323,-0.000000,95.000000)},tree4x4={coord=vec3(-502.885956,5520.119141,73.358948),rot=vec3(75.822243,-0.000000,95.000000)},tree2x23={coord=vec3(-510.099976,5519.488281,71.226585),rot=vec3(73.592323,-0.000000,95.000000)},tree3x3={coord=vec3(-506.469543,5519.805664,72.299690),rot=vec3(73.592323,-0.000000,95.000000)},tree2x2={coord=vec3(-510.185974,5519.480957,71.201164),rot=vec3(73.592323,-0.000000,95.000000)},},[125]={tree2x234={coord=vec3(-510.223907,5519.828613,71.208961),rot=vec3(78.900841,0.000001,124.999985)},tree4x4={coord=vec3(-504.155792,5524.077637,72.662201),rot=vec3(80.486732,0.000001,124.999985)},tree2x23={coord=vec3(-510.223907,5519.828613,71.208961),rot=vec3(78.900841,0.000001,124.999985)},tree3x3={coord=vec3(-507.170166,5521.966797,71.940300),rot=vec3(78.900841,0.000001,124.999985)},tree2x2={coord=vec3(-510.296234,5519.777832,71.191635),rot=vec3(78.900841,0.000001,124.999985)},},[155]={tree2x234={coord=vec3(-510.501465,5520.061523,71.074638),rot=vec3(85.438248,0.000000,154.999954)},tree4x4={coord=vec3(-507.321259,5526.881836,71.675026),rot=vec3(86.292625,-0.000000,154.999954)},tree2x23={coord=vec3(-510.501465,5520.061523,71.074638),rot=vec3(85.438248,0.000000,154.999954)},tree3x3={coord=vec3(-508.901031,5523.493652,71.376785),rot=vec3(85.438248,0.000000,154.999954)},tree2x2={coord=vec3(-510.539368,5519.979980,71.067482),rot=vec3(85.438248,0.000000,154.999954)},},[185]={tree2x234={coord=vec3(-510.858307,5520.124512,70.973602),rot=vec3(93.442513,0.000001,-174.999893)},tree4x4={coord=vec3(-511.515076,5527.631348,70.520302),rot=vec3(93.442513,0.000001,-174.999893)},tree2x23={coord=vec3(-510.858307,5520.124512,70.973602),rot=vec3(93.442505,0.000001,-174.999893)},tree3x3={coord=vec3(-511.188812,5523.902344,70.745483),rot=vec3(93.442505,0.000001,-174.999893)},tree2x2={coord=vec3(-510.850464,5520.035156,70.979004),rot=vec3(94.376923,0.000001,-174.999893)},},[215]={tree2x234={coord=vec3(-511.198792,5520.000488,70.926254),rot=vec3(101.184914,0.000001,-144.999969)},tree4x4={coord=vec3(-515.446472,5526.066895,69.461929),rot=vec3(101.184914,0.000001,-144.999969)},tree2x23={coord=vec3(-511.198792,5520.000488,70.926254),rot=vec3(101.184914,0.000001,-144.999969)},tree3x3={coord=vec3(-513.336426,5523.053223,70.189339),rot=vec3(101.184914,0.000001,-144.999969)},tree2x2={coord=vec3(-511.148163,5519.928223,70.943710),rot=vec3(103.218704,0.000001,-144.999969)},},[245]={tree2x234={coord=vec3(-511.431732,5519.723145,70.796959),rot=vec3(106.346321,0.000001,-114.999985)},tree4x4={coord=vec3(-517.996826,5522.784668,68.672356),rot=vec3(106.346321,0.000001,-114.999985)},tree2x23={coord=vec3(-511.431732,5519.723145,70.796959),rot=vec3(106.346313,0.000001,-114.999985)},tree3x3={coord=vec3(-514.735596,5521.263672,69.727760),rot=vec3(106.346306,0.000001,-114.999985)},tree2x2={coord=vec3(-511.353455,5519.686523,70.822289),rot=vec3(106.346306,0.000001,-114.999985)},},[275]={tree2x234={coord=vec3(-511.494629,5519.366211,70.780701),rot=vec3(107.409668,-0.000001,-84.999954)},tree4x4={coord=vec3(-518.670349,5518.738281,68.522026),rot=vec3(107.409668,-0.000001,-84.999954)},tree2x23={coord=vec3(-511.494629,5519.366211,70.780701),rot=vec3(107.409660,-0.000001,-84.999954)},tree3x3={coord=vec3(-515.105774,5519.050293,69.644035),rot=vec3(107.409653,-0.000001,-84.999954)},tree2x2={coord=vec3(-511.409088,5519.373535,70.807632),rot=vec3(107.409653,-0.000001,-84.999954)},},[340]={tree2x234={coord=vec3(-511.036713,5518.769531,70.963463),rot=vec3(94.377052,0.000008,-19.999992)},tree4x4={coord=vec3(-513.611084,5511.696289,70.387329),rot=vec3(95.320808,0.000007,-19.999989)},tree2x23={coord=vec3(-511.036713,5518.769531,70.963463),rot=vec3(94.377052,0.000008,-19.999992)},tree3x3={coord=vec3(-512.332275,5515.209961,70.673523),rot=vec3(94.377052,0.000008,-19.999992)},tree2x2={coord=vec3(-511.006042,5518.854004,70.970329),rot=vec3(95.320808,0.000008,-19.999989)},},}
PLT.Info.cutting.trees[27]={coord=vec3(-485.095673,5560.510742,71.547218),[0]={tree2x234={coord=vec3(-485.095673,5559.810547,72.213585),rot=vec3(84.592331,0.000000,0.000000)},tree4x4={coord=vec3(-485.095673,5552.294922,72.925018),rot=vec3(84.592331,0.000000,0.000000)},tree2x23={coord=vec3(-485.095673,5559.810547,72.213585),rot=vec3(84.592323,0.000000,0.000000)},tree3x3={coord=vec3(-485.095673,5556.028320,72.571609),rot=vec3(84.592316,0.000000,0.000000)},tree2x2={coord=vec3(-485.095673,5559.899902,72.205101),rot=vec3(85.438248,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-484.745667,5559.904297,72.224350),rot=vec3(84.592331,-0.000001,29.999979)},tree4x4={coord=vec3(-480.987946,5553.395508,72.935783),rot=vec3(84.592331,-0.000001,29.999979)},tree2x23={coord=vec3(-484.745667,5559.904297,72.224350),rot=vec3(84.592323,-0.000001,29.999979)},tree3x3={coord=vec3(-482.854614,5556.628906,72.582375),rot=vec3(84.592316,-0.000001,29.999979)},tree2x2={coord=vec3(-484.790466,5559.981934,72.215866),rot=vec3(84.592316,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-484.489441,5560.160645,72.295311),rot=vec3(86.258057,-0.000001,60.000000)},tree4x4={coord=vec3(-477.965729,5556.394043,72.812981),rot=vec3(85.395477,-0.000001,60.000000)},tree2x23={coord=vec3(-484.489441,5560.160645,72.245308),rot=vec3(86.249435,-0.000001,60.000000)},tree3x3={coord=vec3(-481.206451,5558.265137,72.493813),rot=vec3(86.249435,-0.000001,60.000000)},tree2x2={coord=vec3(-484.567230,5560.205566,72.239426),rot=vec3(86.249435,-0.000001,60.000000)},},[90]={tree2x234={coord=vec3(-484.395660,5560.510742,72.258110),rot=vec3(86.171707,-0.000000,90.000000)},tree4x4={coord=vec3(-476.863495,5560.510742,72.596977),rot=vec3(81.120811,-0.000000,90.000000)},tree2x23={coord=vec3(-484.395660,5560.510742,72.258110),rot=vec3(87.903748,-0.000000,90.000000)},tree3x3={coord=vec3(-480.599213,5560.510742,72.397072),rot=vec3(87.903748,-0.000000,90.000000)},tree2x2={coord=vec3(-484.485596,5560.510742,72.254814),rot=vec3(89.670601,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-484.489441,5560.860840,72.171516),rot=vec3(89.796539,-0.000000,120.000000)},tree4x4={coord=vec3(-477.951843,5564.634766,72.198318),rot=vec3(90.694496,-0.000000,120.000000)},tree2x23={coord=vec3(-484.489441,5560.860840,72.171516),rot=vec3(89.796532,-0.000000,120.000000)},tree3x3={coord=vec3(-481.199432,5562.760254,72.075005),rot=vec3(88.009575,-0.000000,120.000000)},tree2x2={coord=vec3(-484.567383,5560.815918,72.171196),rot=vec3(91.601418,-0.000000,120.000000)},},[150]={tree2x234={coord=vec3(-484.745667,5561.117188,72.117538),rot=vec3(93.442513,-0.000000,149.999954)},tree4x4={coord=vec3(-480.977966,5567.643555,71.609238),rot=vec3(93.442513,-0.000000,149.999954)},tree2x23={coord=vec3(-484.745667,5561.117188,72.117538),rot=vec3(93.442505,-0.000000,149.999954)},tree3x3={coord=vec3(-482.849579,5564.401367,71.889420),rot=vec3(93.442505,-0.000000,149.999954)},tree2x2={coord=vec3(-484.790588,5561.039551,72.122940),rot=vec3(94.376923,-0.000000,149.999954)},},[180]={tree2x234={coord=vec3(-485.095673,5561.210938,72.081444),rot=vec3(96.273895,-0.000000,-179.999893)},tree4x4={coord=vec3(-485.095673,5568.715332,71.256477),rot=vec3(96.273895,-0.000000,-179.999893)},tree2x23={coord=vec3(-485.095673,5561.210938,72.081444),rot=vec3(96.273888,-0.000000,-179.999893)},tree3x3={coord=vec3(-485.095673,5564.987305,71.666283),rot=vec3(96.273880,-0.000000,-179.999893)},tree2x2={coord=vec3(-485.095673,5561.121582,72.091278),rot=vec3(96.273880,-0.000000,-179.999893)},},[210]={tree2x234={coord=vec3(-485.445679,5561.117188,72.126213),rot=vec3(96.273895,-0.000000,-149.999954)},tree4x4={coord=vec3(-489.197571,5567.615723,71.301247),rot=vec3(96.273895,-0.000000,-149.999954)},tree2x23={coord=vec3(-485.445679,5561.117188,72.126213),rot=vec3(96.273888,-0.000000,-149.999954)},tree3x3={coord=vec3(-487.333801,5564.387695,71.711052),rot=vec3(96.273880,-0.000000,-149.999954)},tree2x2={coord=vec3(-485.400940,5561.039551,72.136047),rot=vec3(97.236610,-0.000000,-149.999954)},},[240]={tree2x234={coord=vec3(-485.701904,5560.860840,72.089661),rot=vec3(95.320694,-0.000002,-119.999985)},tree4x4={coord=vec3(-492.211365,5564.619141,71.389641),rot=vec3(95.320694,-0.000002,-119.999985)},tree2x23={coord=vec3(-485.701904,5560.860840,72.089661),rot=vec3(95.320686,-0.000002,-119.999985)},tree3x3={coord=vec3(-488.977753,5562.751953,71.737381),rot=vec3(95.320679,-0.000002,-119.999985)},tree2x2={coord=vec3(-485.624298,5560.815918,72.098007),rot=vec3(95.320679,-0.000002,-119.999985)},},[295]={tree2x234={coord=vec3(-485.730103,5560.214844,72.177002),rot=vec3(90.694412,-0.000000,-65.000008)},tree4x4={coord=vec3(-492.571350,5557.024414,72.030510),rot=vec3(92.517349,-0.000000,-65.000008)},tree2x23={coord=vec3(-485.730103,5560.214844,72.177002),rot=vec3(90.694412,-0.000000,-65.000008)},tree3x3={coord=vec3(-489.172913,5558.609375,72.130959),rot=vec3(90.694412,-0.000000,-65.000008)},tree2x2={coord=vec3(-485.648529,5560.252930,72.178093),rot=vec3(91.601341,-0.000000,-65.000008)},},[325]={tree2x234={coord=vec3(-485.497162,5559.937500,72.230270),rot=vec3(88.027176,0.000001,-34.999954)},tree4x4={coord=vec3(-489.824524,5553.756836,72.490150),rot=vec3(88.027176,0.000001,-34.999954)},tree2x23={coord=vec3(-485.497162,5559.937500,72.230270),rot=vec3(88.027176,0.000001,-34.999954)},tree3x3={coord=vec3(-487.674896,5556.827148,72.361053),rot=vec3(88.027176,0.000001,-34.999954)},tree2x2={coord=vec3(-485.445557,5560.011230,72.227173),rot=vec3(88.027176,0.000001,-34.999954)},},}
PLT.Info.cutting.trees[28]={coord=vec3(-450.103363,5561.003418,71.654694),[0]={tree2x234={coord=vec3(-450.103363,5560.303223,72.371063),rot=vec3(86.283989,0.000000,0.000000)},tree4x4={coord=vec3(-450.103363,5552.770508,72.860321),rot=vec3(86.283989,0.000000,0.000000)},tree2x23={coord=vec3(-450.103363,5560.303223,72.371063),rot=vec3(86.283989,0.000000,0.000000)},tree3x3={coord=vec3(-450.103363,5556.512207,72.507271),rot=vec3(84.566933,0.000000,0.000000)},tree2x2={coord=vec3(-450.103363,5560.393066,72.365227),rot=vec3(88.018272,0.000000,0.000000)},},[30]={tree2x234={coord=vec3(-449.753357,5560.396973,72.284149),rot=vec3(91.601341,-0.000001,29.999979)},tree4x4={coord=vec3(-445.980347,5553.861816,72.073196),rot=vec3(93.442513,-0.000001,29.999979)},tree2x23={coord=vec3(-449.753357,5560.396973,72.284149),rot=vec3(91.601334,-0.000001,29.999979)},tree3x3={coord=vec3(-447.854614,5557.108398,72.177986),rot=vec3(91.601326,-0.000001,29.999979)},tree2x2={coord=vec3(-449.798340,5560.474609,72.286667),rot=vec3(92.517349,-0.000001,29.999979)},},[60]={tree2x234={coord=vec3(-449.497131,5560.653320,72.221924),rot=vec3(98.209000,-0.000000,60.000000)},tree4x4={coord=vec3(-443.026489,5556.917480,71.034042),rot=vec3(100.182983,-0.000000,60.000000)},tree2x23={coord=vec3(-449.497131,5560.653320,72.221924),rot=vec3(98.209000,-0.000000,60.000000)},tree3x3={coord=vec3(-446.240814,5558.773438,71.679489),rot=vec3(98.209000,-0.000000,60.000000)},tree2x2={coord=vec3(-449.574280,5560.697754,72.234772),rot=vec3(98.209000,-0.000000,60.000000)},},[90]={tree2x234={coord=vec3(-449.403351,5561.003418,72.229866),rot=vec3(103.218605,-0.000000,90.000000)},tree4x4={coord=vec3(-442.054382,5561.003418,70.393654),rot=vec3(105.293289,-0.000000,90.000000)},tree2x23={coord=vec3(-449.403351,5561.003418,72.229866),rot=vec3(103.218597,-0.000000,90.000000)},tree3x3={coord=vec3(-445.705017,5561.003418,71.361160),rot=vec3(103.218590,-0.000000,90.000000)},tree2x2={coord=vec3(-449.490967,5561.003418,72.250443),rot=vec3(103.218590,-0.000000,90.000000)},},[120]={tree2x234={coord=vec3(-449.497131,5561.353516,72.242569),rot=vec3(104.250893,-0.000000,120.000000)},tree4x4={coord=vec3(-443.160706,5565.011719,70.274239),rot=vec3(107.409775,-0.000000,120.000000)},tree2x23={coord=vec3(-449.497131,5561.353516,72.242569),rot=vec3(104.250885,-0.000000,120.000000)},tree3x3={coord=vec3(-446.308350,5563.194336,71.307373),rot=vec3(104.250877,-0.000000,120.000000)},tree2x2={coord=vec3(-449.572662,5561.309570,72.264725),rot=vec3(104.250877,-0.000000,120.000000)},},[160]={tree2x234={coord=vec3(-449.863953,5561.661133,72.188927),rot=vec3(97.236641,0.000001,159.999939)},tree4x4={coord=vec3(-447.302582,5568.698242,71.127991),rot=vec3(103.218605,-0.000000,159.999939)},tree2x23={coord=vec3(-449.863953,5561.661133,72.188927),rot=vec3(97.236641,0.000000,159.999939)},tree3x3={coord=vec3(-448.574951,5565.202637,71.710373),rot=vec3(97.236641,0.000000,159.999939)},tree2x2={coord=vec3(-449.894501,5561.577148,72.200264),rot=vec3(97.236641,0.000000,159.999939)},},[190]={tree2x234={coord=vec3(-450.224915,5561.692871,72.241516),rot=vec3(93.442513,-0.000000,-169.999908)},tree4x4={coord=vec3(-451.533447,5569.113770,71.788216),rot=vec3(94.376930,-0.000000,-169.999908)},tree2x23={coord=vec3(-450.224915,5561.692871,72.241516),rot=vec3(93.442505,-0.000000,-169.999908)},tree3x3={coord=vec3(-450.883423,5565.427246,72.013397),rot=vec3(93.442505,-0.000000,-169.999908)},tree2x2={coord=vec3(-450.209320,5561.604492,72.246918),rot=vec3(93.442505,-0.000000,-169.999908)},},[220]={tree2x234={coord=vec3(-450.553314,5561.539551,72.306747),rot=vec3(90.694412,-0.000001,-139.999985)},tree4x4={coord=vec3(-455.405365,5567.321777,72.160255),rot=vec3(92.517349,-0.000001,-139.999985)},tree2x23={coord=vec3(-450.553314,5561.539551,72.306747),rot=vec3(90.694412,-0.000001,-139.999985)},tree3x3={coord=vec3(-452.995087,5564.449707,72.260704),rot=vec3(90.694412,-0.000001,-139.999985)},tree2x2={coord=vec3(-450.495453,5561.470703,72.307838),rot=vec3(91.601341,-0.000001,-139.999985)},},[250]={tree2x234={coord=vec3(-450.761139,5561.242676,72.361732),rot=vec3(88.907364,0.000001,-109.999962)},tree4x4={coord=vec3(-457.853607,5563.824219,72.450684),rot=vec3(91.601341,0.000001,-109.999962)},tree2x23={coord=vec3(-450.761139,5561.242676,72.361732),rot=vec3(88.907356,0.000001,-109.999962)},tree3x3={coord=vec3(-454.330383,5562.541992,72.434174),rot=vec3(88.907349,0.000001,-109.999962)},tree2x2={coord=vec3(-450.676575,5561.211914,72.360016),rot=vec3(89.796417,0.000001,-109.999962)},},[280]={tree2x234={coord=vec3(-450.792725,5560.881836,72.333023),rot=vec3(86.292725,0.000000,-79.999969)},tree4x4={coord=vec3(-458.211487,5559.573730,72.821129),rot=vec3(87.155647,0.000000,-79.999969)},tree2x23={coord=vec3(-450.792725,5560.881836,72.333023),rot=vec3(86.292725,0.000000,-79.999969)},tree3x3={coord=vec3(-454.526184,5560.223633,72.578659),rot=vec3(86.292725,0.000000,-79.999969)},tree2x2={coord=vec3(-450.704285,5560.897461,72.327209),rot=vec3(86.292725,0.000000,-79.999969)},},[310]={tree2x234={coord=vec3(-450.639587,5560.553223,72.340981),rot=vec3(84.592331,-0.000000,-49.999832)},tree4x4={coord=vec3(-456.396698,5555.722168,73.002411),rot=vec3(83.746399,-0.000001,-49.999836)},tree2x23={coord=vec3(-450.639587,5560.553223,72.340981),rot=vec3(85.438248,-0.000000,-49.999828)},tree3x3={coord=vec3(-453.540558,5558.119141,72.643127),rot=vec3(85.438248,-0.000000,-49.999828)},tree2x2={coord=vec3(-450.570862,5560.610840,72.333824),rot=vec3(85.438248,-0.000000,-49.999828)},},[340]={tree2x234={coord=vec3(-450.342773,5560.345703,72.393631),rot=vec3(85.318665,0.000007,-19.999992)},tree4x4={coord=vec3(-452.916077,5553.275391,73.059738),rot=vec3(85.318665,0.000007,-19.999992)},tree2x23={coord=vec3(-450.342773,5560.345703,72.393631),rot=vec3(85.318665,0.000007,-19.999992)},tree3x3={coord=vec3(-451.637787,5556.787598,72.653679),rot=vec3(84.465477,0.000007,-19.999992)},tree2x2={coord=vec3(-450.312103,5560.430176,72.386284),rot=vec3(87.033554,0.000007,-19.999992)},},}
--PLT.Info.cutting.trees[29]={coord=vec3(-500.235016, 5049.307129, 139.756302),[0]={tree2x234={coord=vec3(-500.235016, 5048.606934, 140.646957),rot=vec3(72.863625, 0.000000, 0.000000)},tree4x4={coord=vec3(-500.235016, 5041.393066, 142.701248),rot=vec3(78.119644, 0.000000, 0.000000)},tree2x23={coord=vec3(-500.235016, 5048.606934, 140.596954),rot=vec3(72.134987, 0.000000, 0.000000)},tree3x3={coord=vec3(-500.235016, 5044.991211, 141.762390),rot=vec3(74.320724, 0.000000, 0.000000)},tree2x2={coord=vec3(-500.235016, 5048.692383, 140.569351),rot=vec3(72.856323, 0.000000, 0.000000)},},[30]={tree2x234={coord=vec3(-499.885010, 5048.700684, 140.672302),rot=vec3(67.247971, -0.000000, 29.999979)},tree4x4={coord=vec3(-496.404205, 5042.671387, 143.591827),rot=vec3(67.247971, -0.000000, 29.999979)},tree2x23={coord=vec3(-499.885010, 5048.700684, 140.672302),rot=vec3(67.247971, -0.000000, 29.999979)},tree3x3={coord=vec3(-498.133301, 5045.666504, 142.141541),rot=vec3(67.247971, -0.000000, 29.999979)},tree2x2={coord=vec3(-499.926514, 5048.772461, 140.637497),rot=vec3(67.920433, 0.000002, 29.999979)},},[60]={tree2x234={coord=vec3(-499.628784, 5048.957031, 140.809937),rot=vec3(70.720734, -0.000002, 60.000000)},tree4x4={coord=vec3(-493.457794, 5045.394043, 143.302429),rot=vec3(73.592262, -0.000000, 60.000000)},tree2x23={coord=vec3(-499.628784, 5048.957031, 140.641190),rot=vec3(68.613373, 0.000001, 60.000000)},tree3x3={coord=vec3(-496.565308, 5047.188477, 142.026535),rot=vec3(69.299507, 0.000001, 60.000000)},tree2x2={coord=vec3(-499.701355, 5048.999023, 140.608368),rot=vec3(69.992493, 0.000002, 60.000000)},},[90]={tree2x234={coord=vec3(-499.535004, 5049.307129, 140.529053),rot=vec3(78.119644, -0.000000, 90.000000)},tree4x4={coord=vec3(-492.147705, 5049.307129, 141.973160),rot=vec3(82.104507, -0.000000, 90.000000)},tree2x23={coord=vec3(-499.535004, 5049.307129, 140.529053),rot=vec3(78.119644, -0.000000, 90.000000)},tree3x3={coord=vec3(-495.817383, 5049.307129, 141.311142),rot=vec3(78.119644, -0.000000, 90.000000)},tree2x2={coord=vec3(-499.623077, 5049.307129, 140.510513),rot=vec3(78.119644, -0.000000, 90.000000)},},[120]={tree2x234={coord=vec3(-499.628784, 5049.657227, 140.525391),rot=vec3(92.517448, -0.000000, 120.000000)},tree4x4={coord=vec3(-493.097443, 5053.427734, 139.963806),rot=vec3(96.274010, -0.000000, 120.000000)},tree2x23={coord=vec3(-499.628784, 5049.657227, 140.525391),rot=vec3(92.517441, -0.000000, 120.000000)},tree3x3={coord=vec3(-496.341919, 5051.554688, 140.358521),rot=vec3(92.517433, -0.000000, 120.000000)},tree2x2={coord=vec3(-499.706665, 5049.612305, 140.479340),rot=vec3(91.592255, -0.000000, 120.000000)},},[150]={tree2x234={coord=vec3(-499.885010, 5049.913574, 140.429260),rot=vec3(106.346214, -0.000000, 149.999954)},tree4x4={coord=vec3(-496.263092, 5056.187012, 138.194672),rot=vec3(109.568588, -0.000000, 149.999954)},tree2x23={coord=vec3(-499.885010, 5049.913574, 140.429260),rot=vec3(106.346207, 0.000000, 149.999954)},tree3x3={coord=vec3(-498.062286, 5053.070801, 139.360062),rot=vec3(106.346199, 0.000000, 149.999954)},tree2x2={coord=vec3(-499.928192, 5049.838867, 140.454590),rot=vec3(106.346199, 0.000000, 149.999954)},},[180]={tree2x234={coord=vec3(-500.235016, 5050.007324, 140.381561),rot=vec3(115.157654, -0.000000, -179.999893)},tree4x4={coord=vec3(-500.235016, 5056.839844, 137.172409),rot=vec3(118.647003, -0.000000, -179.999893)},tree2x23={coord=vec3(-500.235016, 5050.007324, 140.381561),rot=vec3(115.157654, -0.000000, -179.999893)},tree3x3={coord=vec3(-500.235016, 5053.445801, 138.766571),rot=vec3(115.157654, -0.000000, -179.999893)},tree2x2={coord=vec3(-500.235016, 5049.925781, 140.419830),rot=vec3(115.157654, -0.000000, -179.999893)},},[210]={tree2x234={coord=vec3(-500.585022, 5049.913574, 140.416306),rot=vec3(118.647003, -0.000000, -149.999954)},tree4x4={coord=vec3(-503.897491, 5055.650879, 136.797211),rot=vec3(118.647003, -0.000000, -149.999954)},tree2x23={coord=vec3(-500.585022, 5049.913574, 140.416306),rot=vec3(118.646988, -0.000000, -149.999954)},tree3x3={coord=vec3(-502.252014, 5052.800781, 138.595016),rot=vec3(118.646980, -0.000000, -149.999954)},tree2x2={coord=vec3(-500.545532, 5049.845215, 140.459457),rot=vec3(118.646980, -0.000000, -149.999954)},},[240]={tree2x234={coord=vec3(-500.841248, 5049.657227, 140.308609),rot=vec3(116.309212, -0.000001, -119.999985)},tree4x4={coord=vec3(-506.701660, 5053.041016, 136.962769),rot=vec3(116.309212, -0.000001, -119.999985)},tree2x23={coord=vec3(-500.841248, 5049.657227, 140.308609),rot=vec3(116.309212, -0.000001, -119.999985)},tree3x3={coord=vec3(-503.790466, 5051.359863, 138.624832),rot=vec3(116.309212, -0.000001, -119.999985)},tree2x2={coord=vec3(-500.771393, 5049.616699, 140.398499),rot=vec3(116.309212, -0.000001, -119.999985)},},[270]={tree2x234={coord=vec3(-500.935028, 5049.307129, 140.339554),rot=vec3(110.664268, 0.000000, -90.000000)},tree4x4={coord=vec3(-507.998352, 5049.307129, 137.675568),rot=vec3(112.888603, 0.000000, -90.000000)},tree2x23={coord=vec3(-500.935028, 5049.307129, 140.339554),rot=vec3(110.664261, 0.000000, -90.000000)},tree3x3={coord=vec3(-504.489624, 5049.307129, 138.998917),rot=vec3(110.664253, 0.000000, -90.000000)},tree2x2={coord=vec3(-500.850800, 5049.307129, 140.371323),rot=vec3(110.664253, 0.000000, -90.000000)},},[300]={tree2x234={coord=vec3(-500.841248, 5048.957031, 140.465698),rot=vec3(101.184807, -0.000000, -60.000000)},tree4x4={coord=vec3(-507.254700, 5045.254883, 138.891388),rot=vec3(103.218605, -0.000000, -60.000000)},tree2x23={coord=vec3(-500.841248, 5048.957031, 140.465698),rot=vec3(101.184799, -0.000000, -60.000000)},tree3x3={coord=vec3(-504.068787, 5047.093750, 139.728790),rot=vec3(101.184792, -0.000000, -60.000000)},tree2x2={coord=vec3(-500.764801, 5049.000977, 140.483154),rot=vec3(101.184792, -0.000000, -60.000000)},},[330]={tree2x234={coord=vec3(-500.585022, 5048.700684, 140.554291),rot=vec3(87.155548, 0.000001, -29.999979)},tree4x4={coord=vec3(-504.354858, 5042.170898, 140.818909),rot=vec3(89.796448, 0.000001, -29.999979)},tree2x23={coord=vec3(-500.585022, 5048.700684, 140.554291),rot=vec3(87.155548, 0.000001, -29.999979)},tree3x3={coord=vec3(-502.482178, 5045.414551, 140.742813),rot=vec3(87.155548, 0.000001, -29.999979)},tree2x2={coord=vec3(-500.540070, 5048.778320, 140.549820),rot=vec3(87.155548, 0.000001, -29.999979)},},}