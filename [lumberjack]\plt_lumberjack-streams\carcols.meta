<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<CVehicleModelInfoVarGlobal>
  <Lights>
    <Item>
      <id value="1005"/>
      <indicator>
        <intensity value="0.37500000"/>
        <falloffMax value="2.50000000"/>
        <falloffExponent value="8.00000000"/>
        <innerConeAngle value="30.00000000"/>
        <outerConeAngle value="80.00000000"/>
        <emmissiveBoost value="false"/>
        <color value="0xFFFF8000"/>
        <textureName/>
        <mirrorTexture value="true"/>
      </indicator>
      <rearIndicatorCorona>
        <size value="0.00000000"/>
        <size_far value="0.00000000"/>
        <intensity value="0.00000000"/>
        <intensity_far value="0.00000000"/>
        <color value="0x00000000"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="128"/>
        <distBetweenCoronas_far value="255"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </rearIndicatorCorona>
      <frontIndicatorCorona>
        <size value="0.00000000"/>
        <size_far value="0.00000000"/>
        <intensity value="0.00000000"/>
        <intensity_far value="0.00000000"/>
        <color value="0x00000000"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="128"/>
        <distBetweenCoronas_far value="255"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </frontIndicatorCorona>
      <tailLight>
        <intensity value="0.25000000"/>
        <falloffMax value="4.00000000"/>
        <falloffExponent value="16.00000000"/>
        <innerConeAngle value="45.00000000"/>
        <outerConeAngle value="90.00000000"/>
        <emmissiveBoost value="false"/>
        <color value="0xFFFF0000"/>
        <textureName/>
        <mirrorTexture value="true"/>
      </tailLight>
      <tailLightCorona>
        <size value="1.20000000"/>
        <size_far value="3.00000000"/>
        <intensity value="4.00000000"/>
        <intensity_far value="1.00000000"/>
        <color value="0xFFFF0F05"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="128"/>
        <distBetweenCoronas_far value="255"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </tailLightCorona>
      <tailLightMiddleCorona>
        <size value="0.00000000"/>
        <size_far value="0.00000000"/>
        <intensity value="0.00000000"/>
        <intensity_far value="0.00000000"/>
        <color value="0x00000000"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="128"/>
        <distBetweenCoronas_far value="255"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </tailLightMiddleCorona>
      <headLight>
        <intensity value="1.00000000"/>
        <falloffMax value="35.00000000"/>
        <falloffExponent value="16.00000000"/>
        <innerConeAngle value="0.00000000"/>
        <outerConeAngle value="60.21000000"/>
        <emmissiveBoost value="false"/>
        <color value="0xFFFAECC8"/>
        <textureName>VehicleLight_car_utility</textureName>
        <mirrorTexture value="false"/>
      </headLight>
      <headLightCorona>
        <size value="0.10000000"/>
        <size_far value="10.00000000"/>
        <intensity value="5.00000000"/>
        <intensity_far value="1.00000000"/>
        <color value="0xFFFAD6AC"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="128"/>
        <distBetweenCoronas_far value="255"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </headLightCorona>
      <reversingLight>
        <intensity value="0.50000000"/>
        <falloffMax value="4.00000000"/>
        <falloffExponent value="32.00000000"/>
        <innerConeAngle value="45.00000000"/>
        <outerConeAngle value="90.00000000"/>
        <emmissiveBoost value="false"/>
        <color value="0xFFFFFFFF"/>
        <textureName/>
        <mirrorTexture value="true"/>
      </reversingLight>
      <reversingLightCorona>
        <size value="0.80000000"/>
        <size_far value="2.00000000"/>
        <intensity value="1.50000000"/>
        <intensity_far value="1.00000000"/>
        <color value="0x00F7F7F7"/>
        <numCoronas value="1"/>
        <distBetweenCoronas value="128"/>
        <distBetweenCoronas_far value="255"/>
        <xRotation value="0.00000000"/>
        <yRotation value="0.00000000"/>
        <zRotation value="0.00000000"/>
        <zBias value="0.25000000"/>
        <pullCoronaIn value="false"/>
      </reversingLightCorona>
      <name>util</name>
    </Item>
  </Lights>
  <Sirens>
  <Item>
      <id value="1005"/>
      <name>orange_globes</name>
      <timeMultiplier value="0.50000000"/>
      <lightFalloffMax value="10.00000000"/>
      <lightFalloffExponent value="16.00000000"/>
      <lightInnerConeAngle value="0.00000000"/>
      <lightOuterConeAngle value="50.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="150"/>
      <leftHeadLight>
        <sequencer value="0"/>
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0"/>
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="0"/>
      </leftTailLight>
      <rightTailLight>
        <sequencer value="0"/>
      </rightTailLight>
      <leftHeadLightMultiples value="4"/>
      <rightHeadLightMultiples value="4"/>
      <leftTailLightMultiples value="4"/>
      <rightTailLightMultiples value="4"/>
      <useRealLights value="true"/>
      <sirens>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="9.90000000"/>
            <sequencer value="4294967295"/>
            <multiples value="4"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="99.99000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF4800"/>
          <intensity value="5.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="2.48054900"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="9.90000000"/>
            <sequencer value="4294967295"/>
            <multiples value="4"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF4800"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.52479600"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="9.90000000"/>
            <sequencer value="4294967295"/>
            <multiples value="4"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF4800"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="3.63553400"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="5.70681000"/>
            <speed value="9.90000000"/>
            <sequencer value="4294967295"/>
            <multiples value="4"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.10000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF4800"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="3.54648400"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="9.90000000"/>
            <sequencer value="4294967295"/>
            <multiples value="4"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="10.00000000"/>
            <size value="0.30000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF4800"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="9.90000000"/>
            <sequencer value="4294967295"/>
            <multiples value="4"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="10.00000000"/>
            <size value="0.30000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF4800"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="9.90000000"/>
            <sequencer value="4294967295"/>
            <multiples value="4"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="10.00000000"/>
            <size value="0.30000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF4800"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="0.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="5.75958500"/>
            <speed value="9.90000000"/>
            <sequencer value="4294967295"/>
            <multiples value="4"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="10.00000000"/>
            <size value="0.30000000"/>
            <pull value="0.20000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFF4800"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="true"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="1.00000000"/>
            <sequencer value="0"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="1.00000000"/>
            <size value="0.10000000"/>
            <pull value="0.10000000"/>
            <faceCamera value="true"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="false"/>
          <scaleFactor value="0"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
      </sirens>
    </Item>
  </Sirens>
  <GlobalVariationData>
  </GlobalVariationData>
</CVehicleModelInfoVarGlobal>