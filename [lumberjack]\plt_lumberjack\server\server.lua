ESX = exports["es_extended"]:getSharedObject()

function PltAddMoney(src, money, stage)
    local xPlayer = ESX.GetPlayerFromId(src)

    local lumberyardItems = {
        "lumber",
        "plywood",
        "wood_chips",
        "sawdust",
        "timber",
        "coca",
        "agave"
    }

    local randomcount = math.floor((money / 5000) + 0.5)
    local adjustment = math.random(1, 4)
    local giveCount = randomcount + adjustment

    for _, item in ipairs(lumberyardItems) do
        xPlayer.addInventoryItem(item, giveCount)
    end

    if xPlayer then
      xPlayer.addAccountMoney('bank', money)
      exports["gksphone"]:BankSaveHistory(src, 2, money, "Lumber Job Earnings")
      local data = {
        ['Player'] = src,
        ['Log'] = 'Lumber Job', 
        ['Title'] = 'Lumber Job Payments',
        ['Message'] = GetPlayerName(src)..' Earned '..money, 
        ['Color'] = 'green', 
    }
    TriggerEvent('Melx_Core:ZLog', data,"https://discord.com/api/webhooks/1236976741516316673/qmXe3q82y9wGNX70DDr5oqjNPfkb6XnudjMOfamEQ-lbIlBOFf-bhzyA-t5ZiSdOA0ak")
      --xPlayer.addMoney(money)
    else
      print(src, "Couldn't get paid for logging out of the game or something went wrong:"..money)
      local data = {
        ['Player'] = src,
        ['Log'] = 'Lumber Job', 
        ['Title'] = 'Lumber Job Payments Logout',
        ['Message'] = GetPlayerName(src)..' Earned '..money, 
        ['Color'] = 'green', 
    }
    TriggerEvent('Melx_Core:ZLog', data,"https://discord.com/api/webhooks/1236976741516316673/qmXe3q82y9wGNX70DDr5oqjNPfkb6XnudjMOfamEQ-lbIlBOFf-bhzyA-t5ZiSdOA0ak")
    end
end