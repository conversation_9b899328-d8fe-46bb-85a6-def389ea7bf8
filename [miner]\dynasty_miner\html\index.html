<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="style.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js" type="text/javascript"></script>
  <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
  <script src="https://kit.fontawesome.com/cf5621f688.js" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.2/anime.min.js"></script>
  <title>Document</title>
</head>

<body>



  <div id="notification-container">
    <i id="notification-icon"></i>
    <div id="notification-text"></div>
    <div id="notification-progress"></div>
  </div>
  <div id="hint-container">
    <i id="hint-icon"></i>
    <div id="hint-text"></div>
  </div>
  <div id="help-container">
    <i id="help-icon"></i>
    <div id="help-text"></div>
  </div>
  <!-- <div id="wallet-container">
    <div id="key-text"></div>
    <div id="level-text"></div>
    <div id="experience-container">
      <div id="experience-bar"></div>
    </div>
    <div id="total-text"></div>
    <div id="money-text"></div>
    <div id="mineral-list"></div>
  </div> -->
  <div class="quest-container">
    <div id="questMenu" style="display: none;">
      <div id="questName"></div>
      <div id="questDescription"></div> <!-- Descripción de la misión -->
      <div id="mineralList"></div>
      <div id="questFooter">
        <div id="questPayment">
          <i class="fa-solid fa-sack-dollar"></i>
          <span id="paymentAmount"></span>
        </div>
        <div id="questExperience">
          <i class="fa-solid fa-angles-up"></i>
          <span id="experienceAmount"></span>
        </div>
      </div>
    </div>
  </div>
  <div class="level-menu">
    <div id="levelContainer" style="display: none;">
      <div id = "menuKey">Show menu (N)</div>
      <div id="statsContainer">
          <div id="statsTitle">Player stats</div>
          <div id="itemDropRateBonusText" class="itemDropRateBonusContainer">
          </div>
      </div>
      <div id="levelCircle"></div>
      <div id="experienceBarContainer">
        <div id="experienceText"></div>
        <div id="experienceBar"></div>
      </div>
    </div>
  </div>



  <div id="menuContainer" style="display: none;">
    <div class="menu-container" id="party-menu">
      <div id="groupListContainer">
        <h2 id="group-title"><i class="fas fa-users"></i>Your Group</h2>
        <ul id="groupList"></ul>
        <button id="startMissionButton">Start quest</button>
      </div>
      <div id="playerListContainer">
        <h2><i class="fas fa-user-friends"></i> Nearby Players</h2>
        <ul id="playerList"></ul>
        <button id="closeMenuButton">Close Menu</button>
      </div>
    </div>
  </div>


  <div id="waitingMenu" style="display: none;">
    <div id="waitingMenuContent">
      <h2 id="waitingMenuTitle"></h2>
      <div id="waitingQuestInfo">
        <span id="waitingQuestName"></span>
        <div id="memberIcons"></div> <!-- Contenedor para los íconos -->
        <div id="memberCount"></div>
      </div>
      <div id="bottomContainer">
        <div id="progressContainer">
          <div id="progressRing">
            <button id="leavePartyButton">
              <div id="menu-key"></div>
            </button>
          </div>
        </div>
        <div id="leavePartyText"></div>
      </div>
      <div id="waitingFooter">
        <div id="waitingQuestPayment"></div>
        <div id="waitingQuestExperience"></div>
      </div>
    </div>
  </div>

  <div class="notify-system">
    <div class="notify-container"></div>
  </div>
  <script src="script.js"></script>
</body>

</html>