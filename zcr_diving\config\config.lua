Config                    = {}

Config.Framework          = 'esx'           -- esx, oldesx, qb, oldqb

Config.CurrencyUnit       = '$'            -- '€' -- '₺'  '$'
Config.SQL = "oxmysql" -- oxmysql / mysql-async / ghmattimysql
Config.Inventory = "ox_inventory" -- qb_inventory / esx_inventory / ox_inventory / qs_inventory / codem-inventory
Config.ServeName          = "ZION CITY" -- Server Name MAX 10
Config.MoneyType          = "$" -- Money Type 
Config.InteractionHandler = 'ox-target'     --  qb-target, drawtext,ox-target
Config.ExampleProfilePicture = "https://r2.fivemanage.com/biv23I9cFWICSObhZsr4C/LogoNEW.png"
Config.AnchorCommand = "anchor" -- Anchor Command
Config.JobResetCommand = "jobreset" -- Job Reset Command
Config.Tube = {
    ['level1'] = {
        waitTime = 3, -- 3 seconds
        model = `xm_prop_x17_scuba_tank`
    },
    ['level2'] = {
        waitTime = 5,
        model = `p_michael_scuba_tank_s`
    },
    ['level3'] = {
        waitTime = 7,
        model = `p_s_scuba_tank_s`
    },
    ['mask'] = {
        model = `p_d_scuba_mask_s`
    }
}

Config.Diving = {
    ['coords'] = {
        ['intreactionCoords'] = vector3(-273.87, 6641.97, 7.39),
        ['ped'] = true,
        ['pedCoords'] = vector3(-273.87, 6641.97, 7.39),
        ['pedHeading'] = 232.23,
        ['pedHash'] = 0x49EA5685,
    },
    ['seelCoords'] = {
        ['intreactionCoords'] = vector3(-279.59, 6636.75, -27.55),
        ['ped'] = true,
        ['pedCoords'] = vector3(-279.59, 6636.75, 7.55),
        ['pedHeading'] = 221.69,
        ['pedHash'] = 0x49EA5685,
    },
    ['job'] = 'all', 
    ['blip'] = {
        show = true,
        blipName = 'Diving Job',
        blipType = 317,
        blipColor = 3,
        blipScale = 0.55
    },
    ['marker'] = {
        ['type'] = 1,
        ['size'] = vector3(1.5, 1.5, 1.5),
        ['color'] = { r = 0, g = 255, b = 0 },
    },
    ['drawtext'] = {
        ['text'] = "Press ~g~[E]~s~ to open the ~g~Diving~s~",
    },
    ['jobVehicle'] = 'tug', -- tug
    ['jobDeliverTheVehicleCoords'] = vector3(-849.16, 6592.56, 0.55),
    ['Mission'] = { 
        {
            regionName = "mission1",
            regionLabel = "Coralwyn Isle",
            regionInfo = "You need to immediately collect overgrown corals in certain areas with diving equipment and tools and clean up the garbage. You must start now!",
            regionStep = "The team will acquire the boat and travel to the designated area.",
            regionStep2 = "The team will collect corals in the area, clean up debris in the sea, and gather hidden items.",
            minLevel = 0,
            jobTask = {
                {name = "seaChanged", img = "coral.png", label = "Take Coral", amount = 20, areaDistance = 30, coords = vector3(-929.52, 6666.6, -27.55) },
                {name = "seaClean", img = "trash.png", label = "Sea Clean", amount = 20, areaDistance = 30,  coords = vector3(-929.52, 6666.6, -27.55)},
                {name = "sealooting", label = "Sea Box", amount = 15, areaDistance = 40,  coords = vector3(-929.52, 6666.6, -27.55)},
                {name = "seaSuitCase", label = "Sea SuitCase", amount = 10, areaDistance = 30,  coords = vector3(-929.52, 6666.6, -27.55)}, 
            },
            regionPNG = "https://r2.fivemanage.com/biv23I9cFWICSObhZsr4C/Mask_group1.png",
            awards = {
                money = 30000,
                xp = 1000,
                onlinejobextra = 1 -- how many percent of the money will increase when a player enters onlinejob 
            },
            vehicleSpawnCoords = {
                vector4(-306.97, 6678.08, 0.62, 33.31)
            },
            jobDeliverTheVehicleCoords = vector3(-306.97, 6678.08, 1),
            boxModel = {
                bigbox = {
                    model = {"ba_prop_battle_crate_closed_bc", "ba_prop_battle_crate_closed_bc"},
                    random = 50,
                    liftHeight = 2.9,
                    riseSpeed = 0.02,
                },
                smallbox = {
                    model = {"gr_prop_gr_rsply_crate01a", "gr_prop_gr_rsply_crate01a"},
                    random = 50,
                    liftHeight = 1.8,
                    riseSpeed = 0.06,
                }
            },
            ChangeModel = {
                ['BeforeModel'] = {"prop_coral_pillar_01"},
                ['AfterModel'] = {"prop_coral_01"},
            },
            CleanModel = {
                {"prop_rub_tyre_03", "prop_rub_tyre_01", "prop_rub_binbag_sd_02"}
            },
            SuitCaseModel = {
                ['BeforeModel'] = {"prop_suitcase_01d" },
                ['AfterModel'] = {"hei_p_attache_case_01b_s"},
            },
        },    
        {
            regionName = "mission2",
            regionLabel = "Serpentrock",
            regionInfo = "You need to immediately collect overgrown corals in certain areas with diving equipment and tools and clean up the garbage. You must start now!",
            regionStep = "The team will acquire the boat and travel to the designated area.",
            regionStep2 = "The team will collect corals in the area, clean up debris in the sea, and gather hidden items.",
            minLevel = 7,
            jobTask = {
                {name = "seaChanged", img = "coral.png", label = "Take Coral", amount = 15, areaDistance = 30, coords = vector3(1798.72, -2966.86, -43.23) },
                {name = "seaClean", img = "trash.png", label = "Sea Clean", amount = 15, areaDistance = 30,  coords = vector3(1798.72, -2966.86, -43.23)},
                {name = "seaSuitCase", label = "Sea SuitCase", amount = 15, areaDistance = 30,  coords = vector3(1798.72, -2966.86, -43.23)}, 
                -- {name = "sealooting", label = "Sea Box", amount = 15, areaDistance = 40,  coords = vector3(1798.72, -2966.86, -43.23)},
                
            },
            regionPNG = "https://r2.fivemanage.com/biv23I9cFWICSObhZsr4C/Mask_group1.png",
            awards = {
                money = 58000,
                xp = 2000,
                onlinejobextra = 1 -- how many percent of the money will increase when a player enters onlinejob 
            },
            vehicleSpawnCoords = {
                vector4(1319.17, -3078.18, 0.37, 10.11),
            },
            jobDeliverTheVehicleCoords = vector3(1320.3, -3071.16, -0.05),
            boxModel = {
                bigbox = {
                    model = {"ba_prop_battle_crate_closed_bc", "ba_prop_battle_crate_closed_bc"},
                    random = 50,
                    liftHeight = 2.9,
                    riseSpeed = 0.02,
                },
                smallbox = {
                    model = {"gr_prop_gr_rsply_crate01a", "gr_prop_gr_rsply_crate01a"},
                    random = 50,
                    liftHeight = 1.8,
                    riseSpeed = 0.06,
                }
            },
            ChangeModel = {
                ['BeforeModel'] = {"prop_coral_pillar_01"},
                ['AfterModel'] = {"prop_coral_01"},
            },
            CleanModel = {
                {"prop_rub_tyre_03", "prop_rub_tyre_01", "prop_rub_binbag_sd_02"}
            },
            SuitCaseModel = {
                ['BeforeModel'] = {"prop_suitcase_01d" },
                ['AfterModel'] = {"hei_p_attache_case_01b_s"},
            },
        },     
        {
            regionName = "mission3",
            regionLabel = "Valoria Cay",
            regionInfo = "You need to immediately collect overgrown corals in certain areas with diving equipment and tools and clean up the garbage. You must start now!",
            regionStep = "The team will acquire the boat and travel to the designated area.",
            regionStep2 = "The team will collect corals in the area, clean up debris in the sea, and gather hidden items.",
            minLevel = 14,
            jobTask = {
                {name = "seaChanged", img = "coral.png", label = "Take Coral", amount = 40, areaDistance = 60, coords = vector3(4204.08, 3637.51, -43.38) },
                {name = "seaClean", img = "trash.png", label = "Sea Clean", amount = 40, areaDistance = 60,  coords = vector3(4204.08, 3637.51, -43.38)},
                {name = "seaSuitCase", label = "Sea SuitCase", amount = 20, areaDistance = 60,  coords = vector3(4204.08, 3637.51, -43.38)}, 
                --{name = "sealooting", label = "Sea Box", amount = 15, areaDistance = 60,  coords = vector3(4204.08, 3637.51, -43.38)}, -- BOX 
            },
            regionPNG = "https://r2.fivemanage.com/biv23I9cFWICSObhZsr4C/Mask_group1.png",
            awards = {
                money = 65000,
                xp = 3000,
                onlinejobextra = 1 -- how many percent of the money will increase when a player enters onlinejob 
            },
            vehicleSpawnCoords = {
                vector4(3902.53, 4484.97, 0.19, 10.71)
            },
            jobDeliverTheVehicleCoords = vector3(3902.53, 4484.97, 0.19),
            boxModel = {
                bigbox = {
                    model = {"ba_prop_battle_crate_closed_bc", "ba_prop_battle_crate_closed_bc"},
                    random = 50,
                    liftHeight = 2.9,
                    riseSpeed = 0.02,
                },
                smallbox = {
                    model = {"gr_prop_gr_rsply_crate01a", "gr_prop_gr_rsply_crate01a"},
                    random = 50,
                    liftHeight = 1.8,
                    riseSpeed = 0.06,
                }
            },
            ChangeModel = {
                ['BeforeModel'] = {"prop_coral_pillar_01"},
                ['AfterModel'] = {"prop_coral_01"},
            },
            CleanModel = {
                {"prop_rub_tyre_03", "prop_rub_tyre_01", "prop_rub_binbag_sd_02"}
            },
            SuitCaseModel = {
                ['BeforeModel'] = {"prop_suitcase_01d" },
                ['AfterModel'] = {"hei_p_attache_case_01b_s"},
            },
        }, 
        {
            regionName = "mission4",
            regionLabel = "Driftmoon Atoll",
            regionInfo = "You need to immediately collect overgrown corals in certain areas with diving equipment and tools and clean up the garbage. You must start now!",
            regionStep = "The team will acquire the boat and travel to the designated area.",
            regionStep2 = "The team will collect corals in the area, clean up debris in the sea, and gather hidden items.",
            minLevel = 21,
            jobTask = {
                {name = "seaChanged", img = "coral.png", label = "Take Coral", amount = 50, areaDistance = 70, coords = vector4(3323.94, 6580.64, -65.03, 102.81) },
                {name = "seaClean", img = "trash.png", label = "Sea Clean", amount = 50, areaDistance = 70,  coords = vector4(3323.94, 6580.64, -65.03, 102.81)},
                {name = "seaSuitCase", label = "Sea SuitCase", amount = 20, areaDistance = 70,  coords = vector4(3323.94, 6580.64, -65.03, 102.81)}, 
                {name = "sealooting", label = "Sea Box", amount = 20, areaDistance = 70,  coords = vector4(3323.94, 6580.64, -65.03, 102.81)}, -- BOX 
            },
            regionPNG = "https://r2.fivemanage.com/biv23I9cFWICSObhZsr4C/Mask_group1.png",
            awards = {
                money = 78000,
                xp = 4000,
                onlinejobextra = 1 -- how many percent of the money will increase when a player enters onlinejob 
            },
            vehicleSpawnCoords = {
                vector4(1540.32, 6712.64, 0.45, 10.04)
            },
            jobDeliverTheVehicleCoords = vector3(1540.32, 6712.64, 0.45),
            boxModel = {
                bigbox = {
                    model = {"ba_prop_battle_crate_closed_bc", "ba_prop_battle_crate_closed_bc"},
                    random = 50,
                    liftHeight = 2.9,
                    riseSpeed = 0.02,
                },
                smallbox = {
                    model = {"gr_prop_gr_rsply_crate01a", "gr_prop_gr_rsply_crate01a"},
                    random = 50,
                    liftHeight = 1.8,
                    riseSpeed = 0.06,
                }
            },
            ChangeModel = {
                ['BeforeModel'] = {"prop_coral_pillar_01"},
                ['AfterModel'] = {"prop_coral_01"},
            },
            CleanModel = {
                {"prop_rub_tyre_03", "prop_rub_tyre_01", "prop_rub_binbag_sd_02"}
            },
            SuitCaseModel = {
                ['BeforeModel'] = {"prop_suitcase_01d" },
                ['AfterModel'] = {"hei_p_attache_case_01b_s"},
            },
        }, 
    },
    ["Market"] = {
        ["BuyItems"] = {
            {itemName = "scuba_tube_l1", itemLabel = "Scuba Tube Level 1", itemKG = 1, itemCount = 1, itemPrice = 500, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/scuba_tube_l1.png" },
            {itemName = "scuba_tube_l2", itemLabel = "Scuba Tube Level 2", itemKG = 1, itemCount = 1, itemPrice = 1000, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/scuba_tube_l2.png" },
            {itemName = "scuba_tube_l3", itemLabel = "Scuba Tube Level 3", itemKG = 1, itemCount = 1, itemPrice = 1500, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/scuba_tube_l3.png" },
            {itemName = "scuba_gear", itemLabel = "Scuba Mask", itemKG = 1, itemCount = 1, itemPrice = 700, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/liftbag.png" },
            {itemName = "liftbag", itemLabel = "Diving Liftbag", itemKG = 1, itemCount = 1, itemPrice = 200, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/liftbag.png" },
        },
        ["SellItems"] = {
            {itemName = "dendrogyra_coral", itemLabel = "Dendrogyra", itemKG = 1, itemCount = 1, itemPrice = 1500, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/dendrogyra_coral.png"},
            {itemName = "antipatharia_coral", itemLabel = "Antipatharia", itemKG = 1, itemCount = 1, itemPrice = 1500, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/antipatharia_coral.png"},
            {itemName = "montastraea_coral", itemLabel = "Montastraea", itemKG = 1, itemCount = 1, itemPrice = 1500, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/montastraea_coral.png"},
            {itemName = "keshi_pearl", itemLabel = "Keshi Pearls", itemKG = 1, itemCount = 1, itemPrice = 750, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/keshi_pearl.png"},
            {itemName = "cortez_pearl", itemLabel = "Cortez Pearls", itemKG = 1, itemCount = 1, itemPrice = 2000, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/cortez_pearl.png"},
            {itemName = "marine_fossil", itemLabel = "Marine Fossil", itemKG = 1, itemCount = 1, itemPrice = 2500, itemImage = "https://r2.fivemanage.com/mBjKmdHlV2sZQ0SXzTZmD/images/marine_fossil.png"},
        }
    }
}

Config.RandomCoral = {
    { item = "dendrogyra_coral", probability = 70, amount = math.random(1,3) },  -- The probability of normal coral is 70%
    { item = "antipatharia_coral", probability = 50, amount = math.random(1,3) },     -- There's a 50 percent chance of a rare coral.
    { item = "montastraea_coral", probability = 40, amount = math.random(1,3) },  -- The probability of normal coral is 40%
    { item = "keshi_pearl", probability = 30, amount = math.random(1,3) },
    { item = "cortez_pearl", probability = 20, amount = math.random(1,3) },
    { item = "marine_fossil", probability = 10, amount = math.random(1,3) },
}

Config.RandomTrash = {
    { item = "seawater", probability = 45, amount = math.random(10,20) },
    { item = "bglass", probability = 45, amount = math.random(10,20) },
    { item = "wmetal", probability = 45, amount = math.random(10,20) },
}

Config.RandomSuitCase = { 
    { item = "ring", probability = 90, amount = math.random(1,2) },  -- The probability of normal coral is 70%
    { item = "rolex", probability = 20, amount = math.random(1,2) },    -- The probability of a rare coral is 20%
    { item = "gold", probability = 10, amount = math.random(1,2) } -- Legendary coral has a 10% chance of appearing
}

Config.RandomShipBox = { 
    { item = "ring", probability = 40, amount = math.random(1,2) },  -- The probability of normal coral is 70%
    { item = "rolex", probability = 40, amount = math.random(1,2) },    -- The probability of a rare coral is 20%
    { item = "gold", probability = 20, amount = math.random(1,2) } -- Legendary coral has a 10% chance of appearing
    -- { item = "seawater", probability = 30, amount = math.random(10,30) },
}

Config.Vehiclekey          = true
Config.VehicleSystem       = "cd_garage"       -- cd_garage / qs-vehiclekeys / wasabi-carlock / qb-vehiclekeys
Config.Removekeys          = true
Config.RemoveVehicleSystem = "cd_garage"       -- cd_garage / qs-vehiclekeys / wasabi-carlock / qb-vehiclekeys

Config.GiveVehicleKey = function(plate, model, vehicle) -- you can change vehiclekeys export if you use another vehicle key system
    if Config.Vehiclekey then
        if Config.VehicleSystem == 'cd_garage' then
            TriggerEvent('cd_garage:AddKeys', exports['cd_garage']:GetPlate(vehicle))
        elseif Config.VehicleSystem == 'qs-vehiclekeys' then
            model = GetDisplayNameFromVehicleModel(GetEntityModel(vehicle))
            exports['qs-vehiclekeys']:GiveKeys(plate, model, true)
        elseif Config.VehicleSystem == 'wasabi-carlock' then
            exports.wasabi_carlock:GiveKey(plate)
        elseif Config.VehicleSystem == 'qb-vehiclekeys' then
            TriggerServerEvent('qb-vehiclekeys:server:AcquireVehicleKeys', plate)
        end
    end
end

Config.RemoveVehiclekey    = function(plate, model, vehicle)
    if Config.Removekeys then
        if Config.RemoveVehicleSystem == 'cd_garage' then
            TriggerServerEvent('cd_garage:RemovePersistentVehicles', exports['cd_garage']:GetPlate(vehicle))
        elseif Config.RemoveVehicleSystem == 'qs-vehiclekeys' then
            model = GetDisplayNameFromVehicleModel(GetEntityModel(vehicle))
            exports['qs-vehiclekeys']:RemoveKeys(plate, model)
        elseif Config.RemoveVehicleSystem == 'wasabi-carlock' then
            exports.wasabi_carlock:RemoveKey(plate)
        elseif Config.RemoveVehicleSystem == 'qb-vehiclekeys' then
            TriggerServerEvent('qb-vehiclekeys:client:RemoveKeys', plate)
        end
    end
end

Config.FuelSystem = "ox_fuel"                 

Config.SetVehicleFuel = function(vehicle, fuel_level) 
    if Config.FuelSystem == 'LegacyFuel' then
        return exports["LegacyFuel"]:SetFuel(vehicle, fuel_level)
    elseif Config.FuelSystem == 'ox_fuel' then
        Entity(vehicle).state.fuel = fuel_level
    else
        return SetVehicleFuelLevel(vehicle, fuel_level + 0.0)
    end
end

Config.NotificationText = {
    ['oxygen'] = {
        text = "Oxygen is over.",
        type = "errorNotify"
    },
    ['vehicleexist'] = {
        text = "No ship spawns because a ship is found",
        type = "errorNotify"
    },
    ['oxygenLow'] = {
        text = "Oxygen is low.",
        type = "infoNotify"
    },
    ['needoxygen'] = {
        text = "You need Oxygen",
        type = "errorNotify"
    },
    ['needoliftbag'] = {
        text = "You need liftbag",
        type = "errorNotify"
    },
    ['level1tube'] = {
        text = "You wear the tube level 1",
        type = "infoNotify"
    },
    ['level2tube'] = {
        text = "You wear the tube level 2",
        type = "infoNotify"
    },
    ['level3tube'] = {
        text = "You wear the tube level 3",
        type = "infoNotify"
    },
    ['requiredTube'] = {
        text = "You must wear the tube first",
        type = "errorNotify"
    },
    ['wearMask'] = {
        text = "You wear the mask",
        type = "infoNotify"
    },
    ['achordrop'] = {
        text = "Anchor dropped",
        type = "infoNotify"
    },
    ['achorpickeddrop'] = {
        text = "Anchor picked up",
        type = "infoNotify"
    },
    ['gotosite'] = {
        text = "Pick up the vehicle and go to the site",
        type = "infoNotify"
    },
    ['needKnife'] = {
        text = "You need a knife to open the suitcase",
        type = "infoNotify"
    },
    ['towedrope'] = {
        text = "The box is being towed by rope, please wait",
        type = "infoNotify"
    },
    ['needAnchor'] = {
        text = "You need to anchor the vehicle",
        type = "errorNotify"
    },
    ['pressanchor'] = {
        text = "Press /anchor to anchor the vehicle",
        type = "infoNotify"
    },

    ['notenoughitem'] = {
        text = "Not enough %s to sell.",
        type = "errorNotify"
    },
    ['lobbyfull'] = {
        text = "The lobby is full.",
        type = "succesNotify"
    },
    ['jobnotstarted'] = {
        text = "You have not started the job yet.",
        type = "errorNotify"
    },
    ['jobalreadystarted'] = {
        text = "You have already started the job.",
        type = "errorNotify"
    },
    ['maxlevel'] = {
        text = "You have reached the maximum level.",
        type = "errorNotify"
    },
    ['playeralreadyinlobby'] = {
        text = "You are already in the lobby.",
        type = "errorNotify"
    },
    ['missionnotselected'] = {
        text = "You have not selected a mission.",
        type = "errorNotify"
    },
    ['playerleftlobby'] = {
        text = "The mission is terminated because the lobbyist is out of the game.",
        type = "errorNotify"
    },
    ['deliverVehile'] = {
        text = "Deliver the vehicle",
        type = "infoNotify"
    },
    ['resetJob'] = {
        text = "You have reset the job.",
        type = "errorNotify"
    },
    ['notowner'] = {
        text = "You are not the owner of the vehicle.",
        type = "errorNotify"
    },
    
}

Config.RequiredXP = {
    [1] = 1000,
    [2] = 1500,
    [3] = 2000,
    [4] = 2500,
    [5] = 3000,
    [6] = 3500,
    [7] = 4000,
    [8] = 4500,
    [9] = 5000,
    [10] = 5500,
    [11] = 6000,
    [12] = 6500,
    [13] = 7000,
    [14] = 7500,
    [15] = 8000,
    [16] = 8500,
    [17] = 9000,
    [18] = 9500,
    [19] = 10000,
    [20] = 10500,
    [21] = 11000,
    [22] = 11500,
    [23] = 12000,
    [24] = 12500,
    [25] = 13000,
    [26] = 13500,
    [27] = 14000,
    [28] = 14500,
    [29] = 15000,
    [30] = 15500,
    [31] = 16000,
    [32] = 16500,
    [33] = 17000,
    [34] = 17500,
    [35] = 18000,
    [36] = 18500,
    [37] = 19000,
    [38] = 19500,
    [39] = 20000,
    [40] = 20500,
    [41] = 21000,
    [42] = 21500,
    [43] = 22000,
    [44] = 22500,
    [45] = 23000,
    [46] = 23500,
    [47] = 24000,
    [48] = 24500,
    [49] = 25000,
    [50] = 25500,
    [51] = 26500,
    [52] = 27500,
    [53] = 28500,
    [54] = 29500,
    [55] = 30500,
    [56] = 31500,
    [57] = 32500,
    [58] = 33500,
    [59] = 34500,
    [60] = 35500,
    [61] = 36500,
    [62] = 37500,
    [63] = 38500,
    [64] = 39500,
    [65] = 40500,
    [66] = 41500,
    [67] = 42500,
    [68] = 43500,
    [69] = 44500,
    [70] = 45500,

}