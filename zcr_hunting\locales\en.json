{"weapon_not_allowed_title": "**🔒 Unauthorized Weapon Usage Alert**", "weapon_not_allowed_content": "*Unauthorized usage of this weapon is prohibited in this designated area*", "interact_haverest_animal": "Harvest Animal", "harvesting_animal": "Harvesting animal", "invalid_harvesting_weapon": "You don't have Knife to harvest this animal", "tracking_animal": "Tracking animal", "animal_tracked": "An animal has been successfully tracked", "could_not_track_animal": "Unable to track any animals", "already_tracking": "You are already tracking an animal", "wait_for_another_track": "Please wait before starting another track", "interact_campfire": "Interact with Campfire", "campfire_menu_title": "Campfire", "no_items": "You don't have the required items", "cooking_item": "Cooking %s", "placing_campfire": "Placing Campfire", "cook_items": "Cook items", "take_campfire": "Take", "animal_near_bait": "An animal is near the bait", "animal_ate_bait": "An animal ate the bait", "bait_despawned": "The bait has been despawned", "buy_item_x": "Buying %s", "sell_item_x": "Selling %s", "shop_sell_item": "<PERSON>ll", "shop_buy_item": "Buy", "item_quantity_title": "Quantity", "interact_open_shop": "Open %s", "not_enough_money": "You don't have enough money", "not_enough_item": "Invalid Quantity", "interact_talk_huntmaster": "Talk to the <PERSON>master", "hunting_missions_menu_title": "Hunting Missions", "info_mission_time": "- Time Left: ", "info_mission_name": "- Mission: ", "info_mission_item_requirements": "- Required Items:\n", "info_mission_content": "- Required:  \n", "mission_started": "Mission %s started", "label_dont_start_mission": "Another time", "label_start_mission": "Start", "mission_time_finished": "Time finished for the mission", "finish_mission": "Finish mission", "mission_finished": "Completed %s mission", "wait_do_another_mission": "You have to wait %s seconds to do another mission", "drag_animal": "Drag the animal to the vehicle", "interact_drag_animal": "Drag Animal", "put_animal_vehicle": "Put Animal Vehicle", "take_animal_to_hunt": "Take animal back to hunt master", "no_animal_brought": "You didnt brought the animal", "take_animal_to_huntmaster": "Take animal to hunt master", "talk_to_finish_mission": "Talk to the hunt master to collect your reward"}