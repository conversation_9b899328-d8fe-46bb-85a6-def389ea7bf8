local holdingUp = false
local store = ''
local blipRobbery = nil

function DrawTxt(x,y, width, height, scale, text, r, g, b, a, outline)
	SetTextFont(0)
	SetTextScale(scale, scale)
	SetTextColour(r, g, b, a)
	SetTextDropshadow(0, 0, 0, 0,255)
	SetTextDropShadow()
	if outline then
        SetTextOutline()
    end
	BeginTextCommandDisplayText('STRING')
	AddTextComponentSubstringPlayerName(text)
	EndTextCommandDisplayText(x - width / 2, y - height / 2 + 0.005)
end

RegisterNetEvent('zcrh_drugheists:currentlyRobbing')
AddEventHandler('zcrh_drugheists:currentlyRobbing', function(currentStore)
	holdingUp, store = true, currentStore

	local data = exports['cd_dispatch']:GetPlayerInfo()
	TriggerServerEvent('cd_dispatch:AddNotification', {
		job_table = {'police'}, 
		coords = data.coords,
		title = '10-99 | DRUG HEIST',
		message = 'A '..data.sex..' is found robbing a drug lab at '..data.street, 
		flash = 1,
		unique_id = tostring(math.random(0000000,9999999)),
		blip = {
			sprite = 431, 
			scale = 1.2, 
			colour = 1,
			flashes = true, 
			text = '911 - Drug Heist',
			time = (8*60*1000),
			sound = 1,
		}
	})
end)

RegisterNetEvent('zcrh_drugheists:killBlip')
AddEventHandler('zcrh_drugheists:killBlip', function()
	RemoveBlip(blipRobbery)
end)

RegisterNetEvent('zcrh_drugheists:setBlip')
AddEventHandler('zcrh_drugheists:setBlip', function(position)
	blipRobbery = AddBlipForCoord(position)
	SetBlipSprite(blipRobbery, 161)
	SetBlipScale(blipRobbery, 2.0)
	SetBlipColour(blipRobbery, 3)
	PulseBlip(blipRobbery)
end)

RegisterNetEvent('zcrh_drugheists:tooFar')
AddEventHandler('zcrh_drugheists:tooFar', function()
	holdingUp, store = false, ''
	-- Clean up blip for the robber
	if blipRobbery then
		RemoveBlip(blipRobbery)
		blipRobbery = nil
	end

	-- Show heist failure notification
	if Config.HeistNotifications.Failed.Use then
		TriggerEvent('heist-notify:show', Config.HeistNotifications.Failed, nil, 5, true)
	else
		ESX.ShowNotification(_U('robbery_cancelled'))
	end
end)

RegisterNetEvent('zcrh_drugheists:robberyComplete')
AddEventHandler('zcrh_drugheists:robberyComplete', function(award, amount)
	holdingUp, store = false, ''
	-- Clean up blip for the robber
	if blipRobbery then
		RemoveBlip(blipRobbery)
		blipRobbery = nil
	end
	-- Success notification now handled by server with actual reward data
	ESX.ShowNotification('You got some '..award..' from here')
end)

RegisterNetEvent('zcrh_drugheists:startTimer')
AddEventHandler('zcrh_drugheists:startTimer', function()
	local timer = Stores[store].secondsRemaining
	CreateThread(function()
		while timer > 0 and holdingUp do
			Wait(1000)
			if timer > 0 then
				timer = timer - 1
			end
		end
	end)
	CreateThread(function()
		while holdingUp do
			Wait(0)
			DrawTxt(0.43, 0.95, 0.0, 0.0, 0.3, _U('robbery_timer', timer), 255, 255, 255, 255)
		end
	end)
end)

CreateThread(function()
	for k,v in pairs(Stores) do
		local blip = AddBlipForCoord(v.position)
		SetBlipSprite(blip, 403)
		SetBlipColour(blip, 53)
		SetBlipScale(blip, 1.0)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName('STRING')
		AddTextComponentSubstringPlayerName('Drug Heist')
		EndTextCommandSetBlipName(blip)
	end
end)

CreateThread(function()
	while true do
		Wait(0)
		local playerPos, letSleep = GetEntityCoords(PlayerPedId()), true
		for k,v in pairs(Stores) do
			local distance = #(playerPos - v.position)
			if distance < Config.Marker.DrawDistance then
				if not holdingUp then
                    letSleep = false
					DrawMarker(Config.Marker.Type, v.position, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Config.Marker.x, Config.Marker.y, Config.Marker.z, Config.Marker.r, Config.Marker.g, Config.Marker.b, Config.Marker.a, false, false, 2, false, false, false, false)
					if distance < 2.0 then
						ESX.ShowHelpNotification(_U('press_to_rob', v.nameOfStore))
						if IsControlJustReleased(0, 38) then
							TriggerServerEvent('zcrh_drugheists:robberyStarted', k)
						end
					end
				end
                break
			end
		end
		if holdingUp then
            letSleep = false
			if #(playerPos - Stores[store].position) > Config.MaxDistance then
				TriggerServerEvent('zcrh_drugheists:tooFar', store)
			end
		end
        if letSleep then
            Wait(500)
        end
	end
end)

function getStreetandZone(coords)
	local zone = GetLabelText(GetNameOfZone(coords.x, coords.y, coords.z))
	local currentStreetHash = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
	currentStreetName = GetStreetNameFromHashKey(currentStreetHash)
	playerStreetsLocation = currentStreetName .. ", " .. zone
	return playerStreetsLocation
end