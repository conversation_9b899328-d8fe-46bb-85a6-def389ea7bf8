PLT = zcr_deliveryjob
U = PLT.U
ESX = nil
PlayersUsedUi = {}
PlayerWantsNotify = {}
Orders = {}
LastOrderTime = 0

ESX = exports["es_extended"]:getSharedObject()

RegisterNetEvent('zcr_deliveryjob:Delivey')
AddEventHandler('zcr_deliveryjob:Delivey', function(info)
  local src = source
  if Orders[info.orderId] == nil then
    singleNotify(src, "error", U["error"], 5000)
    return
  end
  local xPlayer = ESX.GetPlayerFromId(src)
  if xPlayer ~= nil then
    local total = xPlayer.getInventoryItem(info.name).count
    if total and total >= info.amount then
      xPlayer.removeInventoryItem(info.name, info.amount)
      singleNotify(src, "inform", "-" .. info.amount .. "x " .. info.name, 5000)
      xPlayer.addMoney(info.amount * PLT.DeliveryItems[info.name].price)
      singleNotify(src, "success", "+" .. (info.amount * PLT.DeliveryItems[info.name].price) .. "$", 5000)
      Orders[info.orderId] = nil
    else
      singleNotify(src, "error", U["dont_have"] .. info.amount .. "x " .. info.label, 5000)
      Orders[info.orderId].source = nil
    end
  else
    Orders[info.orderId].source = nil
  end
end)

AddEventHandler('playerDropped', function(reason)
  local src = source
  for k, v in pairs(Orders) do
    if v.source == src then
      v.source = nil
    end
  end
end)

Citizen.CreateThread(function(...)
  local id = 1
  while true do
    Citizen.Wait(1000)
    if tableNum(Orders) < PLT.MaxOrder and PLT.NewOrderTime + LastOrderTime < os.time() then
      local orderPercentage = math.random(0, PLT.TotalPercent)
      for k, v in pairs(PLT.DeliveryItems) do
        Citizen.Wait(0)
        if orderPercentage > v.minPercent and orderPercentage <= v.maxPercent then
          Orders[id] = {
            orderId = id,
            name = k,
            label = v.label,
            coord = PLT.DoorsCoordinate[math.random(1, #PLT.DoorsCoordinate)],
            amount = math.random(v.minMaxAmount[1], v.minMaxAmount[2]),
            cancelTime = os.time() + v.timeToCancel
          }
          SendNewOrderNotify(Orders[id])
          id = id + 1
          LastOrderTime = os.time()
          updateUi()
          break
        end
      end
    else
      local now = os.time()
      for k, v in pairs(Orders) do
        Citizen.Wait(0)
        if now > v.cancelTime then
          if v.source ~= nil then
            singleNotify(v.source, "error", U["too_late"], 5000)
            TriggerClientEvent("zcr_deliveryjob:CancelOrder", v.source)
          end
          Orders[k] = nil
        end
      end
    end
  end
end)

RegisterNetEvent('zcr_deliveryjob:SendMeOrders')
AddEventHandler('zcr_deliveryjob:SendMeOrders', function()
  local src = source
  TriggerClientEvent("zcr_deliveryjob:OpenOrders", src, Orders, os.time())
  PlayersUsedUi[src] = true
end)


RegisterNetEvent('zcr_deliveryjob:NewOrderNotify')
AddEventHandler('zcr_deliveryjob:NewOrderNotify', function(value)
  local src = source
  if value then
    PlayerWantsNotify[src] = value
    singleNotify(src, "success", U["notify_on"], 5000)
  else
    PlayerWantsNotify[src] = nil
    singleNotify(src, "error", U["notify_off"], 5000)
  end
end)

RegisterNetEvent('zcr_deliveryjob:CanIOrder')
AddEventHandler('zcr_deliveryjob:CanIOrder', function(id)
  local src = source
  PlayersUsedUi[src] = nil
  if Orders[id] == nil then
    singleNotify(src, "error", U["error"], 5000)
    return
  end
  if Orders[id].source then
    singleNotify(src, "error", U["other_guy_take"], 5000)
    return
  end
  if OnAnyOrder(src) then
    singleNotify(src, "error", U["you_have_already"], 5000)
    return
  end
  Orders[id].source = src
  singleNotify(src, "inform", U["taked_order"], 5000)
  TriggerClientEvent("zcr_deliveryjob:GetOrder", src, Orders[id])
end)



RegisterNetEvent('zcr_deliveryjob:ImTurnoffui')
AddEventHandler('zcr_deliveryjob:ImTurnoffui', function()
  PlayersUsedUi[source] = nil
end)

RegisterNetEvent('zcr_deliveryjob:GetMeCoordForshow')
AddEventHandler('zcr_deliveryjob:GetMeCoordForshow', function(id)
  local src = source
  PlayersUsedUi[src] = nil
  TriggerClientEvent("zcr_deliveryjob:ShowCoords", src, Orders[id].coord)
end)


RegisterNetEvent('zcr_deliveryjob:CancelOrder')
AddEventHandler('zcr_deliveryjob:CancelOrder', function(id)
  local src = source
  Orders[id].source = nil
  TriggerClientEvent("zcr_deliveryjob:OpenOrders", src, Orders, os.time())
end)

function updateUi()
  for k, v in pairs(PlayersUsedUi) do
    TriggerClientEvent("zcr_deliveryjob:OpenOrders", k, Orders, os.time())
  end
end

function SendNewOrderNotify(data)
  for k, v in pairs(PlayerWantsNotify) do
    singleNotify(k, "inform",
      U["new_order"] .. data.amount .. "x " .. data.label ..
      " for " .. (data.amount * PLT.DeliveryItems[data.name].price) .. "$", 5000)
  end
end

function dump(o) if type(o) == 'table' then
    local s = '{ '
    for k, v in pairs(o) do
      if type(k) ~= 'number' then k = '"' .. k .. '"' end
      s = s .. '[' .. k .. '] = ' .. dump(v) .. ','
    end
    return s .. '} '
  else return tostring(o) end end

function tableNum(table)
  local t = 0
  if type(table) ~= 'table' then return t end
  for k, v in pairs(table) do t = t + 1 end
  return t
end

function OnAnyOrder(src)
  for k, v in pairs(Orders) do if v.source == src then return true end end
  return false
end

function singleNotify(src, type, message, time) TriggerClientEvent('zcr_deliveryjob:SendNotify', src, type, message, time) end
