@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap');

:root {
    /* MODIFICACION */

    --background1: linear-gradient(158deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.8) 100%);
    --background2: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.5) 47%, rgba(0, 0, 0, 0.466) 100%);
    --body-background: linear-gradient(158deg, rgba(12, 12, 12, 0.9) 0%, rgba(12, 12, 12, 0.9) 100%);
    --color1: #d10031;
    --color2: #e0e0e0;
    --color3: #2b2b2b;
    --logo-url: url(../images/pickaxe.png);
}

* {
    font-weight: 500;
    color: white;
    user-select: none;
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
}

@font-face {
    font-family: 'Mazzard';
    src: url('MazzardH-Light.otf') format('opentype');
}

@font-face {
    font-family: 'MazzardRegular';
    src: url('MazzardH-Regular.otf') format('opentype');
}

@font-face {
    font-family: 'MazzardBold';
    src: url('MazzardH-Bold.otf') format('opentype');
}

@font-face {
    font-family: 'Milker';
    src: url('Milker.otf') format('opentype');
}

#hint-container {
    position: absolute;
    max-width: 40vh;
    left: 2%;
    top: 5%;
    background: linear-gradient(158deg, rgba(25, 25, 25, 0.9) 0%, rgba(10, 10, 10, 0.9) 100%);
    border: 2px solid #333;
    border-top: 3px ridge #d10031;
    padding: 0.7rem;
    border-radius: 10px 10px 5px 5px;

    font-family: 'MazzardBold', sans-serif;
    display: none;
    align-items: center;
    text-align: left;
    font-size: 0.8rem;
}

#help-container {
    position: absolute;
    max-width: 60vh;
    top: 5%;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(158deg, rgba(25, 25, 25, 0.9) 0%, rgba(10, 10, 10, 0.9) 100%);
    border: 2px solid #0e0e0ee0;
    border-top: 3px ridge #d10031;
    padding: 0.7rem;
    border-radius: 10px 10px 5px 5px;
    font-family: 'MazzardBold', sans-serif;
    display: none;
    align-items: center;
    text-align: left;
    font-size: 0.8rem;
}

#help-container .text {
    margin-left: 1rem;
    font-family: 'MazzardBold', sans-serif;
}

#hint-container .text {
    font-family: 'MazzardBold', sans-serif;
}

#help-container .icon {
    font-size: 1.5rem;
}

.text {
    font-family: 'MazzardRegular', sans-serif;
    word-wrap: break-word;
    color: var(--color2);
}

.icon {
    font-size: 1rem;
}

.key {
    display: inline-block;
    border-radius: 4px;
    background: linear-gradient(90deg, #888888, #a3a3a3, #cfcfcf, #ececec);
    color: rgb(36, 36, 36);
    border: 1px solid #888888;
    padding: 0.2em 0.6em;
    font-weight: 600;
    box-shadow: 0 4px #858585;
    font-size: 1.5vh;
    margin-bottom: 4px;
    font-family: 'MazzardRegular', sans-serif;
}


.quest-container {
    margin: 0;
    padding: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

#questMenu {
    margin: 20px;
    position: relative;
    background: linear-gradient(158deg, rgba(25, 25, 25, 0.9) 0%, rgba(10, 10, 10, 0.9) 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    width: 15vw;
    font-family: 'MazzardBold', sans-serif;
    border-top: 5px ridge #d10031;
    border-bottom: 5px ridge #d10031;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: auto;
}

#questFooter {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

#questPayment {
    font-size: 1.5vh;
    font-family: 'MazzardRegular', sans-serif;
    color: #ffffff;
    display: flex;
    align-items: center;
}

#questExperience {
    font-size: 1.5vh;
    font-family: 'MazzardRegular', sans-serif;
    color: #ffffff;
    display: flex;
    align-items: center;
}

#questPayment i,
#questExperience i {
    color: #d10031;
    margin-right: 5px;
}

#questName {
    margin-top: 0;
    font-size: 2vh;
}

.quest-menu-hidden {
    display: none;
    opacity: 0;
    transform: scale(0);
}

#questDescription {
    margin-top: 7px;
    color: #999999;
    font-size: 1.5vh;
}

.mineral-highlight {
    color: #ffffff;
    font-weight: bold;
}

.mineral-item {
    font-size: 1.5vh;
    font-family: 'MazzardBold', sans-serif;
    color: #ffffff;
}

.mineral-item>p {
    text-align: right;
    margin-bottom: 5px;
}

.amount {
    font-family: 'MazzardBold', sans-serif;
    color: #999999;
    font-size: 1.5vh;
}

.quest-icon {
    font-size: 2.25vh;
    color: #d10031;
}

.progress-bar {
    width: 100%;
    background-color: #555555;
    border-radius: 7px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    /* Borde alrededor del contenedor */
    padding: 1px;
    /* Espacio entre el borde y la barra de progreso */

}

.progress {
    height: 1vh;
    background-color: rgb(255, 14, 55);
    /* Color de fondo sólido */
    background-image: linear-gradient(135deg,
            rgba(255, 255, 255, 0.5) 25%,
            /* Mayor contraste en el gradiente */
            rgba(255, 255, 255, 0.1) 25%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(255, 255, 255, 0.5) 50%,
            rgba(255, 255, 255, 0.5) 75%,
            rgba(255, 255, 255, 0.1) 75%,
            rgba(255, 255, 255, 0.1) 100%);
    border-radius: 7px;
    background-size: 25px 25px;
    /* Aumentar el tamaño del patrón */
    animation: move 8s linear infinite;
    width: 0%;
    /* Este es el ancho inicial de la barra de progreso */
}

@keyframes move {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 50px 50px;
        /* Cambiar posición para un movimiento más notorio */
    }
}

.level-menu {
    margin: 0;
    padding: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    overflow: hidden;
}

/* Estilo general del contenedor del nivel */
#levelContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 2vh;
    width: 100%;
    /* Asegura que el contenedor ocupe todo el ancho disponible */
    box-sizing: border-box;
    /* Incluye padding y border en el tamaño total */
}

/* Estilo del círculo que muestra el nivel */
#levelCircle {
    width: 7.5vh;
    height: 7.5vh;
    border-radius: 50%;
    background: linear-gradient(145deg, #d6d6d6, #c0c0c0);
    /* Gradiente de fondo */
    border: 4px solid #333;
    /* Borde más oscuro */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3vh;
    font-family: 'MazzardBold', sans-serif;
    font-weight: bold;
    color: #fff;
    margin-right: -3.75vh;
    box-sizing: border-box;
    /* Incluye padding y border en el tamaño total */
    z-index: 10;
    position: relative;
    overflow: hidden;
    /* Asegura que los efectos se mantengan dentro del círculo */
    text-shadow:
        -1px -1px 0 #000,
        1px -1px 0 #000,
        -1px 1px 0 #000,
        1px 1px 0 #000;
}

/* Agregar un efecto de sombra para un estilo 3D */
#levelCircle::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    background: rgba(0, 0, 0, 0.4);
    /* Sombra ligera */
    border-radius: 50%;
    z-index: -1;
}

/* Agregar un brillo al círculo */
#levelCircle::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.5) 40%, transparent 70%);
    /* Brillo en el centro */
    border-radius: 50%;
    z-index: -1;
}

#experienceBarContainer {
    flex: 1;
    /* Ocupa el espacio restante en el contenedor */
    position: relative;
    height: 4vh;
    background: linear-gradient(158deg, rgba(25, 25, 25, 0.9) 0%, rgba(10, 10, 10, 0.9) 100%);

    border-radius: 0 7px 7px 0;
    overflow: hidden;
    max-width: 40vh;
    /* Limita el ancho máximo del contenedor de la barra de progreso */
    border: 2px solid rgb(145, 145, 145);
    /* Borde alrededor del contenedor */
    padding: 1px;
    /* Espacio entre el borde y la barra de progreso */
}

/* Estilo de la barra de progreso interna */
#experienceBar {
    height: 100%;
    background-color: rgb(255, 15, 55);
    /* Color de fondo sólido */
    background-image: linear-gradient(135deg,
            rgba(255, 255, 255, 0.5) 25%,
            /* Mayor contraste en el gradiente */
            rgba(255, 255, 255, 0.1) 25%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(255, 255, 255, 0.5) 50%,
            rgba(255, 255, 255, 0.5) 75%,
            rgba(255, 255, 255, 0.1) 75%,
            rgba(255, 255, 255, 0.1) 100%);
    border-radius: 0;
    background-size: 25px 25px;
    /* Tamaño del patrón del gradiente */
    animation: move 8s linear infinite;
    width: 0%;
    /* Inicialmente vacío; se ajustará con JS */
    box-sizing: border-box;
    box-shadow:     inset 0 8px 15px rgba(200, 200, 200, 0.1),
    inset 0 -8px 15px rgba(0, 0, 0, 0.3),
    inset 0 0 50px rgba(255, 255, 255, 0.1),
    inset 0 8px 15px rgba(255, 0, 55, 0.4),
    inset 0 0 50px rgba(255, 7, 69, 0.2);
    /* Asegura que padding y border se incluyan en el tamaño total */
}

/* Animación del patrón de gradiente */
@keyframes move {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 50px 50px;
        /* Cambiar posición para un movimiento más notorio */
    }
}

/* Estilo del texto central de la barra de experiencia */
#experienceText {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.85vh;
    font-family: 'MazzardBold', sans-serif;
    color: #ffffff;
    /* white-space: nowrap; */
    text-shadow:
        -1px -1px 0 #000,
        1px -1px 0 #000,
        -1px 1px 0 #000,
        1px 1px 0 #000;
    /* Delineado negro alrededor del texto */
}

#statsContainer {
    position: absolute;
    margin-left: calc(3.75vh + 2px);
    width: 30vh;
    height: 5vh;
    margin-bottom: calc(10vh + 10px);
    background-color: red;
    border: 2px solid #333;
    border-top: 3px ridge #d10031;
    border-bottom: none;
    /* Elimina el borde inferior */
    border-radius: 10px 10px 0 0;
    /* Borde redondeado solo en la parte superior */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    /* Asegura que esté sobre otros elementos */
    background: linear-gradient(158deg, rgba(25, 25, 25, 0.9) 0%, rgba(10, 10, 10, 0.9) 100%);
    display: flex;
    flex-direction: column;
    /* Organiza los elementos en columna */
    justify-content: center;
    align-items: center;
    transform: translateY(100%);
    /* Inicialmente fuera de la vista, debajo */
    clip-path: inset(0 0 100% 0);
    /* Oculta el contenedor durante la animación */
    opacity: 0;
    /* Inicialmente invisible */
    transition: transform 0.6s ease-out, opacity 0.6s ease-out, clip-path 0.6s ease-out;
    /* Suaviza la transición */
    padding: 0.5vh;
}

#menuKey {
    position: absolute;
    margin-left: calc(3.75vh + 2px);
    margin-bottom: calc(6.5vh + 15px);
    /* Posición inicial */
    font-size: 1.8vh;
    text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
    font-family: 'MazzardBold', sans-serif;
    /* background: linear-gradient(158deg, rgba(25, 25, 25, 0.9) 0%, rgba(10, 10, 10, 0.9) 100%),
    radial-gradient(circle, rgba(255, 255, 255, 0.2) 20%, transparent 50%);    border-radius: 4px 4px 0 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); */
    border-bottom: none;
    padding: 0.4vh 0.8vh 0 0.8vh;
    /* Padding arriba, derecha e izquierda */
    transform: translateY(0);
    /* No afecta la posición inicial */
    transition: transform 0.6s ease-out, margin-bottom 0.6s ease-out;
}

#menuKey.show {
    transform: translateY(calc(13.5vh - 20vh));
    /* Sube desde la posición inicial a la final */
}



#statsContainer.show {
    transform: translateY(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
}

#statsTitle {
    font-size: 1.7vh;
    font-family: 'Milker', sans-serif;
    color: #ffffff;
    margin-bottom: 0.5vh;
    text-shadow:
    -1px -1px 0 #000,
    1px -1px 0 #000,
    -1px 1px 0 #000,
    1px 1px 0 #000;
}


.itemDropRateBonusContainer {
    display: flex;
    align-items: center;
    /* Alinea verticalmente en el centro */
}

.miningBonusTitle {
    font-size: 1.5vh;
    font-family: 'MazzardBold', sans-serif;
    color: #a3a1a1;
    margin-right: 0.5vh;
    /* Espacio entre el título y la tasa */
}

.miningBonusRate {
    font-size: 1.5vh;
    font-family: 'MazzardRegular', sans-serif;
    color: #00ff00;
    /* Verde para el texto de bonificación */
}

#menuContainer {
    display: flex;
    flex-direction: column;
    /* Alinea los elementos en una columna */
    justify-content: center;
    /* Coloca los elementos al final (abajo) */
    align-items: center;
    height: 100vh;
    position: relative;
    /* Posiciona el contenedor como relativo para los botones absolutos */
    z-index: 1;
    width: 100vw;
}

.menu-container {
    width: 65%;
    height: 70vh;
    /* Aumenta la altura del contenedor para dar más espacio */
    background: linear-gradient(158deg, rgba(50, 50, 50, 0.6) 0%, rgba(35, 35, 35, 0.6) 50%, rgba(20, 20, 20, 0.6) 100%);
    /* Fondo del contenedor */
    display: flex;
    border-radius: 10px;
    flex-direction: row;
    /* Alinea los elementos en fila */
    box-sizing: border-box;
    /* Incluye el padding en el tamaño del contenedor */
    border-top: 5px ridge #d10031;
    border-bottom: 5px ridge #d10031;
}

#groupListContainer,
#playerListContainer {
    display: flex;
    flex-direction: column;
    width: 50%;
    padding: 0 20px;
}

h2 {
    display: flex;
    align-items: center;
    /* Alinea verticalmente el ícono y el texto */
    color: #ffffff;
    font-family: 'Milker', 'sans-serif';
    font-size: 1.5rem;
    /* Tamaño del texto */
    margin: 0;
    /* Elimina el margen predeterminado */
    padding: 20px 0;
    /* Espacio alrededor del título */
    text-transform: uppercase;
    /* Convierte el texto a mayúsculas */
    background: linear-gradient(to right, #ffffff, #cfcfcf);
    /* Gradiente blanco a blanco más oscuro */
    -webkit-background-clip: text;
    /* Recorta el gradiente al texto */
    color: transparent;
    /* Hace que el color de texto sea transparente para mostrar el gradiente */
}

h2 i {
    margin-right: 10px;
    /* Espacio entre el ícono y el texto */
    font-size: 1.5rem;
    /* Tamaño del ícono */
}

ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    width: 100%;
    /* Asegura que la lista ocupe el 100% del contenedor */
    flex-grow: 1;
    /* Permite que la lista crezca para llenar el espacio disponible */
}

#groupList,
#playerList {
    width: 100%;
    overflow-y: scroll;
    padding: 10px;
    box-sizing: border-box;
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6), inset 0 0 25px rgba(0, 0, 0, 0.3);
    background: linear-gradient(158deg, rgba(50, 50, 50, 0.6) 0%, rgba(35, 35, 35, 0.6) 50%, rgba(20, 20, 20, 0.6) 100%);
    /* Fondo del contenedor */
    border: 1px solid #777777;
    border-radius: 7px;
}

.leader-icon {
    color: #ca002c;
    /* Color del ícono */
    margin-left: 10px;
    /* Espacio entre el texto y el ícono */
    font-size: 16px;
    /* Tamaño del ícono */
}

.player-in-list {
    display: flex;
    align-items: center;
    /* Alinea verticalmente el contenido al centro */
    margin: 10px;
    width: calc(100% - 20px);
    /* Ajusta el ancho para compensar el margen */
    height: 8vh;
    /* Aumenta la altura de los elementos de la lista */
    border: 1px solid #777777;
    border-radius: 5px;
    color: #ffffff;
    padding: 10px;
    /* Espacio interno para separar el contenido de los bordes del elemento */
    box-sizing: border-box;
    /* Incluye el padding en el tamaño del elemento */
    background: linear-gradient(158deg, rgba(60, 60, 60, 0.6) 0%, rgba(45, 45, 45, 0.6) 50%, rgba(30, 30, 30, 0.6) 100%);
    /* Fondo del contenedor */

}

.level-circle {
    width: 5vh;
    /* Tamaño del círculo según tu diseño */
    height: 5vh;
    border-radius: 50%;
    background: linear-gradient(145deg, #d6d6d6, #c0c0c0);
    /* Gradiente de fondo */
    border: 2px solid #42dff4;
    /* Borde más oscuro */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.25vh;
    /* Tamaño del texto dentro del círculo */
    font-family: 'MazzardBold', sans-serif;
    /* Fuente */
    font-weight: bold;
    color: #fff;
    margin-right: 10px;
    /* Espacio entre el círculo y el texto */
    box-sizing: border-box;
    /* Incluye padding y border en el tamaño total */
    position: relative;
    overflow: hidden;
    /* Asegura que los efectos se mantengan dentro del círculo */
    text-shadow:
        -1px -1px 0 #000,
        1px -1px 0 #000,
        -1px 1px 0 #000,
        1px 1px 0 #000;
    /* Sombra del texto */
    /* Asegúrate de que el texto esté alineado correctamente dentro del círculo */
    padding: 0;
    /* Asegúrate de que no haya padding adicional */
    text-align: center;
}

.level-circle::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    background: rgba(0, 0, 0, 0.4);
    /* Sombra ligera */
    border-radius: 50%;
    z-index: -1;
}

.level-circle::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.5) 40%, transparent 70%);
    /* Brillo en el centro */
    border-radius: 50%;
    z-index: -1;
}

span {
    flex-grow: 1;
    /* Permite que el texto ocupe el espacio restante */
    font-size: 2.25vh;
    font-family: 'MazzardRegular', 'sans-serif';
}

button {
    background-color: #333;
    /* Color de fondo oscuro */
    color: #eee;
    /* Texto en color claro para contraste */
    border: 2px solid #555;
    /* Borde del botón en un tono más claro */
    border-radius: 10px;
    /* Esquinas redondeadas suaves */
    padding: 8px 16px;
    /* Ajusta el tamaño del botón */
    cursor: pointer;
    font-size: 16px;
    /* Tamaño del texto del botón */
    font-family: 'MazzardRegular', 'sans-serif';
    margin: 10px 0;
    /* Espacio arriba y abajo del botón */
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
    /* Transiciones suaves */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    /* Sombra sutil para profundidad */
    outline: none;
    /* Elimina el contorno del enfoque */
    position: relative;
    /* Posiciona el botón para efectos internos */
}

button:hover {
    background-color: #444;
    /* Color de fondo más claro en hover */
    border-color: #666;
    /* Borde más claro en hover */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    /* Sombra más pronunciada en hover */
}

button:active {
    background-color: #222;
    /* Color de fondo aún más oscuro en clic */
    border-color: #444;
    /* Borde más oscuro en clic */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
    /* Sombra más tenue en clic */
}

/* Estilos específicos para el botón de iniciar misión */
#startMissionButton {
    background-color: #1e1e1e;
    /* Color de fondo oscuro */
    border-color: #444;
    /* Borde del botón */
    margin-bottom: 15px;
    /* Espacio abajo del botón */
    padding: 14px 28px;
    /* Tamaño del botón ajustado */
    font-size: 18px;
    /* Tamaño del texto del botón */
    text-transform: uppercase;
    /* Texto en mayúsculas */
    font-family: 'Milker', 'sans-serif';
    letter-spacing: 1px;
    /* Espaciado entre letras */
    width: 80%;
    left: 10%;
}

#startMissionButton:hover {
    background-color: #2b2b2b;
    /* Color de fondo más claro en hover */
    border-color: #555;
    /* Borde más claro en hover */
}

#startMissionButton:active {
    background-color: #181818;
    /* Color de fondo aún más oscuro en clic */
    border-color: #444;
    /* Borde más oscuro en clic */
}

/* Estilos específicos para el botón de cerrar menú */
#closeMenuButton {
    background-color: #444;
    /* Color de fondo oscuro */
    border-color: #666;
    /* Borde del botón */
    margin-bottom: 15px;
    /* Espacio abajo del botón */
    padding: 14px 28px;
    /* Tamaño del botón ajustado */
    font-size: 18px;
    /* Tamaño del texto del botón */
    text-transform: uppercase;
    /* Texto en mayúsculas */
    font-family: 'Milker', 'sans-serif';
    letter-spacing: 1px;
    /* Espaciado entre letras */
    width: 80%;
    left: 10%;
}

#closeMenuButton:hover {
    background-color: #555;
    /* Color de fondo más claro en hover */
    border-color: #777;
    /* Borde más claro en hover */
}

#closeMenuButton:active {
    background-color: #333;
    /* Color de fondo aún más oscuro en clic */
    border-color: #666;
    /* Borde más oscuro en clic */
}

/* Estilo para la barra de desplazamiento */
#playerList::-webkit-scrollbar,
#groupList::-webkit-scrollbar {
    width: 8px;
    /* Ancho de la barra de desplazamiento */
    height: 12px;
    /* Alto de la barra de desplazamiento horizontal */
}

/* Estilo para el "track" de la barra de desplazamiento */
#playerList::-webkit-scrollbar-track,
#groupList::-webkit-scrollbar-track {
    border-radius: 4px;
    /* Bordes redondeados del track */
}

/* Estilo para el "thumb" de la barra de desplazamiento */
#playerList::-webkit-scrollbar-thumb,
#groupList::-webkit-scrollbar-thumb {
    background: #d10031;
    /* Color del thumb */
    border-radius: 4px;
    /* Bordes redondeados del thumb */
}

/* Estilo para el hover sobre el "thumb" de la barra de desplazamiento */
#playerList::-webkit-scrollbar-thumb:hover,
#groupList::-webkit-scrollbar-thumb:hover {
    background: #db0534c0;
    /* Color del thumb al pasar el mouse sobre él */
}




















#waitingMenu {
    margin: 0;
    padding: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

#waitingMenuContent {
    margin: 20px;
    background: linear-gradient(158deg, rgba(25, 25, 25, 0.9) 0%, rgba(10, 10, 10, 0.9) 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    width: 15vw;
    font-family: 'MazzardBold', sans-serif;
    border-top: 5px ridge #d10031;
    border-bottom: 5px ridge #d10031;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: auto;
}

#waitingMenuTitle {
    font-size: 2vh;
    /* margin-bottom: 20px; */
    font-family: 'MazzardBold', sans-serif;
}

#waitingMenuTitle>i {
    color: #d10031;
}

#bottomContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    /* Espacio entre los elementos */
}

#progressContainer {
    position: relative;
    width: 4.5vh;
    /* Tamaño del anillo */
    height: 4.5vh;
    /* Tamaño del anillo */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

#progressRing {
    width: 4.5vh;
    /* Tamaño del anillo */
    height: 4.5vh;
    /* Tamaño del anillo */
    border-radius: 1vh;
    background: conic-gradient(#d10031 0%, #d10031 var(--progress, 0%), #ffffff00 var(--progress, 0%));
    /* Color del progreso y del fondo */
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

#leavePartyButton {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0;
    height: 4vh;
    width: 4vh;
    background-color: black;
    color: #fff;
    border-radius: 1vh;
    z-index: 2;
}

#menu-key {
    font-family: 'MazzardBold', sans-serif;
}

#leavePartyText {
    font-family: 'MazzardBold', sans-serif;
    color: white;
    font-size: 1.5vh;
}

#waitingQuestInfo {
    display: block;
    /* Asegura que los elementos hijos se ubiquen en renglones diferentes */
    /* margin-bottom: 20px; */
    color: #fff;
    margin: 0;
}

#waitingQuestName {
    display: block;
    /* Asegura que el nombre de la quest esté en un renglón separado */
    color: #999999;
    font-size: 1.5vh;
    /* margin-top: 10px; */
}

#memberIcons {
    display: flex;
    gap: 5px;
    /* Espacio entre íconos */
    justify-content: center;
    /* Centra los íconos */
}

#memberIcons i {
    margin-top: 10px;
    font-size: 2.75vh;
    /* Tamaño de los íconos */
    color: #ccc;
    /* Color por defecto para íconos vacíos */
}

#memberIcons .icon-filled {
    color: #d10031;
    /* Color para íconos llenos */
}

#memberCount {
    text-align: center;
    /* Centra el texto del contador */
    margin-top: 5px;
    font-size: 1.5vh;
    /* Tamaño del texto */
}

@keyframes dotFlashing {

    0%,
    20% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }

    80%,
    100% {
        opacity: 1;
    }
}

.dot {
    display: inline-block;
    width: 0.5em;
    height: 0.5em;
    background-color: #fff;
    border-radius: 50%;
    margin: 0 0.1em;
    animation: dotFlashing 1.5s infinite;
}

.dot:nth-child(2) {
    animation-delay: 0.2s;
}

.dot:nth-child(3) {
    animation-delay: 0.4s;
}

.dot:nth-child(4) {
    animation-delay: 0.6s;
}

#waitingFooter {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

#waitingQuestPayment,
#waitingQuestExperience {
    font-size: 1.5vh;
    font-family: 'MazzardRegular', sans-serif;
    color: #ffffff;
    display: flex;
    align-items: center;
}

#waitingQuestPayment i,
#waitingQuestExperience i {
    color: #d10031;
    margin-right: 5px;
}

.ping-tooltip {
    height: auto;
    text-shadow: 0px 0px 2px rgb(255, 255, 255);
    visibility: hidden;
    width: auto;
    color: var(--color2);
    text-align: center;
    border-radius: 15%;
    padding: 0.5vh;
    position: fixed;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.5s, visibility 0.1s;
    font-size: 2vh;
}

.ping-icon:hover .ping-tooltip {
    visibility: visible;
    opacity: 1;
    background-color: rgba(20, 20, 20, 0.5);
}














.notify-system {
    margin: 0;
    padding: 0;
    position: absolute;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.notify-container {
    /* position: absolute;
    right: 0;
    top: 50%; */
    margin-left: 100px;
    width: 100px;
    font-family: 'MazzardBold', sans-serif;
}

.notify-box {
    width: 100px;
    height: auto;
}

.main {
    margin: 12px 16px 12px 16px;
    position: relative;
}

.main::before {
    top: calc(50% - 12px);
    left: -40px;
    line-height: 24px;
    position: absolute;
}

.notify-box {
    /* border: 1px solid rgba(255, 255, 255, 0.1); */
    padding: 5px 5px 5px 5px;
    margin-bottom: 10px;
    width: 275px;
    margin: 0 0 8px -90px;
    border-radius: 7px;
    overflow: hidden;
    border: 2px solid #333;
    border-top: 3px ridge #d10031;
    border-radius: 10px 10px 5px 5px;
}

.notify-progress-bar-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 8px;
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6), inset 0 0 25px rgba(0, 0, 0, 0.3);
    background-color: #636363;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    overflow: hidden;
    /* Asegúrate de que el contenido interno no sobresalga */
}

/* Barra de progreso */
.notify-progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 25px 25px;
    animation: move 8s linear infinite;
    box-sizing: border-box;
}

/* Color sólido por defecto */
.notify-progress-bar-default {
    background-color: rgb(244, 197, 66);
    /* Color sólido por defecto */
}

/* Gradiente */
.notify-progress-bar-gradient {
    background-color: rgb(244, 197, 66);
    background-image: linear-gradient(135deg,
            rgba(255, 255, 255, 0.4) 25%,
            rgba(255, 255, 255, 0.1) 25%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(255, 255, 255, 0.4) 50%,
            rgba(255, 255, 255, 0.4) 75%,
            rgba(255, 255, 255, 0.1) 75%,
            rgba(255, 255, 255, 0.1) 100%);
}

/* Animación del gradiente */
@keyframes move {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 50px 50px;
        /* Cambiar posición para un movimiento más notorio */
    }
}

.notify-progress-bright {
    position: absolute;
    bottom: 0;
    left: 0;
}

.notify-counter {
    color: #fff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 15px;
    font-weight: 500;
}

.notify-title {
    font-family: 'Milker', 'sans-serif';
}






















.player-scoreboard-container {
    position: absolute;
    width: 50vw;
    height: 90vh;
    top: calc(50% - 45vh);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0vh 2vw 1vh 2vw;
    background: var(--background1);
    box-shadow: var(--container-box-shadow);
    border-radius: 21px;
    border-top: 5px ridge var(--color1);
    border-bottom: 5px ridge var(--color1);
}

.tabs-container {
    display: flex;
    justify-content: center;
    width: calc(100% + 4vw);
    margin-left: -2vw;
    margin-right: -2vw;
}

.tab {
    padding: 10px 20px;
    background-color: transparent;
    color: var(--color2);
    cursor: pointer;
    font-size: 1.8vh;
    font-family: 'Milker', 'sans-serif';
    text-shadow: 0 2px 4px rgba(0, 0, 0, 1);
    text-align: center;
    width: 100%;
    transition: background-color 0.3s, color 0.3s;
    /* Añadir transiciones para suavizar el cambio de estilo */
}

.tab.active {
    background: linear-gradient(to bottom, rgba(244, 197, 66, 0.5), rgba(244, 197, 66, 0)); /* Amarillo más claro y más transparente */
    color: var(--color1);
    /* Color de texto diferente para la solapa activa */
}

/* Bordes redondeados para la primera y tercera solapas */
.first-tab {
    border-top-left-radius: 21px;
    border-right: 1px solid rgb(244, 197, 66, 0.7) ;
    /* Separador a la derecha */
}

.last-tab {
    border-top-right-radius: 21px;
    border-left: 1px solid rgb(244, 197, 66, 0.7) ;
    /* Separador a la izquierda */
}
.top-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
}

.players-list-title {
    width: 100%;
    font-size: 3.5vh;
    color: var(--color2);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 1);
    font-family: 'Milker', sans-serif;
    padding: 5px;
    margin-top: 15px;
    text-align: center;
}

.search-container {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: flex-end;
    height: 80%;
}


.search-icon {
    color: var(--color1);
    text-shadow: 0px 0px 2px rgb(255, 255, 255);
    font-size: 2.75vh;
    pointer-events: none;
    padding-bottom: 1vh;
    margin-left: 8vh;
}


.search-input {
    width: 60%;
    padding: 5px;
    border: 2px solid var(--color1);
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    outline: none;
    font-family: 'MazzardRegular', sans-serif;
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6), inset 0 0 25px rgba(0, 0, 0, 0.3);
    background: linear-gradient(158deg, rgba(50, 50, 50, 0.6) 0%, rgba(35, 35, 35, 0.6) 50%, rgba(20, 20, 20, 0.6) 100%);
    border: 1px solid #777777;
    color: var(--color2);
    font-size: 2.5vh;
    margin-left: 1vh;
}


.search-input:focus {
    border-color: var(--color1);
    box-shadow: 0 2px 5px rgba(0, 153, 105, 0.2);
}

.search-input::placeholder {
    color: rgb(193, 193, 193);
}

.titles div {
    font-size: 2vh;
    text-align: center;
    padding: 10px 0;
    width: 100%;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 1);
    color: var(--color2);

}

.titles,
.table {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.title-place,
.place {
    max-width: 8rem;

}

.title-nombre,
.nombre {
    max-width: 20rem;

}

.title-experience,
.experience {
    max-width: 14rem;
}

.titles {
    display: flex;
    width: 100%;
    justify-content: center;
    font-family: 'Milker', 'sans-serif';
}


.experience,
.nombre,
.earnings,
.place {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-family: 'MazzardRegular', 'sans-serif';
}


.table-container {
    width: 100%;
    overflow-y: auto;
    padding: 1vh;
    height: 60vh;
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6), inset 0 0 25px rgba(0, 0, 0, 0.3);
    background: linear-gradient(158deg, rgba(50, 50, 50, 0.6) 0%, rgba(35, 35, 35, 0.6) 50%, rgba(20, 20, 20, 0.6) 100%);
    border: 1px solid #777777;
    /* Fondo del contenedor */
    border-radius: 1vh;
}

.table,
.table-highlighted {
    display: flex;
    width: auto;
    background: var(--background2);
    border-radius: 5px;
    white-space: nowrap;
    margin-top: 2px;
    justify-content: center;
    align-items: center;
    font-size: 2.25vh;
    text-align: center;
}

.table-highlighted {
    animation: highlightAnimation 3s infinite;
}

@keyframes highlightAnimation {

    0%,
    100% {
        background-color: var(--background2);
    }

    50% {
        background-color: var(--color1);
    }
}

.table-highlighted div {
    color: var(--color1);
}


.table div {
    color: var(--color2);
}

.table div,
.table-highlighted div {
    text-shadow: 0px 0px 2px rgb(255, 255, 255);
    text-align: center;
    padding: 5px 0;
    width: 100%;
}

.table-container::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
}

.table-container::-webkit-scrollbar-thumb {
    background-color: var(--color1);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background-color: #009269;
}


.table:hover,
.table-highlighted:hover {
    background-color: var(--color1);
}

.players-title {
    font-family: 'MazzardBold', sans-serif;
    color: var(--color2);
    text-shadow: 0px 0px 2px rgb(255, 255, 255);
    font-size: 2.25vh;
    border-radius: 3px;
    color: var(--color1);
    text-shadow: 0px 0px 2px rgb(255, 255, 255);
    text-align: center;
}


/* .server-text {
    position: absolute;
    top: 0;
    font-family: 'MazzardBold', sans-serif;
    text-shadow: 0px 0px 2px rgb(255, 255, 255);
}

.server-text:hover {
    color: var(--color1);
    text-shadow: 0px 0px 10px rgb(255, 255, 255);
}

.server-title {
    font-size: 7vh;
    text-align: center;
    margin-right: 2vh;
    color: #d10031;
}

.server-subtitle {
    font-size: 5vh;
    justify-content: center;
    color: var(--color2);
    text-align: center;
    margin-right: 2vh;

} */


.scoreboard-box {
    display: flex;
    position: absolute;
    width: 20vw;
    height: 70vh;
    top: calc(50% - 35vh);
    right: 0;
    margin-right: 4vw;
    overflow: hidden;
    padding-bottom: 2vh;
    padding-top: 1vh;
    justify-content: center;
    align-items: center;
}

.time-remaining-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    text-align: center;
    height: auto;
    overflow: hidden;
}

.daily-rewards-container,
.season-rewards-container {
    display: flex;
    border-radius: 10px;
    width: 100%;
    align-items: center;

}

/* Estilos generales para los contenedores */
.info-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3.5vh 0;
    margin: 0.5vh;
}

/* Estilos personalizados solo para .time-container */
.time-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5vh 0; 
    margin: 0.5vh;
    position: relative;
    background: #4d4d4d; /* Color base metálico */
    border: 3px solid #666;
    border-radius: 10px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.7), /* Sombra interior para profundidad */
                0 0 7px rgba(0, 0, 0, 0.5); /* Sombra exterior para realzar el contorno */
    max-width: 90%;
    flex-direction: column;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 1);
    color: var(--color1);
    max-width: 40%;

    /* Gradientes para textura metálica y desgaste */
    background: linear-gradient(135deg, #5a5a5a, #3d3d3d); /* Gradiente metálico */
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.2) 10%, rgba(0, 0, 0, 0.2) 70%), linear-gradient(135deg, #5a5a5a, #3d3d3d);
}

/* Estilos de texto dentro del contenedor */
.time-container * {
    color: var(--color2);
    font-size: 3.5vh;
    font-family: 'Milker', 'sans-serif';
}

/* Tornillos en las esquinas de .time-container */
/* Estilos personalizados solo para los tornillos */
.time-container::before,
.time-container::after,
.time-container div::before,
.time-container div::after {
    content: "";
    position: absolute;
    width: 15px;
    height: 15px;
    background: radial-gradient(circle at 30% 30%, #888, #555);
    border-radius: 50%;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 3px rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #666; /* Agrega un borde metálico */
}

/* Línea negra en el centro del tornillo */
.time-container::before::after,
.time-container::after::after,
.time-container div::before::after,
.time-container div::after::after {
    content: "";
    position: absolute;
    width: 8px;
    height: 2px;
    background: #000;
    border-radius: 2px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.8); /* Agrega un poco de sombra para dar efecto de relieve */
}

.time-container::before {
    top: 5px;
    left: 5px;
}

.time-container::after {
    top: 5px;
    right: 5px;
}

.time-container div::before {
    bottom: 5px;
    left: 5px;
}

.time-container div::after {
    bottom: 5px;
    right: 5px;
}

.info-container,
.time-remaining-container {
    box-sizing: border-box;
}


.info-container {
    display: flex;
    flex-direction: column;
    width: 70%;
    height: auto;
    flex-grow: 1;
    /* Permite que crezca dentro del contenedor */
    overflow: auto;
    /* Permite desplazamiento si el contenido es muy grande */
}

.winner-table {
    border-collapse: separate;
    border-spacing: 0 0.5vh;
    width: 100%;
}

.winner-table th,
.winner-table td {
    text-align: center;
    /* text-wrap: nowrap; */
}

.winner-table td {
    background: var(--background2);
    font-family: 'MazzardRegular', 'sans-serif';
    font-size: 1.7vh;

}

.winner-table th {
    font-family: 'Milker', 'sans-serif';
    color: var(--color2);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 1);
    font-size: 2vh;

}

.time-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%
}

.winner-table th.place-col,
.winner-table td.place-col {
    width: 25%;
    border-radius: 5px 0 0 5px;
    /* Border-radius: arriba izquierda, arriba derecha, abajo derecha, abajo izquierda */
}

.winner-table th.reward-col,
.winner-table td.reward-col {
    width: 25%;
}

.winner-table th.winner-col,
.winner-table td.winner-col {
    border-radius: 0 5px 5px 0;
    /* Border-radius: arriba izquierda, arriba derecha, abajo derecha, abajo izquierda */
}