<svg width="30" height="32" viewBox="0 0 30 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_0_46)">
<path d="M17.8019 16L14.6918 12.0262C14.6437 11.9652 14.6871 11.875 14.7656 11.875H15.6714C15.7289 11.875 15.7839 11.902 15.8191 11.9465L18.8109 15.7691C18.8627 15.8352 18.8909 15.9167 18.8909 16.0006C18.8909 16.0845 18.8627 16.166 18.8109 16.232L15.8191 20.0535C15.7839 20.0992 15.7289 20.125 15.6714 20.125H14.7656C14.6871 20.125 14.6437 20.0348 14.6918 19.9738L17.8019 16ZM14.2394 16L11.1293 12.0262C11.0812 11.9652 11.1246 11.875 11.2031 11.875H12.1089C12.1664 11.875 12.2214 11.902 12.2566 11.9465L15.2484 15.7691C15.3002 15.8352 15.3284 15.9167 15.3284 16.0006C15.3284 16.0845 15.3002 16.166 15.2484 16.232L12.2566 20.0535C12.2214 20.0992 12.1664 20.125 12.1089 20.125H11.2031C11.1246 20.125 11.0812 20.0348 11.1293 19.9738L14.2394 16Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_0_46" x="-2" y="-1" width="34" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_46"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_46" result="shape"/>
</filter>
</defs>
</svg>
