Config = {}

Config.Framework = "ESX"  -- ESX or QBCORE

Config.RequireJob = false -- true if you want it to require a job, false to not require any job

Config.MinerJob = {       -- List of jobs that can interact with the mining job. Set to true to enable interaction. Multiple jobs can be included, but they must be set to true to function.
    ['miner'] = false,
    --['jobname'] = true,
}

Config.Target = true                -- if you want to use target script or press a key when nearby

Config.TargetScript = "ox"           -- "ox" or "qb"

Config.DynastyNotifications = false   -- false if you have a custom notify system

Config.EnableMarker = false           -- Set to true to display a 3D marker at interaction locations to guide the player

Config.MarkerColor = "red"         -- At the moment only "green" and "red" are available

Config.RespawnTime = 5000            -- time in miliseconds

Config.K4MB1Cave = false             -- Enable only if you have the K4MB1 Cave asset

Config.DrawOutline = false           -- False if you want to remove the color outline from the ores

Config.InventoryPickaxe = true       -- True if your inventory can handle durability for the pickaxe

Config.InventoryPickaxeScript = "ox" -- Only "ox" at the moment

-- You can check all the keys in https://docs.fivem.net/docs/game-references/controls/
Config.Keys = {
    TogglePickaxe = 74, -- "H"
    InteractKey = 38, -- "E"
    MineStone = 38, -- "E"
    OpenStatsMenu = "n" -- Key to open stats menu
}

Config.Locations = {
    miningZone = {
        -- From here you can change the blips, find them at https://docs.fivem.net/docs/game-references/blips/
        public = true, -- Set to true if you want all players to be able to see the blip
        coords = { x = 2943.91, y = 2747.42, z = 43.33 },
        details = { name = 'Mining Zone', sprite = 365, size = 1.0, color = 6, display = 2 },
        ---@Use this if you have Config.K4MB1Cave enabled
        -- public = true, -- Set to true if you want all players to be able to see the blip
        -- coords = { x = -594.52, y = 2127.86, z = 44.50 },
        -- details = { name = 'Mining Zone', sprite = 762, size = 1.0, color = 6, display = 2 },
    },
    pedManager = {
        model = "s_m_m_dockwork_01",
        pos = vec4(2943.91, 2747.42, 43.33, 277.795288),
        marker = vec3(2943.91, 2747.42, 44.50),
        ---@Use this if you have Config.K4MB1Cave enabled
        -- model = "s_m_m_dockwork_01",
        -- pos = vec4(-594.52, 2127.86, 128.55, 76.53),
        -- marker = vec3(-594.52, 2127.86, 128.55),
    },
    pedSeller = {
        model = "s_m_m_dockwork_01",
        pos = vec4(2834.7656, 2799.9731, 57.4930, 197.7161),
        details = { name = 'Sell minerals', sprite = 431, size = 1.0, color = 10, display = 1 },
        coords = { x = 2951.063, y = 2746.15, z = 43.43 }
        ---@Use this if you have Config.K4MB1Cave enabled
        -- model = "s_m_m_dockwork_01",
        -- pos = vec4(-590.52, 2123.86, 128.55, 76.53),
    },
    pedQuest = {
        model = "s_m_m_dockwork_01",
        pos = vec4(2948.84, 2751.30, 43.34, 195.59),
        details = { name = 'Miners quest', sprite = 306, size = 1.3, color = 10, display = 1 },
        coords = { x = 2948.84, y = 2751.30, z = 43.34 }
        ---@Use this if you have Config.K4MB1Cave enabled
        -- model = "s_m_m_dockwork_01",
        -- pos = vec4(-590.52, 2123.86, 128.55, 76.53),
    },
    pedLeaderboard = {
        model = "s_m_m_dockwork_01",
        pos = vec4(2956.73, 2745.66, 43.51, 5.66),
        details = { name = 'Miners leaderboard', sprite = 439, size = 1.0, color = 10, display = 1 },
        coords = { x = 2956.73, y = 2745.66, z = 43.51 },
        ---@Use this if you have Config.K4MB1Cave enabled
        -- model = "s_m_m_dockwork_01",
        -- pos = vec4(-590.52, 2123.86, 128.55, 76.53),
    },
    pickaxeBox = {
        marker = vec3(2945.56, 2745.92, 43.80)
        ---@Use this if you have Config.K4MB1Cave enabled
        -- marker = vec3(-591.69, 2132.88, 128.08)
    },
    conveyor = {
        marker = vec3(2954.04, 2794.23, 41.23)
    },
}

Config.MinesLevels = {
    [1] = {
        levelRequired = 1,                               -- Minimum level required to collect minerals at this tier
        chances = { coal = 80, gold = 15, emerald = 8 }, -- Chance for minerals to spawn, The sum of all chances must be exactly 100 for the system to function correctly.
        coords = {
            vec3(2936.808838, 2780.795654, 39.305542),
            vec3(2941.780273, 2774.043945, 39.255005),
            vec3(2947.898926, 2775.666016, 39.187500),
            vec3(2946.738525, 2783.287842, 39.777344),
            vec3(2939.261475, 2786.597900, 39.861572),
            vec3(2945.604492, 2789.986816, 40.333374),
            vec3(2941.529785, 2779.344971, 39.339233)
            ---@Locations for K4MB1, use only if you have Config.K4MB1Cave enabled
            --             vec3(-579.138489, 1912.246094, 120.100342),
            --             vec3(-589.780212, 1892.371460, 121.027100),
            --             vec3(-587.670349, 1866.685669, 123.032227),
            --             vec3(-553.476929, 1899.375854, 118.420171),
            --             vec3(-567.903320, 1903.951660, 120.319458),
            --             vec3(-577.701111, 1914.553833, 120.201416),
            --             vec3(-580.562622, 1885.714233, 120.572144)
        }
    },
    [2] = {
        levelRequired = 5,                                -- Minimum level required to collect minerals at this tier
        chances = { gold = 60, emerald = 30, ruby = 12 }, -- Chance for minerals to spawn, The sum of all chances must be exactly 100 for the system to function correctly.
        coords = {
            vec3(2929.595703, 2803.938477, 41.816162),
            vec3(2932.720947, 2810.914307, 43.147217),
            vec3(2942.188965, 2809.635254, 41.950928),
            vec3(2941.859375, 2816.413086, 42.894531),
            vec3(2948.004395, 2816.940674, 42.372192),
            vec3(2951.393311, 2813.076904, 41.934082),
            vec3(2936.663818, 2805.731934, 41.765625)
            ---@Locations for K4MB1, use only if you have Config.K4MB1Cave enabled
            --             vec3(-567.560425, 1865.894531, 122.139160),
            --             vec3(-563.881348, 1856.571411, 120.403687),
            --             vec3(-551.406616, 1871.683472, 118.294141),
            --             vec3(-516.118652, 1880.793457, 119.662231),
            --             vec3(-506.123077, 1877.854980, 119.628540),
            --             vec3(-487.107697, 1868.083496, 119.055664),
            --             vec3(-460.575836, 1859.063721, 117.471802)
        }
    },
    [3] = {
        levelRequired = 10,                                   -- Minimum level required to collect minerals at this tier
        chances = { ruby = 70, amethyst = 27, diamond = 6 }, -- Chance for minerals to spawn, The sum of all chances must be exactly 100 for the system to function correctly.
        coords = {
            vec3(2973.982422, 2780.531982, 38.749512),
            vec3(2977.582520, 2785.714355, 39.642456),
            vec3(2974.958252, 2790.685791, 40.333374),
            vec3(2966.070312, 2783.644043, 39.288696),
            vec3(2965.661621, 2791.041748, 40.131104),
            vec3(2968.694580, 2796.672607, 40.939941),
            vec3(2969.854980, 2786.347168, 39.440308)
            ---@Locations for K4MB1, use only if you have Config.K4MB1Cave enabled
            --             vec3(-464.795593, 2125.635254, 115.365601),
            --             vec3(-449.367035, 2069.063721, 121.347290),
            --             vec3(-399.850555, 2062.602295, 119.926392),
            --             vec3(-400.074707, 2059.371338, 120.487915),
            --             vec3(-432.580200, 2079.283447, 120.588989),
            --             vec3(-394.127472, 2069.578125, 119.915039),
            --             vec3(-408.092316, 2057.947266, 121.953857)
        }
    },
}

Config.Minerals = {
    ["coal"] = {
        model = "prop_dynasty_coal",
        items = {
            { "coal_ore",     "Coal Ore",     100, 8, 25 }, --- item name, item ingame label, probability, max drop, sell price (2.5x increase)
            { "flint",        "Flint",        20,  6, 63 }, --- item name, item ingame label, probability, max drop, sell price (2.5x increase)
            { "sulfur_chunk", "Sulfur Chunk", 10,  4, 125 }  --- item name, item ingame label, probability, max drop, sell price (2.5x increase)
        },
        experience = 35,                                   -- Experience gained for collecting this mineral
        totalhits = 3,                                     -- Number of hits required with the pickaxe to collect this mineral
        durabilitypenalty = 1,                             -- How much durability is reduced on the pickaxe per hit
    },
    ["gold"] = {
        model = "prop_dynasty_gold",
        items = {
            { "gold_nugget",    "Gold Nugget",    100, 6, 15 }, --- item name, item ingame label, probability, max drop, sell price
            { "gold_dust",      "Gold Dust",      30,  4, 30 }, --- item name, item ingame label, probability, max drop, sell price
            { "quartz_crystal", "Quartz Crystal", 10,  3, 75 }   --- item name, item ingame label, probability, max drop, sell price
        },
        experience = 75,                                       -- Experience gained for collecting this mineral
        totalhits = 4,                                          -- Number of hits required with the pickaxe to collect this mineral
        durabilitypenalty = 1,                                  -- How much durability is reduced on the pickaxe per hit
    },
    ["emerald"] = {
        model = "prop_dynasty_emerald",
        items = {
            { "emerald_crystal", "Emerald Crystal", 100, 6, 30 }, --- item name, item ingame label, probability, max drop, sell price
            { "beryl_chunk",     "Beryl Chunk",     25,  4, 75 }, --- item name, item ingame label, probability, max drop, sell price
            { "green_garnet",    "Green Garnet",    10,  3, 125 }  --- item name, item ingame label, probability, max drop, sell price
        },
        experience = 100,                                         -- Experience gained for collecting this mineral
        totalhits = 5,                                            -- Number of hits required with the pickaxe to collect this mineral
        durabilitypenalty = 1,                                    -- How much durability is reduced on the pickaxe per hit
    },
    ["ruby"] = {
        model = "prop_dynasty_ruby",
        items = {
            { "ruby_crystal",   "Ruby Crystal",   100, 4, 125 }, --- item name, item ingame label, probability, max drop, sell price
            { "corundum_chunk", "Corundum Chunk", 25,  4, 150 }, --- item name, item ingame label, probability, max drop, sell price
            { "pink_sapphire",  "Pink Sapphire",  10,  2, 300 }  --- item name, item ingame label, probability, max drop, sell price
        },
        experience = 120,                                       -- Experience gained for collecting this mineral
        totalhits = 6,                                          -- Number of hits required with the pickaxe to collect this mineral
        durabilitypenalty = 1,                                  -- How much durability is reduced on the pickaxe per hit
    },
    ["amethyst"] = {
        model = "prop_dynasty_amethyst",
        items = {
            { "amethyst_geode", "Amethyst Geode", 100, 5, 350 }, --- item name, item ingame label, probability, max drop, sell price
            { "purple_quartz",  "Purple Quartz",  30,  8, 400 }, --- item name, item ingame label, probability, max drop, sell price
            { "clear_crystal",  "Clear Crystal",  10,  3, 450 }  --- item name, item ingame label, probability, max drop, sell price
        },
        experience = 140,                                       -- Experience gained for collecting this mineral
        totalhits = 7,                                          -- Number of hits required with the pickaxe to collect this mineral
        durabilitypenalty = 1,                                  -- How much durability is reduced on the pickaxe per hit
    },
    ["diamond"] = {
        model = "prop_dynasty_diamond",
        items = {
            { "diamond_crystal", "Diamond Crystal", 100, 6, 500 }, --- item name, item ingame label, probability, max drop, sell price
            { "graphite_chunk",  "Graphite Chunk",  20,  4, 650 }, --- item name, item ingame label, probability, max drop, sell price
            { "blue_diamond",    "Blue Diamond",    10,  3, 750 }  --- item name, item ingame label, probability, max drop, sell price
        },
        experience = 170,                                         -- Experience gained for collecting this mineral
        totalhits = 8,                                            -- Number of hits required with the pickaxe to collect this mineral
        durabilitypenalty = 1,                                    -- How much durability is reduced on the pickaxe per hit
    },
}


Config.LevelExperience = {
    LevelBonusMultiplier = 1.0, -- Percentage increase in item drop rate per player level (e.g., 0.5% more chance per level)
    BaseExperience = 4000,      -- Base experience required for level 1
    GrowthFactor = 1.5,         -- How much the experience required increases with each level, allowing for infinite leveling
}

--Customize your notification system here.
function SendTextMessage(msg, type)
    if type == 'info' then
        exports['zcr_notify']:notification('fas fa-mountain text-info', 'MINER', "Notification", msg, 9500)
    end
    if type == 'error' then
        exports['zcr_notify']:notification('fas fa-mountain text-danger', 'MINER', "Notification", msg, 9500)
    end
    if type == 'success' then
        exports['zcr_notify']:notification('fas fa-mountain text-success', 'MINER', "Notification", msg, 9500)
    end
end

Config.Quests = {
    ["Daily quest"] = {                -- This quest can only be done ONCE PER DAY
        amount = { 1 },                -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 1,             -- Minimum level required to undertake this quest
        payment = 12500,                 -- Payment received upon completing the quest (2.5x increase)
        experience = 100,              -- Experience gained upon completing the quest
        mineralRequireds = { "coal" }, -- Minerals required for the quest, quantity defined in amount in the same order
        isDaily = true                 -- States if this quest can only be done once per day
    },
    ["Beginner's Bounty"] = {
        amount = { 1 },                -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 1,             -- Minimum level required to undertake this quest
        payment = 1300,                 -- Payment received upon completing the quest
        experience = 100,              -- Experience gained upon completing the quest
        mineralRequireds = { "coal" }, -- Minerals required for the quest, quantity defined in amount in the same order
        isDaily = false

    },
    ["Novice's Quest"] = {
        amount = { 1, 1 },                     -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 3,                     -- Minimum level required to undertake this quest
        payment = 2000,                         -- Payment received upon completing the quest
        experience = 250,                     -- Experience gained upon completing the quest
        mineralRequireds = { "coal", "gold" }, -- Minerals required for the quest, quantity defined in amount in the same order
        isDaily = false
    },
    ["Apprentice's Challenge"] = {
        amount = { 15 },               -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 5,             -- Minimum level required to undertake this quest
        payment = 3400,                 -- Payment received upon completing the quest
        experience = 300,              -- Experience gained upon completing the quest
        mineralRequireds = { "gold" }, -- Minerals required for the quest, quantity defined in amount in the same order
        isDaily = false

    },
    ["Journeyman's Task"] = {
        amount = { 25, 15, 10, 5 },                        -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 6,                                -- Minimum level required to undertake this quest
        payment = 50000,                                    -- Payment received upon completing the quest
        experience = 400,                                 -- Experience gained upon completing the quest
        mineralRequireds = { "gold", "emerald", "ruby" }, -- Minerals required for the quest, quantity defined in amount in the same order
        isDaily = false

    },
    ["Master's Trial"] = {
        amount = { 30, 30 },                       -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 8,                         -- Minimum level required to undertake this quest
        payment = 100000,                             -- Payment received upon completing the quest
        experience = 500,                          -- Experience gained upon completing the quest
        mineralRequireds = { "ruby", "amethyst" }, -- Minerals required for the quest, quantity defined in amount in the same order
        isDaily = false

    },
    ["Grandmaster's Quest"] = {
        amount = { 40, 25, 15 },                              -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 10,                                    -- Minimum level required to undertake this quest
        payment = 150000,                                       -- Payment received upon completing the quest
        experience = 600,                                     -- Experience gained upon completing the quest
        mineralRequireds = { "ruby", "amethyst", "diamond" }, -- Minerals required for the quest, quantity defined in amount in the same order
        isDaily = false

    }
}

Config.GroupQuests = {
    ["Beginner's Expedition"] = {
        amount = { 3, 6 },                     -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 1,                     -- Minimum level required to undertake this quest
        payment = 12500,                         -- Payment received upon completing the quest (2.5x increase)
        experience = 300,                      -- Experience gained upon completing the quest
        mineralRequireds = { "coal", "gold" }, -- Minerals required for the quest, quantity defined in amount in the same order
        recommendedPlayers = 2                 -- Recommended number of players for this quest
    },
    ["Novice's Expedition"] = {
        amount = { 5, 10 },                     -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 3,                     -- Minimum level required to undertake this quest
        payment = 10000,                         -- Payment received upon completing the quest
        experience = 500,                      -- Experience gained upon completing the quest
        mineralRequireds = { "coal", "gold" }, -- Minerals required for the quest, quantity defined in amount in the same order
        recommendedPlayers = 3                 -- Recommended number of players for this quest
    },
    ["Apprentice's Expedition"] = {
        amount = { 40, 20, 10 },                           -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 5,                                -- Minimum level required to undertake this quest
        payment = 100000,                                   -- Payment received upon completing the quest
        experience = 800,                                 -- Experience gained upon completing the quest
        mineralRequireds = { "gold", "emerald", "coal" }, -- Minerals required for the quest, quantity defined in amount in the same order
        recommendedPlayers = 4                            -- Recommended number of players for this quest
    },
    ["Journeyman's Expedition"] = {
        amount = { 60, 30, 20 },                          -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 6,                                -- Minimum level required to undertake this quest
        payment = 150000,                                   -- Payment received upon completing the quest
        experience = 1200,                                -- Experience gained upon completing the quest
        mineralRequireds = { "gold", "emerald", "ruby" }, -- Minerals required for the quest, quantity defined in amount in the same order
        recommendedPlayers = 5                            -- Recommended number of players for this quest
    },
    ["Master's Expedition"] = {
        amount = { 100, 60, 40 },                              -- Quantity required for each mineral in the same order as listed in mineralRequireds
        requiredLevel = 8,                                   -- Minimum level required to undertake this quest
        payment = 200000,                                       -- Payment received upon completing the quest
        experience = 2000,                                    -- Experience gained upon completing the quest
        mineralRequireds = { "ruby", "amethyst", "diamond" }, -- Minerals required for the quest, quantity defined in amount in the same order
        recommendedPlayers = 6                                -- Recommended number of players for this quest
    }
}

Config.PickaxeLevel = {
    ["pickaxe_rusty"] = {
        label = 'Rusty Pickaxe',
        durability = 100,        -- The lifespan of the pickaxe, i.e., the number of times you can use it before it breaks
        price = 500,              -- The cost to purchase the pickaxe
        levelRequired = 1,       -- Minimum level required to purchase this pickaxe
        itemAcquisitionBonus = 0 -- Extra percentage chance to acquire items when using this pickaxe
    },
    ["pickaxe_stone"] = {
        label = 'Stone-Crafted Pickaxe',
        durability = 200,        -- The lifespan of the pickaxe, i.e., the number of times you can use it before it breaks
        price = 1500,             -- The cost to purchase the pickaxe
        levelRequired = 3,       -- Minimum level required to purchase this pickaxe
        itemAcquisitionBonus = 5 -- Extra percentage chance to acquire items when using this pickaxe
    },
    ["pickaxe_iron"] = {
        label = 'Iron-Edged Pickaxe',
        durability = 300,         -- The lifespan of the pickaxe, i.e., the number of times you can use it before it breaks
        price = 3000,              -- The cost to purchase the pickaxe
        levelRequired = 5,        -- Minimum level required to purchase this pickaxe
        itemAcquisitionBonus = 10 -- Extra percentage chance to acquire items when using this pickaxe
    },
    ["pickaxe_steel"] = {
        label = 'Steel Pickaxe',
        durability = 500,         -- The lifespan of the pickaxe, i.e., the number of times you can use it before it breaks
        price = 6000,              -- The cost to purchase the pickaxe
        levelRequired = 6,        -- Minimum level required to purchase this pickaxe
        itemAcquisitionBonus = 15 -- Extra percentage chance to acquire items when using this pickaxe
    },
    ["pickaxe_diamond"] = {
        label = 'Diamond-Encrusted Pickaxe',
        durability = 800,         -- The lifespan of the pickaxe, i.e., the number of times you can use it before it breaks
        price = 12000,             -- The cost to purchase the pickaxe
        levelRequired = 8,        -- Minimum level required to purchase this pickaxe
        itemAcquisitionBonus = 20 -- Extra percentage chance to acquire items when using this pickaxe
    },
    ["pickaxe_mythical"] = {
        label = 'Mythical Pickaxe',
        durability = 1200,        -- The lifespan of the pickaxe, i.e., the number of times you can use it before it breaks
        price = 20000,             -- The cost to purchase the pickaxe
        levelRequired = 10,        -- Minimum level required to purchase this pickaxe
        itemAcquisitionBonus = 25 -- Extra percentage chance to acquire items when using this pickaxe
    }
}

Config.UpdateLeaderboard = 1 -- Minutes between next leaderboard update

Config.LeaderBoardRewards = { -- Leaderboard rewards
    Daily = {                 -- Resets at 00:00 server hour
        [1] = 25000,            --1st Place
        [2] = 12500,            --2nd Place
        [3] = 7500             --3rd Place
    },
    Season = {                -- Resets at 00:00 server hour the first day of every month
        [1] = 500000,
        [2] = 250000,
        [3] = 100000
    }
}