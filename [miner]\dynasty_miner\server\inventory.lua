RegisterServerEvent("dynasty_miner:givePickaxeToPlayer", function(pickaxeType)
    local src = source
    local durability = 100
    if Config.InventoryPickaxeScript == "ox" then
        local id = math.random(1000, 9999)
        exports.ox_inventory:AddItem(src, pickaxeType, 1, {
            durability = durability,
            id = id
        })
    end
end)

RegisterServerEvent('dynasty_miner:updatePickaxeDurability')
AddEventHandler('dynasty_miner:updatePickaxeDurability', function(slot, newDurability, id)
    local src = source
    if Config.InventoryPickaxeScript == "ox" then
        exports.ox_inventory:SetMetadata(src, slot, { durability = newDurability, id = id })
    end
end)
