$(function () {
    function toggleDisplay(id, status) {
        $("#" + id).toggle(status);
    }

    const displayItems = [
        { type: "detanateu", id: "detonateug" },
        { type: "shiplaysh", id: "shiplayout" },
        { type: "bombersh", id: "bombersh" },
        { type: "controllersh", id: "controllersh" },
        { type: "diversh", id: "diversh" },
        { type: "dronersh", id: "dronersh" },
        { type: "gunnersh", id: "gunnersh" }
    ];

    displayItems.forEach(item => {
        toggleDisplay(item.id, false);
    });

    window.addEventListener('message', function(event) {
        displayItems.forEach(item => {
            if (event.data.type === item.type) {
                toggleDisplay(item.id, event.data.status);
            }
        });
    });

    $("#detanatebut").click(function () {
        $.post('http://sb_sub_heist/detanatebutsh', JSON.stringify({}));
    });

    $("#shiplaybutt").click(function () {
        $.post('http://sb_sub_heist/shiplaybuttsh', JSON.stringify({}));
    });
});
