(function(){const v=document.createElement("link").relList;if(v&&v.supports&&v.supports("modulepreload"))return;for(const T of document.querySelectorAll('link[rel="modulepreload"]'))f(T);new MutationObserver(T=>{for(const _ of T)if(_.type==="childList")for(const U of _.addedNodes)U.tagName==="LINK"&&U.rel==="modulepreload"&&f(U)}).observe(document,{childList:!0,subtree:!0});function o(T){const _={};return T.integrity&&(_.integrity=T.integrity),T.referrerPolicy&&(_.referrerPolicy=T.referrerPolicy),T.crossOrigin==="use-credentials"?_.credentials="include":T.crossOrigin==="anonymous"?_.credentials="omit":_.credentials="same-origin",_}function f(T){if(T.ep)return;T.ep=!0;const _=o(T);fetch(T.href,_)}})();function Wd(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Ar={exports:{}},Vn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jd;function zh(){if(jd)return Vn;jd=1;var r=Symbol.for("react.transitional.element"),v=Symbol.for("react.fragment");function o(f,T,_){var U=null;if(_!==void 0&&(U=""+_),T.key!==void 0&&(U=""+T.key),"key"in T){_={};for(var B in T)B!=="key"&&(_[B]=T[B])}else _=T;return T=_.ref,{$$typeof:r,type:f,key:U,ref:T!==void 0?T:null,props:_}}return Vn.Fragment=v,Vn.jsx=o,Vn.jsxs=o,Vn}var Hd;function Rh(){return Hd||(Hd=1,Ar.exports=zh()),Ar.exports}var N=Rh(),zr={exports:{}},et={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qd;function _h(){if(qd)return et;qd=1;var r=Symbol.for("react.transitional.element"),v=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),T=Symbol.for("react.profiler"),_=Symbol.for("react.consumer"),U=Symbol.for("react.context"),B=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),S=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),k=Symbol.iterator;function V(d){return d===null||typeof d!="object"?null:(d=k&&d[k]||d["@@iterator"],typeof d=="function"?d:null)}var F={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ct=Object.assign,it={};function Rt(d,A,q){this.props=d,this.context=A,this.refs=it,this.updater=q||F}Rt.prototype.isReactComponent={},Rt.prototype.setState=function(d,A){if(typeof d!="object"&&typeof d!="function"&&d!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,d,A,"setState")},Rt.prototype.forceUpdate=function(d){this.updater.enqueueForceUpdate(this,d,"forceUpdate")};function ht(){}ht.prototype=Rt.prototype;function ee(d,A,q){this.props=d,this.context=A,this.refs=it,this.updater=q||F}var lt=ee.prototype=new ht;lt.constructor=ee,ct(lt,Rt.prototype),lt.isPureReactComponent=!0;var pt=Array.isArray,J={H:null,A:null,T:null,S:null,V:null},Mt=Object.prototype.hasOwnProperty;function vt(d,A,q,M,Y,H){return q=H.ref,{$$typeof:r,type:d,key:A,ref:q!==void 0?q:null,props:H}}function X(d,A){return vt(d.type,A,void 0,void 0,void 0,d.props)}function Ct(d){return typeof d=="object"&&d!==null&&d.$$typeof===r}function gt(d){var A={"=":"=0",":":"=2"};return"$"+d.replace(/[=:]/g,function(q){return A[q]})}var Yt=/\/+/g;function _t(d,A){return typeof d=="object"&&d!==null&&d.key!=null?gt(""+d.key):A.toString(36)}function Et(){}function Ot(d){switch(d.status){case"fulfilled":return d.value;case"rejected":throw d.reason;default:switch(typeof d.status=="string"?d.then(Et,Et):(d.status="pending",d.then(function(A){d.status==="pending"&&(d.status="fulfilled",d.value=A)},function(A){d.status==="pending"&&(d.status="rejected",d.reason=A)})),d.status){case"fulfilled":return d.value;case"rejected":throw d.reason}}throw d}function dt(d,A,q,M,Y){var H=typeof d;(H==="undefined"||H==="boolean")&&(d=null);var G=!1;if(d===null)G=!0;else switch(H){case"bigint":case"string":case"number":G=!0;break;case"object":switch(d.$$typeof){case r:case v:G=!0;break;case C:return G=d._init,dt(G(d._payload),A,q,M,Y)}}if(G)return Y=Y(d),G=M===""?"."+_t(d,0):M,pt(Y)?(q="",G!=null&&(q=G.replace(Yt,"$&/")+"/"),dt(Y,A,q,"",function(jt){return jt})):Y!=null&&(Ct(Y)&&(Y=X(Y,q+(Y.key==null||d&&d.key===Y.key?"":(""+Y.key).replace(Yt,"$&/")+"/")+G)),A.push(Y)),1;G=0;var tt=M===""?".":M+":";if(pt(d))for(var I=0;I<d.length;I++)M=d[I],H=tt+_t(M,I),G+=dt(M,A,q,H,Y);else if(I=V(d),typeof I=="function")for(d=I.call(d),I=0;!(M=d.next()).done;)M=M.value,H=tt+_t(M,I++),G+=dt(M,A,q,H,Y);else if(H==="object"){if(typeof d.then=="function")return dt(Ot(d),A,q,M,Y);throw A=String(d),Error("Objects are not valid as a React child (found: "+(A==="[object Object]"?"object with keys {"+Object.keys(d).join(", ")+"}":A)+"). If you meant to render a collection of children, use an array instead.")}return G}function x(d,A,q){if(d==null)return d;var M=[],Y=0;return dt(d,M,"","",function(H){return A.call(q,H,Y++)}),M}function j(d){if(d._status===-1){var A=d._result;A=A(),A.then(function(q){(d._status===0||d._status===-1)&&(d._status=1,d._result=q)},function(q){(d._status===0||d._status===-1)&&(d._status=2,d._result=q)}),d._status===-1&&(d._status=0,d._result=A)}if(d._status===1)return d._result.default;throw d._result}var w=typeof reportError=="function"?reportError:function(d){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var A=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof d=="object"&&d!==null&&typeof d.message=="string"?String(d.message):String(d),error:d});if(!window.dispatchEvent(A))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",d);return}console.error(d)};function ot(){}return et.Children={map:x,forEach:function(d,A,q){x(d,function(){A.apply(this,arguments)},q)},count:function(d){var A=0;return x(d,function(){A++}),A},toArray:function(d){return x(d,function(A){return A})||[]},only:function(d){if(!Ct(d))throw Error("React.Children.only expected to receive a single React element child.");return d}},et.Component=Rt,et.Fragment=o,et.Profiler=T,et.PureComponent=ee,et.StrictMode=f,et.Suspense=R,et.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=J,et.__COMPILER_RUNTIME={__proto__:null,c:function(d){return J.H.useMemoCache(d)}},et.cache=function(d){return function(){return d.apply(null,arguments)}},et.cloneElement=function(d,A,q){if(d==null)throw Error("The argument must be a React element, but you passed "+d+".");var M=ct({},d.props),Y=d.key,H=void 0;if(A!=null)for(G in A.ref!==void 0&&(H=void 0),A.key!==void 0&&(Y=""+A.key),A)!Mt.call(A,G)||G==="key"||G==="__self"||G==="__source"||G==="ref"&&A.ref===void 0||(M[G]=A[G]);var G=arguments.length-2;if(G===1)M.children=q;else if(1<G){for(var tt=Array(G),I=0;I<G;I++)tt[I]=arguments[I+2];M.children=tt}return vt(d.type,Y,void 0,void 0,H,M)},et.createContext=function(d){return d={$$typeof:U,_currentValue:d,_currentValue2:d,_threadCount:0,Provider:null,Consumer:null},d.Provider=d,d.Consumer={$$typeof:_,_context:d},d},et.createElement=function(d,A,q){var M,Y={},H=null;if(A!=null)for(M in A.key!==void 0&&(H=""+A.key),A)Mt.call(A,M)&&M!=="key"&&M!=="__self"&&M!=="__source"&&(Y[M]=A[M]);var G=arguments.length-2;if(G===1)Y.children=q;else if(1<G){for(var tt=Array(G),I=0;I<G;I++)tt[I]=arguments[I+2];Y.children=tt}if(d&&d.defaultProps)for(M in G=d.defaultProps,G)Y[M]===void 0&&(Y[M]=G[M]);return vt(d,H,void 0,void 0,null,Y)},et.createRef=function(){return{current:null}},et.forwardRef=function(d){return{$$typeof:B,render:d}},et.isValidElement=Ct,et.lazy=function(d){return{$$typeof:C,_payload:{_status:-1,_result:d},_init:j}},et.memo=function(d,A){return{$$typeof:S,type:d,compare:A===void 0?null:A}},et.startTransition=function(d){var A=J.T,q={};J.T=q;try{var M=d(),Y=J.S;Y!==null&&Y(q,M),typeof M=="object"&&M!==null&&typeof M.then=="function"&&M.then(ot,w)}catch(H){w(H)}finally{J.T=A}},et.unstable_useCacheRefresh=function(){return J.H.useCacheRefresh()},et.use=function(d){return J.H.use(d)},et.useActionState=function(d,A,q){return J.H.useActionState(d,A,q)},et.useCallback=function(d,A){return J.H.useCallback(d,A)},et.useContext=function(d){return J.H.useContext(d)},et.useDebugValue=function(){},et.useDeferredValue=function(d,A){return J.H.useDeferredValue(d,A)},et.useEffect=function(d,A,q){var M=J.H;if(typeof q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return M.useEffect(d,A)},et.useId=function(){return J.H.useId()},et.useImperativeHandle=function(d,A,q){return J.H.useImperativeHandle(d,A,q)},et.useInsertionEffect=function(d,A){return J.H.useInsertionEffect(d,A)},et.useLayoutEffect=function(d,A){return J.H.useLayoutEffect(d,A)},et.useMemo=function(d,A){return J.H.useMemo(d,A)},et.useOptimistic=function(d,A){return J.H.useOptimistic(d,A)},et.useReducer=function(d,A,q){return J.H.useReducer(d,A,q)},et.useRef=function(d){return J.H.useRef(d)},et.useState=function(d){return J.H.useState(d)},et.useSyncExternalStore=function(d,A,q){return J.H.useSyncExternalStore(d,A,q)},et.useTransition=function(){return J.H.useTransition()},et.version="19.1.0",et}var Bd;function qr(){return Bd||(Bd=1,zr.exports=_h()),zr.exports}var D=qr();const Br=Wd(D);var Rr={exports:{}},Kn={},_r={exports:{}},Or={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yd;function Oh(){return Yd||(Yd=1,function(r){function v(x,j){var w=x.length;x.push(j);t:for(;0<w;){var ot=w-1>>>1,d=x[ot];if(0<T(d,j))x[ot]=j,x[w]=d,w=ot;else break t}}function o(x){return x.length===0?null:x[0]}function f(x){if(x.length===0)return null;var j=x[0],w=x.pop();if(w!==j){x[0]=w;t:for(var ot=0,d=x.length,A=d>>>1;ot<A;){var q=2*(ot+1)-1,M=x[q],Y=q+1,H=x[Y];if(0>T(M,w))Y<d&&0>T(H,M)?(x[ot]=H,x[Y]=w,ot=Y):(x[ot]=M,x[q]=w,ot=q);else if(Y<d&&0>T(H,w))x[ot]=H,x[Y]=w,ot=Y;else break t}}return j}function T(x,j){var w=x.sortIndex-j.sortIndex;return w!==0?w:x.id-j.id}if(r.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var _=performance;r.unstable_now=function(){return _.now()}}else{var U=Date,B=U.now();r.unstable_now=function(){return U.now()-B}}var R=[],S=[],C=1,k=null,V=3,F=!1,ct=!1,it=!1,Rt=!1,ht=typeof setTimeout=="function"?setTimeout:null,ee=typeof clearTimeout=="function"?clearTimeout:null,lt=typeof setImmediate<"u"?setImmediate:null;function pt(x){for(var j=o(S);j!==null;){if(j.callback===null)f(S);else if(j.startTime<=x)f(S),j.sortIndex=j.expirationTime,v(R,j);else break;j=o(S)}}function J(x){if(it=!1,pt(x),!ct)if(o(R)!==null)ct=!0,Mt||(Mt=!0,_t());else{var j=o(S);j!==null&&dt(J,j.startTime-x)}}var Mt=!1,vt=-1,X=5,Ct=-1;function gt(){return Rt?!0:!(r.unstable_now()-Ct<X)}function Yt(){if(Rt=!1,Mt){var x=r.unstable_now();Ct=x;var j=!0;try{t:{ct=!1,it&&(it=!1,ee(vt),vt=-1),F=!0;var w=V;try{e:{for(pt(x),k=o(R);k!==null&&!(k.expirationTime>x&&gt());){var ot=k.callback;if(typeof ot=="function"){k.callback=null,V=k.priorityLevel;var d=ot(k.expirationTime<=x);if(x=r.unstable_now(),typeof d=="function"){k.callback=d,pt(x),j=!0;break e}k===o(R)&&f(R),pt(x)}else f(R);k=o(R)}if(k!==null)j=!0;else{var A=o(S);A!==null&&dt(J,A.startTime-x),j=!1}}break t}finally{k=null,V=w,F=!1}j=void 0}}finally{j?_t():Mt=!1}}}var _t;if(typeof lt=="function")_t=function(){lt(Yt)};else if(typeof MessageChannel<"u"){var Et=new MessageChannel,Ot=Et.port2;Et.port1.onmessage=Yt,_t=function(){Ot.postMessage(null)}}else _t=function(){ht(Yt,0)};function dt(x,j){vt=ht(function(){x(r.unstable_now())},j)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(x){x.callback=null},r.unstable_forceFrameRate=function(x){0>x||125<x?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):X=0<x?Math.floor(1e3/x):5},r.unstable_getCurrentPriorityLevel=function(){return V},r.unstable_next=function(x){switch(V){case 1:case 2:case 3:var j=3;break;default:j=V}var w=V;V=j;try{return x()}finally{V=w}},r.unstable_requestPaint=function(){Rt=!0},r.unstable_runWithPriority=function(x,j){switch(x){case 1:case 2:case 3:case 4:case 5:break;default:x=3}var w=V;V=x;try{return j()}finally{V=w}},r.unstable_scheduleCallback=function(x,j,w){var ot=r.unstable_now();switch(typeof w=="object"&&w!==null?(w=w.delay,w=typeof w=="number"&&0<w?ot+w:ot):w=ot,x){case 1:var d=-1;break;case 2:d=250;break;case 5:d=1073741823;break;case 4:d=1e4;break;default:d=5e3}return d=w+d,x={id:C++,callback:j,priorityLevel:x,startTime:w,expirationTime:d,sortIndex:-1},w>ot?(x.sortIndex=w,v(S,x),o(R)===null&&x===o(S)&&(it?(ee(vt),vt=-1):it=!0,dt(J,w-ot))):(x.sortIndex=d,v(R,x),ct||F||(ct=!0,Mt||(Mt=!0,_t()))),x},r.unstable_shouldYield=gt,r.unstable_wrapCallback=function(x){var j=V;return function(){var w=V;V=j;try{return x.apply(this,arguments)}finally{V=w}}}}(Or)),Or}var Gd;function Dh(){return Gd||(Gd=1,_r.exports=Oh()),_r.exports}var Dr={exports:{}},me={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xd;function Nh(){if(Xd)return me;Xd=1;var r=qr();function v(R){var S="https://react.dev/errors/"+R;if(1<arguments.length){S+="?args[]="+encodeURIComponent(arguments[1]);for(var C=2;C<arguments.length;C++)S+="&args[]="+encodeURIComponent(arguments[C])}return"Minified React error #"+R+"; visit "+S+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var f={d:{f:o,r:function(){throw Error(v(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},T=Symbol.for("react.portal");function _(R,S,C){var k=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:T,key:k==null?null:""+k,children:R,containerInfo:S,implementation:C}}var U=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function B(R,S){if(R==="font")return"";if(typeof S=="string")return S==="use-credentials"?S:""}return me.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=f,me.createPortal=function(R,S){var C=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!S||S.nodeType!==1&&S.nodeType!==9&&S.nodeType!==11)throw Error(v(299));return _(R,S,null,C)},me.flushSync=function(R){var S=U.T,C=f.p;try{if(U.T=null,f.p=2,R)return R()}finally{U.T=S,f.p=C,f.d.f()}},me.preconnect=function(R,S){typeof R=="string"&&(S?(S=S.crossOrigin,S=typeof S=="string"?S==="use-credentials"?S:"":void 0):S=null,f.d.C(R,S))},me.prefetchDNS=function(R){typeof R=="string"&&f.d.D(R)},me.preinit=function(R,S){if(typeof R=="string"&&S&&typeof S.as=="string"){var C=S.as,k=B(C,S.crossOrigin),V=typeof S.integrity=="string"?S.integrity:void 0,F=typeof S.fetchPriority=="string"?S.fetchPriority:void 0;C==="style"?f.d.S(R,typeof S.precedence=="string"?S.precedence:void 0,{crossOrigin:k,integrity:V,fetchPriority:F}):C==="script"&&f.d.X(R,{crossOrigin:k,integrity:V,fetchPriority:F,nonce:typeof S.nonce=="string"?S.nonce:void 0})}},me.preinitModule=function(R,S){if(typeof R=="string")if(typeof S=="object"&&S!==null){if(S.as==null||S.as==="script"){var C=B(S.as,S.crossOrigin);f.d.M(R,{crossOrigin:C,integrity:typeof S.integrity=="string"?S.integrity:void 0,nonce:typeof S.nonce=="string"?S.nonce:void 0})}}else S==null&&f.d.M(R)},me.preload=function(R,S){if(typeof R=="string"&&typeof S=="object"&&S!==null&&typeof S.as=="string"){var C=S.as,k=B(C,S.crossOrigin);f.d.L(R,C,{crossOrigin:k,integrity:typeof S.integrity=="string"?S.integrity:void 0,nonce:typeof S.nonce=="string"?S.nonce:void 0,type:typeof S.type=="string"?S.type:void 0,fetchPriority:typeof S.fetchPriority=="string"?S.fetchPriority:void 0,referrerPolicy:typeof S.referrerPolicy=="string"?S.referrerPolicy:void 0,imageSrcSet:typeof S.imageSrcSet=="string"?S.imageSrcSet:void 0,imageSizes:typeof S.imageSizes=="string"?S.imageSizes:void 0,media:typeof S.media=="string"?S.media:void 0})}},me.preloadModule=function(R,S){if(typeof R=="string")if(S){var C=B(S.as,S.crossOrigin);f.d.m(R,{as:typeof S.as=="string"&&S.as!=="script"?S.as:void 0,crossOrigin:C,integrity:typeof S.integrity=="string"?S.integrity:void 0})}else f.d.m(R)},me.requestFormReset=function(R){f.d.r(R)},me.unstable_batchedUpdates=function(R,S){return R(S)},me.useFormState=function(R,S,C){return U.H.useFormState(R,S,C)},me.useFormStatus=function(){return U.H.useHostTransitionStatus()},me.version="19.1.0",me}var Ld;function wh(){if(Ld)return Dr.exports;Ld=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(v){console.error(v)}}return r(),Dr.exports=Nh(),Dr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function Mh(){if(Qd)return Kn;Qd=1;var r=Dh(),v=qr(),o=wh();function f(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function T(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function _(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function U(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function B(t){if(_(t)!==t)throw Error(f(188))}function R(t){var e=t.alternate;if(!e){if(e=_(t),e===null)throw Error(f(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return B(n),t;if(u===a)return B(n),e;u=u.sibling}throw Error(f(188))}if(l.return!==a.return)l=n,a=u;else{for(var c=!1,i=n.child;i;){if(i===l){c=!0,l=n,a=u;break}if(i===a){c=!0,a=n,l=u;break}i=i.sibling}if(!c){for(i=u.child;i;){if(i===l){c=!0,l=u,a=n;break}if(i===a){c=!0,a=u,l=n;break}i=i.sibling}if(!c)throw Error(f(189))}}if(l.alternate!==a)throw Error(f(190))}if(l.tag!==3)throw Error(f(188));return l.stateNode.current===l?t:e}function S(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=S(t),e!==null)return e;t=t.sibling}return null}var C=Object.assign,k=Symbol.for("react.element"),V=Symbol.for("react.transitional.element"),F=Symbol.for("react.portal"),ct=Symbol.for("react.fragment"),it=Symbol.for("react.strict_mode"),Rt=Symbol.for("react.profiler"),ht=Symbol.for("react.provider"),ee=Symbol.for("react.consumer"),lt=Symbol.for("react.context"),pt=Symbol.for("react.forward_ref"),J=Symbol.for("react.suspense"),Mt=Symbol.for("react.suspense_list"),vt=Symbol.for("react.memo"),X=Symbol.for("react.lazy"),Ct=Symbol.for("react.activity"),gt=Symbol.for("react.memo_cache_sentinel"),Yt=Symbol.iterator;function _t(t){return t===null||typeof t!="object"?null:(t=Yt&&t[Yt]||t["@@iterator"],typeof t=="function"?t:null)}var Et=Symbol.for("react.client.reference");function Ot(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Et?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case ct:return"Fragment";case Rt:return"Profiler";case it:return"StrictMode";case J:return"Suspense";case Mt:return"SuspenseList";case Ct:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case F:return"Portal";case lt:return(t.displayName||"Context")+".Provider";case ee:return(t._context.displayName||"Context")+".Consumer";case pt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case vt:return e=t.displayName||null,e!==null?e:Ot(t.type)||"Memo";case X:e=t._payload,t=t._init;try{return Ot(t(e))}catch{}}return null}var dt=Array.isArray,x=v.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,j=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,w={pending:!1,data:null,method:null,action:null},ot=[],d=-1;function A(t){return{current:t}}function q(t){0>d||(t.current=ot[d],ot[d]=null,d--)}function M(t,e){d++,ot[d]=t.current,t.current=e}var Y=A(null),H=A(null),G=A(null),tt=A(null);function I(t,e){switch(M(G,e),M(H,t),M(Y,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?fd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=fd(e),t=sd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}q(Y),M(Y,t)}function jt(){q(Y),q(H),q(G)}function he(t){t.memoizedState!==null&&M(tt,t);var e=Y.current,l=sd(e,t.type);e!==l&&(M(H,t),M(Y,l))}function ge(t){H.current===t&&(q(Y),q(H)),tt.current===t&&(q(tt),Gn._currentValue=w)}var le=Object.prototype.hasOwnProperty,ol=r.unstable_scheduleCallback,Hl=r.unstable_cancelCallback,ql=r.unstable_shouldYield,aa=r.unstable_requestPaint,ve=r.unstable_now,sc=r.unstable_getCurrentPriorityLevel,yt=r.unstable_ImmediatePriority,kt=r.unstable_UserBlockingPriority,Tt=r.unstable_NormalPriority,be=r.unstable_LowPriority,Ft=r.unstable_IdlePriority,Gt=r.log,Jt=r.unstable_setDisableYieldValue,qt=null,st=null;function Zt(t){if(typeof Gt=="function"&&Jt(t),st&&typeof st.setStrictMode=="function")try{st.setStrictMode(qt,t)}catch{}}var St=Math.clz32?Math.clz32:dc,Jn=Math.log,oc=Math.LN2;function dc(t){return t>>>=0,t===0?32:31-(Jn(t)/oc|0)|0}var na=256,Wn=4194304;function Bl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function $n(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,c=t.pingedLanes;t=t.warmLanes;var i=a&134217727;return i!==0?(a=i&~u,a!==0?n=Bl(a):(c&=i,c!==0?n=Bl(c):l||(l=i&~t,l!==0&&(n=Bl(l))))):(i=a&~u,i!==0?n=Bl(i):c!==0?n=Bl(c):l||(l=a&~t,l!==0&&(n=Bl(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function Ja(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function d0(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zr(){var t=na;return na<<=1,(na&4194048)===0&&(na=256),t}function Vr(){var t=Wn;return Wn<<=1,(Wn&62914560)===0&&(Wn=4194304),t}function mc(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function Wa(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function m0(t,e,l,a,n,u){var c=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var i=t.entanglements,s=t.expirationTimes,y=t.hiddenUpdates;for(l=c&~l;0<l;){var E=31-St(l),O=1<<E;i[E]=0,s[E]=-1;var b=y[E];if(b!==null)for(y[E]=null,E=0;E<b.length;E++){var p=b[E];p!==null&&(p.lane&=-536870913)}l&=~O}a!==0&&Kr(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(c&~e))}function Kr(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-St(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function kr(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-St(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function hc(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function gc(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Jr(){var t=j.p;return t!==0?t:(t=window.event,t===void 0?32:Dd(t.type))}function h0(t,e){var l=j.p;try{return j.p=t,e()}finally{j.p=l}}var dl=Math.random().toString(36).slice(2),oe="__reactFiber$"+dl,pe="__reactProps$"+dl,ua="__reactContainer$"+dl,yc="__reactEvents$"+dl,g0="__reactListeners$"+dl,y0="__reactHandles$"+dl,Wr="__reactResources$"+dl,$a="__reactMarker$"+dl;function vc(t){delete t[oe],delete t[pe],delete t[yc],delete t[g0],delete t[y0]}function ca(t){var e=t[oe];if(e)return e;for(var l=t.parentNode;l;){if(e=l[ua]||l[oe]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=hd(t);t!==null;){if(l=t[oe])return l;t=hd(t)}return e}t=l,l=t.parentNode}return null}function ia(t){if(t=t[oe]||t[ua]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Fa(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(f(33))}function ra(t){var e=t[Wr];return e||(e=t[Wr]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ae(t){t[$a]=!0}var $r=new Set,Fr={};function Yl(t,e){fa(t,e),fa(t+"Capture",e)}function fa(t,e){for(Fr[t]=e,t=0;t<e.length;t++)$r.add(e[t])}var v0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ir={},Pr={};function b0(t){return le.call(Pr,t)?!0:le.call(Ir,t)?!1:v0.test(t)?Pr[t]=!0:(Ir[t]=!0,!1)}function Fn(t,e,l){if(b0(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function In(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function We(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var bc,tf;function sa(t){if(bc===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);bc=e&&e[1]||"",tf=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+bc+t+tf}var pc=!1;function Sc(t,e){if(!t||pc)return"";pc=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var O=function(){throw Error()};if(Object.defineProperty(O.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(O,[])}catch(p){var b=p}Reflect.construct(t,[],O)}else{try{O.call()}catch(p){b=p}t.call(O.prototype)}}else{try{throw Error()}catch(p){b=p}(O=t())&&typeof O.catch=="function"&&O.catch(function(){})}}catch(p){if(p&&b&&typeof p.stack=="string")return[p.stack,b.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),c=u[0],i=u[1];if(c&&i){var s=c.split(`
`),y=i.split(`
`);for(n=a=0;a<s.length&&!s[a].includes("DetermineComponentFrameRoot");)a++;for(;n<y.length&&!y[n].includes("DetermineComponentFrameRoot");)n++;if(a===s.length||n===y.length)for(a=s.length-1,n=y.length-1;1<=a&&0<=n&&s[a]!==y[n];)n--;for(;1<=a&&0<=n;a--,n--)if(s[a]!==y[n]){if(a!==1||n!==1)do if(a--,n--,0>n||s[a]!==y[n]){var E=`
`+s[a].replace(" at new "," at ");return t.displayName&&E.includes("<anonymous>")&&(E=E.replace("<anonymous>",t.displayName)),E}while(1<=a&&0<=n);break}}}finally{pc=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?sa(l):""}function p0(t){switch(t.tag){case 26:case 27:case 5:return sa(t.type);case 16:return sa("Lazy");case 13:return sa("Suspense");case 19:return sa("SuspenseList");case 0:case 15:return Sc(t.type,!1);case 11:return Sc(t.type.render,!1);case 1:return Sc(t.type,!0);case 31:return sa("Activity");default:return""}}function ef(t){try{var e="";do e+=p0(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function we(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function lf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function S0(t){var e=lf(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(c){a=""+c,u.call(this,c)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(c){a=""+c},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Pn(t){t._valueTracker||(t._valueTracker=S0(t))}function af(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=lf(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function tu(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var x0=/[\n"\\]/g;function Me(t){return t.replace(x0,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function xc(t,e,l,a,n,u,c,i){t.name="",c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"?t.type=c:t.removeAttribute("type"),e!=null?c==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+we(e)):t.value!==""+we(e)&&(t.value=""+we(e)):c!=="submit"&&c!=="reset"||t.removeAttribute("value"),e!=null?Tc(t,c,we(e)):l!=null?Tc(t,c,we(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?t.name=""+we(i):t.removeAttribute("name")}function nf(t,e,l,a,n,u,c,i){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+we(l):"",e=e!=null?""+we(e):l,i||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=i?t.checked:!!a,t.defaultChecked=!!a,c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.name=c)}function Tc(t,e,l){e==="number"&&tu(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function oa(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+we(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function uf(t,e,l){if(e!=null&&(e=""+we(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+we(l):""}function cf(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(f(92));if(dt(a)){if(1<a.length)throw Error(f(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=we(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function da(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var T0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function rf(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||T0.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function ff(t,e,l){if(e!=null&&typeof e!="object")throw Error(f(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&rf(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&rf(t,u,e[u])}function Ec(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var E0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),A0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function eu(t){return A0.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ac=null;function zc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ma=null,ha=null;function sf(t){var e=ia(t);if(e&&(t=e.stateNode)){var l=t[pe]||null;t:switch(t=e.stateNode,e.type){case"input":if(xc(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Me(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[pe]||null;if(!n)throw Error(f(90));xc(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&af(a)}break t;case"textarea":uf(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&oa(t,!!l.multiple,e,!1)}}}var Rc=!1;function of(t,e,l){if(Rc)return t(e,l);Rc=!0;try{var a=t(e);return a}finally{if(Rc=!1,(ma!==null||ha!==null)&&(Gu(),ma&&(e=ma,t=ha,ha=ma=null,sf(e),t)))for(e=0;e<t.length;e++)sf(t[e])}}function Ia(t,e){var l=t.stateNode;if(l===null)return null;var a=l[pe]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(f(231,e,typeof l));return l}var $e=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),_c=!1;if($e)try{var Pa={};Object.defineProperty(Pa,"passive",{get:function(){_c=!0}}),window.addEventListener("test",Pa,Pa),window.removeEventListener("test",Pa,Pa)}catch{_c=!1}var ml=null,Oc=null,lu=null;function df(){if(lu)return lu;var t,e=Oc,l=e.length,a,n="value"in ml?ml.value:ml.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var c=l-t;for(a=1;a<=c&&e[l-a]===n[u-a];a++);return lu=n.slice(t,1<a?1-a:void 0)}function au(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function nu(){return!0}function mf(){return!1}function Se(t){function e(l,a,n,u,c){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=c,this.currentTarget=null;for(var i in t)t.hasOwnProperty(i)&&(l=t[i],this[i]=l?l(u):u[i]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?nu:mf,this.isPropagationStopped=mf,this}return C(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=nu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=nu)},persist:function(){},isPersistent:nu}),e}var Gl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},uu=Se(Gl),tn=C({},Gl,{view:0,detail:0}),z0=Se(tn),Dc,Nc,en,cu=C({},tn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Mc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==en&&(en&&t.type==="mousemove"?(Dc=t.screenX-en.screenX,Nc=t.screenY-en.screenY):Nc=Dc=0,en=t),Dc)},movementY:function(t){return"movementY"in t?t.movementY:Nc}}),hf=Se(cu),R0=C({},cu,{dataTransfer:0}),_0=Se(R0),O0=C({},tn,{relatedTarget:0}),wc=Se(O0),D0=C({},Gl,{animationName:0,elapsedTime:0,pseudoElement:0}),N0=Se(D0),w0=C({},Gl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),M0=Se(w0),U0=C({},Gl,{data:0}),gf=Se(U0),C0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},j0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},H0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function q0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=H0[t])?!!e[t]:!1}function Mc(){return q0}var B0=C({},tn,{key:function(t){if(t.key){var e=C0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=au(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?j0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Mc,charCode:function(t){return t.type==="keypress"?au(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?au(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Y0=Se(B0),G0=C({},cu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),yf=Se(G0),X0=C({},tn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Mc}),L0=Se(X0),Q0=C({},Gl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Z0=Se(Q0),V0=C({},cu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),K0=Se(V0),k0=C({},Gl,{newState:0,oldState:0}),J0=Se(k0),W0=[9,13,27,32],Uc=$e&&"CompositionEvent"in window,ln=null;$e&&"documentMode"in document&&(ln=document.documentMode);var $0=$e&&"TextEvent"in window&&!ln,vf=$e&&(!Uc||ln&&8<ln&&11>=ln),bf=" ",pf=!1;function Sf(t,e){switch(t){case"keyup":return W0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function xf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ga=!1;function F0(t,e){switch(t){case"compositionend":return xf(e);case"keypress":return e.which!==32?null:(pf=!0,bf);case"textInput":return t=e.data,t===bf&&pf?null:t;default:return null}}function I0(t,e){if(ga)return t==="compositionend"||!Uc&&Sf(t,e)?(t=df(),lu=Oc=ml=null,ga=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return vf&&e.locale!=="ko"?null:e.data;default:return null}}var P0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Tf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!P0[t.type]:e==="textarea"}function Ef(t,e,l,a){ma?ha?ha.push(a):ha=[a]:ma=a,e=Ku(e,"onChange"),0<e.length&&(l=new uu("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var an=null,nn=null;function tm(t){nd(t,0)}function iu(t){var e=Fa(t);if(af(e))return t}function Af(t,e){if(t==="change")return e}var zf=!1;if($e){var Cc;if($e){var jc="oninput"in document;if(!jc){var Rf=document.createElement("div");Rf.setAttribute("oninput","return;"),jc=typeof Rf.oninput=="function"}Cc=jc}else Cc=!1;zf=Cc&&(!document.documentMode||9<document.documentMode)}function _f(){an&&(an.detachEvent("onpropertychange",Of),nn=an=null)}function Of(t){if(t.propertyName==="value"&&iu(nn)){var e=[];Ef(e,nn,t,zc(t)),of(tm,e)}}function em(t,e,l){t==="focusin"?(_f(),an=e,nn=l,an.attachEvent("onpropertychange",Of)):t==="focusout"&&_f()}function lm(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return iu(nn)}function am(t,e){if(t==="click")return iu(e)}function nm(t,e){if(t==="input"||t==="change")return iu(e)}function um(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ae=typeof Object.is=="function"?Object.is:um;function un(t,e){if(Ae(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!le.call(e,n)||!Ae(t[n],e[n]))return!1}return!0}function Df(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Nf(t,e){var l=Df(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Df(l)}}function wf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?wf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Mf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=tu(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=tu(t.document)}return e}function Hc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var cm=$e&&"documentMode"in document&&11>=document.documentMode,ya=null,qc=null,cn=null,Bc=!1;function Uf(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Bc||ya==null||ya!==tu(a)||(a=ya,"selectionStart"in a&&Hc(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),cn&&un(cn,a)||(cn=a,a=Ku(qc,"onSelect"),0<a.length&&(e=new uu("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=ya)))}function Xl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var va={animationend:Xl("Animation","AnimationEnd"),animationiteration:Xl("Animation","AnimationIteration"),animationstart:Xl("Animation","AnimationStart"),transitionrun:Xl("Transition","TransitionRun"),transitionstart:Xl("Transition","TransitionStart"),transitioncancel:Xl("Transition","TransitionCancel"),transitionend:Xl("Transition","TransitionEnd")},Yc={},Cf={};$e&&(Cf=document.createElement("div").style,"AnimationEvent"in window||(delete va.animationend.animation,delete va.animationiteration.animation,delete va.animationstart.animation),"TransitionEvent"in window||delete va.transitionend.transition);function Ll(t){if(Yc[t])return Yc[t];if(!va[t])return t;var e=va[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Cf)return Yc[t]=e[l];return t}var jf=Ll("animationend"),Hf=Ll("animationiteration"),qf=Ll("animationstart"),im=Ll("transitionrun"),rm=Ll("transitionstart"),fm=Ll("transitioncancel"),Bf=Ll("transitionend"),Yf=new Map,Gc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Gc.push("scrollEnd");function Ge(t,e){Yf.set(t,e),Yl(e,[t])}var Gf=new WeakMap;function Ue(t,e){if(typeof t=="object"&&t!==null){var l=Gf.get(t);return l!==void 0?l:(e={value:t,source:e,stack:ef(e)},Gf.set(t,e),e)}return{value:t,source:e,stack:ef(e)}}var Ce=[],ba=0,Xc=0;function ru(){for(var t=ba,e=Xc=ba=0;e<t;){var l=Ce[e];Ce[e++]=null;var a=Ce[e];Ce[e++]=null;var n=Ce[e];Ce[e++]=null;var u=Ce[e];if(Ce[e++]=null,a!==null&&n!==null){var c=a.pending;c===null?n.next=n:(n.next=c.next,c.next=n),a.pending=n}u!==0&&Xf(l,n,u)}}function fu(t,e,l,a){Ce[ba++]=t,Ce[ba++]=e,Ce[ba++]=l,Ce[ba++]=a,Xc|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Lc(t,e,l,a){return fu(t,e,l,a),su(t)}function pa(t,e){return fu(t,null,null,e),su(t)}function Xf(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-St(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function su(t){if(50<Mn)throw Mn=0,Ji=null,Error(f(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Sa={};function sm(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(t,e,l,a){return new sm(t,e,l,a)}function Qc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Fe(t,e){var l=t.alternate;return l===null?(l=ze(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Lf(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function ou(t,e,l,a,n,u){var c=0;if(a=t,typeof t=="function")Qc(t)&&(c=1);else if(typeof t=="string")c=dh(t,l,Y.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Ct:return t=ze(31,l,e,n),t.elementType=Ct,t.lanes=u,t;case ct:return Ql(l.children,n,u,e);case it:c=8,n|=24;break;case Rt:return t=ze(12,l,e,n|2),t.elementType=Rt,t.lanes=u,t;case J:return t=ze(13,l,e,n),t.elementType=J,t.lanes=u,t;case Mt:return t=ze(19,l,e,n),t.elementType=Mt,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case ht:case lt:c=10;break t;case ee:c=9;break t;case pt:c=11;break t;case vt:c=14;break t;case X:c=16,a=null;break t}c=29,l=Error(f(130,t===null?"null":typeof t,"")),a=null}return e=ze(c,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function Ql(t,e,l,a){return t=ze(7,t,a,e),t.lanes=l,t}function Zc(t,e,l){return t=ze(6,t,null,e),t.lanes=l,t}function Vc(t,e,l){return e=ze(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var xa=[],Ta=0,du=null,mu=0,je=[],He=0,Zl=null,Ie=1,Pe="";function Vl(t,e){xa[Ta++]=mu,xa[Ta++]=du,du=t,mu=e}function Qf(t,e,l){je[He++]=Ie,je[He++]=Pe,je[He++]=Zl,Zl=t;var a=Ie;t=Pe;var n=32-St(a)-1;a&=~(1<<n),l+=1;var u=32-St(e)+n;if(30<u){var c=n-n%5;u=(a&(1<<c)-1).toString(32),a>>=c,n-=c,Ie=1<<32-St(e)+n|l<<n|a,Pe=u+t}else Ie=1<<u|l<<n|a,Pe=t}function Kc(t){t.return!==null&&(Vl(t,1),Qf(t,1,0))}function kc(t){for(;t===du;)du=xa[--Ta],xa[Ta]=null,mu=xa[--Ta],xa[Ta]=null;for(;t===Zl;)Zl=je[--He],je[He]=null,Pe=je[--He],je[He]=null,Ie=je[--He],je[He]=null}var ye=null,Lt=null,xt=!1,Kl=null,Ze=!1,Jc=Error(f(519));function kl(t){var e=Error(f(418,""));throw sn(Ue(e,t)),Jc}function Zf(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[oe]=t,e[pe]=a,l){case"dialog":ft("cancel",e),ft("close",e);break;case"iframe":case"object":case"embed":ft("load",e);break;case"video":case"audio":for(l=0;l<Cn.length;l++)ft(Cn[l],e);break;case"source":ft("error",e);break;case"img":case"image":case"link":ft("error",e),ft("load",e);break;case"details":ft("toggle",e);break;case"input":ft("invalid",e),nf(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Pn(e);break;case"select":ft("invalid",e);break;case"textarea":ft("invalid",e),cf(e,a.value,a.defaultValue,a.children),Pn(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||rd(e.textContent,l)?(a.popover!=null&&(ft("beforetoggle",e),ft("toggle",e)),a.onScroll!=null&&ft("scroll",e),a.onScrollEnd!=null&&ft("scrollend",e),a.onClick!=null&&(e.onclick=ku),e=!0):e=!1,e||kl(t)}function Vf(t){for(ye=t.return;ye;)switch(ye.tag){case 5:case 13:Ze=!1;return;case 27:case 3:Ze=!0;return;default:ye=ye.return}}function rn(t){if(t!==ye)return!1;if(!xt)return Vf(t),xt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||sr(t.type,t.memoizedProps)),l=!l),l&&Lt&&kl(t),Vf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(f(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Lt=Le(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Lt=null}}else e===27?(e=Lt,Dl(t.type)?(t=hr,hr=null,Lt=t):Lt=e):Lt=ye?Le(t.stateNode.nextSibling):null;return!0}function fn(){Lt=ye=null,xt=!1}function Kf(){var t=Kl;return t!==null&&(Ee===null?Ee=t:Ee.push.apply(Ee,t),Kl=null),t}function sn(t){Kl===null?Kl=[t]:Kl.push(t)}var Wc=A(null),Jl=null,tl=null;function hl(t,e,l){M(Wc,e._currentValue),e._currentValue=l}function el(t){t._currentValue=Wc.current,q(Wc)}function $c(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Fc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var c=n.child;u=u.firstContext;t:for(;u!==null;){var i=u;u=n;for(var s=0;s<e.length;s++)if(i.context===e[s]){u.lanes|=l,i=u.alternate,i!==null&&(i.lanes|=l),$c(u.return,l,t),a||(c=null);break t}u=i.next}}else if(n.tag===18){if(c=n.return,c===null)throw Error(f(341));c.lanes|=l,u=c.alternate,u!==null&&(u.lanes|=l),$c(c,l,t),c=null}else c=n.child;if(c!==null)c.return=n;else for(c=n;c!==null;){if(c===t){c=null;break}if(n=c.sibling,n!==null){n.return=c.return,c=n;break}c=c.return}n=c}}function on(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var c=n.alternate;if(c===null)throw Error(f(387));if(c=c.memoizedProps,c!==null){var i=n.type;Ae(n.pendingProps.value,c.value)||(t!==null?t.push(i):t=[i])}}else if(n===tt.current){if(c=n.alternate,c===null)throw Error(f(387));c.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Gn):t=[Gn])}n=n.return}t!==null&&Fc(e,t,l,a),e.flags|=262144}function hu(t){for(t=t.firstContext;t!==null;){if(!Ae(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Wl(t){Jl=t,tl=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function de(t){return kf(Jl,t)}function gu(t,e){return Jl===null&&Wl(t),kf(t,e)}function kf(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},tl===null){if(t===null)throw Error(f(308));tl=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else tl=tl.next=e;return l}var om=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},dm=r.unstable_scheduleCallback,mm=r.unstable_NormalPriority,It={$$typeof:lt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ic(){return{controller:new om,data:new Map,refCount:0}}function dn(t){t.refCount--,t.refCount===0&&dm(mm,function(){t.controller.abort()})}var mn=null,Pc=0,Ea=0,Aa=null;function hm(t,e){if(mn===null){var l=mn=[];Pc=0,Ea=er(),Aa={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Pc++,e.then(Jf,Jf),e}function Jf(){if(--Pc===0&&mn!==null){Aa!==null&&(Aa.status="fulfilled");var t=mn;mn=null,Ea=0,Aa=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function gm(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Wf=x.S;x.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&hm(t,e),Wf!==null&&Wf(t,e)};var $l=A(null);function ti(){var t=$l.current;return t!==null?t:Ht.pooledCache}function yu(t,e){e===null?M($l,$l.current):M($l,e.pool)}function $f(){var t=ti();return t===null?null:{parent:It._currentValue,pool:t}}var hn=Error(f(460)),Ff=Error(f(474)),vu=Error(f(542)),ei={then:function(){}};function If(t){return t=t.status,t==="fulfilled"||t==="rejected"}function bu(){}function Pf(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(bu,bu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,es(t),t;default:if(typeof e.status=="string")e.then(bu,bu);else{if(t=Ht,t!==null&&100<t.shellSuspendCounter)throw Error(f(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,es(t),t}throw gn=e,hn}}var gn=null;function ts(){if(gn===null)throw Error(f(459));var t=gn;return gn=null,t}function es(t){if(t===hn||t===vu)throw Error(f(483))}var gl=!1;function li(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ai(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function yl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function vl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(At&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=su(t),Xf(t,null,l),e}return fu(t,a,e,l),su(t)}function yn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,kr(t,l)}}function ni(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var c={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=c:u=u.next=c,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var ui=!1;function vn(){if(ui){var t=Aa;if(t!==null)throw t}}function bn(t,e,l,a){ui=!1;var n=t.updateQueue;gl=!1;var u=n.firstBaseUpdate,c=n.lastBaseUpdate,i=n.shared.pending;if(i!==null){n.shared.pending=null;var s=i,y=s.next;s.next=null,c===null?u=y:c.next=y,c=s;var E=t.alternate;E!==null&&(E=E.updateQueue,i=E.lastBaseUpdate,i!==c&&(i===null?E.firstBaseUpdate=y:i.next=y,E.lastBaseUpdate=s))}if(u!==null){var O=n.baseState;c=0,E=y=s=null,i=u;do{var b=i.lane&-536870913,p=b!==i.lane;if(p?(mt&b)===b:(a&b)===b){b!==0&&b===Ea&&(ui=!0),E!==null&&(E=E.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});t:{var P=t,W=i;b=e;var wt=l;switch(W.tag){case 1:if(P=W.payload,typeof P=="function"){O=P.call(wt,O,b);break t}O=P;break t;case 3:P.flags=P.flags&-65537|128;case 0:if(P=W.payload,b=typeof P=="function"?P.call(wt,O,b):P,b==null)break t;O=C({},O,b);break t;case 2:gl=!0}}b=i.callback,b!==null&&(t.flags|=64,p&&(t.flags|=8192),p=n.callbacks,p===null?n.callbacks=[b]:p.push(b))}else p={lane:b,tag:i.tag,payload:i.payload,callback:i.callback,next:null},E===null?(y=E=p,s=O):E=E.next=p,c|=b;if(i=i.next,i===null){if(i=n.shared.pending,i===null)break;p=i,i=p.next,p.next=null,n.lastBaseUpdate=p,n.shared.pending=null}}while(!0);E===null&&(s=O),n.baseState=s,n.firstBaseUpdate=y,n.lastBaseUpdate=E,u===null&&(n.shared.lanes=0),zl|=c,t.lanes=c,t.memoizedState=O}}function ls(t,e){if(typeof t!="function")throw Error(f(191,t));t.call(e)}function as(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)ls(l[t],e)}var za=A(null),pu=A(0);function ns(t,e){t=rl,M(pu,t),M(za,e),rl=t|e.baseLanes}function ci(){M(pu,rl),M(za,za.current)}function ii(){rl=pu.current,q(za),q(pu)}var bl=0,at=null,Dt=null,Wt=null,Su=!1,Ra=!1,Fl=!1,xu=0,pn=0,_a=null,ym=0;function Vt(){throw Error(f(321))}function ri(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!Ae(t[l],e[l]))return!1;return!0}function fi(t,e,l,a,n,u){return bl=u,at=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,x.H=t===null||t.memoizedState===null?Xs:Ls,Fl=!1,u=l(a,n),Fl=!1,Ra&&(u=cs(e,l,a,n)),us(t),u}function us(t){x.H=_u;var e=Dt!==null&&Dt.next!==null;if(bl=0,Wt=Dt=at=null,Su=!1,pn=0,_a=null,e)throw Error(f(300));t===null||ne||(t=t.dependencies,t!==null&&hu(t)&&(ne=!0))}function cs(t,e,l,a){at=t;var n=0;do{if(Ra&&(_a=null),pn=0,Ra=!1,25<=n)throw Error(f(301));if(n+=1,Wt=Dt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}x.H=Em,u=e(l,a)}while(Ra);return u}function vm(){var t=x.H,e=t.useState()[0];return e=typeof e.then=="function"?Sn(e):e,t=t.useState()[0],(Dt!==null?Dt.memoizedState:null)!==t&&(at.flags|=1024),e}function si(){var t=xu!==0;return xu=0,t}function oi(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function di(t){if(Su){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Su=!1}bl=0,Wt=Dt=at=null,Ra=!1,pn=xu=0,_a=null}function xe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Wt===null?at.memoizedState=Wt=t:Wt=Wt.next=t,Wt}function $t(){if(Dt===null){var t=at.alternate;t=t!==null?t.memoizedState:null}else t=Dt.next;var e=Wt===null?at.memoizedState:Wt.next;if(e!==null)Wt=e,Dt=t;else{if(t===null)throw at.alternate===null?Error(f(467)):Error(f(310));Dt=t,t={memoizedState:Dt.memoizedState,baseState:Dt.baseState,baseQueue:Dt.baseQueue,queue:Dt.queue,next:null},Wt===null?at.memoizedState=Wt=t:Wt=Wt.next=t}return Wt}function mi(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Sn(t){var e=pn;return pn+=1,_a===null&&(_a=[]),t=Pf(_a,t,e),e=at,(Wt===null?e.memoizedState:Wt.next)===null&&(e=e.alternate,x.H=e===null||e.memoizedState===null?Xs:Ls),t}function Tu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Sn(t);if(t.$$typeof===lt)return de(t)}throw Error(f(438,String(t)))}function hi(t){var e=null,l=at.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=at.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=mi(),at.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=gt;return e.index++,l}function ll(t,e){return typeof e=="function"?e(t):e}function Eu(t){var e=$t();return gi(e,Dt,t)}function gi(t,e,l){var a=t.queue;if(a===null)throw Error(f(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var c=n.next;n.next=u.next,u.next=c}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var i=c=null,s=null,y=e,E=!1;do{var O=y.lane&-536870913;if(O!==y.lane?(mt&O)===O:(bl&O)===O){var b=y.revertLane;if(b===0)s!==null&&(s=s.next={lane:0,revertLane:0,action:y.action,hasEagerState:y.hasEagerState,eagerState:y.eagerState,next:null}),O===Ea&&(E=!0);else if((bl&b)===b){y=y.next,b===Ea&&(E=!0);continue}else O={lane:0,revertLane:y.revertLane,action:y.action,hasEagerState:y.hasEagerState,eagerState:y.eagerState,next:null},s===null?(i=s=O,c=u):s=s.next=O,at.lanes|=b,zl|=b;O=y.action,Fl&&l(u,O),u=y.hasEagerState?y.eagerState:l(u,O)}else b={lane:O,revertLane:y.revertLane,action:y.action,hasEagerState:y.hasEagerState,eagerState:y.eagerState,next:null},s===null?(i=s=b,c=u):s=s.next=b,at.lanes|=O,zl|=O;y=y.next}while(y!==null&&y!==e);if(s===null?c=u:s.next=i,!Ae(u,t.memoizedState)&&(ne=!0,E&&(l=Aa,l!==null)))throw l;t.memoizedState=u,t.baseState=c,t.baseQueue=s,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function yi(t){var e=$t(),l=e.queue;if(l===null)throw Error(f(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var c=n=n.next;do u=t(u,c.action),c=c.next;while(c!==n);Ae(u,e.memoizedState)||(ne=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function is(t,e,l){var a=at,n=$t(),u=xt;if(u){if(l===void 0)throw Error(f(407));l=l()}else l=e();var c=!Ae((Dt||n).memoizedState,l);c&&(n.memoizedState=l,ne=!0),n=n.queue;var i=ss.bind(null,a,n,t);if(xn(2048,8,i,[t]),n.getSnapshot!==e||c||Wt!==null&&Wt.memoizedState.tag&1){if(a.flags|=2048,Oa(9,Au(),fs.bind(null,a,n,l,e),null),Ht===null)throw Error(f(349));u||(bl&124)!==0||rs(a,e,l)}return l}function rs(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=at.updateQueue,e===null?(e=mi(),at.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function fs(t,e,l,a){e.value=l,e.getSnapshot=a,os(e)&&ds(t)}function ss(t,e,l){return l(function(){os(e)&&ds(t)})}function os(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!Ae(t,l)}catch{return!0}}function ds(t){var e=pa(t,2);e!==null&&Ne(e,t,2)}function vi(t){var e=xe();if(typeof t=="function"){var l=t;if(t=l(),Fl){Zt(!0);try{l()}finally{Zt(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ll,lastRenderedState:t},e}function ms(t,e,l,a){return t.baseState=l,gi(t,Dt,typeof a=="function"?a:ll)}function bm(t,e,l,a,n){if(Ru(t))throw Error(f(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(c){u.listeners.push(c)}};x.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,hs(e,u)):(u.next=l.next,e.pending=l.next=u)}}function hs(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=x.T,c={};x.T=c;try{var i=l(n,a),s=x.S;s!==null&&s(c,i),gs(t,e,i)}catch(y){bi(t,e,y)}finally{x.T=u}}else try{u=l(n,a),gs(t,e,u)}catch(y){bi(t,e,y)}}function gs(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){ys(t,e,a)},function(a){return bi(t,e,a)}):ys(t,e,l)}function ys(t,e,l){e.status="fulfilled",e.value=l,vs(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,hs(t,l)))}function bi(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,vs(e),e=e.next;while(e!==a)}t.action=null}function vs(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function bs(t,e){return e}function ps(t,e){if(xt){var l=Ht.formState;if(l!==null){t:{var a=at;if(xt){if(Lt){e:{for(var n=Lt,u=Ze;n.nodeType!==8;){if(!u){n=null;break e}if(n=Le(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Lt=Le(n.nextSibling),a=n.data==="F!";break t}}kl(a)}a=!1}a&&(e=l[0])}}return l=xe(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:bs,lastRenderedState:e},l.queue=a,l=Bs.bind(null,at,a),a.dispatch=l,a=vi(!1),u=Ei.bind(null,at,!1,a.queue),a=xe(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=bm.bind(null,at,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Ss(t){var e=$t();return xs(e,Dt,t)}function xs(t,e,l){if(e=gi(t,e,bs)[0],t=Eu(ll)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=Sn(e)}catch(c){throw c===hn?vu:c}else a=e;e=$t();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(at.flags|=2048,Oa(9,Au(),pm.bind(null,n,l),null)),[a,u,t]}function pm(t,e){t.action=e}function Ts(t){var e=$t(),l=Dt;if(l!==null)return xs(e,l,t);$t(),e=e.memoizedState,l=$t();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function Oa(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=at.updateQueue,e===null&&(e=mi(),at.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function Au(){return{destroy:void 0,resource:void 0}}function Es(){return $t().memoizedState}function zu(t,e,l,a){var n=xe();a=a===void 0?null:a,at.flags|=t,n.memoizedState=Oa(1|e,Au(),l,a)}function xn(t,e,l,a){var n=$t();a=a===void 0?null:a;var u=n.memoizedState.inst;Dt!==null&&a!==null&&ri(a,Dt.memoizedState.deps)?n.memoizedState=Oa(e,u,l,a):(at.flags|=t,n.memoizedState=Oa(1|e,u,l,a))}function As(t,e){zu(8390656,8,t,e)}function zs(t,e){xn(2048,8,t,e)}function Rs(t,e){return xn(4,2,t,e)}function _s(t,e){return xn(4,4,t,e)}function Os(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Ds(t,e,l){l=l!=null?l.concat([t]):null,xn(4,4,Os.bind(null,e,t),l)}function pi(){}function Ns(t,e){var l=$t();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&ri(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function ws(t,e){var l=$t();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&ri(e,a[1]))return a[0];if(a=t(),Fl){Zt(!0);try{t()}finally{Zt(!1)}}return l.memoizedState=[a,e],a}function Si(t,e,l){return l===void 0||(bl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=jo(),at.lanes|=t,zl|=t,l)}function Ms(t,e,l,a){return Ae(l,e)?l:za.current!==null?(t=Si(t,l,a),Ae(t,e)||(ne=!0),t):(bl&42)===0?(ne=!0,t.memoizedState=l):(t=jo(),at.lanes|=t,zl|=t,e)}function Us(t,e,l,a,n){var u=j.p;j.p=u!==0&&8>u?u:8;var c=x.T,i={};x.T=i,Ei(t,!1,e,l);try{var s=n(),y=x.S;if(y!==null&&y(i,s),s!==null&&typeof s=="object"&&typeof s.then=="function"){var E=gm(s,a);Tn(t,e,E,De(t))}else Tn(t,e,a,De(t))}catch(O){Tn(t,e,{then:function(){},status:"rejected",reason:O},De())}finally{j.p=u,x.T=c}}function Sm(){}function xi(t,e,l,a){if(t.tag!==5)throw Error(f(476));var n=Cs(t).queue;Us(t,n,e,w,l===null?Sm:function(){return js(t),l(a)})}function Cs(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:w,baseState:w,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ll,lastRenderedState:w},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ll,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function js(t){var e=Cs(t).next.queue;Tn(t,e,{},De())}function Ti(){return de(Gn)}function Hs(){return $t().memoizedState}function qs(){return $t().memoizedState}function xm(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=De();t=yl(l);var a=vl(e,t,l);a!==null&&(Ne(a,e,l),yn(a,e,l)),e={cache:Ic()},t.payload=e;return}e=e.return}}function Tm(t,e,l){var a=De();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Ru(t)?Ys(e,l):(l=Lc(t,e,l,a),l!==null&&(Ne(l,t,a),Gs(l,e,a)))}function Bs(t,e,l){var a=De();Tn(t,e,l,a)}function Tn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Ru(t))Ys(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var c=e.lastRenderedState,i=u(c,l);if(n.hasEagerState=!0,n.eagerState=i,Ae(i,c))return fu(t,e,n,0),Ht===null&&ru(),!1}catch{}finally{}if(l=Lc(t,e,n,a),l!==null)return Ne(l,t,a),Gs(l,e,a),!0}return!1}function Ei(t,e,l,a){if(a={lane:2,revertLane:er(),action:a,hasEagerState:!1,eagerState:null,next:null},Ru(t)){if(e)throw Error(f(479))}else e=Lc(t,l,a,2),e!==null&&Ne(e,t,2)}function Ru(t){var e=t.alternate;return t===at||e!==null&&e===at}function Ys(t,e){Ra=Su=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Gs(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,kr(t,l)}}var _u={readContext:de,use:Tu,useCallback:Vt,useContext:Vt,useEffect:Vt,useImperativeHandle:Vt,useLayoutEffect:Vt,useInsertionEffect:Vt,useMemo:Vt,useReducer:Vt,useRef:Vt,useState:Vt,useDebugValue:Vt,useDeferredValue:Vt,useTransition:Vt,useSyncExternalStore:Vt,useId:Vt,useHostTransitionStatus:Vt,useFormState:Vt,useActionState:Vt,useOptimistic:Vt,useMemoCache:Vt,useCacheRefresh:Vt},Xs={readContext:de,use:Tu,useCallback:function(t,e){return xe().memoizedState=[t,e===void 0?null:e],t},useContext:de,useEffect:As,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,zu(4194308,4,Os.bind(null,e,t),l)},useLayoutEffect:function(t,e){return zu(4194308,4,t,e)},useInsertionEffect:function(t,e){zu(4,2,t,e)},useMemo:function(t,e){var l=xe();e=e===void 0?null:e;var a=t();if(Fl){Zt(!0);try{t()}finally{Zt(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=xe();if(l!==void 0){var n=l(e);if(Fl){Zt(!0);try{l(e)}finally{Zt(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=Tm.bind(null,at,t),[a.memoizedState,t]},useRef:function(t){var e=xe();return t={current:t},e.memoizedState=t},useState:function(t){t=vi(t);var e=t.queue,l=Bs.bind(null,at,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:pi,useDeferredValue:function(t,e){var l=xe();return Si(l,t,e)},useTransition:function(){var t=vi(!1);return t=Us.bind(null,at,t.queue,!0,!1),xe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=at,n=xe();if(xt){if(l===void 0)throw Error(f(407));l=l()}else{if(l=e(),Ht===null)throw Error(f(349));(mt&124)!==0||rs(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,As(ss.bind(null,a,u,t),[t]),a.flags|=2048,Oa(9,Au(),fs.bind(null,a,u,l,e),null),l},useId:function(){var t=xe(),e=Ht.identifierPrefix;if(xt){var l=Pe,a=Ie;l=(a&~(1<<32-St(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=xu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=ym++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Ti,useFormState:ps,useActionState:ps,useOptimistic:function(t){var e=xe();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=Ei.bind(null,at,!0,l),l.dispatch=e,[t,e]},useMemoCache:hi,useCacheRefresh:function(){return xe().memoizedState=xm.bind(null,at)}},Ls={readContext:de,use:Tu,useCallback:Ns,useContext:de,useEffect:zs,useImperativeHandle:Ds,useInsertionEffect:Rs,useLayoutEffect:_s,useMemo:ws,useReducer:Eu,useRef:Es,useState:function(){return Eu(ll)},useDebugValue:pi,useDeferredValue:function(t,e){var l=$t();return Ms(l,Dt.memoizedState,t,e)},useTransition:function(){var t=Eu(ll)[0],e=$t().memoizedState;return[typeof t=="boolean"?t:Sn(t),e]},useSyncExternalStore:is,useId:Hs,useHostTransitionStatus:Ti,useFormState:Ss,useActionState:Ss,useOptimistic:function(t,e){var l=$t();return ms(l,Dt,t,e)},useMemoCache:hi,useCacheRefresh:qs},Em={readContext:de,use:Tu,useCallback:Ns,useContext:de,useEffect:zs,useImperativeHandle:Ds,useInsertionEffect:Rs,useLayoutEffect:_s,useMemo:ws,useReducer:yi,useRef:Es,useState:function(){return yi(ll)},useDebugValue:pi,useDeferredValue:function(t,e){var l=$t();return Dt===null?Si(l,t,e):Ms(l,Dt.memoizedState,t,e)},useTransition:function(){var t=yi(ll)[0],e=$t().memoizedState;return[typeof t=="boolean"?t:Sn(t),e]},useSyncExternalStore:is,useId:Hs,useHostTransitionStatus:Ti,useFormState:Ts,useActionState:Ts,useOptimistic:function(t,e){var l=$t();return Dt!==null?ms(l,Dt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:hi,useCacheRefresh:qs},Da=null,En=0;function Ou(t){var e=En;return En+=1,Da===null&&(Da=[]),Pf(Da,t,e)}function An(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Du(t,e){throw e.$$typeof===k?Error(f(525)):(t=Object.prototype.toString.call(e),Error(f(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Qs(t){var e=t._init;return e(t._payload)}function Zs(t){function e(h,m){if(t){var g=h.deletions;g===null?(h.deletions=[m],h.flags|=16):g.push(m)}}function l(h,m){if(!t)return null;for(;m!==null;)e(h,m),m=m.sibling;return null}function a(h){for(var m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function n(h,m){return h=Fe(h,m),h.index=0,h.sibling=null,h}function u(h,m,g){return h.index=g,t?(g=h.alternate,g!==null?(g=g.index,g<m?(h.flags|=67108866,m):g):(h.flags|=67108866,m)):(h.flags|=1048576,m)}function c(h){return t&&h.alternate===null&&(h.flags|=67108866),h}function i(h,m,g,z){return m===null||m.tag!==6?(m=Zc(g,h.mode,z),m.return=h,m):(m=n(m,g),m.return=h,m)}function s(h,m,g,z){var L=g.type;return L===ct?E(h,m,g.props.children,z,g.key):m!==null&&(m.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===X&&Qs(L)===m.type)?(m=n(m,g.props),An(m,g),m.return=h,m):(m=ou(g.type,g.key,g.props,null,h.mode,z),An(m,g),m.return=h,m)}function y(h,m,g,z){return m===null||m.tag!==4||m.stateNode.containerInfo!==g.containerInfo||m.stateNode.implementation!==g.implementation?(m=Vc(g,h.mode,z),m.return=h,m):(m=n(m,g.children||[]),m.return=h,m)}function E(h,m,g,z,L){return m===null||m.tag!==7?(m=Ql(g,h.mode,z,L),m.return=h,m):(m=n(m,g),m.return=h,m)}function O(h,m,g){if(typeof m=="string"&&m!==""||typeof m=="number"||typeof m=="bigint")return m=Zc(""+m,h.mode,g),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case V:return g=ou(m.type,m.key,m.props,null,h.mode,g),An(g,m),g.return=h,g;case F:return m=Vc(m,h.mode,g),m.return=h,m;case X:var z=m._init;return m=z(m._payload),O(h,m,g)}if(dt(m)||_t(m))return m=Ql(m,h.mode,g,null),m.return=h,m;if(typeof m.then=="function")return O(h,Ou(m),g);if(m.$$typeof===lt)return O(h,gu(h,m),g);Du(h,m)}return null}function b(h,m,g,z){var L=m!==null?m.key:null;if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return L!==null?null:i(h,m,""+g,z);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case V:return g.key===L?s(h,m,g,z):null;case F:return g.key===L?y(h,m,g,z):null;case X:return L=g._init,g=L(g._payload),b(h,m,g,z)}if(dt(g)||_t(g))return L!==null?null:E(h,m,g,z,null);if(typeof g.then=="function")return b(h,m,Ou(g),z);if(g.$$typeof===lt)return b(h,m,gu(h,g),z);Du(h,g)}return null}function p(h,m,g,z,L){if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return h=h.get(g)||null,i(m,h,""+z,L);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case V:return h=h.get(z.key===null?g:z.key)||null,s(m,h,z,L);case F:return h=h.get(z.key===null?g:z.key)||null,y(m,h,z,L);case X:var ut=z._init;return z=ut(z._payload),p(h,m,g,z,L)}if(dt(z)||_t(z))return h=h.get(g)||null,E(m,h,z,L,null);if(typeof z.then=="function")return p(h,m,g,Ou(z),L);if(z.$$typeof===lt)return p(h,m,g,gu(m,z),L);Du(m,z)}return null}function P(h,m,g,z){for(var L=null,ut=null,K=m,$=m=0,ce=null;K!==null&&$<g.length;$++){K.index>$?(ce=K,K=null):ce=K.sibling;var bt=b(h,K,g[$],z);if(bt===null){K===null&&(K=ce);break}t&&K&&bt.alternate===null&&e(h,K),m=u(bt,m,$),ut===null?L=bt:ut.sibling=bt,ut=bt,K=ce}if($===g.length)return l(h,K),xt&&Vl(h,$),L;if(K===null){for(;$<g.length;$++)K=O(h,g[$],z),K!==null&&(m=u(K,m,$),ut===null?L=K:ut.sibling=K,ut=K);return xt&&Vl(h,$),L}for(K=a(K);$<g.length;$++)ce=p(K,h,$,g[$],z),ce!==null&&(t&&ce.alternate!==null&&K.delete(ce.key===null?$:ce.key),m=u(ce,m,$),ut===null?L=ce:ut.sibling=ce,ut=ce);return t&&K.forEach(function(Cl){return e(h,Cl)}),xt&&Vl(h,$),L}function W(h,m,g,z){if(g==null)throw Error(f(151));for(var L=null,ut=null,K=m,$=m=0,ce=null,bt=g.next();K!==null&&!bt.done;$++,bt=g.next()){K.index>$?(ce=K,K=null):ce=K.sibling;var Cl=b(h,K,bt.value,z);if(Cl===null){K===null&&(K=ce);break}t&&K&&Cl.alternate===null&&e(h,K),m=u(Cl,m,$),ut===null?L=Cl:ut.sibling=Cl,ut=Cl,K=ce}if(bt.done)return l(h,K),xt&&Vl(h,$),L;if(K===null){for(;!bt.done;$++,bt=g.next())bt=O(h,bt.value,z),bt!==null&&(m=u(bt,m,$),ut===null?L=bt:ut.sibling=bt,ut=bt);return xt&&Vl(h,$),L}for(K=a(K);!bt.done;$++,bt=g.next())bt=p(K,h,$,bt.value,z),bt!==null&&(t&&bt.alternate!==null&&K.delete(bt.key===null?$:bt.key),m=u(bt,m,$),ut===null?L=bt:ut.sibling=bt,ut=bt);return t&&K.forEach(function(Ah){return e(h,Ah)}),xt&&Vl(h,$),L}function wt(h,m,g,z){if(typeof g=="object"&&g!==null&&g.type===ct&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case V:t:{for(var L=g.key;m!==null;){if(m.key===L){if(L=g.type,L===ct){if(m.tag===7){l(h,m.sibling),z=n(m,g.props.children),z.return=h,h=z;break t}}else if(m.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===X&&Qs(L)===m.type){l(h,m.sibling),z=n(m,g.props),An(z,g),z.return=h,h=z;break t}l(h,m);break}else e(h,m);m=m.sibling}g.type===ct?(z=Ql(g.props.children,h.mode,z,g.key),z.return=h,h=z):(z=ou(g.type,g.key,g.props,null,h.mode,z),An(z,g),z.return=h,h=z)}return c(h);case F:t:{for(L=g.key;m!==null;){if(m.key===L)if(m.tag===4&&m.stateNode.containerInfo===g.containerInfo&&m.stateNode.implementation===g.implementation){l(h,m.sibling),z=n(m,g.children||[]),z.return=h,h=z;break t}else{l(h,m);break}else e(h,m);m=m.sibling}z=Vc(g,h.mode,z),z.return=h,h=z}return c(h);case X:return L=g._init,g=L(g._payload),wt(h,m,g,z)}if(dt(g))return P(h,m,g,z);if(_t(g)){if(L=_t(g),typeof L!="function")throw Error(f(150));return g=L.call(g),W(h,m,g,z)}if(typeof g.then=="function")return wt(h,m,Ou(g),z);if(g.$$typeof===lt)return wt(h,m,gu(h,g),z);Du(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint"?(g=""+g,m!==null&&m.tag===6?(l(h,m.sibling),z=n(m,g),z.return=h,h=z):(l(h,m),z=Zc(g,h.mode,z),z.return=h,h=z),c(h)):l(h,m)}return function(h,m,g,z){try{En=0;var L=wt(h,m,g,z);return Da=null,L}catch(K){if(K===hn||K===vu)throw K;var ut=ze(29,K,null,h.mode);return ut.lanes=z,ut.return=h,ut}finally{}}}var Na=Zs(!0),Vs=Zs(!1),qe=A(null),Ve=null;function pl(t){var e=t.alternate;M(Pt,Pt.current&1),M(qe,t),Ve===null&&(e===null||za.current!==null||e.memoizedState!==null)&&(Ve=t)}function Ks(t){if(t.tag===22){if(M(Pt,Pt.current),M(qe,t),Ve===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ve=t)}}else Sl()}function Sl(){M(Pt,Pt.current),M(qe,qe.current)}function al(t){q(qe),Ve===t&&(Ve=null),q(Pt)}var Pt=A(0);function Nu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||mr(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Ai(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:C({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var zi={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=De(),n=yl(a);n.payload=e,l!=null&&(n.callback=l),e=vl(t,n,a),e!==null&&(Ne(e,t,a),yn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=De(),n=yl(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=vl(t,n,a),e!==null&&(Ne(e,t,a),yn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=De(),a=yl(l);a.tag=2,e!=null&&(a.callback=e),e=vl(t,a,l),e!==null&&(Ne(e,t,l),yn(e,t,l))}};function ks(t,e,l,a,n,u,c){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,c):e.prototype&&e.prototype.isPureReactComponent?!un(l,a)||!un(n,u):!0}function Js(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&zi.enqueueReplaceState(e,e.state,null)}function Il(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=C({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var wu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Ws(t){wu(t)}function $s(t){console.error(t)}function Fs(t){wu(t)}function Mu(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function Is(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Ri(t,e,l){return l=yl(l),l.tag=3,l.payload={element:null},l.callback=function(){Mu(t,e)},l}function Ps(t){return t=yl(t),t.tag=3,t}function to(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){Is(e,l,a)}}var c=l.stateNode;c!==null&&typeof c.componentDidCatch=="function"&&(t.callback=function(){Is(e,l,a),typeof n!="function"&&(Rl===null?Rl=new Set([this]):Rl.add(this));var i=a.stack;this.componentDidCatch(a.value,{componentStack:i!==null?i:""})})}function Am(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&on(e,l,n,!0),l=qe.current,l!==null){switch(l.tag){case 13:return Ve===null?$i():l.alternate===null&&Qt===0&&(Qt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===ei?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Ii(t,a,n)),!1;case 22:return l.flags|=65536,a===ei?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Ii(t,a,n)),!1}throw Error(f(435,l.tag))}return Ii(t,a,n),$i(),!1}if(xt)return e=qe.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Jc&&(t=Error(f(422),{cause:a}),sn(Ue(t,l)))):(a!==Jc&&(e=Error(f(423),{cause:a}),sn(Ue(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=Ue(a,l),n=Ri(t.stateNode,a,n),ni(t,n),Qt!==4&&(Qt=2)),!1;var u=Error(f(520),{cause:a});if(u=Ue(u,l),wn===null?wn=[u]:wn.push(u),Qt!==4&&(Qt=2),e===null)return!0;a=Ue(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=Ri(l.stateNode,a,t),ni(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Rl===null||!Rl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=Ps(n),to(n,t,l,a),ni(l,n),!1}l=l.return}while(l!==null);return!1}var eo=Error(f(461)),ne=!1;function re(t,e,l,a){e.child=t===null?Vs(e,null,l,a):Na(e,t.child,l,a)}function lo(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var c={};for(var i in a)i!=="ref"&&(c[i]=a[i])}else c=a;return Wl(e),a=fi(t,e,l,c,u,n),i=si(),t!==null&&!ne?(oi(t,e,n),nl(t,e,n)):(xt&&i&&Kc(e),e.flags|=1,re(t,e,a,n),e.child)}function ao(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!Qc(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,no(t,e,u,a,n)):(t=ou(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Ci(t,n)){var c=u.memoizedProps;if(l=l.compare,l=l!==null?l:un,l(c,a)&&t.ref===e.ref)return nl(t,e,n)}return e.flags|=1,t=Fe(u,a),t.ref=e.ref,t.return=e,e.child=t}function no(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(un(u,a)&&t.ref===e.ref)if(ne=!1,e.pendingProps=a=u,Ci(t,n))(t.flags&131072)!==0&&(ne=!0);else return e.lanes=t.lanes,nl(t,e,n)}return _i(t,e,l,a,n)}function uo(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return co(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&yu(e,u!==null?u.cachePool:null),u!==null?ns(e,u):ci(),Ks(e);else return e.lanes=e.childLanes=536870912,co(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(yu(e,u.cachePool),ns(e,u),Sl(),e.memoizedState=null):(t!==null&&yu(e,null),ci(),Sl());return re(t,e,n,l),e.child}function co(t,e,l,a){var n=ti();return n=n===null?null:{parent:It._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&yu(e,null),ci(),Ks(e),t!==null&&on(t,e,a,!0),null}function Uu(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(f(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function _i(t,e,l,a,n){return Wl(e),l=fi(t,e,l,a,void 0,n),a=si(),t!==null&&!ne?(oi(t,e,n),nl(t,e,n)):(xt&&a&&Kc(e),e.flags|=1,re(t,e,l,n),e.child)}function io(t,e,l,a,n,u){return Wl(e),e.updateQueue=null,l=cs(e,a,l,n),us(t),a=si(),t!==null&&!ne?(oi(t,e,u),nl(t,e,u)):(xt&&a&&Kc(e),e.flags|=1,re(t,e,l,u),e.child)}function ro(t,e,l,a,n){if(Wl(e),e.stateNode===null){var u=Sa,c=l.contextType;typeof c=="object"&&c!==null&&(u=de(c)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=zi,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},li(e),c=l.contextType,u.context=typeof c=="object"&&c!==null?de(c):Sa,u.state=e.memoizedState,c=l.getDerivedStateFromProps,typeof c=="function"&&(Ai(e,l,c,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(c=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),c!==u.state&&zi.enqueueReplaceState(u,u.state,null),bn(e,a,u,n),vn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var i=e.memoizedProps,s=Il(l,i);u.props=s;var y=u.context,E=l.contextType;c=Sa,typeof E=="object"&&E!==null&&(c=de(E));var O=l.getDerivedStateFromProps;E=typeof O=="function"||typeof u.getSnapshotBeforeUpdate=="function",i=e.pendingProps!==i,E||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(i||y!==c)&&Js(e,u,a,c),gl=!1;var b=e.memoizedState;u.state=b,bn(e,a,u,n),vn(),y=e.memoizedState,i||b!==y||gl?(typeof O=="function"&&(Ai(e,l,O,a),y=e.memoizedState),(s=gl||ks(e,l,s,a,b,y,c))?(E||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=y),u.props=a,u.state=y,u.context=c,a=s):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,ai(t,e),c=e.memoizedProps,E=Il(l,c),u.props=E,O=e.pendingProps,b=u.context,y=l.contextType,s=Sa,typeof y=="object"&&y!==null&&(s=de(y)),i=l.getDerivedStateFromProps,(y=typeof i=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(c!==O||b!==s)&&Js(e,u,a,s),gl=!1,b=e.memoizedState,u.state=b,bn(e,a,u,n),vn();var p=e.memoizedState;c!==O||b!==p||gl||t!==null&&t.dependencies!==null&&hu(t.dependencies)?(typeof i=="function"&&(Ai(e,l,i,a),p=e.memoizedState),(E=gl||ks(e,l,E,a,b,p,s)||t!==null&&t.dependencies!==null&&hu(t.dependencies))?(y||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,p,s),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,p,s)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||c===t.memoizedProps&&b===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||c===t.memoizedProps&&b===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=p),u.props=a,u.state=p,u.context=s,a=E):(typeof u.componentDidUpdate!="function"||c===t.memoizedProps&&b===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||c===t.memoizedProps&&b===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Uu(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=Na(e,t.child,null,n),e.child=Na(e,null,l,n)):re(t,e,l,n),e.memoizedState=u.state,t=e.child):t=nl(t,e,n),t}function fo(t,e,l,a){return fn(),e.flags|=256,re(t,e,l,a),e.child}var Oi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Di(t){return{baseLanes:t,cachePool:$f()}}function Ni(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Be),t}function so(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,c;if((c=u)||(c=t!==null&&t.memoizedState===null?!1:(Pt.current&2)!==0),c&&(n=!0,e.flags&=-129),c=(e.flags&32)!==0,e.flags&=-33,t===null){if(xt){if(n?pl(e):Sl(),xt){var i=Lt,s;if(s=i){t:{for(s=i,i=Ze;s.nodeType!==8;){if(!i){i=null;break t}if(s=Le(s.nextSibling),s===null){i=null;break t}}i=s}i!==null?(e.memoizedState={dehydrated:i,treeContext:Zl!==null?{id:Ie,overflow:Pe}:null,retryLane:536870912,hydrationErrors:null},s=ze(18,null,null,0),s.stateNode=i,s.return=e,e.child=s,ye=e,Lt=null,s=!0):s=!1}s||kl(e)}if(i=e.memoizedState,i!==null&&(i=i.dehydrated,i!==null))return mr(i)?e.lanes=32:e.lanes=536870912,null;al(e)}return i=a.children,a=a.fallback,n?(Sl(),n=e.mode,i=Cu({mode:"hidden",children:i},n),a=Ql(a,n,l,null),i.return=e,a.return=e,i.sibling=a,e.child=i,n=e.child,n.memoizedState=Di(l),n.childLanes=Ni(t,c,l),e.memoizedState=Oi,a):(pl(e),wi(e,i))}if(s=t.memoizedState,s!==null&&(i=s.dehydrated,i!==null)){if(u)e.flags&256?(pl(e),e.flags&=-257,e=Mi(t,e,l)):e.memoizedState!==null?(Sl(),e.child=t.child,e.flags|=128,e=null):(Sl(),n=a.fallback,i=e.mode,a=Cu({mode:"visible",children:a.children},i),n=Ql(n,i,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Na(e,t.child,null,l),a=e.child,a.memoizedState=Di(l),a.childLanes=Ni(t,c,l),e.memoizedState=Oi,e=n);else if(pl(e),mr(i)){if(c=i.nextSibling&&i.nextSibling.dataset,c)var y=c.dgst;c=y,a=Error(f(419)),a.stack="",a.digest=c,sn({value:a,source:null,stack:null}),e=Mi(t,e,l)}else if(ne||on(t,e,l,!1),c=(l&t.childLanes)!==0,ne||c){if(c=Ht,c!==null&&(a=l&-l,a=(a&42)!==0?1:hc(a),a=(a&(c.suspendedLanes|l))!==0?0:a,a!==0&&a!==s.retryLane))throw s.retryLane=a,pa(t,a),Ne(c,t,a),eo;i.data==="$?"||$i(),e=Mi(t,e,l)}else i.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=s.treeContext,Lt=Le(i.nextSibling),ye=e,xt=!0,Kl=null,Ze=!1,t!==null&&(je[He++]=Ie,je[He++]=Pe,je[He++]=Zl,Ie=t.id,Pe=t.overflow,Zl=e),e=wi(e,a.children),e.flags|=4096);return e}return n?(Sl(),n=a.fallback,i=e.mode,s=t.child,y=s.sibling,a=Fe(s,{mode:"hidden",children:a.children}),a.subtreeFlags=s.subtreeFlags&65011712,y!==null?n=Fe(y,n):(n=Ql(n,i,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,i=t.child.memoizedState,i===null?i=Di(l):(s=i.cachePool,s!==null?(y=It._currentValue,s=s.parent!==y?{parent:y,pool:y}:s):s=$f(),i={baseLanes:i.baseLanes|l,cachePool:s}),n.memoizedState=i,n.childLanes=Ni(t,c,l),e.memoizedState=Oi,a):(pl(e),l=t.child,t=l.sibling,l=Fe(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(c=e.deletions,c===null?(e.deletions=[t],e.flags|=16):c.push(t)),e.child=l,e.memoizedState=null,l)}function wi(t,e){return e=Cu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Cu(t,e){return t=ze(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Mi(t,e,l){return Na(e,t.child,null,l),t=wi(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function oo(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),$c(t.return,e,l)}function Ui(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function mo(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(re(t,e,a.children,l),a=Pt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&oo(t,l,e);else if(t.tag===19)oo(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(M(Pt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&Nu(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),Ui(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Nu(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}Ui(e,!0,l,null,u);break;case"together":Ui(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function nl(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),zl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(on(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(f(153));if(e.child!==null){for(t=e.child,l=Fe(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Fe(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Ci(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&hu(t)))}function zm(t,e,l){switch(e.tag){case 3:I(e,e.stateNode.containerInfo),hl(e,It,t.memoizedState.cache),fn();break;case 27:case 5:he(e);break;case 4:I(e,e.stateNode.containerInfo);break;case 10:hl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(pl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?so(t,e,l):(pl(e),t=nl(t,e,l),t!==null?t.sibling:null);pl(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(on(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return mo(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),M(Pt,Pt.current),a)break;return null;case 22:case 23:return e.lanes=0,uo(t,e,l);case 24:hl(e,It,t.memoizedState.cache)}return nl(t,e,l)}function ho(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)ne=!0;else{if(!Ci(t,l)&&(e.flags&128)===0)return ne=!1,zm(t,e,l);ne=(t.flags&131072)!==0}else ne=!1,xt&&(e.flags&1048576)!==0&&Qf(e,mu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Qc(a)?(t=Il(a,t),e.tag=1,e=ro(null,e,a,t,l)):(e.tag=0,e=_i(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===pt){e.tag=11,e=lo(null,e,a,t,l);break t}else if(n===vt){e.tag=14,e=ao(null,e,a,t,l);break t}}throw e=Ot(a)||a,Error(f(306,e,""))}}return e;case 0:return _i(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Il(a,e.pendingProps),ro(t,e,a,n,l);case 3:t:{if(I(e,e.stateNode.containerInfo),t===null)throw Error(f(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,ai(t,e),bn(e,a,null,l);var c=e.memoizedState;if(a=c.cache,hl(e,It,a),a!==u.cache&&Fc(e,[It],l,!0),vn(),a=c.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:c.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=fo(t,e,a,l);break t}else if(a!==n){n=Ue(Error(f(424)),e),sn(n),e=fo(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Lt=Le(t.firstChild),ye=e,xt=!0,Kl=null,Ze=!0,l=Vs(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(fn(),a===n){e=nl(t,e,l);break t}re(t,e,a,l)}e=e.child}return e;case 26:return Uu(t,e),t===null?(l=bd(e.type,null,e.pendingProps,null))?e.memoizedState=l:xt||(l=e.type,t=e.pendingProps,a=Ju(G.current).createElement(l),a[oe]=e,a[pe]=t,se(a,l,t),ae(a),e.stateNode=a):e.memoizedState=bd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return he(e),t===null&&xt&&(a=e.stateNode=gd(e.type,e.pendingProps,G.current),ye=e,Ze=!0,n=Lt,Dl(e.type)?(hr=n,Lt=Le(a.firstChild)):Lt=n),re(t,e,e.pendingProps.children,l),Uu(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&xt&&((n=a=Lt)&&(a=Pm(a,e.type,e.pendingProps,Ze),a!==null?(e.stateNode=a,ye=e,Lt=Le(a.firstChild),Ze=!1,n=!0):n=!1),n||kl(e)),he(e),n=e.type,u=e.pendingProps,c=t!==null?t.memoizedProps:null,a=u.children,sr(n,u)?a=null:c!==null&&sr(n,c)&&(e.flags|=32),e.memoizedState!==null&&(n=fi(t,e,vm,null,null,l),Gn._currentValue=n),Uu(t,e),re(t,e,a,l),e.child;case 6:return t===null&&xt&&((t=l=Lt)&&(l=th(l,e.pendingProps,Ze),l!==null?(e.stateNode=l,ye=e,Lt=null,t=!0):t=!1),t||kl(e)),null;case 13:return so(t,e,l);case 4:return I(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Na(e,null,a,l):re(t,e,a,l),e.child;case 11:return lo(t,e,e.type,e.pendingProps,l);case 7:return re(t,e,e.pendingProps,l),e.child;case 8:return re(t,e,e.pendingProps.children,l),e.child;case 12:return re(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,hl(e,e.type,a.value),re(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,Wl(e),n=de(n),a=a(n),e.flags|=1,re(t,e,a,l),e.child;case 14:return ao(t,e,e.type,e.pendingProps,l);case 15:return no(t,e,e.type,e.pendingProps,l);case 19:return mo(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=Cu(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Fe(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return uo(t,e,l);case 24:return Wl(e),a=de(It),t===null?(n=ti(),n===null&&(n=Ht,u=Ic(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},li(e),hl(e,It,n)):((t.lanes&l)!==0&&(ai(t,e),bn(e,null,null,l),vn()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),hl(e,It,a)):(a=u.cache,hl(e,It,a),a!==n.cache&&Fc(e,[It],l,!0))),re(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(f(156,e.tag))}function ul(t){t.flags|=4}function go(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Ed(e)){if(e=qe.current,e!==null&&((mt&4194048)===mt?Ve!==null:(mt&62914560)!==mt&&(mt&536870912)===0||e!==Ve))throw gn=ei,Ff;t.flags|=8192}}function ju(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Vr():536870912,t.lanes|=e,Ca|=e)}function zn(t,e){if(!xt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Xt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function Rm(t,e,l){var a=e.pendingProps;switch(kc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Xt(e),null;case 1:return Xt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),el(It),jt(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(rn(e)?ul(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Kf())),Xt(e),null;case 26:return l=e.memoizedState,t===null?(ul(e),l!==null?(Xt(e),go(e,l)):(Xt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(ul(e),Xt(e),go(e,l)):(Xt(e),e.flags&=-16777217):(t.memoizedProps!==a&&ul(e),Xt(e),e.flags&=-16777217),null;case 27:ge(e),l=G.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&ul(e);else{if(!a){if(e.stateNode===null)throw Error(f(166));return Xt(e),null}t=Y.current,rn(e)?Zf(e):(t=gd(n,a,l),e.stateNode=t,ul(e))}return Xt(e),null;case 5:if(ge(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&ul(e);else{if(!a){if(e.stateNode===null)throw Error(f(166));return Xt(e),null}if(t=Y.current,rn(e))Zf(e);else{switch(n=Ju(G.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[oe]=e,t[pe]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(se(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&ul(e)}}return Xt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&ul(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(f(166));if(t=G.current,rn(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=ye,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[oe]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||rd(t.nodeValue,l)),t||kl(e)}else t=Ju(t).createTextNode(a),t[oe]=e,e.stateNode=t}return Xt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=rn(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(f(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(f(317));n[oe]=e}else fn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Xt(e),n=!1}else n=Kf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(al(e),e):(al(e),null)}if(al(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),ju(e,e.updateQueue),Xt(e),null;case 4:return jt(),t===null&&ur(e.stateNode.containerInfo),Xt(e),null;case 10:return el(e.type),Xt(e),null;case 19:if(q(Pt),n=e.memoizedState,n===null)return Xt(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)zn(n,!1);else{if(Qt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Nu(t),u!==null){for(e.flags|=128,zn(n,!1),t=u.updateQueue,e.updateQueue=t,ju(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Lf(l,t),l=l.sibling;return M(Pt,Pt.current&1|2),e.child}t=t.sibling}n.tail!==null&&ve()>Bu&&(e.flags|=128,a=!0,zn(n,!1),e.lanes=4194304)}else{if(!a)if(t=Nu(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ju(e,t),zn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!xt)return Xt(e),null}else 2*ve()-n.renderingStartTime>Bu&&l!==536870912&&(e.flags|=128,a=!0,zn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=ve(),e.sibling=null,t=Pt.current,M(Pt,a?t&1|2:t&1),e):(Xt(e),null);case 22:case 23:return al(e),ii(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Xt(e),e.subtreeFlags&6&&(e.flags|=8192)):Xt(e),l=e.updateQueue,l!==null&&ju(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&q($l),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),el(It),Xt(e),null;case 25:return null;case 30:return null}throw Error(f(156,e.tag))}function _m(t,e){switch(kc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return el(It),jt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return ge(e),null;case 13:if(al(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(f(340));fn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return q(Pt),null;case 4:return jt(),null;case 10:return el(e.type),null;case 22:case 23:return al(e),ii(),t!==null&&q($l),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return el(It),null;case 25:return null;default:return null}}function yo(t,e){switch(kc(e),e.tag){case 3:el(It),jt();break;case 26:case 27:case 5:ge(e);break;case 4:jt();break;case 13:al(e);break;case 19:q(Pt);break;case 10:el(e.type);break;case 22:case 23:al(e),ii(),t!==null&&q($l);break;case 24:el(It)}}function Rn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,c=l.inst;a=u(),c.destroy=a}l=l.next}while(l!==n)}}catch(i){Ut(e,e.return,i)}}function xl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var c=a.inst,i=c.destroy;if(i!==void 0){c.destroy=void 0,n=e;var s=l,y=i;try{y()}catch(E){Ut(n,s,E)}}}a=a.next}while(a!==u)}}catch(E){Ut(e,e.return,E)}}function vo(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{as(e,l)}catch(a){Ut(t,t.return,a)}}}function bo(t,e,l){l.props=Il(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Ut(t,e,a)}}function _n(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Ut(t,e,n)}}function Ke(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Ut(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Ut(t,e,n)}else l.current=null}function po(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Ut(t,t.return,n)}}function ji(t,e,l){try{var a=t.stateNode;Jm(a,t.type,l,e),a[pe]=e}catch(n){Ut(t,t.return,n)}}function So(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Dl(t.type)||t.tag===4}function Hi(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||So(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Dl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function qi(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=ku));else if(a!==4&&(a===27&&Dl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(qi(t,e,l),t=t.sibling;t!==null;)qi(t,e,l),t=t.sibling}function Hu(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&Dl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Hu(t,e,l),t=t.sibling;t!==null;)Hu(t,e,l),t=t.sibling}function xo(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);se(e,a,l),e[oe]=t,e[pe]=l}catch(u){Ut(t,t.return,u)}}var cl=!1,Kt=!1,Bi=!1,To=typeof WeakSet=="function"?WeakSet:Set,ue=null;function Om(t,e){if(t=t.containerInfo,rr=tc,t=Mf(t),Hc(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var c=0,i=-1,s=-1,y=0,E=0,O=t,b=null;e:for(;;){for(var p;O!==l||n!==0&&O.nodeType!==3||(i=c+n),O!==u||a!==0&&O.nodeType!==3||(s=c+a),O.nodeType===3&&(c+=O.nodeValue.length),(p=O.firstChild)!==null;)b=O,O=p;for(;;){if(O===t)break e;if(b===l&&++y===n&&(i=c),b===u&&++E===a&&(s=c),(p=O.nextSibling)!==null)break;O=b,b=O.parentNode}O=p}l=i===-1||s===-1?null:{start:i,end:s}}else l=null}l=l||{start:0,end:0}}else l=null;for(fr={focusedElem:t,selectionRange:l},tc=!1,ue=e;ue!==null;)if(e=ue,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,ue=t;else for(;ue!==null;){switch(e=ue,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var P=Il(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(P,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(W){Ut(l,l.return,W)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)dr(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":dr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(f(163))}if(t=e.sibling,t!==null){t.return=e.return,ue=t;break}ue=e.return}}function Eo(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Tl(t,l),a&4&&Rn(5,l);break;case 1:if(Tl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(c){Ut(l,l.return,c)}else{var n=Il(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(c){Ut(l,l.return,c)}}a&64&&vo(l),a&512&&_n(l,l.return);break;case 3:if(Tl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{as(t,e)}catch(c){Ut(l,l.return,c)}}break;case 27:e===null&&a&4&&xo(l);case 26:case 5:Tl(t,l),e===null&&a&4&&po(l),a&512&&_n(l,l.return);break;case 12:Tl(t,l);break;case 13:Tl(t,l),a&4&&Ro(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=qm.bind(null,l),eh(t,l))));break;case 22:if(a=l.memoizedState!==null||cl,!a){e=e!==null&&e.memoizedState!==null||Kt,n=cl;var u=Kt;cl=a,(Kt=e)&&!u?El(t,l,(l.subtreeFlags&8772)!==0):Tl(t,l),cl=n,Kt=u}break;case 30:break;default:Tl(t,l)}}function Ao(t){var e=t.alternate;e!==null&&(t.alternate=null,Ao(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&vc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Bt=null,Te=!1;function il(t,e,l){for(l=l.child;l!==null;)zo(t,e,l),l=l.sibling}function zo(t,e,l){if(st&&typeof st.onCommitFiberUnmount=="function")try{st.onCommitFiberUnmount(qt,l)}catch{}switch(l.tag){case 26:Kt||Ke(l,e),il(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Kt||Ke(l,e);var a=Bt,n=Te;Dl(l.type)&&(Bt=l.stateNode,Te=!1),il(t,e,l),Hn(l.stateNode),Bt=a,Te=n;break;case 5:Kt||Ke(l,e);case 6:if(a=Bt,n=Te,Bt=null,il(t,e,l),Bt=a,Te=n,Bt!==null)if(Te)try{(Bt.nodeType===9?Bt.body:Bt.nodeName==="HTML"?Bt.ownerDocument.body:Bt).removeChild(l.stateNode)}catch(u){Ut(l,e,u)}else try{Bt.removeChild(l.stateNode)}catch(u){Ut(l,e,u)}break;case 18:Bt!==null&&(Te?(t=Bt,md(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Zn(t)):md(Bt,l.stateNode));break;case 4:a=Bt,n=Te,Bt=l.stateNode.containerInfo,Te=!0,il(t,e,l),Bt=a,Te=n;break;case 0:case 11:case 14:case 15:Kt||xl(2,l,e),Kt||xl(4,l,e),il(t,e,l);break;case 1:Kt||(Ke(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&bo(l,e,a)),il(t,e,l);break;case 21:il(t,e,l);break;case 22:Kt=(a=Kt)||l.memoizedState!==null,il(t,e,l),Kt=a;break;default:il(t,e,l)}}function Ro(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Zn(t)}catch(l){Ut(e,e.return,l)}}function Dm(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new To),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new To),e;default:throw Error(f(435,t.tag))}}function Yi(t,e){var l=Dm(t);e.forEach(function(a){var n=Bm.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function Re(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,c=e,i=c;t:for(;i!==null;){switch(i.tag){case 27:if(Dl(i.type)){Bt=i.stateNode,Te=!1;break t}break;case 5:Bt=i.stateNode,Te=!1;break t;case 3:case 4:Bt=i.stateNode.containerInfo,Te=!0;break t}i=i.return}if(Bt===null)throw Error(f(160));zo(u,c,n),Bt=null,Te=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)_o(e,t),e=e.sibling}var Xe=null;function _o(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Re(e,t),_e(t),a&4&&(xl(3,t,t.return),Rn(3,t),xl(5,t,t.return));break;case 1:Re(e,t),_e(t),a&512&&(Kt||l===null||Ke(l,l.return)),a&64&&cl&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Xe;if(Re(e,t),_e(t),a&512&&(Kt||l===null||Ke(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[$a]||u[oe]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),se(u,a,l),u[oe]=t,ae(u),a=u;break t;case"link":var c=xd("link","href",n).get(a+(l.href||""));if(c){for(var i=0;i<c.length;i++)if(u=c[i],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){c.splice(i,1);break e}}u=n.createElement(a),se(u,a,l),n.head.appendChild(u);break;case"meta":if(c=xd("meta","content",n).get(a+(l.content||""))){for(i=0;i<c.length;i++)if(u=c[i],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){c.splice(i,1);break e}}u=n.createElement(a),se(u,a,l),n.head.appendChild(u);break;default:throw Error(f(468,a))}u[oe]=t,ae(u),a=u}t.stateNode=a}else Td(n,t.type,t.stateNode);else t.stateNode=Sd(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?Td(n,t.type,t.stateNode):Sd(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&ji(t,t.memoizedProps,l.memoizedProps)}break;case 27:Re(e,t),_e(t),a&512&&(Kt||l===null||Ke(l,l.return)),l!==null&&a&4&&ji(t,t.memoizedProps,l.memoizedProps);break;case 5:if(Re(e,t),_e(t),a&512&&(Kt||l===null||Ke(l,l.return)),t.flags&32){n=t.stateNode;try{da(n,"")}catch(p){Ut(t,t.return,p)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,ji(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Bi=!0);break;case 6:if(Re(e,t),_e(t),a&4){if(t.stateNode===null)throw Error(f(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(p){Ut(t,t.return,p)}}break;case 3:if(Fu=null,n=Xe,Xe=Wu(e.containerInfo),Re(e,t),Xe=n,_e(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Zn(e.containerInfo)}catch(p){Ut(t,t.return,p)}Bi&&(Bi=!1,Oo(t));break;case 4:a=Xe,Xe=Wu(t.stateNode.containerInfo),Re(e,t),_e(t),Xe=a;break;case 12:Re(e,t),_e(t);break;case 13:Re(e,t),_e(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Vi=ve()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Yi(t,a)));break;case 22:n=t.memoizedState!==null;var s=l!==null&&l.memoizedState!==null,y=cl,E=Kt;if(cl=y||n,Kt=E||s,Re(e,t),Kt=E,cl=y,_e(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||s||cl||Kt||Pl(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){s=l=e;try{if(u=s.stateNode,n)c=u.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none";else{i=s.stateNode;var O=s.memoizedProps.style,b=O!=null&&O.hasOwnProperty("display")?O.display:null;i.style.display=b==null||typeof b=="boolean"?"":(""+b).trim()}}catch(p){Ut(s,s.return,p)}}}else if(e.tag===6){if(l===null){s=e;try{s.stateNode.nodeValue=n?"":s.memoizedProps}catch(p){Ut(s,s.return,p)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Yi(t,l))));break;case 19:Re(e,t),_e(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Yi(t,a)));break;case 30:break;case 21:break;default:Re(e,t),_e(t)}}function _e(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(So(a)){l=a;break}a=a.return}if(l==null)throw Error(f(160));switch(l.tag){case 27:var n=l.stateNode,u=Hi(t);Hu(t,u,n);break;case 5:var c=l.stateNode;l.flags&32&&(da(c,""),l.flags&=-33);var i=Hi(t);Hu(t,i,c);break;case 3:case 4:var s=l.stateNode.containerInfo,y=Hi(t);qi(t,y,s);break;default:throw Error(f(161))}}catch(E){Ut(t,t.return,E)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Oo(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Oo(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Tl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Eo(t,e.alternate,e),e=e.sibling}function Pl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:xl(4,e,e.return),Pl(e);break;case 1:Ke(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&bo(e,e.return,l),Pl(e);break;case 27:Hn(e.stateNode);case 26:case 5:Ke(e,e.return),Pl(e);break;case 22:e.memoizedState===null&&Pl(e);break;case 30:Pl(e);break;default:Pl(e)}t=t.sibling}}function El(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,c=u.flags;switch(u.tag){case 0:case 11:case 15:El(n,u,l),Rn(4,u);break;case 1:if(El(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(y){Ut(a,a.return,y)}if(a=u,n=a.updateQueue,n!==null){var i=a.stateNode;try{var s=n.shared.hiddenCallbacks;if(s!==null)for(n.shared.hiddenCallbacks=null,n=0;n<s.length;n++)ls(s[n],i)}catch(y){Ut(a,a.return,y)}}l&&c&64&&vo(u),_n(u,u.return);break;case 27:xo(u);case 26:case 5:El(n,u,l),l&&a===null&&c&4&&po(u),_n(u,u.return);break;case 12:El(n,u,l);break;case 13:El(n,u,l),l&&c&4&&Ro(n,u);break;case 22:u.memoizedState===null&&El(n,u,l),_n(u,u.return);break;case 30:break;default:El(n,u,l)}e=e.sibling}}function Gi(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&dn(l))}function Xi(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&dn(t))}function ke(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Do(t,e,l,a),e=e.sibling}function Do(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:ke(t,e,l,a),n&2048&&Rn(9,e);break;case 1:ke(t,e,l,a);break;case 3:ke(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&dn(t)));break;case 12:if(n&2048){ke(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,c=u.id,i=u.onPostCommit;typeof i=="function"&&i(c,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(s){Ut(e,e.return,s)}}else ke(t,e,l,a);break;case 13:ke(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,c=e.alternate,e.memoizedState!==null?u._visibility&2?ke(t,e,l,a):On(t,e):u._visibility&2?ke(t,e,l,a):(u._visibility|=2,wa(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Gi(c,e);break;case 24:ke(t,e,l,a),n&2048&&Xi(e.alternate,e);break;default:ke(t,e,l,a)}}function wa(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,c=e,i=l,s=a,y=c.flags;switch(c.tag){case 0:case 11:case 15:wa(u,c,i,s,n),Rn(8,c);break;case 23:break;case 22:var E=c.stateNode;c.memoizedState!==null?E._visibility&2?wa(u,c,i,s,n):On(u,c):(E._visibility|=2,wa(u,c,i,s,n)),n&&y&2048&&Gi(c.alternate,c);break;case 24:wa(u,c,i,s,n),n&&y&2048&&Xi(c.alternate,c);break;default:wa(u,c,i,s,n)}e=e.sibling}}function On(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:On(l,a),n&2048&&Gi(a.alternate,a);break;case 24:On(l,a),n&2048&&Xi(a.alternate,a);break;default:On(l,a)}e=e.sibling}}var Dn=8192;function Ma(t){if(t.subtreeFlags&Dn)for(t=t.child;t!==null;)No(t),t=t.sibling}function No(t){switch(t.tag){case 26:Ma(t),t.flags&Dn&&t.memoizedState!==null&&hh(Xe,t.memoizedState,t.memoizedProps);break;case 5:Ma(t);break;case 3:case 4:var e=Xe;Xe=Wu(t.stateNode.containerInfo),Ma(t),Xe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Dn,Dn=16777216,Ma(t),Dn=e):Ma(t));break;default:Ma(t)}}function wo(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Nn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];ue=a,Uo(a,t)}wo(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Mo(t),t=t.sibling}function Mo(t){switch(t.tag){case 0:case 11:case 15:Nn(t),t.flags&2048&&xl(9,t,t.return);break;case 3:Nn(t);break;case 12:Nn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,qu(t)):Nn(t);break;default:Nn(t)}}function qu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];ue=a,Uo(a,t)}wo(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:xl(8,e,e.return),qu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,qu(e));break;default:qu(e)}t=t.sibling}}function Uo(t,e){for(;ue!==null;){var l=ue;switch(l.tag){case 0:case 11:case 15:xl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:dn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,ue=a;else t:for(l=t;ue!==null;){a=ue;var n=a.sibling,u=a.return;if(Ao(a),a===l){ue=null;break t}if(n!==null){n.return=u,ue=n;break t}ue=u}}}var Nm={getCacheForType:function(t){var e=de(It),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},wm=typeof WeakMap=="function"?WeakMap:Map,At=0,Ht=null,rt=null,mt=0,zt=0,Oe=null,Al=!1,Ua=!1,Li=!1,rl=0,Qt=0,zl=0,ta=0,Qi=0,Be=0,Ca=0,wn=null,Ee=null,Zi=!1,Vi=0,Bu=1/0,Yu=null,Rl=null,fe=0,_l=null,ja=null,Ha=0,Ki=0,ki=null,Co=null,Mn=0,Ji=null;function De(){if((At&2)!==0&&mt!==0)return mt&-mt;if(x.T!==null){var t=Ea;return t!==0?t:er()}return Jr()}function jo(){Be===0&&(Be=(mt&536870912)===0||xt?Zr():536870912);var t=qe.current;return t!==null&&(t.flags|=32),Be}function Ne(t,e,l){(t===Ht&&(zt===2||zt===9)||t.cancelPendingCommit!==null)&&(qa(t,0),Ol(t,mt,Be,!1)),Wa(t,l),((At&2)===0||t!==Ht)&&(t===Ht&&((At&2)===0&&(ta|=l),Qt===4&&Ol(t,mt,Be,!1)),Je(t))}function Ho(t,e,l){if((At&6)!==0)throw Error(f(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||Ja(t,e),n=a?Cm(t,e):Fi(t,e,!0),u=a;do{if(n===0){Ua&&!a&&Ol(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!Mm(l)){n=Fi(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var c=0;else c=t.pendingLanes&-536870913,c=c!==0?c:c&536870912?536870912:0;if(c!==0){e=c;t:{var i=t;n=wn;var s=i.current.memoizedState.isDehydrated;if(s&&(qa(i,c).flags|=256),c=Fi(i,c,!1),c!==2){if(Li&&!s){i.errorRecoveryDisabledLanes|=u,ta|=u,n=4;break t}u=Ee,Ee=n,u!==null&&(Ee===null?Ee=u:Ee.push.apply(Ee,u))}n=c}if(u=!1,n!==2)continue}}if(n===1){qa(t,0),Ol(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(f(345));case 4:if((e&4194048)!==e)break;case 6:Ol(a,e,Be,!Al);break t;case 2:Ee=null;break;case 3:case 5:break;default:throw Error(f(329))}if((e&62914560)===e&&(n=Vi+300-ve(),10<n)){if(Ol(a,e,Be,!Al),$n(a,0,!0)!==0)break t;a.timeoutHandle=od(qo.bind(null,a,l,Ee,Yu,Zi,e,Be,ta,Ca,Al,u,2,-0,0),n);break t}qo(a,l,Ee,Yu,Zi,e,Be,ta,Ca,Al,u,0,-0,0)}}break}while(!0);Je(t)}function qo(t,e,l,a,n,u,c,i,s,y,E,O,b,p){if(t.timeoutHandle=-1,O=e.subtreeFlags,(O&8192||(O&16785408)===16785408)&&(Yn={stylesheets:null,count:0,unsuspend:mh},No(e),O=gh(),O!==null)){t.cancelPendingCommit=O(Zo.bind(null,t,e,u,l,a,n,c,i,s,E,1,b,p)),Ol(t,u,c,!y);return}Zo(t,e,u,l,a,n,c,i,s)}function Mm(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!Ae(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Ol(t,e,l,a){e&=~Qi,e&=~ta,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-St(n),c=1<<u;a[u]=-1,n&=~c}l!==0&&Kr(t,l,e)}function Gu(){return(At&6)===0?(Un(0),!1):!0}function Wi(){if(rt!==null){if(zt===0)var t=rt.return;else t=rt,tl=Jl=null,di(t),Da=null,En=0,t=rt;for(;t!==null;)yo(t.alternate,t),t=t.return;rt=null}}function qa(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,$m(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Wi(),Ht=t,rt=l=Fe(t.current,null),mt=e,zt=0,Oe=null,Al=!1,Ua=Ja(t,e),Li=!1,Ca=Be=Qi=ta=zl=Qt=0,Ee=wn=null,Zi=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-St(a),u=1<<n;e|=t[n],a&=~u}return rl=e,ru(),l}function Bo(t,e){at=null,x.H=_u,e===hn||e===vu?(e=ts(),zt=3):e===Ff?(e=ts(),zt=4):zt=e===eo?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Oe=e,rt===null&&(Qt=1,Mu(t,Ue(e,t.current)))}function Yo(){var t=x.H;return x.H=_u,t===null?_u:t}function Go(){var t=x.A;return x.A=Nm,t}function $i(){Qt=4,Al||(mt&4194048)!==mt&&qe.current!==null||(Ua=!0),(zl&134217727)===0&&(ta&134217727)===0||Ht===null||Ol(Ht,mt,Be,!1)}function Fi(t,e,l){var a=At;At|=2;var n=Yo(),u=Go();(Ht!==t||mt!==e)&&(Yu=null,qa(t,e)),e=!1;var c=Qt;t:do try{if(zt!==0&&rt!==null){var i=rt,s=Oe;switch(zt){case 8:Wi(),c=6;break t;case 3:case 2:case 9:case 6:qe.current===null&&(e=!0);var y=zt;if(zt=0,Oe=null,Ba(t,i,s,y),l&&Ua){c=0;break t}break;default:y=zt,zt=0,Oe=null,Ba(t,i,s,y)}}Um(),c=Qt;break}catch(E){Bo(t,E)}while(!0);return e&&t.shellSuspendCounter++,tl=Jl=null,At=a,x.H=n,x.A=u,rt===null&&(Ht=null,mt=0,ru()),c}function Um(){for(;rt!==null;)Xo(rt)}function Cm(t,e){var l=At;At|=2;var a=Yo(),n=Go();Ht!==t||mt!==e?(Yu=null,Bu=ve()+500,qa(t,e)):Ua=Ja(t,e);t:do try{if(zt!==0&&rt!==null){e=rt;var u=Oe;e:switch(zt){case 1:zt=0,Oe=null,Ba(t,e,u,1);break;case 2:case 9:if(If(u)){zt=0,Oe=null,Lo(e);break}e=function(){zt!==2&&zt!==9||Ht!==t||(zt=7),Je(t)},u.then(e,e);break t;case 3:zt=7;break t;case 4:zt=5;break t;case 7:If(u)?(zt=0,Oe=null,Lo(e)):(zt=0,Oe=null,Ba(t,e,u,7));break;case 5:var c=null;switch(rt.tag){case 26:c=rt.memoizedState;case 5:case 27:var i=rt;if(!c||Ed(c)){zt=0,Oe=null;var s=i.sibling;if(s!==null)rt=s;else{var y=i.return;y!==null?(rt=y,Xu(y)):rt=null}break e}}zt=0,Oe=null,Ba(t,e,u,5);break;case 6:zt=0,Oe=null,Ba(t,e,u,6);break;case 8:Wi(),Qt=6;break t;default:throw Error(f(462))}}jm();break}catch(E){Bo(t,E)}while(!0);return tl=Jl=null,x.H=a,x.A=n,At=l,rt!==null?0:(Ht=null,mt=0,ru(),Qt)}function jm(){for(;rt!==null&&!ql();)Xo(rt)}function Xo(t){var e=ho(t.alternate,t,rl);t.memoizedProps=t.pendingProps,e===null?Xu(t):rt=e}function Lo(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=io(l,e,e.pendingProps,e.type,void 0,mt);break;case 11:e=io(l,e,e.pendingProps,e.type.render,e.ref,mt);break;case 5:di(e);default:yo(l,e),e=rt=Lf(e,rl),e=ho(l,e,rl)}t.memoizedProps=t.pendingProps,e===null?Xu(t):rt=e}function Ba(t,e,l,a){tl=Jl=null,di(e),Da=null,En=0;var n=e.return;try{if(Am(t,n,e,l,mt)){Qt=1,Mu(t,Ue(l,t.current)),rt=null;return}}catch(u){if(n!==null)throw rt=n,u;Qt=1,Mu(t,Ue(l,t.current)),rt=null;return}e.flags&32768?(xt||a===1?t=!0:Ua||(mt&536870912)!==0?t=!1:(Al=t=!0,(a===2||a===9||a===3||a===6)&&(a=qe.current,a!==null&&a.tag===13&&(a.flags|=16384))),Qo(e,t)):Xu(e)}function Xu(t){var e=t;do{if((e.flags&32768)!==0){Qo(e,Al);return}t=e.return;var l=Rm(e.alternate,e,rl);if(l!==null){rt=l;return}if(e=e.sibling,e!==null){rt=e;return}rt=e=t}while(e!==null);Qt===0&&(Qt=5)}function Qo(t,e){do{var l=_m(t.alternate,t);if(l!==null){l.flags&=32767,rt=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){rt=t;return}rt=t=l}while(t!==null);Qt=6,rt=null}function Zo(t,e,l,a,n,u,c,i,s){t.cancelPendingCommit=null;do Lu();while(fe!==0);if((At&6)!==0)throw Error(f(327));if(e!==null){if(e===t.current)throw Error(f(177));if(u=e.lanes|e.childLanes,u|=Xc,m0(t,l,u,c,i,s),t===Ht&&(rt=Ht=null,mt=0),ja=e,_l=t,Ha=l,Ki=u,ki=n,Co=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Ym(Tt,function(){return Wo(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=x.T,x.T=null,n=j.p,j.p=2,c=At,At|=4;try{Om(t,e,l)}finally{At=c,j.p=n,x.T=a}}fe=1,Vo(),Ko(),ko()}}function Vo(){if(fe===1){fe=0;var t=_l,e=ja,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=x.T,x.T=null;var a=j.p;j.p=2;var n=At;At|=4;try{_o(e,t);var u=fr,c=Mf(t.containerInfo),i=u.focusedElem,s=u.selectionRange;if(c!==i&&i&&i.ownerDocument&&wf(i.ownerDocument.documentElement,i)){if(s!==null&&Hc(i)){var y=s.start,E=s.end;if(E===void 0&&(E=y),"selectionStart"in i)i.selectionStart=y,i.selectionEnd=Math.min(E,i.value.length);else{var O=i.ownerDocument||document,b=O&&O.defaultView||window;if(b.getSelection){var p=b.getSelection(),P=i.textContent.length,W=Math.min(s.start,P),wt=s.end===void 0?W:Math.min(s.end,P);!p.extend&&W>wt&&(c=wt,wt=W,W=c);var h=Nf(i,W),m=Nf(i,wt);if(h&&m&&(p.rangeCount!==1||p.anchorNode!==h.node||p.anchorOffset!==h.offset||p.focusNode!==m.node||p.focusOffset!==m.offset)){var g=O.createRange();g.setStart(h.node,h.offset),p.removeAllRanges(),W>wt?(p.addRange(g),p.extend(m.node,m.offset)):(g.setEnd(m.node,m.offset),p.addRange(g))}}}}for(O=[],p=i;p=p.parentNode;)p.nodeType===1&&O.push({element:p,left:p.scrollLeft,top:p.scrollTop});for(typeof i.focus=="function"&&i.focus(),i=0;i<O.length;i++){var z=O[i];z.element.scrollLeft=z.left,z.element.scrollTop=z.top}}tc=!!rr,fr=rr=null}finally{At=n,j.p=a,x.T=l}}t.current=e,fe=2}}function Ko(){if(fe===2){fe=0;var t=_l,e=ja,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=x.T,x.T=null;var a=j.p;j.p=2;var n=At;At|=4;try{Eo(t,e.alternate,e)}finally{At=n,j.p=a,x.T=l}}fe=3}}function ko(){if(fe===4||fe===3){fe=0,aa();var t=_l,e=ja,l=Ha,a=Co;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?fe=5:(fe=0,ja=_l=null,Jo(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Rl=null),gc(l),e=e.stateNode,st&&typeof st.onCommitFiberRoot=="function")try{st.onCommitFiberRoot(qt,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=x.T,n=j.p,j.p=2,x.T=null;try{for(var u=t.onRecoverableError,c=0;c<a.length;c++){var i=a[c];u(i.value,{componentStack:i.stack})}}finally{x.T=e,j.p=n}}(Ha&3)!==0&&Lu(),Je(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Ji?Mn++:(Mn=0,Ji=t):Mn=0,Un(0)}}function Jo(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,dn(e)))}function Lu(t){return Vo(),Ko(),ko(),Wo()}function Wo(){if(fe!==5)return!1;var t=_l,e=Ki;Ki=0;var l=gc(Ha),a=x.T,n=j.p;try{j.p=32>l?32:l,x.T=null,l=ki,ki=null;var u=_l,c=Ha;if(fe=0,ja=_l=null,Ha=0,(At&6)!==0)throw Error(f(331));var i=At;if(At|=4,Mo(u.current),Do(u,u.current,c,l),At=i,Un(0,!1),st&&typeof st.onPostCommitFiberRoot=="function")try{st.onPostCommitFiberRoot(qt,u)}catch{}return!0}finally{j.p=n,x.T=a,Jo(t,e)}}function $o(t,e,l){e=Ue(l,e),e=Ri(t.stateNode,e,2),t=vl(t,e,2),t!==null&&(Wa(t,2),Je(t))}function Ut(t,e,l){if(t.tag===3)$o(t,t,l);else for(;e!==null;){if(e.tag===3){$o(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Rl===null||!Rl.has(a))){t=Ue(l,t),l=Ps(2),a=vl(e,l,2),a!==null&&(to(l,a,e,t),Wa(a,2),Je(a));break}}e=e.return}}function Ii(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new wm;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Li=!0,n.add(l),t=Hm.bind(null,t,e,l),e.then(t,t))}function Hm(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Ht===t&&(mt&l)===l&&(Qt===4||Qt===3&&(mt&62914560)===mt&&300>ve()-Vi?(At&2)===0&&qa(t,0):Qi|=l,Ca===mt&&(Ca=0)),Je(t)}function Fo(t,e){e===0&&(e=Vr()),t=pa(t,e),t!==null&&(Wa(t,e),Je(t))}function qm(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),Fo(t,l)}function Bm(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(f(314))}a!==null&&a.delete(e),Fo(t,l)}function Ym(t,e){return ol(t,e)}var Qu=null,Ya=null,Pi=!1,Zu=!1,tr=!1,ea=0;function Je(t){t!==Ya&&t.next===null&&(Ya===null?Qu=Ya=t:Ya=Ya.next=t),Zu=!0,Pi||(Pi=!0,Xm())}function Un(t,e){if(!tr&&Zu){tr=!0;do for(var l=!1,a=Qu;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var c=a.suspendedLanes,i=a.pingedLanes;u=(1<<31-St(42|t)+1)-1,u&=n&~(c&~i),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,ed(a,u))}else u=mt,u=$n(a,a===Ht?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||Ja(a,u)||(l=!0,ed(a,u));a=a.next}while(l);tr=!1}}function Gm(){Io()}function Io(){Zu=Pi=!1;var t=0;ea!==0&&(Wm()&&(t=ea),ea=0);for(var e=ve(),l=null,a=Qu;a!==null;){var n=a.next,u=Po(a,e);u===0?(a.next=null,l===null?Qu=n:l.next=n,n===null&&(Ya=l)):(l=a,(t!==0||(u&3)!==0)&&(Zu=!0)),a=n}Un(t)}function Po(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var c=31-St(u),i=1<<c,s=n[c];s===-1?((i&l)===0||(i&a)!==0)&&(n[c]=d0(i,e)):s<=e&&(t.expiredLanes|=i),u&=~i}if(e=Ht,l=mt,l=$n(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(zt===2||zt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&Hl(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Ja(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&Hl(a),gc(l)){case 2:case 8:l=kt;break;case 32:l=Tt;break;case 268435456:l=Ft;break;default:l=Tt}return a=td.bind(null,t),l=ol(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&Hl(a),t.callbackPriority=2,t.callbackNode=null,2}function td(t,e){if(fe!==0&&fe!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Lu()&&t.callbackNode!==l)return null;var a=mt;return a=$n(t,t===Ht?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Ho(t,a,e),Po(t,ve()),t.callbackNode!=null&&t.callbackNode===l?td.bind(null,t):null)}function ed(t,e){if(Lu())return null;Ho(t,e,!0)}function Xm(){Fm(function(){(At&6)!==0?ol(yt,Gm):Io()})}function er(){return ea===0&&(ea=Zr()),ea}function ld(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:eu(""+t)}function ad(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Lm(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=ld((n[pe]||null).action),c=a.submitter;c&&(e=(e=c[pe]||null)?ld(e.formAction):c.getAttribute("formAction"),e!==null&&(u=e,c=null));var i=new uu("action","action",null,a,n);t.push({event:i,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ea!==0){var s=c?ad(n,c):new FormData(n);xi(l,{pending:!0,data:s,method:n.method,action:u},null,s)}}else typeof u=="function"&&(i.preventDefault(),s=c?ad(n,c):new FormData(n),xi(l,{pending:!0,data:s,method:n.method,action:u},u,s))},currentTarget:n}]})}}for(var lr=0;lr<Gc.length;lr++){var ar=Gc[lr],Qm=ar.toLowerCase(),Zm=ar[0].toUpperCase()+ar.slice(1);Ge(Qm,"on"+Zm)}Ge(jf,"onAnimationEnd"),Ge(Hf,"onAnimationIteration"),Ge(qf,"onAnimationStart"),Ge("dblclick","onDoubleClick"),Ge("focusin","onFocus"),Ge("focusout","onBlur"),Ge(im,"onTransitionRun"),Ge(rm,"onTransitionStart"),Ge(fm,"onTransitionCancel"),Ge(Bf,"onTransitionEnd"),fa("onMouseEnter",["mouseout","mouseover"]),fa("onMouseLeave",["mouseout","mouseover"]),fa("onPointerEnter",["pointerout","pointerover"]),fa("onPointerLeave",["pointerout","pointerover"]),Yl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Yl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Yl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Yl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Yl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Yl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Cn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Vm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Cn));function nd(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var c=a.length-1;0<=c;c--){var i=a[c],s=i.instance,y=i.currentTarget;if(i=i.listener,s!==u&&n.isPropagationStopped())break t;u=i,n.currentTarget=y;try{u(n)}catch(E){wu(E)}n.currentTarget=null,u=s}else for(c=0;c<a.length;c++){if(i=a[c],s=i.instance,y=i.currentTarget,i=i.listener,s!==u&&n.isPropagationStopped())break t;u=i,n.currentTarget=y;try{u(n)}catch(E){wu(E)}n.currentTarget=null,u=s}}}}function ft(t,e){var l=e[yc];l===void 0&&(l=e[yc]=new Set);var a=t+"__bubble";l.has(a)||(ud(e,t,2,!1),l.add(a))}function nr(t,e,l){var a=0;e&&(a|=4),ud(l,t,a,e)}var Vu="_reactListening"+Math.random().toString(36).slice(2);function ur(t){if(!t[Vu]){t[Vu]=!0,$r.forEach(function(l){l!=="selectionchange"&&(Vm.has(l)||nr(l,!1,t),nr(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Vu]||(e[Vu]=!0,nr("selectionchange",!1,e))}}function ud(t,e,l,a){switch(Dd(e)){case 2:var n=bh;break;case 8:n=ph;break;default:n=pr}l=n.bind(null,e,l,t),n=void 0,!_c||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function cr(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var c=a.tag;if(c===3||c===4){var i=a.stateNode.containerInfo;if(i===n)break;if(c===4)for(c=a.return;c!==null;){var s=c.tag;if((s===3||s===4)&&c.stateNode.containerInfo===n)return;c=c.return}for(;i!==null;){if(c=ca(i),c===null)return;if(s=c.tag,s===5||s===6||s===26||s===27){a=u=c;continue t}i=i.parentNode}}a=a.return}of(function(){var y=u,E=zc(l),O=[];t:{var b=Yf.get(t);if(b!==void 0){var p=uu,P=t;switch(t){case"keypress":if(au(l)===0)break t;case"keydown":case"keyup":p=Y0;break;case"focusin":P="focus",p=wc;break;case"focusout":P="blur",p=wc;break;case"beforeblur":case"afterblur":p=wc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=hf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=_0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=L0;break;case jf:case Hf:case qf:p=N0;break;case Bf:p=Z0;break;case"scroll":case"scrollend":p=z0;break;case"wheel":p=K0;break;case"copy":case"cut":case"paste":p=M0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=yf;break;case"toggle":case"beforetoggle":p=J0}var W=(e&4)!==0,wt=!W&&(t==="scroll"||t==="scrollend"),h=W?b!==null?b+"Capture":null:b;W=[];for(var m=y,g;m!==null;){var z=m;if(g=z.stateNode,z=z.tag,z!==5&&z!==26&&z!==27||g===null||h===null||(z=Ia(m,h),z!=null&&W.push(jn(m,z,g))),wt)break;m=m.return}0<W.length&&(b=new p(b,P,null,l,E),O.push({event:b,listeners:W}))}}if((e&7)===0){t:{if(b=t==="mouseover"||t==="pointerover",p=t==="mouseout"||t==="pointerout",b&&l!==Ac&&(P=l.relatedTarget||l.fromElement)&&(ca(P)||P[ua]))break t;if((p||b)&&(b=E.window===E?E:(b=E.ownerDocument)?b.defaultView||b.parentWindow:window,p?(P=l.relatedTarget||l.toElement,p=y,P=P?ca(P):null,P!==null&&(wt=_(P),W=P.tag,P!==wt||W!==5&&W!==27&&W!==6)&&(P=null)):(p=null,P=y),p!==P)){if(W=hf,z="onMouseLeave",h="onMouseEnter",m="mouse",(t==="pointerout"||t==="pointerover")&&(W=yf,z="onPointerLeave",h="onPointerEnter",m="pointer"),wt=p==null?b:Fa(p),g=P==null?b:Fa(P),b=new W(z,m+"leave",p,l,E),b.target=wt,b.relatedTarget=g,z=null,ca(E)===y&&(W=new W(h,m+"enter",P,l,E),W.target=g,W.relatedTarget=wt,z=W),wt=z,p&&P)e:{for(W=p,h=P,m=0,g=W;g;g=Ga(g))m++;for(g=0,z=h;z;z=Ga(z))g++;for(;0<m-g;)W=Ga(W),m--;for(;0<g-m;)h=Ga(h),g--;for(;m--;){if(W===h||h!==null&&W===h.alternate)break e;W=Ga(W),h=Ga(h)}W=null}else W=null;p!==null&&cd(O,b,p,W,!1),P!==null&&wt!==null&&cd(O,wt,P,W,!0)}}t:{if(b=y?Fa(y):window,p=b.nodeName&&b.nodeName.toLowerCase(),p==="select"||p==="input"&&b.type==="file")var L=Af;else if(Tf(b))if(zf)L=nm;else{L=lm;var ut=em}else p=b.nodeName,!p||p.toLowerCase()!=="input"||b.type!=="checkbox"&&b.type!=="radio"?y&&Ec(y.elementType)&&(L=Af):L=am;if(L&&(L=L(t,y))){Ef(O,L,l,E);break t}ut&&ut(t,b,y),t==="focusout"&&y&&b.type==="number"&&y.memoizedProps.value!=null&&Tc(b,"number",b.value)}switch(ut=y?Fa(y):window,t){case"focusin":(Tf(ut)||ut.contentEditable==="true")&&(ya=ut,qc=y,cn=null);break;case"focusout":cn=qc=ya=null;break;case"mousedown":Bc=!0;break;case"contextmenu":case"mouseup":case"dragend":Bc=!1,Uf(O,l,E);break;case"selectionchange":if(cm)break;case"keydown":case"keyup":Uf(O,l,E)}var K;if(Uc)t:{switch(t){case"compositionstart":var $="onCompositionStart";break t;case"compositionend":$="onCompositionEnd";break t;case"compositionupdate":$="onCompositionUpdate";break t}$=void 0}else ga?Sf(t,l)&&($="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&($="onCompositionStart");$&&(vf&&l.locale!=="ko"&&(ga||$!=="onCompositionStart"?$==="onCompositionEnd"&&ga&&(K=df()):(ml=E,Oc="value"in ml?ml.value:ml.textContent,ga=!0)),ut=Ku(y,$),0<ut.length&&($=new gf($,t,null,l,E),O.push({event:$,listeners:ut}),K?$.data=K:(K=xf(l),K!==null&&($.data=K)))),(K=$0?F0(t,l):I0(t,l))&&($=Ku(y,"onBeforeInput"),0<$.length&&(ut=new gf("onBeforeInput","beforeinput",null,l,E),O.push({event:ut,listeners:$}),ut.data=K)),Lm(O,t,y,l,E)}nd(O,e)})}function jn(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Ku(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Ia(t,l),n!=null&&a.unshift(jn(t,n,u)),n=Ia(t,e),n!=null&&a.push(jn(t,n,u))),t.tag===3)return a;t=t.return}return[]}function Ga(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function cd(t,e,l,a,n){for(var u=e._reactName,c=[];l!==null&&l!==a;){var i=l,s=i.alternate,y=i.stateNode;if(i=i.tag,s!==null&&s===a)break;i!==5&&i!==26&&i!==27||y===null||(s=y,n?(y=Ia(l,u),y!=null&&c.unshift(jn(l,y,s))):n||(y=Ia(l,u),y!=null&&c.push(jn(l,y,s)))),l=l.return}c.length!==0&&t.push({event:e,listeners:c})}var Km=/\r\n?/g,km=/\u0000|\uFFFD/g;function id(t){return(typeof t=="string"?t:""+t).replace(Km,`
`).replace(km,"")}function rd(t,e){return e=id(e),id(t)===e}function ku(){}function Nt(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||da(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&da(t,""+a);break;case"className":In(t,"class",a);break;case"tabIndex":In(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":In(t,l,a);break;case"style":ff(t,a,u);break;case"data":if(e!=="object"){In(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=eu(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&Nt(t,e,"name",n.name,n,null),Nt(t,e,"formEncType",n.formEncType,n,null),Nt(t,e,"formMethod",n.formMethod,n,null),Nt(t,e,"formTarget",n.formTarget,n,null)):(Nt(t,e,"encType",n.encType,n,null),Nt(t,e,"method",n.method,n,null),Nt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=eu(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=ku);break;case"onScroll":a!=null&&ft("scroll",t);break;case"onScrollEnd":a!=null&&ft("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(f(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(f(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=eu(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":ft("beforetoggle",t),ft("toggle",t),Fn(t,"popover",a);break;case"xlinkActuate":We(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":We(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":We(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":We(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":We(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":We(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":We(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":We(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":We(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Fn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=E0.get(l)||l,Fn(t,l,a))}}function ir(t,e,l,a,n,u){switch(l){case"style":ff(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(f(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(f(60));t.innerHTML=l}}break;case"children":typeof a=="string"?da(t,a):(typeof a=="number"||typeof a=="bigint")&&da(t,""+a);break;case"onScroll":a!=null&&ft("scroll",t);break;case"onScrollEnd":a!=null&&ft("scrollend",t);break;case"onClick":a!=null&&(t.onclick=ku);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Fr.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[pe]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Fn(t,l,a)}}}function se(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ft("error",t),ft("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var c=l[u];if(c!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(f(137,e));default:Nt(t,e,u,c,l,null)}}n&&Nt(t,e,"srcSet",l.srcSet,l,null),a&&Nt(t,e,"src",l.src,l,null);return;case"input":ft("invalid",t);var i=u=c=n=null,s=null,y=null;for(a in l)if(l.hasOwnProperty(a)){var E=l[a];if(E!=null)switch(a){case"name":n=E;break;case"type":c=E;break;case"checked":s=E;break;case"defaultChecked":y=E;break;case"value":u=E;break;case"defaultValue":i=E;break;case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(f(137,e));break;default:Nt(t,e,a,E,l,null)}}nf(t,u,i,s,y,c,n,!1),Pn(t);return;case"select":ft("invalid",t),a=c=u=null;for(n in l)if(l.hasOwnProperty(n)&&(i=l[n],i!=null))switch(n){case"value":u=i;break;case"defaultValue":c=i;break;case"multiple":a=i;default:Nt(t,e,n,i,l,null)}e=u,l=c,t.multiple=!!a,e!=null?oa(t,!!a,e,!1):l!=null&&oa(t,!!a,l,!0);return;case"textarea":ft("invalid",t),u=n=a=null;for(c in l)if(l.hasOwnProperty(c)&&(i=l[c],i!=null))switch(c){case"value":a=i;break;case"defaultValue":n=i;break;case"children":u=i;break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(f(91));break;default:Nt(t,e,c,i,l,null)}cf(t,a,n,u),Pn(t);return;case"option":for(s in l)if(l.hasOwnProperty(s)&&(a=l[s],a!=null))switch(s){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Nt(t,e,s,a,l,null)}return;case"dialog":ft("beforetoggle",t),ft("toggle",t),ft("cancel",t),ft("close",t);break;case"iframe":case"object":ft("load",t);break;case"video":case"audio":for(a=0;a<Cn.length;a++)ft(Cn[a],t);break;case"image":ft("error",t),ft("load",t);break;case"details":ft("toggle",t);break;case"embed":case"source":case"link":ft("error",t),ft("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(y in l)if(l.hasOwnProperty(y)&&(a=l[y],a!=null))switch(y){case"children":case"dangerouslySetInnerHTML":throw Error(f(137,e));default:Nt(t,e,y,a,l,null)}return;default:if(Ec(e)){for(E in l)l.hasOwnProperty(E)&&(a=l[E],a!==void 0&&ir(t,e,E,a,l,void 0));return}}for(i in l)l.hasOwnProperty(i)&&(a=l[i],a!=null&&Nt(t,e,i,a,l,null))}function Jm(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,c=null,i=null,s=null,y=null,E=null;for(p in l){var O=l[p];if(l.hasOwnProperty(p)&&O!=null)switch(p){case"checked":break;case"value":break;case"defaultValue":s=O;default:a.hasOwnProperty(p)||Nt(t,e,p,null,a,O)}}for(var b in a){var p=a[b];if(O=l[b],a.hasOwnProperty(b)&&(p!=null||O!=null))switch(b){case"type":u=p;break;case"name":n=p;break;case"checked":y=p;break;case"defaultChecked":E=p;break;case"value":c=p;break;case"defaultValue":i=p;break;case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(f(137,e));break;default:p!==O&&Nt(t,e,b,p,a,O)}}xc(t,c,i,s,y,E,u,n);return;case"select":p=c=i=b=null;for(u in l)if(s=l[u],l.hasOwnProperty(u)&&s!=null)switch(u){case"value":break;case"multiple":p=s;default:a.hasOwnProperty(u)||Nt(t,e,u,null,a,s)}for(n in a)if(u=a[n],s=l[n],a.hasOwnProperty(n)&&(u!=null||s!=null))switch(n){case"value":b=u;break;case"defaultValue":i=u;break;case"multiple":c=u;default:u!==s&&Nt(t,e,n,u,a,s)}e=i,l=c,a=p,b!=null?oa(t,!!l,b,!1):!!a!=!!l&&(e!=null?oa(t,!!l,e,!0):oa(t,!!l,l?[]:"",!1));return;case"textarea":p=b=null;for(i in l)if(n=l[i],l.hasOwnProperty(i)&&n!=null&&!a.hasOwnProperty(i))switch(i){case"value":break;case"children":break;default:Nt(t,e,i,null,a,n)}for(c in a)if(n=a[c],u=l[c],a.hasOwnProperty(c)&&(n!=null||u!=null))switch(c){case"value":b=n;break;case"defaultValue":p=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(f(91));break;default:n!==u&&Nt(t,e,c,n,a,u)}uf(t,b,p);return;case"option":for(var P in l)if(b=l[P],l.hasOwnProperty(P)&&b!=null&&!a.hasOwnProperty(P))switch(P){case"selected":t.selected=!1;break;default:Nt(t,e,P,null,a,b)}for(s in a)if(b=a[s],p=l[s],a.hasOwnProperty(s)&&b!==p&&(b!=null||p!=null))switch(s){case"selected":t.selected=b&&typeof b!="function"&&typeof b!="symbol";break;default:Nt(t,e,s,b,a,p)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in l)b=l[W],l.hasOwnProperty(W)&&b!=null&&!a.hasOwnProperty(W)&&Nt(t,e,W,null,a,b);for(y in a)if(b=a[y],p=l[y],a.hasOwnProperty(y)&&b!==p&&(b!=null||p!=null))switch(y){case"children":case"dangerouslySetInnerHTML":if(b!=null)throw Error(f(137,e));break;default:Nt(t,e,y,b,a,p)}return;default:if(Ec(e)){for(var wt in l)b=l[wt],l.hasOwnProperty(wt)&&b!==void 0&&!a.hasOwnProperty(wt)&&ir(t,e,wt,void 0,a,b);for(E in a)b=a[E],p=l[E],!a.hasOwnProperty(E)||b===p||b===void 0&&p===void 0||ir(t,e,E,b,a,p);return}}for(var h in l)b=l[h],l.hasOwnProperty(h)&&b!=null&&!a.hasOwnProperty(h)&&Nt(t,e,h,null,a,b);for(O in a)b=a[O],p=l[O],!a.hasOwnProperty(O)||b===p||b==null&&p==null||Nt(t,e,O,b,a,p)}var rr=null,fr=null;function Ju(t){return t.nodeType===9?t:t.ownerDocument}function fd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function sr(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var or=null;function Wm(){var t=window.event;return t&&t.type==="popstate"?t===or?!1:(or=t,!0):(or=null,!1)}var od=typeof setTimeout=="function"?setTimeout:void 0,$m=typeof clearTimeout=="function"?clearTimeout:void 0,dd=typeof Promise=="function"?Promise:void 0,Fm=typeof queueMicrotask=="function"?queueMicrotask:typeof dd<"u"?function(t){return dd.resolve(null).then(t).catch(Im)}:od;function Im(t){setTimeout(function(){throw t})}function Dl(t){return t==="head"}function md(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var c=t.ownerDocument;if(l&1&&Hn(c.documentElement),l&2&&Hn(c.body),l&4)for(l=c.head,Hn(l),c=l.firstChild;c;){var i=c.nextSibling,s=c.nodeName;c[$a]||s==="SCRIPT"||s==="STYLE"||s==="LINK"&&c.rel.toLowerCase()==="stylesheet"||l.removeChild(c),c=i}}if(n===0){t.removeChild(u),Zn(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);Zn(e)}function dr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":dr(l),vc(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Pm(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[$a])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Le(t.nextSibling),t===null)break}return null}function th(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Le(t.nextSibling),t===null))return null;return t}function mr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function eh(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Le(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var hr=null;function hd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function gd(t,e,l){switch(e=Ju(l),t){case"html":if(t=e.documentElement,!t)throw Error(f(452));return t;case"head":if(t=e.head,!t)throw Error(f(453));return t;case"body":if(t=e.body,!t)throw Error(f(454));return t;default:throw Error(f(451))}}function Hn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);vc(t)}var Ye=new Map,yd=new Set;function Wu(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var fl=j.d;j.d={f:lh,r:ah,D:nh,C:uh,L:ch,m:ih,X:fh,S:rh,M:sh};function lh(){var t=fl.f(),e=Gu();return t||e}function ah(t){var e=ia(t);e!==null&&e.tag===5&&e.type==="form"?js(e):fl.r(t)}var Xa=typeof document>"u"?null:document;function vd(t,e,l){var a=Xa;if(a&&typeof e=="string"&&e){var n=Me(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),yd.has(n)||(yd.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),se(e,"link",t),ae(e),a.head.appendChild(e)))}}function nh(t){fl.D(t),vd("dns-prefetch",t,null)}function uh(t,e){fl.C(t,e),vd("preconnect",t,e)}function ch(t,e,l){fl.L(t,e,l);var a=Xa;if(a&&t&&e){var n='link[rel="preload"][as="'+Me(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+Me(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+Me(l.imageSizes)+'"]')):n+='[href="'+Me(t)+'"]';var u=n;switch(e){case"style":u=La(t);break;case"script":u=Qa(t)}Ye.has(u)||(t=C({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),Ye.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(qn(u))||e==="script"&&a.querySelector(Bn(u))||(e=a.createElement("link"),se(e,"link",t),ae(e),a.head.appendChild(e)))}}function ih(t,e){fl.m(t,e);var l=Xa;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+Me(a)+'"][href="'+Me(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Qa(t)}if(!Ye.has(u)&&(t=C({rel:"modulepreload",href:t},e),Ye.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Bn(u)))return}a=l.createElement("link"),se(a,"link",t),ae(a),l.head.appendChild(a)}}}function rh(t,e,l){fl.S(t,e,l);var a=Xa;if(a&&t){var n=ra(a).hoistableStyles,u=La(t);e=e||"default";var c=n.get(u);if(!c){var i={loading:0,preload:null};if(c=a.querySelector(qn(u)))i.loading=5;else{t=C({rel:"stylesheet",href:t,"data-precedence":e},l),(l=Ye.get(u))&&gr(t,l);var s=c=a.createElement("link");ae(s),se(s,"link",t),s._p=new Promise(function(y,E){s.onload=y,s.onerror=E}),s.addEventListener("load",function(){i.loading|=1}),s.addEventListener("error",function(){i.loading|=2}),i.loading|=4,$u(c,e,a)}c={type:"stylesheet",instance:c,count:1,state:i},n.set(u,c)}}}function fh(t,e){fl.X(t,e);var l=Xa;if(l&&t){var a=ra(l).hoistableScripts,n=Qa(t),u=a.get(n);u||(u=l.querySelector(Bn(n)),u||(t=C({src:t,async:!0},e),(e=Ye.get(n))&&yr(t,e),u=l.createElement("script"),ae(u),se(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function sh(t,e){fl.M(t,e);var l=Xa;if(l&&t){var a=ra(l).hoistableScripts,n=Qa(t),u=a.get(n);u||(u=l.querySelector(Bn(n)),u||(t=C({src:t,async:!0,type:"module"},e),(e=Ye.get(n))&&yr(t,e),u=l.createElement("script"),ae(u),se(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function bd(t,e,l,a){var n=(n=G.current)?Wu(n):null;if(!n)throw Error(f(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=La(l.href),l=ra(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=La(l.href);var u=ra(n).hoistableStyles,c=u.get(t);if(c||(n=n.ownerDocument||n,c={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,c),(u=n.querySelector(qn(t)))&&!u._p&&(c.instance=u,c.state.loading=5),Ye.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Ye.set(t,l),u||oh(n,t,l,c.state))),e&&a===null)throw Error(f(528,""));return c}if(e&&a!==null)throw Error(f(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Qa(l),l=ra(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(f(444,t))}}function La(t){return'href="'+Me(t)+'"'}function qn(t){return'link[rel="stylesheet"]['+t+"]"}function pd(t){return C({},t,{"data-precedence":t.precedence,precedence:null})}function oh(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),se(e,"link",l),ae(e),t.head.appendChild(e))}function Qa(t){return'[src="'+Me(t)+'"]'}function Bn(t){return"script[async]"+t}function Sd(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+Me(l.href)+'"]');if(a)return e.instance=a,ae(a),a;var n=C({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),ae(a),se(a,"style",n),$u(a,l.precedence,t),e.instance=a;case"stylesheet":n=La(l.href);var u=t.querySelector(qn(n));if(u)return e.state.loading|=4,e.instance=u,ae(u),u;a=pd(l),(n=Ye.get(n))&&gr(a,n),u=(t.ownerDocument||t).createElement("link"),ae(u);var c=u;return c._p=new Promise(function(i,s){c.onload=i,c.onerror=s}),se(u,"link",a),e.state.loading|=4,$u(u,l.precedence,t),e.instance=u;case"script":return u=Qa(l.src),(n=t.querySelector(Bn(u)))?(e.instance=n,ae(n),n):(a=l,(n=Ye.get(u))&&(a=C({},l),yr(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),ae(n),se(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(f(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,$u(a,l.precedence,t));return e.instance}function $u(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,c=0;c<a.length;c++){var i=a[c];if(i.dataset.precedence===e)u=i;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function gr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function yr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Fu=null;function xd(t,e,l){if(Fu===null){var a=new Map,n=Fu=new Map;n.set(l,a)}else n=Fu,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[$a]||u[oe]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var c=u.getAttribute(e)||"";c=t+c;var i=a.get(c);i?i.push(u):a.set(c,[u])}}return a}function Td(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function dh(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Ed(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Yn=null;function mh(){}function hh(t,e,l){if(Yn===null)throw Error(f(475));var a=Yn;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=La(l.href),u=t.querySelector(qn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Iu.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,ae(u);return}u=t.ownerDocument||t,l=pd(l),(n=Ye.get(n))&&gr(l,n),u=u.createElement("link"),ae(u);var c=u;c._p=new Promise(function(i,s){c.onload=i,c.onerror=s}),se(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Iu.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function gh(){if(Yn===null)throw Error(f(475));var t=Yn;return t.stylesheets&&t.count===0&&vr(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&vr(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Iu(){if(this.count--,this.count===0){if(this.stylesheets)vr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Pu=null;function vr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Pu=new Map,e.forEach(yh,t),Pu=null,Iu.call(t))}function yh(t,e){if(!(e.state.loading&4)){var l=Pu.get(t);if(l)var a=l.get(null);else{l=new Map,Pu.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var c=n[u];(c.nodeName==="LINK"||c.getAttribute("media")!=="not all")&&(l.set(c.dataset.precedence,c),a=c)}a&&l.set(null,a)}n=e.instance,c=n.getAttribute("data-precedence"),u=l.get(c)||a,u===a&&l.set(null,n),l.set(c,n),this.count++,a=Iu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Gn={$$typeof:lt,Provider:null,Consumer:null,_currentValue:w,_currentValue2:w,_threadCount:0};function vh(t,e,l,a,n,u,c,i){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=mc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mc(0),this.hiddenUpdates=mc(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=c,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Ad(t,e,l,a,n,u,c,i,s,y,E,O){return t=new vh(t,e,l,c,i,s,y,O),e=1,u===!0&&(e|=24),u=ze(3,null,null,e),t.current=u,u.stateNode=t,e=Ic(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},li(u),t}function zd(t){return t?(t=Sa,t):Sa}function Rd(t,e,l,a,n,u){n=zd(n),a.context===null?a.context=n:a.pendingContext=n,a=yl(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=vl(t,a,e),l!==null&&(Ne(l,t,e),yn(l,t,e))}function _d(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function br(t,e){_d(t,e),(t=t.alternate)&&_d(t,e)}function Od(t){if(t.tag===13){var e=pa(t,67108864);e!==null&&Ne(e,t,67108864),br(t,67108864)}}var tc=!0;function bh(t,e,l,a){var n=x.T;x.T=null;var u=j.p;try{j.p=2,pr(t,e,l,a)}finally{j.p=u,x.T=n}}function ph(t,e,l,a){var n=x.T;x.T=null;var u=j.p;try{j.p=8,pr(t,e,l,a)}finally{j.p=u,x.T=n}}function pr(t,e,l,a){if(tc){var n=Sr(a);if(n===null)cr(t,e,a,ec,l),Nd(t,a);else if(xh(n,t,e,l,a))a.stopPropagation();else if(Nd(t,a),e&4&&-1<Sh.indexOf(t)){for(;n!==null;){var u=ia(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var c=Bl(u.pendingLanes);if(c!==0){var i=u;for(i.pendingLanes|=2,i.entangledLanes|=2;c;){var s=1<<31-St(c);i.entanglements[1]|=s,c&=~s}Je(u),(At&6)===0&&(Bu=ve()+500,Un(0))}}break;case 13:i=pa(u,2),i!==null&&Ne(i,u,2),Gu(),br(u,2)}if(u=Sr(a),u===null&&cr(t,e,a,ec,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else cr(t,e,a,null,l)}}function Sr(t){return t=zc(t),xr(t)}var ec=null;function xr(t){if(ec=null,t=ca(t),t!==null){var e=_(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=U(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return ec=t,null}function Dd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(sc()){case yt:return 2;case kt:return 8;case Tt:case be:return 32;case Ft:return 268435456;default:return 32}default:return 32}}var Tr=!1,Nl=null,wl=null,Ml=null,Xn=new Map,Ln=new Map,Ul=[],Sh="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Nd(t,e){switch(t){case"focusin":case"focusout":Nl=null;break;case"dragenter":case"dragleave":wl=null;break;case"mouseover":case"mouseout":Ml=null;break;case"pointerover":case"pointerout":Xn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ln.delete(e.pointerId)}}function Qn(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=ia(e),e!==null&&Od(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function xh(t,e,l,a,n){switch(e){case"focusin":return Nl=Qn(Nl,t,e,l,a,n),!0;case"dragenter":return wl=Qn(wl,t,e,l,a,n),!0;case"mouseover":return Ml=Qn(Ml,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return Xn.set(u,Qn(Xn.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Ln.set(u,Qn(Ln.get(u)||null,t,e,l,a,n)),!0}return!1}function wd(t){var e=ca(t.target);if(e!==null){var l=_(e);if(l!==null){if(e=l.tag,e===13){if(e=U(l),e!==null){t.blockedOn=e,h0(t.priority,function(){if(l.tag===13){var a=De();a=hc(a);var n=pa(l,a);n!==null&&Ne(n,l,a),br(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function lc(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=Sr(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);Ac=a,l.target.dispatchEvent(a),Ac=null}else return e=ia(l),e!==null&&Od(e),t.blockedOn=l,!1;e.shift()}return!0}function Md(t,e,l){lc(t)&&l.delete(e)}function Th(){Tr=!1,Nl!==null&&lc(Nl)&&(Nl=null),wl!==null&&lc(wl)&&(wl=null),Ml!==null&&lc(Ml)&&(Ml=null),Xn.forEach(Md),Ln.forEach(Md)}function ac(t,e){t.blockedOn===e&&(t.blockedOn=null,Tr||(Tr=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Th)))}var nc=null;function Ud(t){nc!==t&&(nc=t,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){nc===t&&(nc=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(xr(a||l)===null)continue;break}var u=ia(l);u!==null&&(t.splice(e,3),e-=3,xi(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Zn(t){function e(s){return ac(s,t)}Nl!==null&&ac(Nl,t),wl!==null&&ac(wl,t),Ml!==null&&ac(Ml,t),Xn.forEach(e),Ln.forEach(e);for(var l=0;l<Ul.length;l++){var a=Ul[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Ul.length&&(l=Ul[0],l.blockedOn===null);)wd(l),l.blockedOn===null&&Ul.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],c=n[pe]||null;if(typeof u=="function")c||Ud(l);else if(c){var i=null;if(u&&u.hasAttribute("formAction")){if(n=u,c=u[pe]||null)i=c.formAction;else if(xr(n)!==null)continue}else i=c.action;typeof i=="function"?l[a+1]=i:(l.splice(a,3),a-=3),Ud(l)}}}function Er(t){this._internalRoot=t}uc.prototype.render=Er.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(f(409));var l=e.current,a=De();Rd(l,a,t,e,null,null)},uc.prototype.unmount=Er.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Rd(t.current,2,null,t,null,null),Gu(),e[ua]=null}};function uc(t){this._internalRoot=t}uc.prototype.unstable_scheduleHydration=function(t){if(t){var e=Jr();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Ul.length&&e!==0&&e<Ul[l].priority;l++);Ul.splice(l,0,t),l===0&&wd(t)}};var Cd=v.version;if(Cd!=="19.1.0")throw Error(f(527,Cd,"19.1.0"));j.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(f(188)):(t=Object.keys(t).join(","),Error(f(268,t)));return t=R(e),t=t!==null?S(t):null,t=t===null?null:t.stateNode,t};var Eh={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:x,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var cc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!cc.isDisabled&&cc.supportsFiber)try{qt=cc.inject(Eh),st=cc}catch{}}return Kn.createRoot=function(t,e){if(!T(t))throw Error(f(299));var l=!1,a="",n=Ws,u=$s,c=Fs,i=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(c=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(i=e.unstable_transitionCallbacks)),e=Ad(t,1,!1,null,null,l,a,n,u,c,i,null),t[ua]=e.current,ur(t),new Er(e)},Kn.hydrateRoot=function(t,e,l){if(!T(t))throw Error(f(299));var a=!1,n="",u=Ws,c=$s,i=Fs,s=null,y=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(c=l.onCaughtError),l.onRecoverableError!==void 0&&(i=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(s=l.unstable_transitionCallbacks),l.formState!==void 0&&(y=l.formState)),e=Ad(t,1,!0,e,l??null,a,n,u,c,i,s,y),e.context=zd(null),l=e.current,a=De(),a=hc(a),n=yl(a),n.callback=null,vl(l,n,a),l=a,e.current.lanes=l,Wa(e,l),Je(e),t[ua]=e.current,ur(t),new uc(e)},Kn.version="19.1.0",Kn}var Zd;function Uh(){if(Zd)return Rr.exports;Zd=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(v){console.error(v)}}return r(),Rr.exports=Mh(),Rr.exports}var Ch=Uh();const Yr=Wd(Ch),jh=()=>!window.invokeNative,Hh=()=>{},Qe=(r,v)=>{const o=D.useRef(Hh);D.useEffect(()=>{o.current=v},[v]),D.useEffect(()=>{const f=T=>{const{action:_,data:U}=T.data;_===r&&o.current&&o.current(U)};return window.addEventListener("message",f),()=>{window.removeEventListener("message",f)}},[r])};async function Ur(r,v,o){const f={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(v)},T=window.GetParentResourceName?window.GetParentResourceName():"nui-frame-app";return await(await fetch(`https://${T}/${r}`,f)).json()}const $d=D.createContext(null),Gr=({children:r})=>{const[v,o]=D.useState(jh()),[f,T]=D.useState(!1),[_,U]=D.useState(null),[B,R]=D.useState(!1),S=D.useRef(!1),C=D.useRef(!1),k=D.useRef(null),V=F=>{F?(R(!1),o(!0)):(!S.current||C.current)&&(R(!0),setTimeout(()=>{o(!1),R(!1)},300))};return D.useEffect(()=>{const F=()=>{C.current=!1,S.current&&(k.current&&clearTimeout(k.current),k.current=setTimeout(()=>{V(!0),Ur("refreshTournament",{})},200))};return window.addEventListener("fishCardClosed",F),()=>{window.removeEventListener("fishCardClosed",F),k.current&&clearTimeout(k.current)}},[]),Qe("setVisible",F=>{(!F&&!S.current||F)&&V(F)}),Qe("setTournamentVisible",F=>{S.current=F,F?V(!0):C.current||V(!1)}),Qe("updateTournament",F=>{U(F),S.current=!0,(!f||!v)&&(T(!0),V(!0))}),Qe("tournamentEnded",()=>{S.current=!1,V(!1),setTimeout(()=>{T(!1),U(null)},300)}),Qe("showFishCatch",F=>{C.current=!0,_&&(S.current=!0)}),N.jsx($d.Provider,{value:{visible:v,setVisible:V,isExiting:B,tournamentData:_,setTournamentData:U},children:N.jsx("div",{style:{visibility:v||B?"visible":"hidden",height:"100%",pointerEvents:v?"auto":"none",position:"relative"},children:r})})},qh=()=>{const r=D.useContext($d);if(r===null)throw new Error("useVisibility was used outside of its provider");return r};function Nr(r,v=500){const[o,f]=D.useState(r),T=D.useRef(r),_=D.useRef(0),U=D.useRef(0);return D.useEffect(()=>{if(T.current===r)return;const B=T.current,R=r-B,S=C=>{U.current||(U.current=C);const k=Math.min((C-U.current)/v,1),V=Math.round(B+R*k);f(V),k<1?_.current=requestAnimationFrame(S):(T.current=r,U.current=0)};return _.current=requestAnimationFrame(S),()=>{_.current&&cancelAnimationFrame(_.current)}},[r,v]),o}function Bh({players:r,timeLeft:v,currentPosition:o=5,totalPlayers:f=30,status:T,totalDuration:_,locales:U={}}){const[B,R]=D.useState(v||0),[S,C]=D.useState(0),[k,V]=D.useState(!1),[F,ct]=D.useState(!1),[it,Rt]=D.useState({}),{visible:ht,isExiting:ee}=qh(),lt=D.useRef(null),pt=D.useRef(void 0),J=D.useRef(null),Mt=D.useRef(Date.now()),vt=D.useRef(null),X=D.useRef(!1),Ct=D.useRef("increment"),gt=D.useRef(null),Yt=D.useRef(0),_t=D.useRef(null),Et=D.useRef(null),Ot=H=>(U==null?void 0:U[H])||H;D.useEffect(()=>{_?(gt.current=_,Et.current=_*.2):T==="active"?(gt.current=1800,Et.current=360):T==="waiting"&&(gt.current=600,Et.current=120),vt.current!==T&&V(!1),Yt.current=0},[_,T]),D.useEffect(()=>{if(ht&&!X.current&&gt.current!==null&&v!==void 0){const H=dt(v,gt.current);C(H),Yt.current=0,setTimeout(()=>{x(H)},50)}X.current=ht},[ht,B]);const dt=(H,G)=>Ct.current==="increment"?Math.max(0,Math.min(100,100-H/G*100)):Math.max(0,Math.min(100,H/G*100)),x=H=>{lt.current&&(Yt.current=H,lt.current.style.width=`${H}%`,lt.current.setAttribute("data-progress",H.toFixed(2)))};D.useEffect(()=>{if(!ht||B===void 0||B===null||!gt.current)return;B!==_t.current&&(_t.current=B,Yt.current=0);const H=dt(B,gt.current);x(H);let G;const tt=performance.now(),I=jt=>{if(!ht)return;const he=(jt-tt)/1e3,ge=Math.max(0,B-he),le=dt(ge,gt.current);x(le),G=requestAnimationFrame(I)};return G=requestAnimationFrame(I),()=>{cancelAnimationFrame(G)}},[ht,B,_,T]),D.useEffect(()=>{T==="active"&&vt.current==="active"&&B<=(Et.current||0)&&V(!0)},[T,B]),D.useEffect(()=>{(T!==vt.current||v!==pt.current)&&(vt.current=T,pt.current=v,Yt.current=0,C(0),_t.current=null,V(!1),lt.current&&(lt.current.style.transition="none",lt.current.style.width="0%",setTimeout(()=>{lt.current&&(lt.current.style.transition="width 0.2s linear")},50)))},[T,v,_,B]),D.useEffect(()=>{if(_!==gt.current&&(gt.current=_??null,_&&(Et.current=_*.2),Yt.current=0),T!==vt.current&&(vt.current=T,T!=="active"&&V(!1),ct(!1),Yt.current=0,Ct.current="increment",gt.current!==null&&v!==void 0)){const H=dt(v,gt.current);x(H)}if(v!==void 0&&v>0&&(R(v),pt.current=v,T==="active"&&v<=(Et.current||0)&&V(!0),gt.current!==null)){const H=dt(v,gt.current);x(H)}return J.current!==null&&window.clearInterval(J.current),Mt.current=Date.now(),J.current=window.setInterval(()=>{const H=Date.now();Mt.current=H,T!==vt.current&&(vt.current=T,V(!1)),R(G=>{if(G<=0)return T==="active"&&ct(!0),0;const tt=G-1;return gt.current!==null&&Et.current!==null&&T==="active"&&vt.current==="active"&&tt<=Et.current&&!k&&V(!0),tt})},1e3),()=>{J.current!==null&&window.clearInterval(J.current)}},[v,T]),D.useEffect(()=>{const H={};r.forEach(G=>{H[G.id]=it[G.id]||G.position}),Rt(H)},[r]);const j=Math.floor(B/60),w=B%60,ot=`${j}:${w.toString().padStart(2,"0")}`,d=Nr(o),A=D.useMemo(()=>{var H;return T!=="active"?0:((H=r.find(G=>G.position===o))==null?void 0:H.points)||0},[r,o,T]),q=Nr(A),M=H=>T!=="active"?0:(H==null?void 0:H.points)||0,Y=D.useMemo(()=>({height:"100%",borderRadius:"999px",background:k?"linear-gradient(90deg, #ef4444, #dc2626)":"linear-gradient(90deg, #10b981, #059669)",width:`${S}%`,transition:"width 0.2s linear, background 0.2s linear",transform:"translateZ(0)",willChange:"width, background"}),[S,k]);return N.jsxs("div",{className:`tournament-card ${!ht&&ee?"exit":""}`,style:{backgroundColor:"rgba(24, 24, 27, 0.95)",padding:"1.25rem",borderRadius:"1rem",width:"320px",boxShadow:"0 4px 24px rgba(0, 0, 0, 0.3)",border:"1px solid rgba(255, 255, 255, 0.06)",opacity:ht?1:0,transform:`translateY(${ht?"0":"20px"})`,transition:"transform 0.3s ease-out, opacity 0.3s ease-out"},children:[N.jsx("div",{style:{marginBottom:"1rem",display:"flex",justifyContent:"center",alignItems:"center"},children:N.jsx("span",{style:{fontSize:"1.125rem",fontWeight:"600",color:"rgba(255, 255, 255, 0.9)",letterSpacing:"0.02em",textTransform:"uppercase"},children:Ot("tournament_title")})}),N.jsxs("div",{style:{borderRadius:"0.75rem",background:"linear-gradient(180deg, rgba(39, 39, 42, 0.6) 0%, rgba(39, 39, 42, 0.4) 100%)",border:"1px solid rgba(255, 255, 255, 0.08)",overflow:"hidden",marginBottom:"0.75rem"},children:[N.jsx("div",{style:{padding:"0.75rem",background:"linear-gradient(180deg, rgba(39, 39, 42, 0.6) 0%, rgba(39, 39, 42, 0.4) 100%)",borderBottom:"1px solid rgba(255, 255, 255, 0.08)"},children:N.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"0.5rem"},children:[N.jsx("div",{style:{background:"rgba(39, 39, 42, 0.4)",borderRadius:"0.75rem",padding:"0.5rem 0.75rem",border:"1px solid rgba(255, 255, 255, 0.08)",boxShadow:"inset 0 1px 1px rgba(255, 255, 255, 0.025)"},children:N.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"0.25rem"},children:[N.jsxs("div",{style:{display:"flex",alignItems:"baseline",gap:"0.25rem"},children:[N.jsx("span",{style:{fontSize:"1rem",fontWeight:"600",color:"rgba(255, 255, 255, 0.95)",letterSpacing:"0.02em"},children:d}),N.jsxs("span",{style:{fontSize:"1rem",color:"rgba(255, 255, 255, 0.5)",fontWeight:"500"},children:["/ ",f]})]}),N.jsx("span",{style:{fontSize:"0.7rem",color:"rgba(255, 255, 255, 0.5)",textTransform:"uppercase",letterSpacing:"0.05em",fontWeight:"500"},children:Ot("position")})]})}),N.jsx("div",{style:{background:"rgba(39, 39, 42, 0.4)",borderRadius:"0.75rem",padding:"0.5rem 0.75rem",border:"1px solid rgba(255, 255, 255, 0.08)",boxShadow:"inset 0 1px 1px rgba(255, 255, 255, 0.025)"},children:N.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"0.25rem"},children:[N.jsx("span",{style:{fontSize:"1rem",fontWeight:"600",color:"rgba(255, 255, 255, 0.95)",letterSpacing:"0.02em"},children:q}),N.jsx("span",{style:{fontSize:"0.7rem",color:"rgba(255, 255, 255, 0.5)",textTransform:"uppercase",letterSpacing:"0.05em",fontWeight:"500"},children:Ot("points")})]})})]})}),N.jsx("div",{style:{padding:"0.75rem",display:"flex",flexDirection:"column",gap:"0.5rem"},children:[...Array(3)].map((H,G)=>{const tt=[...r].sort((aa,ve)=>M(ve)-M(aa)),I=T==="active"?tt[G]:null,jt=G+1,he=I&&it[I.id]>I.position,ge=I&&it[I.id]<I.position,le=F&&jt===1,ol={1:["#ffd700","#ffa500"],2:["#e5e5e5","#a3a3a3"],3:["#dd9933","#804a00"]},Hl={1:"rgba(255, 215, 0, 0.3)",2:"rgba(192, 192, 192, 0.3)",3:"rgba(205, 127, 50, 0.3)"},ql=Nr(M(I));return N.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"0.75rem 1rem",borderRadius:"0.75rem",background:le?"linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05))":"rgba(39, 39, 42, 0.4)",border:le?"1px solid rgba(255, 215, 0, 0.3)":`1px solid ${Hl[jt]}`,transform:"translateX(0)",transition:"all 0.3s ease-out",animation:le?"winnerPulse 2s ease-in-out infinite":he?"slideInFromRight 0.3s ease-out":ge?"slideInFromLeft 0.3s ease-out":"none",boxShadow:le?"0 0 20px rgba(255, 215, 0, 0.15)":"none"},children:[N.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"0.75rem"},children:[N.jsx("div",{style:{width:"2rem",height:"2rem",borderRadius:"0.5rem",display:"flex",justifyContent:"center",alignItems:"center",fontWeight:"600",background:`linear-gradient(135deg, ${ol[jt][0]}, ${ol[jt][1]})`,opacity:I?1:.3,color:"#000",fontSize:"0.875rem"},children:jt}),N.jsx("span",{style:{color:I?F&&jt===1?"#ffd700":"rgba(255, 255, 255, 0.9)":"rgba(255, 255, 255, 0.4)",fontSize:"0.9rem"},children:I?I.name:Ot("no_one")})]}),N.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-end"},children:[N.jsx("span",{style:{color:I?F&&jt===1?"#ffd700":"rgba(255, 255, 255, 0.95)":"rgba(255, 255, 255, 0.4)",fontWeight:"600",fontSize:"1rem"},children:T==="active"?ql:"-"}),N.jsx("span",{style:{fontSize:"0.7rem",color:"rgba(255, 255, 255, 0.5)",textTransform:"uppercase",letterSpacing:"0.05em"},children:Ot("points")})]})]},I?I.name:`empty-${G}`)})})]}),N.jsx("div",{style:{padding:"0.75rem",borderRadius:"0.75rem",background:"linear-gradient(180deg, rgba(39, 39, 42, 0.5) 0%, rgba(39, 39, 42, 0.4) 100%)",border:F?"1px solid rgba(239, 68, 68, 0.2)":"1px solid rgba(255, 255, 255, 0.08)",boxShadow:"inset 0 1px 1px rgba(255, 255, 255, 0.025)"},children:F?N.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"2.125rem"},children:N.jsx("span",{style:{color:"#ef4444",fontSize:"0.9rem",fontWeight:"700",letterSpacing:"0.05em",textTransform:"uppercase"},children:Ot("tournament_ended")})}):N.jsxs(N.Fragment,{children:[N.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"0.5rem"},children:[N.jsx("span",{style:{color:"rgba(255, 255, 255, 0.6)",fontSize:"0.75rem",fontWeight:"500",textTransform:"uppercase",letterSpacing:"0.02em"},children:Ot(T==="waiting"?"starting_in":"time_left")}),N.jsx("span",{style:{fontWeight:"600",fontSize:"0.9rem",color:k?"#ef4444":"#10b981",letterSpacing:"-0.01em"},children:ot})]}),N.jsx("div",{style:{position:"relative",height:"0.375rem"},children:N.jsx("div",{style:{height:"100%",borderRadius:"999px",background:k?"rgba(239, 68, 68, 0.15)":"rgba(16, 185, 129, 0.15)"},children:N.jsx("div",{ref:lt,style:Y,"data-progress":"0"})})})]})})]})}const Yh=`
@keyframes slideInFromRight {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes winnerPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
  }
}

@media screen and (max-height: 1080px) and (max-height: 1439px) {
  .tournament-card {
    transform: scale(0.7);
    transform-origin: bottom right;
    margin: 0;
  }
}

@media screen and (max-height: 900px) {
  .tournament-card {
    transform: scale(0.65);
    transform-origin: bottom right;
    margin: 0;
  }
}

@media screen and (max-height: 720px) {
  .tournament-card {
    transform: scale(0.6);
    transform-origin: bottom right;
    margin: 0;
  }
}

@media screen and (min-aspect-ratio: 16/9) and (max-height: 1080px) {
  .tournament-card {
    transform: scale(0.7);
    transform-origin: bottom right;
    margin: 0;
  }
}

@media screen and (min-aspect-ratio: 21/9) and (max-height: 1080px) {
  .tournament-card {
    transform: scale(0.65);
    transform-origin: bottom right;
    margin: 0;
  }
}
`,Fd=document.createElement("style");Fd.innerText=Yh;document.head.appendChild(Fd);const Gh=()=>{const[r,v]=D.useState(null),[o,f]=D.useState(!1),T=D.useRef(!1),_=D.useRef(null);return Qe("updateTournament",U=>{v(U),f(!0),T.current=!0,_.current&&(_.current.style.visibility="visible")}),Qe("tournamentEnded",()=>{v(null),f(!1),T.current=!1}),Qe("setTournamentVisible",U=>{f(U),_.current&&(_.current.style.visibility=U?"visible":"hidden")}),N.jsx("div",{className:"container",style:{position:"fixed",inset:0,display:"flex",justifyContent:"center",alignItems:"center",pointerEvents:"auto",zIndex:9999,backgroundColor:"transparent"},children:r&&o&&N.jsx("div",{ref:_,className:"tournament-container",style:{position:"fixed",inset:0,display:"flex",justifyContent:"flex-end",alignItems:"flex-end",padding:"1.5rem",pointerEvents:"auto",zIndex:9999},children:N.jsx(Bh,{...r,timeLeft:r.timeLeft||0,currentPosition:r.currentPosition||0})})})};function Id(r){var v,o,f="";if(typeof r=="string"||typeof r=="number")f+=r;else if(typeof r=="object")if(Array.isArray(r)){var T=r.length;for(v=0;v<T;v++)r[v]&&(o=Id(r[v]))&&(f&&(f+=" "),f+=o)}else for(o in r)r[o]&&(f&&(f+=" "),f+=o);return f}function Xh(){for(var r,v,o=0,f="",T=arguments.length;o<T;o++)(r=arguments[o])&&(v=Id(r))&&(f&&(f+=" "),f+=v);return f}const Xr="-",Lh=r=>{const v=Zh(r),{conflictingClassGroups:o,conflictingClassGroupModifiers:f}=r;return{getClassGroupId:U=>{const B=U.split(Xr);return B[0]===""&&B.length!==1&&B.shift(),Pd(B,v)||Qh(U)},getConflictingClassGroupIds:(U,B)=>{const R=o[U]||[];return B&&f[U]?[...R,...f[U]]:R}}},Pd=(r,v)=>{var U;if(r.length===0)return v.classGroupId;const o=r[0],f=v.nextPart.get(o),T=f?Pd(r.slice(1),f):void 0;if(T)return T;if(v.validators.length===0)return;const _=r.join(Xr);return(U=v.validators.find(({validator:B})=>B(_)))==null?void 0:U.classGroupId},Vd=/^\[(.+)\]$/,Qh=r=>{if(Vd.test(r)){const v=Vd.exec(r)[1],o=v==null?void 0:v.substring(0,v.indexOf(":"));if(o)return"arbitrary.."+o}},Zh=r=>{const{theme:v,classGroups:o}=r,f={nextPart:new Map,validators:[]};for(const T in o)Cr(o[T],f,T,v);return f},Cr=(r,v,o,f)=>{r.forEach(T=>{if(typeof T=="string"){const _=T===""?v:Kd(v,T);_.classGroupId=o;return}if(typeof T=="function"){if(Vh(T)){Cr(T(f),v,o,f);return}v.validators.push({validator:T,classGroupId:o});return}Object.entries(T).forEach(([_,U])=>{Cr(U,Kd(v,_),o,f)})})},Kd=(r,v)=>{let o=r;return v.split(Xr).forEach(f=>{o.nextPart.has(f)||o.nextPart.set(f,{nextPart:new Map,validators:[]}),o=o.nextPart.get(f)}),o},Vh=r=>r.isThemeGetter,Kh=r=>{if(r<1)return{get:()=>{},set:()=>{}};let v=0,o=new Map,f=new Map;const T=(_,U)=>{o.set(_,U),v++,v>r&&(v=0,f=o,o=new Map)};return{get(_){let U=o.get(_);if(U!==void 0)return U;if((U=f.get(_))!==void 0)return T(_,U),U},set(_,U){o.has(_)?o.set(_,U):T(_,U)}}},jr="!",Hr=":",kh=Hr.length,Jh=r=>{const{prefix:v,experimentalParseClassName:o}=r;let f=T=>{const _=[];let U=0,B=0,R=0,S;for(let ct=0;ct<T.length;ct++){let it=T[ct];if(U===0&&B===0){if(it===Hr){_.push(T.slice(R,ct)),R=ct+kh;continue}if(it==="/"){S=ct;continue}}it==="["?U++:it==="]"?U--:it==="("?B++:it===")"&&B--}const C=_.length===0?T:T.substring(R),k=Wh(C),V=k!==C,F=S&&S>R?S-R:void 0;return{modifiers:_,hasImportantModifier:V,baseClassName:k,maybePostfixModifierPosition:F}};if(v){const T=v+Hr,_=f;f=U=>U.startsWith(T)?_(U.substring(T.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:U,maybePostfixModifierPosition:void 0}}if(o){const T=f;f=_=>o({className:_,parseClassName:T})}return f},Wh=r=>r.endsWith(jr)?r.substring(0,r.length-1):r.startsWith(jr)?r.substring(1):r,$h=r=>{const v=Object.fromEntries(r.orderSensitiveModifiers.map(f=>[f,!0]));return f=>{if(f.length<=1)return f;const T=[];let _=[];return f.forEach(U=>{U[0]==="["||v[U]?(T.push(..._.sort(),U),_=[]):_.push(U)}),T.push(..._.sort()),T}},Fh=r=>({cache:Kh(r.cacheSize),parseClassName:Jh(r),sortModifiers:$h(r),...Lh(r)}),Ih=/\s+/,Ph=(r,v)=>{const{parseClassName:o,getClassGroupId:f,getConflictingClassGroupIds:T,sortModifiers:_}=v,U=[],B=r.trim().split(Ih);let R="";for(let S=B.length-1;S>=0;S-=1){const C=B[S],{isExternal:k,modifiers:V,hasImportantModifier:F,baseClassName:ct,maybePostfixModifierPosition:it}=o(C);if(k){R=C+(R.length>0?" "+R:R);continue}let Rt=!!it,ht=f(Rt?ct.substring(0,it):ct);if(!ht){if(!Rt){R=C+(R.length>0?" "+R:R);continue}if(ht=f(ct),!ht){R=C+(R.length>0?" "+R:R);continue}Rt=!1}const ee=_(V).join(":"),lt=F?ee+jr:ee,pt=lt+ht;if(U.includes(pt))continue;U.push(pt);const J=T(ht,Rt);for(let Mt=0;Mt<J.length;++Mt){const vt=J[Mt];U.push(lt+vt)}R=C+(R.length>0?" "+R:R)}return R};function tg(){let r=0,v,o,f="";for(;r<arguments.length;)(v=arguments[r++])&&(o=t0(v))&&(f&&(f+=" "),f+=o);return f}const t0=r=>{if(typeof r=="string")return r;let v,o="";for(let f=0;f<r.length;f++)r[f]&&(v=t0(r[f]))&&(o&&(o+=" "),o+=v);return o};function eg(r,...v){let o,f,T,_=U;function U(R){const S=v.reduce((C,k)=>k(C),r());return o=Fh(S),f=o.cache.get,T=o.cache.set,_=B,B(R)}function B(R){const S=f(R);if(S)return S;const C=Ph(R,o);return T(R,C),C}return function(){return _(tg.apply(null,arguments))}}const te=r=>{const v=o=>o[r]||[];return v.isThemeGetter=!0,v},e0=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,l0=/^\((?:(\w[\w-]*):)?(.+)\)$/i,lg=/^\d+\/\d+$/,ag=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ng=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ug=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,cg=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ig=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Za=r=>lg.test(r),nt=r=>!!r&&!Number.isNaN(Number(r)),jl=r=>!!r&&Number.isInteger(Number(r)),wr=r=>r.endsWith("%")&&nt(r.slice(0,-1)),sl=r=>ag.test(r),rg=()=>!0,fg=r=>ng.test(r)&&!ug.test(r),a0=()=>!1,sg=r=>cg.test(r),og=r=>ig.test(r),dg=r=>!Q(r)&&!Z(r),mg=r=>Va(r,c0,a0),Q=r=>e0.test(r),la=r=>Va(r,i0,fg),Mr=r=>Va(r,bg,nt),kd=r=>Va(r,n0,a0),hg=r=>Va(r,u0,og),ic=r=>Va(r,r0,sg),Z=r=>l0.test(r),kn=r=>Ka(r,i0),gg=r=>Ka(r,pg),Jd=r=>Ka(r,n0),yg=r=>Ka(r,c0),vg=r=>Ka(r,u0),rc=r=>Ka(r,r0,!0),Va=(r,v,o)=>{const f=e0.exec(r);return f?f[1]?v(f[1]):o(f[2]):!1},Ka=(r,v,o=!1)=>{const f=l0.exec(r);return f?f[1]?v(f[1]):o:!1},n0=r=>r==="position"||r==="percentage",u0=r=>r==="image"||r==="url",c0=r=>r==="length"||r==="size"||r==="bg-size",i0=r=>r==="length",bg=r=>r==="number",pg=r=>r==="family-name",r0=r=>r==="shadow",Sg=()=>{const r=te("color"),v=te("font"),o=te("text"),f=te("font-weight"),T=te("tracking"),_=te("leading"),U=te("breakpoint"),B=te("container"),R=te("spacing"),S=te("radius"),C=te("shadow"),k=te("inset-shadow"),V=te("text-shadow"),F=te("drop-shadow"),ct=te("blur"),it=te("perspective"),Rt=te("aspect"),ht=te("ease"),ee=te("animate"),lt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],pt=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],J=()=>[...pt(),Z,Q],Mt=()=>["auto","hidden","clip","visible","scroll"],vt=()=>["auto","contain","none"],X=()=>[Z,Q,R],Ct=()=>[Za,"full","auto",...X()],gt=()=>[jl,"none","subgrid",Z,Q],Yt=()=>["auto",{span:["full",jl,Z,Q]},jl,Z,Q],_t=()=>[jl,"auto",Z,Q],Et=()=>["auto","min","max","fr",Z,Q],Ot=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],dt=()=>["start","end","center","stretch","center-safe","end-safe"],x=()=>["auto",...X()],j=()=>[Za,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...X()],w=()=>[r,Z,Q],ot=()=>[...pt(),Jd,kd,{position:[Z,Q]}],d=()=>["no-repeat",{repeat:["","x","y","space","round"]}],A=()=>["auto","cover","contain",yg,mg,{size:[Z,Q]}],q=()=>[wr,kn,la],M=()=>["","none","full",S,Z,Q],Y=()=>["",nt,kn,la],H=()=>["solid","dashed","dotted","double"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],tt=()=>[nt,wr,Jd,kd],I=()=>["","none",ct,Z,Q],jt=()=>["none",nt,Z,Q],he=()=>["none",nt,Z,Q],ge=()=>[nt,Z,Q],le=()=>[Za,"full",...X()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[sl],breakpoint:[sl],color:[rg],container:[sl],"drop-shadow":[sl],ease:["in","out","in-out"],font:[dg],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[sl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[sl],shadow:[sl],spacing:["px",nt],text:[sl],"text-shadow":[sl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Za,Q,Z,Rt]}],container:["container"],columns:[{columns:[nt,Q,Z,B]}],"break-after":[{"break-after":lt()}],"break-before":[{"break-before":lt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:J()}],overflow:[{overflow:Mt()}],"overflow-x":[{"overflow-x":Mt()}],"overflow-y":[{"overflow-y":Mt()}],overscroll:[{overscroll:vt()}],"overscroll-x":[{"overscroll-x":vt()}],"overscroll-y":[{"overscroll-y":vt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Ct()}],"inset-x":[{"inset-x":Ct()}],"inset-y":[{"inset-y":Ct()}],start:[{start:Ct()}],end:[{end:Ct()}],top:[{top:Ct()}],right:[{right:Ct()}],bottom:[{bottom:Ct()}],left:[{left:Ct()}],visibility:["visible","invisible","collapse"],z:[{z:[jl,"auto",Z,Q]}],basis:[{basis:[Za,"full","auto",B,...X()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[nt,Za,"auto","initial","none",Q]}],grow:[{grow:["",nt,Z,Q]}],shrink:[{shrink:["",nt,Z,Q]}],order:[{order:[jl,"first","last","none",Z,Q]}],"grid-cols":[{"grid-cols":gt()}],"col-start-end":[{col:Yt()}],"col-start":[{"col-start":_t()}],"col-end":[{"col-end":_t()}],"grid-rows":[{"grid-rows":gt()}],"row-start-end":[{row:Yt()}],"row-start":[{"row-start":_t()}],"row-end":[{"row-end":_t()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Et()}],"auto-rows":[{"auto-rows":Et()}],gap:[{gap:X()}],"gap-x":[{"gap-x":X()}],"gap-y":[{"gap-y":X()}],"justify-content":[{justify:[...Ot(),"normal"]}],"justify-items":[{"justify-items":[...dt(),"normal"]}],"justify-self":[{"justify-self":["auto",...dt()]}],"align-content":[{content:["normal",...Ot()]}],"align-items":[{items:[...dt(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...dt(),{baseline:["","last"]}]}],"place-content":[{"place-content":Ot()}],"place-items":[{"place-items":[...dt(),"baseline"]}],"place-self":[{"place-self":["auto",...dt()]}],p:[{p:X()}],px:[{px:X()}],py:[{py:X()}],ps:[{ps:X()}],pe:[{pe:X()}],pt:[{pt:X()}],pr:[{pr:X()}],pb:[{pb:X()}],pl:[{pl:X()}],m:[{m:x()}],mx:[{mx:x()}],my:[{my:x()}],ms:[{ms:x()}],me:[{me:x()}],mt:[{mt:x()}],mr:[{mr:x()}],mb:[{mb:x()}],ml:[{ml:x()}],"space-x":[{"space-x":X()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":X()}],"space-y-reverse":["space-y-reverse"],size:[{size:j()}],w:[{w:[B,"screen",...j()]}],"min-w":[{"min-w":[B,"screen","none",...j()]}],"max-w":[{"max-w":[B,"screen","none","prose",{screen:[U]},...j()]}],h:[{h:["screen",...j()]}],"min-h":[{"min-h":["screen","none",...j()]}],"max-h":[{"max-h":["screen",...j()]}],"font-size":[{text:["base",o,kn,la]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[f,Z,Mr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",wr,Q]}],"font-family":[{font:[gg,Q,v]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[T,Z,Q]}],"line-clamp":[{"line-clamp":[nt,"none",Z,Mr]}],leading:[{leading:[_,...X()]}],"list-image":[{"list-image":["none",Z,Q]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,Q]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:w()}],"text-color":[{text:w()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:[nt,"from-font","auto",Z,la]}],"text-decoration-color":[{decoration:w()}],"underline-offset":[{"underline-offset":[nt,"auto",Z,Q]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:X()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ot()}],"bg-repeat":[{bg:d()}],"bg-size":[{bg:A()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},jl,Z,Q],radial:["",Z,Q],conic:[jl,Z,Q]},vg,hg]}],"bg-color":[{bg:w()}],"gradient-from-pos":[{from:q()}],"gradient-via-pos":[{via:q()}],"gradient-to-pos":[{to:q()}],"gradient-from":[{from:w()}],"gradient-via":[{via:w()}],"gradient-to":[{to:w()}],rounded:[{rounded:M()}],"rounded-s":[{"rounded-s":M()}],"rounded-e":[{"rounded-e":M()}],"rounded-t":[{"rounded-t":M()}],"rounded-r":[{"rounded-r":M()}],"rounded-b":[{"rounded-b":M()}],"rounded-l":[{"rounded-l":M()}],"rounded-ss":[{"rounded-ss":M()}],"rounded-se":[{"rounded-se":M()}],"rounded-ee":[{"rounded-ee":M()}],"rounded-es":[{"rounded-es":M()}],"rounded-tl":[{"rounded-tl":M()}],"rounded-tr":[{"rounded-tr":M()}],"rounded-br":[{"rounded-br":M()}],"rounded-bl":[{"rounded-bl":M()}],"border-w":[{border:Y()}],"border-w-x":[{"border-x":Y()}],"border-w-y":[{"border-y":Y()}],"border-w-s":[{"border-s":Y()}],"border-w-e":[{"border-e":Y()}],"border-w-t":[{"border-t":Y()}],"border-w-r":[{"border-r":Y()}],"border-w-b":[{"border-b":Y()}],"border-w-l":[{"border-l":Y()}],"divide-x":[{"divide-x":Y()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":Y()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...H(),"hidden","none"]}],"divide-style":[{divide:[...H(),"hidden","none"]}],"border-color":[{border:w()}],"border-color-x":[{"border-x":w()}],"border-color-y":[{"border-y":w()}],"border-color-s":[{"border-s":w()}],"border-color-e":[{"border-e":w()}],"border-color-t":[{"border-t":w()}],"border-color-r":[{"border-r":w()}],"border-color-b":[{"border-b":w()}],"border-color-l":[{"border-l":w()}],"divide-color":[{divide:w()}],"outline-style":[{outline:[...H(),"none","hidden"]}],"outline-offset":[{"outline-offset":[nt,Z,Q]}],"outline-w":[{outline:["",nt,kn,la]}],"outline-color":[{outline:w()}],shadow:[{shadow:["","none",C,rc,ic]}],"shadow-color":[{shadow:w()}],"inset-shadow":[{"inset-shadow":["none",k,rc,ic]}],"inset-shadow-color":[{"inset-shadow":w()}],"ring-w":[{ring:Y()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:w()}],"ring-offset-w":[{"ring-offset":[nt,la]}],"ring-offset-color":[{"ring-offset":w()}],"inset-ring-w":[{"inset-ring":Y()}],"inset-ring-color":[{"inset-ring":w()}],"text-shadow":[{"text-shadow":["none",V,rc,ic]}],"text-shadow-color":[{"text-shadow":w()}],opacity:[{opacity:[nt,Z,Q]}],"mix-blend":[{"mix-blend":[...G(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":G()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[nt]}],"mask-image-linear-from-pos":[{"mask-linear-from":tt()}],"mask-image-linear-to-pos":[{"mask-linear-to":tt()}],"mask-image-linear-from-color":[{"mask-linear-from":w()}],"mask-image-linear-to-color":[{"mask-linear-to":w()}],"mask-image-t-from-pos":[{"mask-t-from":tt()}],"mask-image-t-to-pos":[{"mask-t-to":tt()}],"mask-image-t-from-color":[{"mask-t-from":w()}],"mask-image-t-to-color":[{"mask-t-to":w()}],"mask-image-r-from-pos":[{"mask-r-from":tt()}],"mask-image-r-to-pos":[{"mask-r-to":tt()}],"mask-image-r-from-color":[{"mask-r-from":w()}],"mask-image-r-to-color":[{"mask-r-to":w()}],"mask-image-b-from-pos":[{"mask-b-from":tt()}],"mask-image-b-to-pos":[{"mask-b-to":tt()}],"mask-image-b-from-color":[{"mask-b-from":w()}],"mask-image-b-to-color":[{"mask-b-to":w()}],"mask-image-l-from-pos":[{"mask-l-from":tt()}],"mask-image-l-to-pos":[{"mask-l-to":tt()}],"mask-image-l-from-color":[{"mask-l-from":w()}],"mask-image-l-to-color":[{"mask-l-to":w()}],"mask-image-x-from-pos":[{"mask-x-from":tt()}],"mask-image-x-to-pos":[{"mask-x-to":tt()}],"mask-image-x-from-color":[{"mask-x-from":w()}],"mask-image-x-to-color":[{"mask-x-to":w()}],"mask-image-y-from-pos":[{"mask-y-from":tt()}],"mask-image-y-to-pos":[{"mask-y-to":tt()}],"mask-image-y-from-color":[{"mask-y-from":w()}],"mask-image-y-to-color":[{"mask-y-to":w()}],"mask-image-radial":[{"mask-radial":[Z,Q]}],"mask-image-radial-from-pos":[{"mask-radial-from":tt()}],"mask-image-radial-to-pos":[{"mask-radial-to":tt()}],"mask-image-radial-from-color":[{"mask-radial-from":w()}],"mask-image-radial-to-color":[{"mask-radial-to":w()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":pt()}],"mask-image-conic-pos":[{"mask-conic":[nt]}],"mask-image-conic-from-pos":[{"mask-conic-from":tt()}],"mask-image-conic-to-pos":[{"mask-conic-to":tt()}],"mask-image-conic-from-color":[{"mask-conic-from":w()}],"mask-image-conic-to-color":[{"mask-conic-to":w()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ot()}],"mask-repeat":[{mask:d()}],"mask-size":[{mask:A()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Z,Q]}],filter:[{filter:["","none",Z,Q]}],blur:[{blur:I()}],brightness:[{brightness:[nt,Z,Q]}],contrast:[{contrast:[nt,Z,Q]}],"drop-shadow":[{"drop-shadow":["","none",F,rc,ic]}],"drop-shadow-color":[{"drop-shadow":w()}],grayscale:[{grayscale:["",nt,Z,Q]}],"hue-rotate":[{"hue-rotate":[nt,Z,Q]}],invert:[{invert:["",nt,Z,Q]}],saturate:[{saturate:[nt,Z,Q]}],sepia:[{sepia:["",nt,Z,Q]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,Q]}],"backdrop-blur":[{"backdrop-blur":I()}],"backdrop-brightness":[{"backdrop-brightness":[nt,Z,Q]}],"backdrop-contrast":[{"backdrop-contrast":[nt,Z,Q]}],"backdrop-grayscale":[{"backdrop-grayscale":["",nt,Z,Q]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[nt,Z,Q]}],"backdrop-invert":[{"backdrop-invert":["",nt,Z,Q]}],"backdrop-opacity":[{"backdrop-opacity":[nt,Z,Q]}],"backdrop-saturate":[{"backdrop-saturate":[nt,Z,Q]}],"backdrop-sepia":[{"backdrop-sepia":["",nt,Z,Q]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":X()}],"border-spacing-x":[{"border-spacing-x":X()}],"border-spacing-y":[{"border-spacing-y":X()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,Q]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[nt,"initial",Z,Q]}],ease:[{ease:["linear","initial",ht,Z,Q]}],delay:[{delay:[nt,Z,Q]}],animate:[{animate:["none",ee,Z,Q]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[it,Z,Q]}],"perspective-origin":[{"perspective-origin":J()}],rotate:[{rotate:jt()}],"rotate-x":[{"rotate-x":jt()}],"rotate-y":[{"rotate-y":jt()}],"rotate-z":[{"rotate-z":jt()}],scale:[{scale:he()}],"scale-x":[{"scale-x":he()}],"scale-y":[{"scale-y":he()}],"scale-z":[{"scale-z":he()}],"scale-3d":["scale-3d"],skew:[{skew:ge()}],"skew-x":[{"skew-x":ge()}],"skew-y":[{"skew-y":ge()}],transform:[{transform:[Z,Q,"","none","gpu","cpu"]}],"transform-origin":[{origin:J()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:le()}],"translate-x":[{"translate-x":le()}],"translate-y":[{"translate-y":le()}],"translate-z":[{"translate-z":le()}],"translate-none":["translate-none"],accent:[{accent:w()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:w()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,Q]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":X()}],"scroll-mx":[{"scroll-mx":X()}],"scroll-my":[{"scroll-my":X()}],"scroll-ms":[{"scroll-ms":X()}],"scroll-me":[{"scroll-me":X()}],"scroll-mt":[{"scroll-mt":X()}],"scroll-mr":[{"scroll-mr":X()}],"scroll-mb":[{"scroll-mb":X()}],"scroll-ml":[{"scroll-ml":X()}],"scroll-p":[{"scroll-p":X()}],"scroll-px":[{"scroll-px":X()}],"scroll-py":[{"scroll-py":X()}],"scroll-ps":[{"scroll-ps":X()}],"scroll-pe":[{"scroll-pe":X()}],"scroll-pt":[{"scroll-pt":X()}],"scroll-pr":[{"scroll-pr":X()}],"scroll-pb":[{"scroll-pb":X()}],"scroll-pl":[{"scroll-pl":X()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,Q]}],fill:[{fill:["none",...w()]}],"stroke-w":[{stroke:[nt,kn,la,Mr]}],stroke:[{stroke:["none",...w()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},xg=eg(Sg);function ka(...r){return xg(Xh(r))}const f0=D.forwardRef(({className:r,transparent:v=!1,...o},f)=>N.jsx("div",{ref:f,className:ka("rounded-lg",v?"bg-transparent":"",r),style:{backgroundColor:v?"transparent":void 0,background:v?"transparent":void 0,boxShadow:v?"none":void 0,border:v?"none":void 0},...o}));f0.displayName="Card";const Tg=D.forwardRef(({className:r,transparent:v=!1,...o},f)=>N.jsx("div",{ref:f,className:ka("flex flex-col space-y-1.5 p-6",r),style:{backgroundColor:v?"transparent":void 0,background:v?"transparent":void 0},...o}));Tg.displayName="CardHeader";const Eg=D.forwardRef(({className:r,...v},o)=>N.jsx("div",{ref:o,className:ka("text-2xl font-semibold leading-none tracking-tight",r),...v}));Eg.displayName="CardTitle";const Ag=D.forwardRef(({className:r,...v},o)=>N.jsx("div",{ref:o,className:ka("text-sm text-muted-foreground",r),...v}));Ag.displayName="CardDescription";const zg=D.forwardRef(({className:r,transparent:v=!1,...o},f)=>N.jsx("div",{ref:f,className:ka("p-6 pt-0",r),style:{backgroundColor:v?"transparent":void 0,background:v?"transparent":void 0},...o}));zg.displayName="CardContent";const Rg=D.forwardRef(({className:r,...v},o)=>N.jsx("div",{ref:o,className:ka("flex items-center p-6 pt-0",r),...v}));Rg.displayName="CardFooter";/**
 * @license lucide-react v0.474.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _g=r=>r.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s0=(...r)=>r.filter((v,o,f)=>!!v&&v.trim()!==""&&f.indexOf(v)===o).join(" ").trim();/**
 * @license lucide-react v0.474.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Og={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.474.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dg=D.forwardRef(({color:r="currentColor",size:v=24,strokeWidth:o=2,absoluteStrokeWidth:f,className:T="",children:_,iconNode:U,...B},R)=>D.createElement("svg",{ref:R,...Og,width:v,height:v,stroke:r,strokeWidth:f?Number(o)*24/Number(v):o,className:s0("lucide",T),...B},[...U.map(([S,C])=>D.createElement(S,C)),...Array.isArray(_)?_:[_]]));/**
 * @license lucide-react v0.474.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lr=(r,v)=>{const o=D.forwardRef(({className:f,...T},_)=>D.createElement(Dg,{ref:_,iconNode:v,className:s0(`lucide-${_g(r)}`,f),...T}));return o.displayName=`${r}`,o};/**
 * @license lucide-react v0.474.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ng=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],wg=Lr("ArrowLeft",Ng);/**
 * @license lucide-react v0.474.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mg=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],Ug=Lr("ArrowRight",Mg);/**
 * @license lucide-react v0.474.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cg=[["path",{d:"M6.5 12c.94-3.46 4.94-6 8.5-6 3.56 0 6.06 2.54 7 6-.94 3.47-3.44 6-7 6s-7.56-2.53-8.5-6Z",key:"15baut"}],["path",{d:"M18 12v.5",key:"18hhni"}],["path",{d:"M16 17.93a9.77 9.77 0 0 1 0-11.86",key:"16dt7o"}],["path",{d:"M7 10.67C7 8 5.58 5.97 2.73 5.5c-1 1.5-1 5 .23 6.5-1.24 1.5-1.24 5-.23 6.5C5.58 18.03 7 16 7 13.33",key:"l9di03"}],["path",{d:"M10.46 7.26C10.2 5.88 9.17 4.24 8 3h5.8a2 2 0 0 1 1.98 1.67l.23 1.4",key:"1kjonw"}],["path",{d:"m16.01 17.93-.23 1.4A2 2 0 0 1 13.8 21H9.5a5.96 5.96 0 0 0 1.49-3.98",key:"1zlm23"}]],jg=Lr("Fish",Cg),fc={common:{color:"from-gray-500/30 via-gray-400/30 to-gray-600/30",border:"border-gray-400/40",text:"text-gray-300",glow:"drop-shadow-[0_0_3px_rgba(156,163,175,0.4)]",shadowColor:"rgba(156,163,175,0.4)",glowColor:"rgba(156,163,175,0.4)"},uncommon:{color:"from-green-500/30 via-emerald-400/30 to-teal-500/30",border:"border-emerald-400/40",text:"text-emerald-300",glow:"drop-shadow-[0_0_3px_rgba(16,185,129,0.4)]",shadowColor:"rgba(16,185,129,0.4)",glowColor:"rgba(16,185,129,0.4)"},rare:{color:"from-blue-600/40 via-blue-500/40 to-blue-400/40",border:"border-blue-400/50",text:"text-blue-300",glow:"drop-shadow-[0_0_4px_rgba(59,130,246,0.5)]",shadowColor:"rgba(59,130,246,0.5)",glowColor:"rgba(59,130,246,0.5)"},epic:{color:"from-purple-600/40 via-purple-500/40 to-purple-400/40",border:"border-purple-400/50",text:"text-purple-300",glow:"drop-shadow-[0_0_4px_rgba(139,92,246,0.5)]",shadowColor:"rgba(139,92,246,0.5)",glowColor:"rgba(139,92,246,0.5)"},legendary:{color:"from-amber-600/40 via-amber-500/40 to-amber-400/40",border:"border-amber-400/50",text:"text-amber-300",glow:"drop-shadow-[0_0_5px_rgba(245,158,11,0.6)]",shadowColor:"rgba(245,158,11,0.6)",glowColor:"rgba(245,158,11,0.6)"},treasure:{color:"from-yellow-600/50 via-yellow-500/50 to-yellow-400/50",border:"border-yellow-300/60",text:"text-yellow-200",glow:"drop-shadow-[0_0_6px_rgba(252,211,77,0.7)]",shadowColor:"rgba(252,211,77,0.7)",glowColor:"rgba(252,211,77,0.7)"},unknown:{color:"from-slate-500/30 via-slate-400/30 to-slate-600/30",border:"border-slate-400/40",text:"text-slate-300",glow:"drop-shadow-[0_0_3px_rgba(148,163,184,0.4)]",shadowColor:"rgba(148,163,184,0.4)",glowColor:"rgba(148,163,184,0.4)"}},ie={rodMoveSpeed:.6,fishSpeed:.15,fishDashSpeed:.3,catchThreshold:8,catchDuration:2e3,dashProbability:.25,progressBarDecay:0,totalTime:15e3,behaviorChangeInterval:{min:3e3,max:5e3},directionChangeInterval:{normal:2e3,dash:1500}},Hg=({onSuccess:r,onFail:v,initialModifiers:o,locales:f={}})=>{const[T,_]=D.useState(!0),[U,B]=D.useState(!1),[R,S]=D.useState(!1),[C,k]=D.useState(50),V=D.useRef(50),[F,ct]=D.useState(()=>I(50)),[it,Rt]=D.useState(0),[ht,ee]=D.useState(100),lt=D.useRef({left:!1,right:!1}),pt=D.useRef(Math.random()>.5?.5:-.5),J=D.useRef((o==null?void 0:o.fishSpeed)||ie.fishSpeed),Mt=D.useRef((o==null?void 0:o.fishDashSpeed)||ie.fishDashSpeed),vt=D.useRef((o==null?void 0:o.catchThreshold)||ie.catchThreshold),X=D.useRef((o==null?void 0:o.catchDuration)||ie.catchDuration),Ct=D.useRef((o==null?void 0:o.dashProbability)||ie.dashProbability),gt=D.useRef((o==null?void 0:o.rodMoveSpeed)||ie.rodMoveSpeed),Yt=D.useRef(Date.now()),_t=D.useRef(Date.now()),Et=D.useRef("normal"),Ot=D.useRef(null),dt=D.useRef(void 0),x=D.useRef((o==null?void 0:o.progressBarDecay)||ie.progressBarDecay),j=D.useRef(Date.now()),w=D.useRef((o==null?void 0:o.totalTime)||ie.totalTime),ot=D.useRef(0),d=D.useRef(I(50)),A=D.useRef(!0),q=D.useRef(!1),[M,Y]=D.useState((o==null?void 0:o.fishRarity)||"unknown"),H=yt=>(f==null?void 0:f[yt])||yt,tt=D.useCallback(()=>{const yt=M.toLowerCase();return fc[yt]||fc.unknown},[M])();D.useEffect(()=>{o?(J.current=o.fishSpeed,Mt.current=o.fishDashSpeed,vt.current=o.catchThreshold,X.current=o.catchDuration,Ct.current=Math.min(1,o.dashProbability),gt.current=o.rodMoveSpeed,x.current=o.progressBarDecay,w.current=o.totalTime||ie.totalTime,o.fishRarity&&Y(o.fishRarity)):gt.current=ie.rodMoveSpeed,j.current=Date.now();const yt=gt.current;return()=>{gt.current=yt}},[o]);function I(yt){let Tt=Math.random()*80+10;return Math.abs(Tt-yt)<30&&(Tt=Tt<yt?Math.max(5,yt-30):Math.min(95,yt+30)),Tt}const jt=D.useCallback(()=>Math.max(1,vt.current),[]),he=D.useCallback(yt=>{var qt,st,Zt,St,Jn;const kt=gt.current,Tt=yt-Yt.current,be=yt-_t.current,Ft=Math.random()*((((qt=o==null?void 0:o.behaviorChangeInterval)==null?void 0:qt.max)||ie.behaviorChangeInterval.max)-(((st=o==null?void 0:o.behaviorChangeInterval)==null?void 0:st.min)||ie.behaviorChangeInterval.min))+(((Zt=o==null?void 0:o.behaviorChangeInterval)==null?void 0:Zt.min)||ie.behaviorChangeInterval.min);let Gt=!1;if(be>Ft){const oc=Math.random(),dc=Math.min(1,Ct.current),na=Et.current;Et.current=oc>1-dc?"dash":"normal",_t.current=yt,Gt=na!==Et.current,Et.current==="dash"?J.current=Mt.current:J.current=(o==null?void 0:o.fishSpeed)||ie.fishSpeed,Gt&&(A.current=!0)}const Jt=Et.current==="normal"?((St=o==null?void 0:o.directionChangeInterval)==null?void 0:St.normal)||ie.directionChangeInterval.normal:((Jn=o==null?void 0:o.directionChangeInterval)==null?void 0:Jn.dash)||ie.directionChangeInterval.dash;Tt>Jt&&(Et.current==="normal"?pt.current=Math.random()>.5?.5:-.5:pt.current=Math.random()>.5?1:-1,Yt.current=yt),gt.current=kt},[o]),ge=D.useCallback((yt,kt)=>{const Tt=jt();if(Math.abs(C-yt)<Tt){const Ft=!q.current;if(q.current=!0,Ot.current===null)Ot.current=kt,A.current=!0,Rt(0);else if(Ft&&it>0){const st=it/100*X.current;Ot.current=kt-st}const Gt=kt-Ot.current,Jt=Gt/X.current*100,qt=Math.min(Jt,100);if(Math.abs(qt-it)>1&&(A.current=!0,Rt(qt)),Gt>=X.current)return _(!1),B(!0),setTimeout(()=>{r()},1500),!0}else if(q.current=!1,Ot.current!==null){const Ft=x.current;Ft!==0?Rt(Gt=>{const Jt=Math.abs(Ft)*2,qt=Math.max(0,Gt-Jt);return qt<=0?(Ot.current=null,0):(A.current=!0,qt)}):(Ot.current=null,A.current=!0,Rt(0))}return!1},[C,r,jt,it]),le=D.useCallback(yt=>{const kt=yt-j.current,Tt=Math.max(0,100-kt/w.current*100);return Math.abs(Tt-ht)>=1&&(A.current=!0,ee(Tt)),kt>=w.current&&T?(_(!1),S(!0),setTimeout(()=>{v()},1500),!0):!1},[T,v,ht]);D.useEffect(()=>{if(!T)return;const yt=1/60,kt=16,Tt=be=>{if(!T)return;const Ft=gt.current;let Gt=0;lt.current.left&&(Gt-=Ft*20*yt),lt.current.right&&(Gt+=Ft*20*yt),Gt!==0&&(V.current=Math.max(0,Math.min(100,V.current+Gt)),A.current=!0,k(V.current));const Jt=Date.now();if(le(Jt))return;if(be-ot.current>=kt){ot.current=be,he(Jt);const st=J.current*pt.current*50*yt,Zt=d.current+st;let St=Zt;Zt>=90&&(pt.current=-Math.abs(pt.current),Et.current="normal",J.current=(o==null?void 0:o.fishSpeed)||ie.fishSpeed,St=90,A.current=!0),Zt<=10&&(pt.current=Math.abs(pt.current),Et.current="normal",J.current=(o==null?void 0:o.fishSpeed)||ie.fishSpeed,St=10,A.current=!0),d.current=St,ge(St,Jt),(A.current||Math.abs(St-F)>.5)&&(ct(St),A.current=!1)}dt.current=requestAnimationFrame(Tt)};return dt.current=requestAnimationFrame(Tt),()=>{dt.current&&cancelAnimationFrame(dt.current)}},[T,he,ge,le,o]),D.useEffect(()=>{const yt=Tt=>{var Jt,qt,st,Zt;if(!T)return;const be=((qt=(Jt=o==null?void 0:o.controls)==null?void 0:Jt.left)==null?void 0:qt.keys)||["ArrowLeft"],Ft=((Zt=(st=o==null?void 0:o.controls)==null?void 0:st.right)==null?void 0:Zt.keys)||["ArrowRight"],Gt=Tt.key.toLowerCase();(Tt.key==="ArrowLeft"||be.some(St=>St.toLowerCase()===Gt))&&(lt.current.left=!0),(Tt.key==="ArrowRight"||Ft.some(St=>St.toLowerCase()===Gt))&&(lt.current.right=!0)},kt=Tt=>{var Jt,qt,st,Zt;const be=((qt=(Jt=o==null?void 0:o.controls)==null?void 0:Jt.left)==null?void 0:qt.keys)||["ArrowLeft"],Ft=((Zt=(st=o==null?void 0:o.controls)==null?void 0:st.right)==null?void 0:Zt.keys)||["ArrowRight"],Gt=Tt.key.toLowerCase();(Tt.key==="ArrowLeft"||be.some(St=>St.toLowerCase()===Gt))&&(lt.current.left=!1),(Tt.key==="ArrowRight"||Ft.some(St=>St.toLowerCase()===Gt))&&(lt.current.right=!1)};return window.addEventListener("keydown",yt),window.addEventListener("keyup",kt),()=>{window.removeEventListener("keydown",yt),window.removeEventListener("keyup",kt)}},[T,o]);const ol=`h-8 w-8 ${M==="legendary"?"text-amber-300 legendary-fish-effect":Et.current==="dash"?"text-[#ffd700] fish-dash":"text-white"} drop-shadow-md fill-current`,Hl=()=>"water-animation",ql=16,aa=2*Math.PI*ql,ve=aa*(1-ht/100),sc=D.useCallback(()=>{var Tt,be,Ft,Gt,Jt,qt;const yt=(Ft=(be=(Tt=o==null?void 0:o.controls)==null?void 0:Tt.left)==null?void 0:be.keys)==null?void 0:Ft.find(st=>st!=="LEFT"&&st!=="RIGHT"&&st!=="ArrowLeft"&&st!=="ArrowRight"),kt=(qt=(Jt=(Gt=o==null?void 0:o.controls)==null?void 0:Gt.right)==null?void 0:Jt.keys)==null?void 0:qt.find(st=>st!=="LEFT"&&st!=="RIGHT"&&st!=="ArrowLeft"&&st!=="ArrowRight");return N.jsxs(N.Fragment,{children:[N.jsx("span",{className:"inline-flex items-center justify-center w-5 h-5 bg-[rgba(39,39,42,0.6)] rounded border border-[rgba(255,255,255,0.08)] mx-0.5",children:N.jsx(wg,{className:"h-3 w-3 text-[rgba(255,255,255,0.9)]"})}),N.jsx("span",{className:"inline-flex items-center justify-center w-5 h-5 bg-[rgba(39,39,42,0.6)] rounded border border-[rgba(255,255,255,0.08)] mx-0.5",children:N.jsx(Ug,{className:"h-3 w-3 text-[rgba(255,255,255,0.9)]"})}),(yt||kt)&&N.jsxs(N.Fragment,{children:[N.jsx("span",{className:"text-xs text-[rgba(255,255,255,0.6)] mx-1",children:H("or")}),N.jsx("span",{className:"inline-flex items-center justify-center w-5 h-5 bg-[rgba(39,39,42,0.6)] rounded border border-[rgba(255,255,255,0.08)] mx-0.5",children:N.jsx("span",{className:"text-xs text-[rgba(255,255,255,0.9)]",children:yt||"A"})}),N.jsx("span",{className:"inline-flex items-center justify-center w-5 h-5 bg-[rgba(39,39,42,0.6)] rounded border border-[rgba(255,255,255,0.08)] mx-0.5",children:N.jsx("span",{className:"text-xs text-[rgba(255,255,255,0.9)]",children:kt||"D"})})]})]})},[o,H]);return N.jsxs(f0,{className:`w-[400px] p-5 bg-[rgba(24,24,27,0.95)] absolute bottom-32 left-1/2 -translate-x-1/2 shadow-lg 
                    border rounded-xl transition-all duration-300
                    ${it>0?`border-${tt.border.substring(7)}`:"border-[rgba(255,255,255,0.06)]"}`,style:{boxShadow:(it>0,"none")},children:[N.jsx("div",{className:"relative mb-3",children:N.jsxs("div",{className:"relative h-12 bg-[rgba(39,39,42,0.4)] rounded-lg overflow-hidden border border-[rgba(255,255,255,0.08)] shadow-inner",children:[N.jsx("div",{className:`absolute inset-0 bg-gradient-to-r from-[rgba(39,39,42,0.2)] via-[rgba(39,39,42,0.1)] to-[rgba(39,39,42,0.2)] ${Hl()}`,style:{transform:"translateZ(0)"}}),N.jsx("div",{className:`absolute w-14 h-8 transform -translate-x-1/2 transition-transform duration-75 ease-out
              ${U?"bg-[rgba(16,185,129,0.4)]":R?"bg-[rgba(239,68,68,0.4)]":"bg-[rgba(59,130,246,0.4)]"} 
              rounded-md shadow-md border border-[rgba(255,255,255,0.1)]`,style:{left:`${C}%`,top:"8px",willChange:"left",transform:"translateX(-50%)"}}),N.jsx("div",{className:"absolute transition-transform duration-75 ease-out",style:{left:`${F}%`,top:"8px",transform:`translateX(-50%) scaleX(${pt.current>0?1:-1})`,willChange:"left, transform"},children:N.jsx(jg,{className:ol,strokeWidth:1.5})})]})}),N.jsx("div",{className:"w-full h-3 bg-[rgba(39,39,42,0.4)] rounded-full overflow-hidden relative border border-[rgba(255,255,255,0.08)] shadow-inner",children:N.jsx("div",{className:"absolute inset-0 flex justify-center",children:N.jsx("div",{className:"h-full",style:{width:`${it}%`,backgroundColor:it<40?"#ef4444":it<80?"#eab308":"#10b981",transition:"width 100ms linear, background-color 200ms ease",transform:"translateZ(0)"}})})}),N.jsx("div",{className:"absolute bottom-2 right-2 w-10 h-10 z-10",children:N.jsxs("svg",{className:"w-10 h-10",style:{transform:"translateZ(0)"},children:[N.jsx("circle",{cx:"20",cy:"20",r:ql,fill:"transparent",stroke:"rgba(255,255,255,0.1)",strokeWidth:"3"}),N.jsx("circle",{cx:"20",cy:"20",r:ql,fill:"transparent",stroke:ht<30?"#ef4444":ht<60?"#eab308":"#10b981",strokeWidth:"3",strokeDasharray:aa,strokeDashoffset:ve,strokeLinecap:"round",className:"transition-all duration-300 ease-linear"}),N.jsx("text",{x:"20",y:"23",textAnchor:"middle",fill:"white",fontSize:"9",fontWeight:"bold",className:"timer-text",children:H("time_remaining").replace("%ds",Math.ceil(ht/100*(w.current/1e3)).toString())})]})}),N.jsx("div",{className:"mt-4 text-center",children:N.jsxs("p",{className:"text-[rgba(255,255,255,0.6)] text-xs flex items-center justify-center gap-1",children:[H("use"),sc(),H("catch_fish")]})}),U&&N.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-[rgba(16,185,129,0.1)] rounded-xl scale-in",children:N.jsx("div",{className:"bg-[rgba(24,24,27,0.95)] px-8 py-4 rounded-lg border border-[rgba(16,185,129,0.3)] shadow-lg",children:N.jsx("div",{className:"text-center text-[#10b981] font-bold text-xl",children:H("caught_it")})})}),R&&N.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-[rgba(239,68,68,0.1)] rounded-xl scale-in",children:N.jsx("div",{className:"bg-[rgba(24,24,27,0.95)] px-8 py-4 rounded-lg border border-[rgba(239,68,68,0.3)] shadow-lg",children:N.jsx("div",{className:"text-center text-[#ef4444] font-bold text-xl",children:ht<=0?H("time_expired"):H("it_got_away")})})}),N.jsx("style",{children:`
          @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
          }
          
          @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
          }
          
          @keyframes waterMove {
            0% { background-position: 0% 0; }
            100% { background-position: 100% 0; }
          }
          
          @keyframes fishDash {
            0%, 100% { transform: translateY(0) translateZ(0); }
            50% { transform: translateY(-2px) translateZ(0); }
          }
          
          @keyframes legendaryPulse {
            0%, 100% { filter: drop-shadow(0 0 5px rgba(245,158,11,0.7)); transform: scale(1.0) translateZ(0); }
            50% { filter: drop-shadow(0 0 10px rgba(245,158,11,0.9)); transform: scale(1.1) translateZ(0); }
          }
          
          @keyframes legendaryWiggle {
            0%, 100% { transform: rotate(-3deg) translateZ(0); }
            50% { transform: rotate(3deg) translateZ(0); }
          }
          
          .legendary-fish-effect {
            animation: legendaryPulse 1.5s ease-in-out infinite, legendaryWiggle 0.6s ease-in-out infinite;
            will-change: transform, filter;
          }
          
          .scale-in {
            animation: scaleIn 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
          }
          
          .water-animation {
            background-size: 200% 100%;
            animation: waterMove 8s linear infinite;
            will-change: background-position;
          }
          
          .fish-dash {
            animation: fishDash 0.3s ease-in-out infinite;
            will-change: transform;
          }
          
          .timer-text {
            font-family: sans-serif;
          }
          
          .rarity-border-common { 
            border-color: rgba(156,163,175,0.6);
          }
          
          .rarity-border-uncommon { 
            border-color: rgba(16,185,129,0.6);
          }
          
          .rarity-border-rare { 
            border-color: rgba(59,130,246,0.6);
          }
          
          .rarity-border-epic { 
            border-color: rgba(139,92,246,0.6);
          }
          
          .rarity-border-legendary { 
            border-color: rgba(245,158,11,0.6);
          }
          
          .rarity-border-treasure { 
            border-color: rgba(252,211,77,0.7);
          }
          
          .rarity-border-unknown { 
            border-color: rgba(148,163,184,0.5);
          }
        `})]})},qg=()=>{const[r,v]=D.useState(!1),[o,f]=D.useState(null),[T,_]=D.useState({});Qe("showFishGame",R=>{R&&R.modifiers&&f(R.modifiers),R&&R.locales&&_(R.locales),v(!0)}),Qe("fishGameComplete",()=>{v(!1),f(null),_({})});const U=()=>{Ur("fishGameComplete",{success:!0})},B=()=>{Ur("fishGameComplete",{success:!1})};return N.jsx("div",{className:"container",style:{position:"fixed",inset:0,display:"flex",justifyContent:"center",alignItems:"center",pointerEvents:"auto",zIndex:9999,backgroundColor:r?"rgba(0,0,0,0.5)":"transparent"},children:r&&N.jsx("div",{style:{position:"fixed",inset:0,display:"flex",justifyContent:"center",alignItems:"center",pointerEvents:"auto",zIndex:1e4},children:N.jsx(Hg,{onSuccess:U,onFail:B,initialModifiers:o,locales:T})})})},Bg=({fishData:r})=>{const v=r.type.toLowerCase(),[o,f]=D.useState(!1),[T,_]=D.useState(!1),[U,B]=D.useState(!1),R=D.useRef({}),S=D.useRef(!1),C=D.useRef(null);if(D.useEffect(()=>(S.current=!1,f(!1),_(!1),R.current.hide=setTimeout(()=>{f(!0)},3900),R.current.remove=setTimeout(()=>{S.current||(_(!0),S.current=!0,window.dispatchEvent(new CustomEvent("fishCardClosed")))},4500),()=>{R.current.hide&&clearTimeout(R.current.hide),R.current.remove&&clearTimeout(R.current.remove),S.current||(window.dispatchEvent(new CustomEvent("fishCardClosed")),S.current=!0)}),[r]),D.useEffect(()=>{const ct=new Image;ct.onload=()=>B(!0),ct.onerror=()=>B(!0),ct.src=r.icon,C.current&&C.current.complete&&B(!0)},[r.icon]),T)return null;const k=fc[v]||fc.common,V=`rarity_${v}`,F=r[V]||r.type;return N.jsx("div",{className:"fixed inset-0 flex items-center justify-center pointer-events-none",children:N.jsxs("div",{className:`
          relative p-4 
          rounded-2xl overflow-hidden
          shadow-[0_8px_32px_rgba(0,0,0,0.4)]
          bg-gradient-to-br from-black/50 via-black/40 to-black/30
          border-t border-l border-white/20
          ${o?"animate-slideDown":"animate-slideUp"}
          ${U?"opacity-100":"opacity-0"} transition-opacity duration-150
        `,onAnimationEnd:()=>{o&&!S.current&&(_(!0),S.current=!0,window.dispatchEvent(new CustomEvent("fishCardClosed")))},children:[N.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${k.color} animate-gradient`}),N.jsxs("div",{className:"relative z-10 flex items-center gap-4",children:[N.jsxs("div",{className:"relative",children:[N.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${k.color} rounded-xl animate-pulse`}),N.jsx("img",{ref:C,src:r.icon,alt:r.label,className:`
                w-12 h-12 object-contain rounded-xl 
                p-1 bg-black/30
                ${k.glow}
              `,onLoad:()=>B(!0)})]}),N.jsx("div",{children:N.jsxs("div",{className:"flex items-center gap-2",children:[N.jsx("h2",{className:"text-lg font-bold text-white",children:r.label}),N.jsx("span",{className:`
                  text-xs font-medium px-2 py-0.5 rounded-full
                  bg-black/30 
                  ${k.text} tracking-wide uppercase
                `,children:F})]})})]})]})})},Yg=`
@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.98) translateY(5px); }
  to { opacity: 1; transform: scale(1) translateY(0); }
}

.animate-fadeIn {
  animation: fadeIn 0.2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes pulse {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.2; }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes gradient {
  0% { opacity: 0.1; }
  50% { opacity: 0.3; }
  100% { opacity: 0.1; }
}

.animate-gradient {
  animation: gradient 3s ease infinite;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(20px); }
}

.animate-slideUp {
  animation: slideUp 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-slideDown {
  animation: slideDown 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}
`,o0=document.createElement("style");o0.innerText=Yg;document.head.appendChild(o0);const Gg=()=>{const[r,v]=D.useState(null);return Qe("showFishCatch",o=>{o&&typeof o=="object"&&o.data?v(o.data):v(o)}),N.jsx("div",{className:"container",style:{position:"fixed",inset:0,display:"flex",justifyContent:"center",alignItems:"center",pointerEvents:"auto",zIndex:9999,backgroundColor:"transparent"},children:r&&N.jsx(Bg,{fishData:r})})},Qr=r=>{const v=document.getElementById(r);if(v)return v;const o=document.createElement("div");return o.id=r,document.body.appendChild(o),o},Xg=Qr("tournament-root");Yr.createRoot(Xg).render(N.jsx(Br.StrictMode,{children:N.jsx(Gr,{children:N.jsx(Gh,{})})}));const Lg=Qr("minigame-root");Yr.createRoot(Lg).render(N.jsx(Br.StrictMode,{children:N.jsx(Gr,{children:N.jsx(qg,{})})}));const Qg=Qr("fishcard-root");Yr.createRoot(Qg).render(N.jsx(Br.StrictMode,{children:N.jsx(Gr,{children:N.jsx(Gg,{})})}));
