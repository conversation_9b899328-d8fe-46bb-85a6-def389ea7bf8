return {
    Areas = {
        { -- Mirror Park
            Payout = {min = 10500, max = 13500},
            Locations = {
                vec3(1223.03, -696.92, 60.8),
                vec3(1229.6, -725.48, 60.95),
                vec3(1264.76, -702.82, 64.91),
                vec3(1270.91, -683.36, 66.03),
                vec3(1265.47, -647.9, 67.92),
                vec3(1240.56, -601.61, 69.78),
                vec3(1303.11, -527.98, 71.46),
                vec3(1301.16, -574.13, 71.73),
                vec3(1348.26, -547.11, 73.89),
                vec3(1388.88, -569.62, 74.5),
                vec3(1367.37, -606.3, 74.71),
                vec3(999.65, -593.97, 59.64),
            },
        },
        { -- Little Seoul
            Payout = {min = 10500, max = 13500},
            Locations = {
                vec3(-668.41, -971.42, 22.35),
                vec3(-741.53, -982.28, 17.44),
                vec3(-766.38, -916.99, 21.3),
                vec3(-728.57, -879.93, 22.71),
                vec3(-716.42, -864.61, 23.2),
            },
        },
        { -- Grove <PERSON>
            Payout = {min = 10500, max = 13500},
            Locations = {
                vec3(-20.61, -1858.66, 25.41),
                vec3(46.04, -1864.3, 23.28),
                vec3(56.45, -1922.61, 21.91),
                vec3(85.26, -1958.87, 21.12),
                vec3(114.14, -1960.96, 21.33),
                vec3(103.96, -1885.28, 24.32),
                vec3(170.2, -1871.74, 24.4),
            },
        },
        { -- Beach Area
            Payout = {min = 10500, max = 13500},
            Locations = {
                vec3(-1246.52, -1182.79, 7.66),
                vec3(-1285.27, -1253.32, 4.52),
                vec3(-1225.62, -1208.05, 8.27),
                vec3(-1087.14, -1277.54, 5.84),
                vec3(-1084.37, -1559.32, 4.78),
                vec3(-988.85, -1575.71, 5.23),
            },
        },
    },
}