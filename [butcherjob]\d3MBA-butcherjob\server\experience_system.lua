local Framework = exports['d3MBA-lib']:GetFrameworkObject()

RegisterNetEvent("d3MBA-butcher:server:IncreaseExperience", function(experienceType) -- experienceType = "catching" or "processing"
    local src = source
    local identifier = Framework.GetPlayerIdentifiers(src)

    if experienceType == "catching" then 
        randomMin = Config.CatchingChickens.IncreaseExperience.Min
        randomMax = Config.CatchingChickens.IncreaseExperience.Max
        randomExperience = math.random(randomMin, randomMax) -- random number for increase experience
    elseif experienceType == "processing" then 
        randomMin = Config.ProcessingTable.IncreaseExperience.Min
        randomMax = Config.ProcessingTable.IncreaseExperience.Max
        randomExperience = math.random(randomMin, randomMax) -- random number for increase experience
    end 

    TriggerClientEvent('d3MBA-lib:sendNotification', src, Config.Notifications["increase_experience"] ..randomExperience, Framework.NotificationsSettings.Info, 5000)
    
    local getExperience = Framework.fetchScalar('SELECT experience FROM d3_butcherjob WHERE identifier = ?', {identifier})
    local experience = json.decode(getExperience)
    
    
    experience[experienceType] = experience[experienceType] + randomExperience 
    local sqlUpdateExperience = json.encode(experience)
    -- Update player experience 
    Framework.execute('UPDATE d3_butcherjob SET experience = ? WHERE identifier = ?', {sqlUpdateExperience, identifier})
end)
